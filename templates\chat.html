{% extends 'base.html' %}
{% block title %}Chat with <PERSON> <PERSON> <PERSON><PERSON>even Assistants{% endblock %}

{% block extra_css %}
<style>
:root {
    --chat-primary: #667eea;
    --chat-secondary: #764ba2;
    --chat-gradient: linear-gradient(135deg, var(--chat-primary) 0%, var(--chat-secondary) 100%);
    --message-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-chat-container {
    background: var(--chat-gradient);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.chat-header-enhanced {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 24px;
}

.chat-title-enhanced {
    color: white;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.stage-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition-smooth);
}

.stage-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.chat-messages-enhanced {
    height: 500px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 24px;
    scroll-behavior: smooth;
}

.chat-messages-enhanced::-webkit-scrollbar {
    width: 6px;
}

.chat-messages-enhanced::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.chat-messages-enhanced::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.message-wrapper-enhanced {
    margin-bottom: 16px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble-enhanced {
    max-width: 75%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 0.95rem;
    line-height: 1.5;
    box-shadow: var(--message-shadow);
    transition: var(--transition-smooth);
    word-wrap: break-word;
}

.message-bubble-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-message-enhanced {
    text-align: right;
}

.user-bubble-enhanced {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-bottom-right-radius: 6px;
    margin-left: auto;
}

.assistant-message-enhanced {
    text-align: left;
}

.assistant-bubble-enhanced {
    background: rgba(255, 255, 255, 0.95);
    color: #2d3748;
    border-bottom-left-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.chat-input-enhanced {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 24px;
}

.input-group-enhanced {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.message-input-enhanced {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 0.95rem;
    transition: var(--transition-smooth);
    resize: none;
    max-height: 100px;
    min-height: 44px;
}

.message-input-enhanced:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: white;
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}

.send-button-enhanced {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--message-shadow);
}

.send-button-enhanced:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.send-button-enhanced:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.typing-indicator-enhanced {
    display: none;
    padding: 16px 0;
    text-align: left;
}

.typing-dots-enhanced {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px 16px;
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    box-shadow: var(--message-shadow);
}

.typing-dot-enhanced {
    width: 6px;
    height: 6px;
    background: #718096;
    border-radius: 50%;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot-enhanced:nth-child(1) { animation-delay: 0s; }
.typing-dot-enhanced:nth-child(2) { animation-delay: 0.2s; }
.typing-dot-enhanced:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-8px); }
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 4px;
    color: rgba(255, 255, 255, 0.8);
}

.assistant-message-enhanced .message-time {
    color: #718096;
}

@media (max-width: 768px) {
    .chat-messages-enhanced {
        height: 400px;
        padding: 16px;
    }

    .message-bubble-enhanced {
        max-width: 85%;
        font-size: 0.9rem;
    }

    .chat-header-enhanced,
    .chat-input-enhanced {
        padding: 16px 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-lg-10 col-md-12">
      <div class="enhanced-chat-container">
        <div class="chat-header-enhanced d-flex justify-content-between align-items-center">
          <h5 class="chat-title-enhanced">
            <i class="fas fa-user-tie"></i>
            Chat with Sarah
          </h5>
          <span class="stage-badge" id="stageStatus">Stage: Opening</span>
        </div>

        <div class="chat-messages-enhanced" id="chatBox">
          <!-- Messages will be added here dynamically -->
        </div>

        <div class="typing-indicator-enhanced" id="typingIndicator">
          <div class="typing-dots-enhanced">
            <div class="typing-dot-enhanced"></div>
            <div class="typing-dot-enhanced"></div>
            <div class="typing-dot-enhanced"></div>
            <span style="margin-left: 8px; color: #718096; font-size: 0.85rem;">Sarah is typing...</span>
          </div>
        </div>

        <div class="chat-input-enhanced">
          <form id="chatForm" class="input-group-enhanced">
            <textarea
              class="message-input-enhanced"
              id="messageInput"
              placeholder="Type your message here..."
              rows="1"
              autocomplete="off"
              required
            ></textarea>
            <button
              class="send-button-enhanced"
              type="submit"
              id="sendButton"
              title="Send message"
            >
              <i class="fas fa-paper-plane"></i>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Enhanced Internal Chat Application
class EnhancedInternalChat {
    constructor() {
        this.sessionId = "{{ session_id }}";
        this.prospectName = localStorage.getItem('prospect_name') || 'there';
        this.isTyping = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadChatHistory();
        this.setupTextareaAutoResize();
    }

    setupEventListeners() {
        const form = document.getElementById('chatForm');
        const input = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSendMessage();
        });

        // Enter key handling
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            const hasText = input.value.trim().length > 0;
            sendButton.disabled = !hasText || this.isTyping;
        });
    }

    setupTextareaAutoResize() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
        });
    }

    formatTime(date = new Date()) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    addMessage(role, text, showTime = true) {
        const chatBox = document.getElementById('chatBox');

        // Create message wrapper
        const wrapper = document.createElement('div');
        wrapper.className = `message-wrapper-enhanced ${role}-message-enhanced`;

        // Create message bubble
        const bubble = document.createElement('div');
        bubble.className = `message-bubble-enhanced ${role}-bubble-enhanced`;

        // Format text with basic markdown support
        const formattedText = this.formatMessageText(text);
        bubble.innerHTML = formattedText;

        // Add timestamp if requested
        if (showTime) {
            const timeElement = document.createElement('div');
            timeElement.className = 'message-time';
            timeElement.textContent = this.formatTime();
            wrapper.appendChild(bubble);
            wrapper.appendChild(timeElement);
        } else {
            wrapper.appendChild(bubble);
        }

        chatBox.appendChild(wrapper);
        this.scrollToBottom();
    }

    formatMessageText(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code style="background: rgba(0,0,0,0.1); padding: 2px 4px; border-radius: 4px;">$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'block';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    scrollToBottom() {
        const chatBox = document.getElementById('chatBox');
        chatBox.scrollTo({
            top: chatBox.scrollHeight,
            behavior: 'smooth'
        });
    }

    updateStage(stage) {
        const stageStatus = document.getElementById('stageStatus');
        if (stageStatus && stage) {
            stageStatus.textContent = `Stage: ${stage}`;
            // Add a subtle animation
            stageStatus.style.transform = 'scale(1.1)';
            setTimeout(() => {
                stageStatus.style.transform = 'scale(1)';
            }, 200);
        }
    }

    async handleSendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();

        if (!message || this.isTyping) return;

        // Disable input during processing
        this.isTyping = true;
        input.disabled = true;
        document.getElementById('sendButton').disabled = true;

        // Add user message
        this.addMessage('user', message);

        // Clear input and reset height
        input.value = '';
        input.style.height = 'auto';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    prospect_name: this.prospectName
                })
            });

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            if (data.success) {
                if (data.reply) {
                    // Simulate typing delay
                    await this.simulateTypingDelay(data.reply);
                    this.addMessage('assistant', data.reply);
                }
                if (data.stage) {
                    this.updateStage(data.stage);
                }
            } else {
                this.addMessage('assistant', '⚠️ I apologize, but I encountered an error. Please try again.', false);
            }

        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('assistant', '⚠️ I\'m having trouble connecting. Please check your connection and try again.', false);
            console.error('Chat error:', error);
        } finally {
            // Re-enable input
            this.isTyping = false;
            input.disabled = false;
            input.focus();
            document.getElementById('sendButton').disabled = false;
        }
    }

    async simulateTypingDelay(text) {
        const baseDelay = 300;
        const charDelay = Math.min(text.length * 15, 1500);
        const totalDelay = baseDelay + charDelay;

        return new Promise(resolve => setTimeout(resolve, totalDelay));
    }

    async loadChatHistory() {
        try {
            const response = await fetch(`/api/get-chat-history/${this.sessionId}`);
            const data = await response.json();

            if (data.success) {
                // Load existing messages
                if (data.history && data.history.length > 0) {
                    data.history.forEach(item => {
                        this.addMessage(
                            item.role === 'user' ? 'user' : 'assistant',
                            item.content,
                            false // Don't show timestamps for historical messages
                        );
                    });
                }

                // Start conversation if no messages exist
                if (data.message_count === 0) {
                    await this.startConversation();
                }
            }
        } catch (error) {
            console.error('History loading error:', error);
            // Still try to start conversation
            await this.startConversation();
        }
    }

    async startConversation() {
        try {
            this.showTypingIndicator();

            const response = await fetch('/api/chat/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    prospect_name: this.prospectName
                })
            });

            const data = await response.json();
            this.hideTypingIndicator();

            if (data.success && data.reply) {
                await this.simulateTypingDelay(data.reply);
                this.addMessage('assistant', data.reply);
                if (data.stage) {
                    this.updateStage(data.stage);
                }
            }
        } catch (error) {
            this.hideTypingIndicator();
            console.error('Conversation start error:', error);
        }
    }
}

// Initialize the enhanced internal chat
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedInternalChat();
});
</script>
{% endblock %}
