#!/usr/bin/env python3
"""
Check Campaign Recipient Criteria
=================================
Check what recipient criteria is stored for campaigns.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_campaign_criteria():
    """Check campaign recipient criteria"""
    try:
        from unified_sales_system import app, db, EmailCampaign, ContactGroup, ContactGroupMembership, Contact
        
        with app.app_context():
            campaigns = EmailCampaign.query.all()
            
            print("📋 CAMPAIGN RECIPIENT CRITERIA ANALYSIS")
            print("=" * 50)
            
            for campaign in campaigns:
                print(f"\n🎯 Campaign: {campaign.name}")
                print(f"   ID: {campaign.id}")
                print(f"   Status: {campaign.status}")
                print(f"   Recipient Criteria: {campaign.recipient_criteria}")
                
                if campaign.recipient_criteria:
                    try:
                        import json
                        criteria = json.loads(campaign.recipient_criteria)
                        print(f"   Parsed Criteria: {criteria}")
                        
                        recipient_type = criteria.get('type', 'unknown')
                        print(f"   Recipient Type: {recipient_type}")
                        
                        if recipient_type == 'groups':
                            group_ids = criteria.get('group_ids', [])
                            print(f"   Selected Group IDs: {group_ids}")
                            
                            if group_ids:
                                # Get group names
                                groups = ContactGroup.query.filter(ContactGroup.id.in_(group_ids)).all()
                                group_names = [g.name for g in groups]
                                print(f"   Selected Groups: {group_names}")
                                
                                # Count contacts in these groups
                                contact_ids = db.session.query(ContactGroupMembership.contact_id).filter(
                                    ContactGroupMembership.group_id.in_(group_ids)
                                ).distinct().all()
                                contact_count = len(contact_ids)
                                print(f"   Contacts in Selected Groups: {contact_count}")
                            else:
                                print("   ⚠️ No group IDs specified!")
                                
                        elif recipient_type == 'specific':
                            contact_ids = criteria.get('contact_ids', [])
                            print(f"   Selected Contact IDs: {contact_ids}")
                            print(f"   Contact Count: {len(contact_ids)}")
                            
                        elif recipient_type == 'all':
                            all_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
                            print(f"   All Active Contacts: {len(all_contacts)}")
                            
                    except json.JSONDecodeError as e:
                        print(f"   ❌ Invalid JSON in recipient_criteria: {e}")
                else:
                    print("   ⚠️ No recipient criteria set - will send to ALL contacts!")
                    all_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
                    print(f"   Default (All Active Contacts): {len(all_contacts)}")
            
            # Show all available groups
            print(f"\n📊 AVAILABLE CONTACT GROUPS")
            print("=" * 30)
            groups = ContactGroup.query.filter_by(is_active=True).all()
            
            for group in groups:
                member_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()
                print(f"   {group.name} (ID: {group.id}) - {member_count} members")
            
            return True
            
    except Exception as e:
        print(f"❌ Error checking campaign criteria: {e}")
        return False

if __name__ == "__main__":
    check_campaign_criteria()
