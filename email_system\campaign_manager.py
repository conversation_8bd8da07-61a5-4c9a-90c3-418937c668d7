"""
Campaign Manager
===============
Manages email campaign creation, execution, and tracking.
"""

import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from .enhanced_smtp_service import EnhancedSMTPService
from .email_templates import EmailTemplateManager
import logging

logger = logging.getLogger(__name__)

class CampaignManager:
    """Manages email campaigns for 24Seven Assistants"""

    def __init__(self, db_session, smtp_service: EnhancedSMTPService, template_manager: EmailTemplateManager):
        """Initialize campaign manager"""
        self.db = db_session
        self.smtp_service = smtp_service
        self.template_manager = template_manager

    def create_campaign(self,
                       name: str,
                       template_name: str,
                       target_filters: Dict = None,
                       scheduled_at: datetime = None,
                       created_by: str = None):
        """
        Create a new email campaign

        Args:
            name: Campaign name
            template_name: Email template to use
            target_filters: Filters for selecting contacts
            scheduled_at: When to send the campaign
            created_by: User creating the campaign

        Returns:
            Created EmailCampaign object
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign

            # Render template to get subject and content
            template_data = self.template_manager.render_template(template_name, {
                'contact_name': 'Prospect',
                'company_name': 'Your Company'
            })

            # Create campaign
            campaign = EmailCampaign(
                name=name,
                subject=template_data['subject'],
                template_name=template_name,
                email_body_html=template_data['html_body'],
                email_body_text=template_data['text_body'],
                contact_filters=target_filters or {},
                scheduled_at=scheduled_at,
                created_by=created_by,
                status='draft'
            )

            self.db.add(campaign)
            self.db.commit()

            logger.info(f"Campaign '{name}' created successfully")
            return campaign

        except Exception as e:
            logger.error(f"Failed to create campaign: {str(e)}")
            self.db.rollback()
            raise

    def get_target_contacts(self, campaign) -> List:
        """
        Get contacts that match campaign targeting criteria

        Args:
            campaign: EmailCampaign object

        Returns:
            List of matching contacts
        """
        # Import models locally to avoid circular imports
        from models.contact import Contact

        query = self.db.query(Contact).filter(
            Contact.is_active == True,
            Contact.do_not_email == False
        )

        # Apply filters from campaign
        filters = campaign.contact_filters or {}

        if filters.get('status'):
            query = query.filter(Contact.status == filters['status'])

        if filters.get('source'):
            query = query.filter(Contact.source == filters['source'])

        if filters.get('industry'):
            query = query.filter(Contact.industry == filters['industry'])

        if filters.get('company_size'):
            query = query.filter(Contact.company_size == filters['company_size'])

        if filters.get('min_lead_score'):
            query = query.filter(Contact.lead_score >= filters['min_lead_score'])

        return query.all()

    def prepare_campaign(self, campaign_id: int) -> Tuple[bool, str]:
        """
        Prepare campaign for sending by creating email logs

        Args:
            campaign_id: ID of campaign to prepare

        Returns:
            Tuple of (success, message)
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign, EmailLog

            campaign = self.db.query(EmailCampaign).get(campaign_id)
            if not campaign:
                return False, "Campaign not found"

            if campaign.status != 'draft':
                return False, f"Campaign must be in draft status, currently {campaign.status}"

            # Get target contacts
            contacts = self.get_target_contacts(campaign)

            if not contacts:
                return False, "No contacts match the targeting criteria"

            # Create email logs for each contact
            email_logs_created = 0

            for contact in contacts:
                # Check if email log already exists
                existing_log = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.contact_id == contact.id
                ).first()

                if existing_log:
                    continue

                # Generate unique session ID for this contact
                session_id = str(uuid.uuid4())

                # Generate chat URL for the template
                chat_url = f"http://127.0.0.1:8000/?ref=email&conversation_id={session_id}&prospect={contact.first_name or 'there'}&source=email_proposal"

                # Render personalized template
                template_data = self.template_manager.render_template(
                    campaign.template_name,
                    {
                        'contact_name': contact.first_name or 'there',
                        'company_name': contact.company or 'your company',
                        'agent_name': 'Allan Scott',
                        'reply_email': '<EMAIL>',
                        'phone_number': '+256 **********',
                        'industry': contact.industry or 'your industry',
                        'chat_url': chat_url,
                        'session_id': session_id
                    }
                )

                # Create email log
                email_log = EmailLog(
                    campaign_id=campaign.id,
                    contact_id=contact.id,
                    recipient_email=contact.email,
                    recipient_name=contact.full_name,
                    subject=template_data['subject'],
                    email_body_html=template_data['html_body'],
                    email_body_text=template_data['text_body'],
                    status='pending'
                )

                self.db.add(email_log)
                email_logs_created += 1

            # Update campaign
            campaign.total_recipients = email_logs_created
            campaign.status = 'ready'

            self.db.commit()

            logger.info(f"Campaign {campaign_id} prepared with {email_logs_created} recipients")
            return True, f"Campaign prepared successfully with {email_logs_created} recipients"

        except Exception as e:
            logger.error(f"Failed to prepare campaign {campaign_id}: {str(e)}")
            self.db.rollback()
            return False, f"Failed to prepare campaign: {str(e)}"

    def send_campaign(self, campaign_id: int, batch_size: int = 50) -> Tuple[bool, str]:
        """
        Send email campaign

        Args:
            campaign_id: ID of campaign to send
            batch_size: Number of emails to send in each batch

        Returns:
            Tuple of (success, message)
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign, EmailLog

            campaign = self.db.query(EmailCampaign).get(campaign_id)
            if not campaign:
                return False, "Campaign not found"

            if campaign.status not in ['ready', 'draft']:
                return False, f"Campaign cannot be sent, status is {campaign.status}"

            # Prepare campaign if still in draft
            if campaign.status == 'draft':
                success, message = self.prepare_campaign(campaign_id)
                if not success:
                    return False, message

                # Refresh campaign object
                self.db.refresh(campaign)

            # Get pending email logs
            pending_logs = self.db.query(EmailLog).filter(
                EmailLog.campaign_id == campaign.id,
                EmailLog.status == 'pending'
            ).limit(batch_size).all()

            if not pending_logs:
                campaign.status = 'completed'
                campaign.completed_at = datetime.utcnow()
                self.db.commit()
                return True, "Campaign already completed"

            # Update campaign status
            campaign.status = 'running'
            if not campaign.started_at:
                campaign.started_at = datetime.utcnow()

            # Send emails
            emails_to_send = []
            for log in pending_logs:
                emails_to_send.append({
                    'to_email': log.recipient_email,
                    'subject': log.subject,
                    'html_body': log.email_body_html,
                    'text_body': log.email_body_text,
                    'from_name': campaign.sender_name,
                    'from_email': campaign.sender_email,
                    'reply_to': campaign.reply_to_email
                })

            # Send batch
            results = self.smtp_service.send_bulk_emails(
                emails_to_send,
                delay_between_emails=campaign.send_delay_seconds
            )

            # Update email logs and campaign statistics
            sent_count = 0
            delivered_count = 0

            for i, result in enumerate(results):
                log = pending_logs[i]

                if result['success']:
                    log.mark_as_sent(result['message_id'])
                    log.mark_as_delivered()  # Assume delivered for now
                    sent_count += 1
                    delivered_count += 1
                else:
                    log.status = 'failed'
                    log.error_message = result['error_message']

                log.updated_at = datetime.utcnow()

            # Update campaign statistics
            campaign.emails_sent += sent_count
            campaign.emails_delivered += delivered_count

            # Check if campaign is complete
            remaining_pending = self.db.query(EmailLog).filter(
                EmailLog.campaign_id == campaign.id,
                EmailLog.status == 'pending'
            ).count()

            if remaining_pending == 0:
                campaign.status = 'completed'
                campaign.completed_at = datetime.utcnow()

            self.db.commit()

            logger.info(f"Campaign {campaign_id} batch sent: {sent_count}/{len(results)} successful")
            return True, f"Batch sent successfully: {sent_count}/{len(results)} emails"

        except Exception as e:
            logger.error(f"Failed to send campaign {campaign_id}: {str(e)}")
            self.db.rollback()
            return False, f"Failed to send campaign: {str(e)}"

    def pause_campaign(self, campaign_id: int) -> Tuple[bool, str]:
        """Pause a running campaign"""
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign

            campaign = self.db.query(EmailCampaign).get(campaign_id)
            if not campaign:
                return False, "Campaign not found"

            if campaign.status != 'running':
                return False, f"Campaign is not running, status is {campaign.status}"

            campaign.status = 'paused'
            self.db.commit()

            logger.info(f"Campaign {campaign_id} paused")
            return True, "Campaign paused successfully"

        except Exception as e:
            logger.error(f"Failed to pause campaign {campaign_id}: {str(e)}")
            self.db.rollback()
            return False, f"Failed to pause campaign: {str(e)}"

    def resume_campaign(self, campaign_id: int) -> Tuple[bool, str]:
        """Resume a paused campaign"""
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign

            campaign = self.db.query(EmailCampaign).get(campaign_id)
            if not campaign:
                return False, "Campaign not found"

            if campaign.status != 'paused':
                return False, f"Campaign is not paused, status is {campaign.status}"

            campaign.status = 'running'
            self.db.commit()

            logger.info(f"Campaign {campaign_id} resumed")
            return True, "Campaign resumed successfully"

        except Exception as e:
            logger.error(f"Failed to resume campaign {campaign_id}: {str(e)}")
            self.db.rollback()
            return False, f"Failed to resume campaign: {str(e)}"

    def get_campaign_status(self, campaign_id: int) -> Dict:
        """Get detailed campaign status and statistics"""
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailCampaign, EmailLog

            campaign = self.db.query(EmailCampaign).get(campaign_id)
            if not campaign:
                return {}

            # Get email log statistics
            total_logs = self.db.query(EmailLog).filter(
                EmailLog.campaign_id == campaign.id
            ).count()

            pending_logs = self.db.query(EmailLog).filter(
                EmailLog.campaign_id == campaign.id,
                EmailLog.status == 'pending'
            ).count()

            return {
                'campaign': campaign.to_dict(),
                'total_logs': total_logs,
                'pending_logs': pending_logs,
                'progress_percent': ((total_logs - pending_logs) / total_logs * 100) if total_logs > 0 else 0
            }

        except Exception as e:
            logger.error(f"Failed to get campaign status {campaign_id}: {str(e)}")
            return {}
