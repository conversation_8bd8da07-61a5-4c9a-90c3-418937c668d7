# 24Seven Assistants Sales Department System Requirements

# Core Framework
flask==2.3.3
flask-sqlalchemy==3.0.5
flask-migrate==4.0.5
flask-mail==0.9.1
flask-wtf==1.1.1
wtforms==3.0.1

# Database
sqlite3  # Built into Python
psycopg2-binary==2.9.7  # For PostgreSQL if needed

# Email & SMTP
smtplib  # Built into Python
email-validator==2.0.0
jinja2==3.1.2

# API & HTTP
requests==2.31.0
gradio==3.50.0

# Data Processing & Analytics
pandas==2.1.1
numpy==1.24.3
plotly==5.17.0
dash==2.14.1
dash-bootstrap-components==1.5.0

# Background Tasks
celery==5.3.2
redis==5.0.0

# Authentication & Security
flask-login==0.6.3
werkzeug==2.3.7
bcrypt==4.0.1

# Configuration & Environment
python-dotenv==1.0.0
pyyaml==6.0.1

# Date & Time
python-dateutil==2.8.2

# Logging & Monitoring
structlog==23.1.0

# Testing
pytest==7.4.2
pytest-flask==1.2.0

# Development
black==23.7.0
flake8==6.0.0
