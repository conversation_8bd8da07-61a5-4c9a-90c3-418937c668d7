#!/usr/bin/env python3
"""
Fix Database Schema
==================
Update the database schema to match the Contact model
"""

import os
import sqlite3
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

def check_database_schema():
    """Check current database schema"""
    try:
        print("🔍 Checking Current Database Schema")
        print("-" * 50)
        
        # Connect to database
        db_path = 'unified_sales.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if contacts table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='contacts'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("✅ Contacts table exists")
            
            # Get current schema
            cursor.execute("PRAGMA table_info(contacts)")
            columns = cursor.fetchall()
            
            print(f"📋 Current table has {len(columns)} columns:")
            existing_columns = []
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                not_null = col[3]
                existing_columns.append(col_name)
                print(f"   {col_name}: {col_type} {'(NOT NULL)' if not_null else ''}")
            
            conn.close()
            return existing_columns
        else:
            print("❌ Contacts table does not exist")
            conn.close()
            return []
            
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        return []

def add_missing_columns():
    """Add missing columns to contacts table"""
    try:
        print("\n🔧 Adding Missing Columns")
        print("-" * 50)
        
        # Connect to database
        db_path = 'unified_sales.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # List of columns that should exist based on the Contact model
        required_columns = [
            ('website', 'TEXT'),
            ('linkedin_url', 'TEXT'),
            ('industry', 'TEXT'),
            ('company_size', 'TEXT'),
            ('lead_score', 'REAL DEFAULT 0.0'),
            ('preferred_contact_method', 'TEXT DEFAULT "email"'),
            ('timezone', 'TEXT'),
            ('best_contact_time', 'TEXT'),
            ('do_not_email', 'BOOLEAN DEFAULT 0'),
            ('do_not_call', 'BOOLEAN DEFAULT 0'),
            ('current_sales_stage', 'TEXT'),
            ('first_email_sent', 'DATETIME'),
            ('email_opened', 'DATETIME'),
            ('chatbot_link_clicked', 'DATETIME'),
            ('chatbot_conversation_started', 'DATETIME'),
            ('conversion_completed', 'DATETIME'),
            ('chatbot_session_id', 'TEXT'),
            ('email_campaign_id', 'INTEGER'),
            ('last_contacted', 'DATETIME'),
            ('last_activity', 'DATETIME'),
            ('is_active', 'BOOLEAN DEFAULT 1'),
            ('is_customer', 'BOOLEAN DEFAULT 0'),
            ('notes', 'TEXT'),
            ('tags', 'TEXT'),
            ('estimated_budget', 'REAL'),
            ('decision_maker', 'BOOLEAN DEFAULT 0'),
            ('pain_points', 'TEXT'),
            ('current_solution', 'TEXT'),
            ('created_at', 'DATETIME'),
            ('updated_at', 'DATETIME')
        ]
        
        # Get existing columns
        cursor.execute("PRAGMA table_info(contacts)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        
        columns_added = 0
        
        for col_name, col_type in required_columns:
            if col_name not in existing_columns:
                try:
                    alter_sql = f"ALTER TABLE contacts ADD COLUMN {col_name} {col_type}"
                    cursor.execute(alter_sql)
                    print(f"✅ Added column: {col_name} ({col_type})")
                    columns_added += 1
                except Exception as e:
                    print(f"⚠️ Could not add {col_name}: {e}")
        
        if columns_added > 0:
            conn.commit()
            print(f"\n✅ Successfully added {columns_added} columns")
        else:
            print("\n✅ All required columns already exist")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to add columns: {e}")
        return False

def create_test_contact_direct():
    """Create test contact using direct SQL"""
    try:
        print("\n👤 Creating Test Contact with Direct SQL")
        print("-" * 50)
        
        # Connect to database
        db_path = 'unified_sales.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if contact already exists
        cursor.execute("SELECT id, first_name, last_name FROM contacts WHERE email = ?", ('<EMAIL>',))
        existing = cursor.fetchone()
        
        if existing:
            print(f"📝 Contact already exists: ID {existing[0]} - {existing[1]} {existing[2]}")
            print("   Updating existing contact...")
            
            update_sql = """
            UPDATE contacts SET 
                first_name = ?,
                last_name = ?,
                phone = ?,
                company = ?,
                job_title = ?,
                source = ?,
                status = ?,
                is_active = ?,
                do_not_email = ?,
                updated_at = ?
            WHERE email = ?
            """
            
            cursor.execute(update_sql, (
                'Alex', 'Scof', '(*************', 'Test Company', 'CEO',
                'manual_entry', 'new', 1, 0, datetime.utcnow().isoformat(),
                '<EMAIL>'
            ))
            
            print("✅ Contact updated successfully")
        else:
            print("📝 Creating new contact...")
            
            insert_sql = """
            INSERT INTO contacts (
                first_name, last_name, email, phone, company, job_title,
                source, status, is_active, do_not_email, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor.execute(insert_sql, (
                'Alex', 'Scof', '<EMAIL>', '(*************',
                'Test Company', 'CEO', 'manual_entry', 'new', 1, 0,
                datetime.utcnow().isoformat()
            ))
            
            contact_id = cursor.lastrowid
            print(f"✅ Contact created successfully with ID: {contact_id}")
        
        conn.commit()
        
        # Verify contact exists
        cursor.execute("SELECT id, first_name, last_name, email, company, is_active, do_not_email FROM contacts WHERE email = ?", ('<EMAIL>',))
        contact = cursor.fetchone()
        
        if contact:
            print(f"\n✅ Contact verification successful!")
            print(f"   ID: {contact[0]}")
            print(f"   Name: {contact[1]} {contact[2]}")
            print(f"   Email: {contact[3]}")
            print(f"   Company: {contact[4]}")
            print(f"   Active: {bool(contact[5])}")
            print(f"   Do Not Email: {bool(contact[6])}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Direct contact creation failed: {e}")
        return False

def test_flask_contact_query():
    """Test if Flask can now query contacts"""
    try:
        print("\n🌐 Testing Flask Contact Query")
        print("-" * 50)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            print("✅ Application context created")
            
            # Try to query contacts
            contact = Contact.query.filter_by(email='<EMAIL>').first()
            
            if contact:
                print(f"✅ Flask query successful!")
                print(f"   ID: {contact.id}")
                print(f"   Name: {contact.full_name}")
                print(f"   Email: {contact.email}")
                print(f"   Company: {contact.company}")
                print(f"   Active: {contact.is_active}")
                print(f"   Do Not Email: {contact.do_not_email}")
                return True
            else:
                print("⚠️ Contact not found via Flask query")
                return False
                
    except Exception as e:
        print(f"❌ Flask query failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🔧 DATABASE SCHEMA FIX")
    print("=" * 60)
    print("Fixing database schema mismatch for contacts table")
    print("=" * 60)
    
    # Step 1: Check current schema
    existing_columns = check_database_schema()
    
    # Step 2: Add missing columns
    if existing_columns:
        schema_fixed = add_missing_columns()
    else:
        schema_fixed = False
    
    # Step 3: Create test contact with direct SQL
    if schema_fixed:
        contact_created = create_test_contact_direct()
    else:
        contact_created = False
    
    # Step 4: Test Flask query
    if contact_created:
        flask_working = test_flask_contact_query()
    else:
        flask_working = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DATABASE SCHEMA FIX SUMMARY")
    print("=" * 60)
    print(f"Schema Check: {'✅ OK' if existing_columns else '❌ FAILED'}")
    print(f"Schema Fix: {'✅ APPLIED' if schema_fixed else '❌ FAILED'}")
    print(f"Contact Creation: {'✅ SUCCESS' if contact_created else '❌ FAILED'}")
    print(f"Flask Query: {'✅ WORKING' if flask_working else '❌ FAILED'}")
    
    if all([existing_columns, schema_fixed, contact_created, flask_working]):
        print("\n🎉 DATABASE SCHEMA FIXED!")
        print("\n✅ Contact system is now working")
        print("✅ Alex Scof contact is ready")
        print("✅ Flask queries are working")
        
        print("\n🚀 READY FOR EMAIL CAMPAIGNS!")
        print("=" * 40)
        print("Next steps:")
        print("1. Restart the application: python start_app_simple.py")
        print("2. Go to: http://localhost:5000/campaigns")
        print("3. Create a new campaign")
        print("4. Select 'All contacts' as recipients")
        print("5. Send the <NAME_EMAIL>")
        print("\n✅ The email system is already working (SMTP confirmed)")
        print("✅ Contact system is now fixed")
        print("✅ Database schema is updated")
    else:
        print("\n❌ SOME ISSUES REMAIN")
        print("Check the error messages above for details")
    
    return all([existing_columns, schema_fixed, contact_created, flask_working])

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
