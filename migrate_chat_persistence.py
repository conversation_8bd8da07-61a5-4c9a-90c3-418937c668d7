#!/usr/bin/env python3
"""
Chat Persistence Migration Script
=================================
This script adds the chat_messages table to existing databases to enable
chat persistence functionality.

Run this script to upgrade your existing database to support chat persistence.
"""

import sqlite3
import os
import sys
from datetime import datetime

def find_database_files():
    """Find all database files in the current directory and subdirectories"""
    db_files = []
    
    # Common database file names
    common_names = [
        'sales_system.db',
        'unified_sales.db', 
        'sales.db',
        'database.db',
        'app.db'
    ]
    
    # Search current directory
    for filename in os.listdir('.'):
        if filename.endswith('.db') or filename in common_names:
            if os.path.isfile(filename):
                db_files.append(filename)
    
    # Search subdirectories
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db') and file not in [os.path.basename(f) for f in db_files]:
                db_files.append(os.path.join(root, file))
    
    return db_files

def check_table_exists(cursor, table_name):
    """Check if a table exists in the database"""
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
    """, (table_name,))
    return cursor.fetchone() is not None

def migrate_database(db_path):
    """Add chat_messages table to the database"""
    print(f"\n🔄 Migrating database: {db_path}")
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if chat_messages table already exists
        if check_table_exists(cursor, 'chat_messages'):
            print(f"✅ chat_messages table already exists in {db_path}")
            conn.close()
            return True
        
        # Create chat_messages table
        print(f"📝 Creating chat_messages table...")
        cursor.execute('''
            CREATE TABLE chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(100) NOT NULL,
                message_type VARCHAR(20) NOT NULL,
                content TEXT NOT NULL,
                stage VARCHAR(50),
                task VARCHAR(200),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                message_order INTEGER NOT NULL,
                FOREIGN KEY (session_id) REFERENCES chatbot_sessions (session_id)
            )
        ''')
        
        # Create index for better performance
        cursor.execute('''
            CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id)
        ''')
        
        cursor.execute('''
            CREATE INDEX idx_chat_messages_order ON chat_messages(session_id, message_order)
        ''')
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print(f"✅ Successfully migrated {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error migrating {db_path}: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """Main migration function"""
    print("🚀 Chat Persistence Migration Tool")
    print("=" * 50)
    print("This tool will add the chat_messages table to your existing databases")
    print("to enable chat persistence functionality.\n")
    
    # Find database files
    db_files = find_database_files()
    
    if not db_files:
        print("❌ No database files found in current directory or subdirectories.")
        print("Make sure you're running this script from the project root directory.")
        return
    
    print(f"📁 Found {len(db_files)} database file(s):")
    for i, db_file in enumerate(db_files, 1):
        print(f"   {i}. {db_file}")
    
    # Ask for confirmation
    print(f"\n⚠️  This will modify your database files. Make sure you have backups!")
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("❌ Migration cancelled.")
        return
    
    # Migrate each database
    success_count = 0
    total_count = len(db_files)
    
    for db_file in db_files:
        if migrate_database(db_file):
            success_count += 1
    
    # Summary
    print(f"\n📊 Migration Summary:")
    print(f"   ✅ Successfully migrated: {success_count}/{total_count} databases")
    
    if success_count == total_count:
        print(f"\n🎉 All databases migrated successfully!")
        print(f"Chat persistence is now enabled. Users can:")
        print(f"   • Continue conversations where they left off")
        print(f"   • Reset conversations using the reset button")
        print(f"   • Have their chat history automatically saved")
    else:
        print(f"\n⚠️  Some databases failed to migrate. Check the errors above.")
        print(f"You may need to manually create the chat_messages table.")
    
    print(f"\n🔧 Next steps:")
    print(f"   1. Restart your sales system application")
    print(f"   2. Test chat persistence by starting a conversation")
    print(f"   3. Refresh the page and verify the conversation continues")

if __name__ == "__main__":
    main()
