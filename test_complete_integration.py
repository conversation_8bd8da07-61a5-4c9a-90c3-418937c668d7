#!/usr/bin/env python3
"""
Complete Integration Test
========================
Test the complete sales funnel from email campaign to chatbot conversation.
"""

import sys
import os
import time
import requests
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

BASE_URL = "http://localhost:5000"
CHATBOT_URL = "http://127.0.0.1:7861"

def check_servers():
    """Check if both servers are running"""
    print("🔍 Checking server status...")
    
    # Check sales system
    try:
        response = requests.get(BASE_URL, timeout=5)
        sales_system_ok = response.status_code == 200
        print(f"📊 Sales System: {'✅ Running' if sales_system_ok else '❌ Not running'}")
    except:
        sales_system_ok = False
        print("📊 Sales System: ❌ Not running")
    
    # Check chatbot
    try:
        response = requests.get(CHATBOT_URL, timeout=5)
        chatbot_ok = response.status_code == 200
        print(f"🤖 Chatbot: {'✅ Running' if chatbot_ok else '❌ Not running'}")
    except:
        chatbot_ok = False
        print("🤖 Chatbot: ❌ Not running")
    
    return sales_system_ok, chatbot_ok

def test_email_campaign():
    """Test creating and sending an email campaign"""
    print("\n📧 Testing Email Campaign...")
    print("-" * 40)
    
    try:
        # Create campaign
        campaign_data = {
            'campaign_name': f'Integration Test Campaign {datetime.now().strftime("%H%M%S")}',
            'template': 'introduction',
            'recipient_type': 'all',
            'daily_send_limit': '50',
            'send_schedule': 'immediate'
        }
        
        print("📝 Creating campaign...")
        response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)
        
        if response.status_code in [200, 302]:
            print("✅ Campaign created successfully")
            
            # Get the latest campaign ID
            campaigns_response = requests.get(f"{BASE_URL}/campaigns")
            if campaigns_response.status_code == 200:
                import re
                content = campaigns_response.text
                pattern = r'/campaigns/(\d+)/'
                matches = re.findall(pattern, content)
                
                if matches:
                    latest_campaign_id = max([int(id) for id in matches])
                    print(f"✅ Campaign ID: {latest_campaign_id}")
                    
                    # Send the campaign
                    print(f"📤 Sending campaign...")
                    send_response = requests.post(f"{BASE_URL}/campaigns/{latest_campaign_id}/send")
                    
                    if send_response.status_code in [200, 302]:
                        print("✅ Campaign sent successfully!")
                        return True, latest_campaign_id
                    else:
                        print(f"❌ Failed to send campaign: {send_response.status_code}")
                        return False, None
                else:
                    print("❌ No campaign IDs found")
                    return False, None
            else:
                print("❌ Failed to get campaigns list")
                return False, None
        else:
            print(f"❌ Failed to create campaign: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Campaign test failed: {str(e)}")
        return False, None

def test_chatbot_link():
    """Test chatbot link redirection"""
    print("\n🔗 Testing Chatbot Link...")
    print("-" * 40)
    
    try:
        # Test a sample chatbot link
        test_session_id = "test-session-12345"
        chatbot_link = f"{BASE_URL}/chat/{test_session_id}"
        
        print(f"🔗 Testing link: {chatbot_link}")
        
        # Follow redirects to see where it goes
        response = requests.get(chatbot_link, allow_redirects=False)
        
        if response.status_code in [301, 302]:
            redirect_url = response.headers.get('Location')
            print(f"✅ Redirect working: {redirect_url}")
            
            # Check if redirect points to chatbot
            if "127.0.0.1:7861" in redirect_url or "localhost:7861" in redirect_url:
                print("✅ Redirects to chatbot correctly")
                return True
            else:
                print(f"❌ Redirects to wrong URL: {redirect_url}")
                return False
        else:
            print(f"❌ No redirect found: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chatbot link test failed: {str(e)}")
        return False

def test_imap_integration():
    """Test IMAP sent folder integration"""
    print("\n📬 Testing IMAP Integration...")
    print("-" * 40)
    
    try:
        # Run IMAP test
        import subprocess
        result = subprocess.run([
            sys.executable, "standalone_imap_test.py"
        ], capture_output=True, text=True, timeout=30)
        
        if "emails" in result.stdout and "retrieved" in result.stdout:
            # Extract email count
            import re
            match = re.search(r'(\d+) emails retrieved', result.stdout)
            if match:
                email_count = int(match.group(1))
                print(f"✅ IMAP working: {email_count} emails in sent folder")
                return True, email_count
            else:
                print("✅ IMAP working: emails found")
                return True, 0
        else:
            print("❌ IMAP test failed")
            return False, 0
            
    except Exception as e:
        print(f"❌ IMAP test failed: {str(e)}")
        return False, 0

def test_analytics_dashboard():
    """Test analytics dashboard"""
    print("\n📊 Testing Analytics Dashboard...")
    print("-" * 40)
    
    try:
        # Test analytics page
        response = requests.get(f"{BASE_URL}/analytics")
        
        if response.status_code == 200:
            print("✅ Analytics dashboard accessible")
            
            # Check for key metrics in the response
            content = response.text
            if "Total Contacts" in content and "Email Campaigns" in content:
                print("✅ Analytics data loading correctly")
                return True
            else:
                print("⚠️ Analytics accessible but data may be missing")
                return True
        else:
            print(f"❌ Analytics dashboard failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Analytics test failed: {str(e)}")
        return False

def test_sent_emails_interface():
    """Test sent emails web interface"""
    print("\n📧 Testing Sent Emails Interface...")
    print("-" * 40)
    
    try:
        # Test sent emails page
        response = requests.get(f"{BASE_URL}/emails/sent")
        
        if response.status_code == 200:
            print("✅ Sent emails interface accessible")
            
            # Check for IMAP content
            content = response.text
            if "IMAP" in content or "sent" in content.lower():
                print("✅ IMAP integration visible in interface")
                return True
            else:
                print("⚠️ Interface accessible but IMAP integration unclear")
                return True
        else:
            print(f"❌ Sent emails interface failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Sent emails interface test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 24Seven Assistants - Complete Integration Test")
    print("=" * 70)
    print("Testing the complete sales funnel integration:")
    print("📧 Email Campaigns → 🔗 Chatbot Links → 🤖 AI Conversations → 📊 Analytics")
    print()
    
    # Check servers
    sales_ok, chatbot_ok = check_servers()
    
    if not sales_ok:
        print("\n❌ Sales system is not running. Please start it first:")
        print("   python unified_sales_system.py")
        return
    
    if not chatbot_ok:
        print("\n❌ Chatbot is not running. Please start it first:")
        print("   python app.py")
        return
    
    print("\n✅ Both servers are running!")
    
    # Run tests
    test_results = {}
    
    # Test 1: Email Campaign
    campaign_success, campaign_id = test_email_campaign()
    test_results['email_campaign'] = campaign_success
    
    # Wait for email processing
    if campaign_success:
        print("\n⏳ Waiting 5 seconds for email processing...")
        time.sleep(5)
    
    # Test 2: Chatbot Link
    chatbot_link_success = test_chatbot_link()
    test_results['chatbot_link'] = chatbot_link_success
    
    # Test 3: IMAP Integration
    imap_success, email_count = test_imap_integration()
    test_results['imap_integration'] = imap_success
    
    # Test 4: Analytics Dashboard
    analytics_success = test_analytics_dashboard()
    test_results['analytics'] = analytics_success
    
    # Test 5: Sent Emails Interface
    sent_emails_success = test_sent_emails_interface()
    test_results['sent_emails_interface'] = sent_emails_success
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 COMPLETE INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    all_passed = True
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():<25}: {status}")
        if not result:
            all_passed = False
    
    if campaign_id:
        print(f"\nLatest Campaign ID: {campaign_id}")
    
    if email_count > 0:
        print(f"Emails in Sent Folder: {email_count}")
    
    print("\n" + "=" * 70)
    
    if all_passed:
        print("🎉 SUCCESS! Complete integration is working!")
        print("\n💡 What this means:")
        print("✅ Email campaigns send real emails with chatbot links")
        print("✅ Chatbot links redirect properly to the AI assistant")
        print("✅ Emails are automatically saved to IMAP sent folder")
        print("✅ Analytics dashboard shows real-time metrics")
        print("✅ Sent emails interface provides complete visibility")
        
        print("\n🌐 Ready for production:")
        print("1. Create campaigns: http://localhost:5000/campaigns/create")
        print("2. Monitor analytics: http://localhost:5000/analytics")
        print("3. View sent emails: http://localhost:5000/emails/sent")
        print("4. Chat with Sarah: http://127.0.0.1:7861")
        
        print("\n🎯 Complete sales funnel operational!")
    else:
        print("⚠️ Some integration issues detected:")
        for test_name, result in test_results.items():
            if not result:
                print(f"- {test_name.replace('_', ' ').title()} needs attention")
        
        print("\nCheck the detailed output above for specific issues.")
    
    print(f"\n📧 Check your email inbox for campaign emails!")

if __name__ == "__main__":
    main()
