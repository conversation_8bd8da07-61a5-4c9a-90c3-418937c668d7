{% extends "base.html" %}

{% block title %}Sales Pipeline Analytics - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line text-primary"></i> Sales Pipeline Analytics</h1>
    <div>
        <a href="{{ url_for('sales_cycle_analytics') }}" class="btn btn-info me-2">
            <i class="fas fa-clock"></i> Sales Cycle Analytics
        </a>
        <a href="{{ url_for('session_analytics') }}" class="btn btn-warning me-2">
            <i class="fas fa-comments"></i> Session Analytics
        </a>
        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Analytics
        </a>
    </div>
</div>

<!-- Pipeline Summary Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.sessions_started }}</div>
                <div class="metric-label">Sessions Started</div>
                <small><i class="fas fa-play"></i> Total pipeline entries</small>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.trust_entered }}</div>
                <div class="metric-label">Trust Stage</div>
                <small><i class="fas fa-handshake"></i> Building rapport</small>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.discovery_entered }}</div>
                <div class="metric-label">Discovery</div>
                <small><i class="fas fa-search"></i> Understanding needs</small>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-secondary text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.demonstration_entered }}</div>
                <div class="metric-label">Demonstration</div>
                <small><i class="fas fa-presentation"></i> Showing solutions</small>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-dark text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.close_entered }}</div>
                <div class="metric-label">Close Attempts</div>
                <small><i class="fas fa-bullseye"></i> Asking for commitment</small>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">{{ pipeline_data.conversions_completed }}</div>
                <div class="metric-label">Conversions</div>
                <small><i class="fas fa-trophy"></i> {{ "%.1f"|format(pipeline_data.overall_conversion_rate) }}% success rate</small>
            </div>
        </div>
    </div>
</div>

<!-- Main Pipeline Funnel -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="chart-container">
            <h5><i class="fas fa-funnel-dollar"></i> Sales Pipeline Progression</h5>
            <div id="pipelineFunnelChart"></div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-bar"></i> Stage-to-Stage Conversion Rates</h5>
            <div id="conversionRatesChart"></div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-tachometer-alt"></i> Sales Velocity</h5>
            <div id="salesVelocityChart"></div>
        </div>
    </div>
</div>

<!-- Performance Heatmap -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="chart-container">
            <h5><i class="fas fa-fire"></i> Stage Performance Heatmap</h5>
            <div id="performanceHeatmapChart"></div>
        </div>
    </div>
</div>

<!-- Detailed Pipeline Metrics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Pipeline Health Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Stage Transition</th>
                                <th>Conversion Rate</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transition, rate in pipeline_data.stage_conversion_rates.items() %}
                            <tr>
                                <td><strong>{{ transition }}</strong></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar
                                            {% if rate >= 80 %}bg-success
                                            {% elif rate >= 60 %}bg-warning
                                            {% else %}bg-danger{% endif %}"
                                            style="width: {{ rate }}%">
                                            {{ "%.1f"|format(rate) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if rate >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif rate >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% elif rate >= 40 %}
                                        <span class="badge bg-warning">Fair</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-stopwatch"></i> Sales Velocity Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Stage Progression</th>
                                <th>Avg Time</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for progression, time in pipeline_data.sales_velocity.items() %}
                            <tr>
                                <td><strong>{{ progression }}</strong></td>
                                <td>{{ "%.1f"|format(time) }} minutes</td>
                                <td>
                                    {% if time <= 5 %}
                                        <span class="badge bg-success">Fast</span>
                                    {% elif time <= 10 %}
                                        <span class="badge bg-warning">Normal</span>
                                    {% else %}
                                        <span class="badge bg-danger">Slow</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stage Performance Details -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-analytics"></i> Detailed Stage Performance</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Sales Stage</th>
                                <th>Success Rate</th>
                                <th>Avg Duration</th>
                                <th>Engagement Score</th>
                                <th>Overall Performance</th>
                                <th>Recommendations</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage, performance in pipeline_data.stage_performance.items() %}
                            <tr>
                                <td><strong>{{ stage.title() }}</strong></td>
                                <td>
                                    <span class="badge
                                        {% if performance.success_rate >= 80 %}bg-success
                                        {% elif performance.success_rate >= 60 %}bg-warning
                                        {% else %}bg-danger{% endif %}">
                                        {{ "%.1f"|format(performance.success_rate) }}%
                                    </span>
                                </td>
                                <td>{{ "%.1f"|format(performance.avg_duration) }}m</td>
                                <td>
                                    <div class="progress" style="height: 20px; width: 100px;">
                                        <div class="progress-bar bg-info" style="width: {{ performance.engagement_score }}%">
                                            {{ "%.0f"|format(performance.engagement_score) }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% set overall_score = (performance.success_rate + performance.engagement_score) / 2 %}
                                    {% if overall_score >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif overall_score >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Work</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if performance.success_rate < 60 %}
                                            Improve conversion tactics
                                        {% elif performance.avg_duration > 10 %}
                                            Reduce time spent
                                        {% elif performance.engagement_score < 50 %}
                                            Increase engagement
                                        {% else %}
                                            Performing well
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.metric-card {
    background: var(--glass-bg, rgba(255, 255, 255, 0.05));
    border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.15));
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.6);
    transition: transform 0.2s;
    color: var(--text-primary, #e6e6e6);
    backdrop-filter: blur(20px);
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
    color: var(--text-secondary, #9ca3af);
}

.chart-container {
    background: var(--glass-bg, rgba(255, 255, 255, 0.05));
    border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.15));
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
    margin-bottom: 20px;
    color: var(--text-primary, #e6e6e6);
    backdrop-filter: blur(20px);
}

.chart-container h5 {
    margin-bottom: 20px;
    color: var(--text-primary, #e6e6e6);
    border-bottom: 2px solid var(--glass-border, rgba(255, 255, 255, 0.15));
    padding-bottom: 10px;
}
</style>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Render pipeline funnel chart
        {% if charts.pipeline_funnel %}
        var pipelineFunnelData = {{ charts.pipeline_funnel|safe }};
        Plotly.newPlot('pipelineFunnelChart', pipelineFunnelData.data, pipelineFunnelData.layout, {responsive: true});
        {% endif %}

        // Render conversion rates chart
        {% if charts.conversion_rates %}
        var conversionRatesData = {{ charts.conversion_rates|safe }};
        Plotly.newPlot('conversionRatesChart', conversionRatesData.data, conversionRatesData.layout, {responsive: true});
        {% endif %}

        // Render sales velocity chart
        {% if charts.sales_velocity %}
        var salesVelocityData = {{ charts.sales_velocity|safe }};
        Plotly.newPlot('salesVelocityChart', salesVelocityData.data, salesVelocityData.layout, {responsive: true});
        {% endif %}

        // Render performance heatmap
        {% if charts.performance_heatmap %}
        var performanceHeatmapData = {{ charts.performance_heatmap|safe }};
        Plotly.newPlot('performanceHeatmapChart', performanceHeatmapData.data, performanceHeatmapData.layout, {responsive: true});
        {% endif %}
    });

    // Auto-refresh every 2 minutes
    setInterval(function() {
        location.reload();
    }, 120000);
</script>
{% endblock %}
