{% extends "base.html" %}

{% block title %}Campaign Details - {{ campaign.name }} - 24Seven Assistants{% endblock %}

{% block extra_css %}
<style>
/* Pagination styling */
.pagination .page-link {
    border-radius: 0.375rem;
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* Contact table improvements */
.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75em;
}

/* Per-page selector styling */
.form-select-sm {
    padding: 0.25rem 1.75rem 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .pagination {
        justify-content: center;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Loading state for pagination */
.pagination .page-link:active {
    transform: translateY(1px);
}

/* Contact actions column */
.table td:last-child {
    white-space: nowrap;
}

.btn-sm {
    margin: 0 1px;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-envelope-open-text text-primary"></i> Campaign Details</h1>
    <div>
        {% if campaign.status == 'draft' %}
        <form method="POST" action="{{ url_for('send_campaign', campaign_id=campaign.id) }}" style="display: inline;">
            <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to start this campaign?')">
                <i class="fas fa-play"></i> Start Campaign
            </button>
        </form>
        {% elif campaign.status == 'paused' %}
        <form method="POST" action="{{ url_for('resume_campaign', campaign_id=campaign.id) }}" style="display: inline;">
            <button type="submit" class="btn btn-warning">
                <i class="fas fa-play"></i> Resume Campaign
            </button>
        </form>
        {% elif campaign.status == 'sending' %}
        <form method="POST" action="{{ url_for('pause_campaign', campaign_id=campaign.id) }}" style="display: inline;">
            <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to pause this campaign?')">
                <i class="fas fa-pause"></i> Pause Campaign
            </button>
        </form>
        {% endif %}

        <a href="{{ url_for('assign_groups_to_campaign', campaign_id=campaign.id) }}" class="btn btn-success">
            <i class="fas fa-layer-group"></i> Assign Groups
        </a>
        <a href="{{ url_for('edit_campaign', campaign_id=campaign.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Campaign
        </a>
        <a href="{{ url_for('campaigns_list') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Campaigns
        </a>
    </div>
</div>

<!-- Campaign Overview -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Campaign Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Campaign Name</h6>
                        <p class="mb-3">{{ campaign.name }}</p>

                        <h6 class="text-muted">Subject Line</h6>
                        <p class="mb-3">{{ campaign.subject or 'No subject set' }}</p>

                        <h6 class="text-muted">Template</h6>
                        <p class="mb-3">{{ campaign.template_name or 'No template' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Status</h6>
                        <p class="mb-3">
                            <span class="badge
                                {% if campaign.status == 'draft' %}bg-secondary
                                {% elif campaign.status == 'sending' %}bg-warning
                                {% elif campaign.status == 'completed' %}bg-success
                                {% elif campaign.status == 'failed' %}bg-danger
                                {% else %}bg-info{% endif %}">
                                {{ campaign.status.title() }}
                            </span>
                        </p>

                        <h6 class="text-muted">Created</h6>
                        <p class="mb-3">{{ campaign.created_at.strftime('%Y-%m-%d %H:%M') if campaign.created_at else 'Unknown' }}</p>

                        {% if campaign.started_at %}
                        <h6 class="text-muted">Started</h6>
                        <p class="mb-3">{{ campaign.started_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ campaign.emails_sent or 0 }}</h4>
                        <small class="text-muted">Emails Sent</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ campaign.chatbot_links_clicked or 0 }}</h4>
                        <small class="text-muted">Links Clicked</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ campaign.conversions_achieved or 0 }}</h4>
                        <small class="text-muted">Conversions</small>
                    </div>
                    <div class="col-6">
                        {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                        <h4 class="text-warning">{{ ((campaign.conversions_achieved or 0) / campaign.emails_sent * 100)|round(1) }}%</h4>
                        {% else %}
                        <h4 class="text-warning">0%</h4>
                        {% endif %}
                        <small class="text-muted">Conversion Rate</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daily Sending Limits Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-day"></i> Daily Sending</h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <h4 class="text-primary">{{ campaign.daily_send_limit or 100 }}</h4>
                        <small class="text-muted">Daily Limit</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ campaign.emails_sent_today or 0 }}</h4>
                        <small class="text-muted">Sent Today</small>
                    </div>
                </div>

                <!-- Progress Bar -->
                {% set daily_progress = ((campaign.emails_sent_today or 0) / (campaign.daily_send_limit or 100) * 100) %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Today's Progress</small>
                        <small class="text-muted">{{ daily_progress|round(1) }}%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar
                            {% if daily_progress >= 100 %}bg-danger
                            {% elif daily_progress >= 80 %}bg-warning
                            {% else %}bg-success{% endif %}"
                            role="progressbar"
                            style="width: {{ daily_progress }}%"
                            aria-valuenow="{{ daily_progress }}"
                            aria-valuemin="0"
                            aria-valuemax="100">
                        </div>
                    </div>
                </div>

                <!-- Status Information -->
                {% if campaign.status == 'paused' and campaign.batch_status == 'paused' %}
                <div class="alert alert-warning alert-sm mb-2">
                    <i class="fas fa-pause-circle"></i>
                    <strong>Paused:</strong> Daily limit reached. Will resume tomorrow.
                </div>
                {% endif %}

                {% if campaign.next_batch_date %}
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-clock"></i> Next batch: {{ campaign.next_batch_date.strftime('%Y-%m-%d') }}
                    </small>
                </div>
                {% endif %}

                <!-- Estimated completion -->
                {% if campaign.status in ['sending', 'paused'] and campaign.total_recipients %}
                {% set remaining = (campaign.total_recipients or 0) - (campaign.emails_sent or 0) %}
                {% if remaining > 0 %}
                {% set estimated_days = (remaining / (campaign.daily_send_limit or 100))|round(0, 'ceil') %}
                <div class="text-center mt-2">
                    <small class="text-info">
                        <i class="fas fa-calendar-alt"></i>
                        {% if estimated_days == 1 %}
                        Estimated completion: Tomorrow
                        {% else %}
                        Estimated completion: {{ estimated_days }} days
                        {% endif %}
                    </small>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>

        <!-- Assigned Groups Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-layer-group"></i> Assigned Groups</h5>
            </div>
            <div class="card-body">
                {% set assigned_groups = campaign.campaign_groups %}
                {% if assigned_groups %}
                    {% for campaign_group in assigned_groups %}
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong style="color: {{ campaign_group.group.color or '#007bff' }};">
                                <i class="fas fa-users"></i> {{ campaign_group.group.name }}
                            </strong>
                            <br>
                            <small class="text-muted">{{ campaign_group.group.contact_count }} contact(s)</small>
                            {% if campaign_group.added_at %}
                            <br><small class="text-muted">Added: {{ campaign_group.added_at.strftime('%Y-%m-%d') }}</small>
                            {% endif %}
                        </div>
                        <div>
                            <a href="{{ url_for('view_group', group_id=campaign_group.group.id) }}" class="btn btn-outline-primary btn-sm me-1" title="View Group">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if campaign.status == 'draft' %}
                            <form method="POST" action="{{ url_for('remove_group_from_campaign', campaign_id=campaign.id, group_id=campaign_group.group.id) }}" style="display: inline;">
                                <button type="submit" class="btn btn-outline-danger btn-sm" title="Remove Group" onclick="return confirm('Remove this group from the campaign?')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-layer-group fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-2">No groups assigned to this campaign</p>
                        <a href="{{ url_for('assign_groups_to_campaign', campaign_id=campaign.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus"></i> Assign Groups
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Campaign Performance -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Performance Metrics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-primary">{{ campaign.total_recipients or 0 }}</h3>
                            <p class="mb-0 text-muted">Total Recipients</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-info">{{ campaign.emails_sent or 0 }}</h3>
                            <p class="mb-0 text-muted">Emails Delivered</p>
                            {% if campaign.total_recipients and campaign.total_recipients > 0 %}
                            <small class="text-muted">{{ ((campaign.emails_sent or 0) / campaign.total_recipients * 100)|round(1) }}% delivery rate</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-success">{{ campaign.chatbot_links_clicked or 0 }}</h3>
                            <p class="mb-0 text-muted">Chatbot Clicks</p>
                            {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                            <small class="text-muted">{{ ((campaign.chatbot_links_clicked or 0) / campaign.emails_sent * 100)|round(1) }}% click rate</small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h3 class="text-warning">{{ campaign.conversions_achieved or 0 }}</h3>
                            <p class="mb-0 text-muted">Conversions</p>
                            {% if campaign.chatbot_links_clicked and campaign.chatbot_links_clicked > 0 %}
                            <small class="text-muted">{{ ((campaign.conversions_achieved or 0) / campaign.chatbot_links_clicked * 100)|round(1) }}% conversion rate</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Contacts -->
{% if contacts or total_contacts > 0 %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-users"></i> Campaign Contacts ({{ total_contacts }})</h5>
                    <div class="d-flex align-items-center gap-3">
                        {% if total_contacts > 0 %}
                        <button class="btn btn-outline-success btn-sm" onclick="exportContacts()" title="Export Contacts to CSV">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        {% endif %}
                        <div class="d-flex align-items-center">
                            <label for="per_page" class="form-label me-2 mb-0">Show:</label>
                            <select id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                <option value="10" {% if request.args.get('per_page', '20') == '10' %}selected{% endif %}>10</option>
                                <option value="20" {% if request.args.get('per_page', '20') == '20' %}selected{% endif %}>20</option>
                                <option value="50" {% if request.args.get('per_page', '20') == '50' %}selected{% endif %}>50</option>
                                <option value="100" {% if request.args.get('per_page', '20') == '100' %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- Enhanced Filter Section -->
                <div class="card bg-dark text-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title text-white mb-3">
                            <i class="fas fa-filter"></i> Filter Campaign Recipients
                        </h6>
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-4">
                                <label for="contactSearch" class="form-label text-white">Search Contacts</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-secondary border-secondary">
                                        <i class="fas fa-search text-white"></i>
                                    </span>
                                    <input type="text" class="form-control" id="contactSearch"
                                           placeholder="Name, email, or company..."
                                           onkeyup="filterContacts()"
                                           style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                </div>
                            </div>

                            <!-- Email Status Filter -->
                            <div class="col-md-2">
                                <label for="statusFilter" class="form-label text-white">Email Status</label>
                                <select class="form-select form-select-sm" id="statusFilter" onchange="filterContacts()"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Statuses</option>
                                    <option value="sent" style="background-color: #1a202c; color: white;">Email Sent</option>
                                    <option value="not-sent" style="background-color: #1a202c; color: white;">Email Not Sent</option>
                                    <option value="failed" style="background-color: #1a202c; color: white;">Email Failed</option>
                                    <option value="bounced" style="background-color: #1a202c; color: white;">Email Bounced</option>
                                </select>
                            </div>

                            <!-- Engagement Filter -->
                            <div class="col-md-2">
                                <label for="engagementFilter" class="form-label text-white">Engagement</label>
                                <select class="form-select form-select-sm" id="engagementFilter" onchange="filterContacts()"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Engagement</option>
                                    <option value="opened" style="background-color: #1a202c; color: white;">Email Opened</option>
                                    <option value="clicked" style="background-color: #1a202c; color: white;">Link Clicked</option>
                                    <option value="chatbot-started" style="background-color: #1a202c; color: white;">Chatbot Started</option>
                                    <option value="no-engagement" style="background-color: #1a202c; color: white;">No Engagement</option>
                                </select>
                            </div>

                            <!-- Sales Stage Filter -->
                            <div class="col-md-2">
                                <label for="salesStageFilter" class="form-label text-white">Sales Stage</label>
                                <select class="form-select form-select-sm" id="salesStageFilter" onchange="filterContacts()"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Stages</option>
                                    <option value="lead" style="background-color: #1a202c; color: white;">Lead</option>
                                    <option value="prospect" style="background-color: #1a202c; color: white;">Prospect</option>
                                    <option value="opportunity" style="background-color: #1a202c; color: white;">Opportunity</option>
                                    <option value="closing" style="background-color: #1a202c; color: white;">Closing</option>
                                    <option value="customer" style="background-color: #1a202c; color: white;">Customer</option>
                                    <option value="lost" style="background-color: #1a202c; color: white;">Lost</option>
                                </select>
                            </div>

                            <!-- Date Range Filter -->
                            <div class="col-md-2">
                                <label for="dateRangeFilter" class="form-label text-white">Activity Period</label>
                                <select class="form-select form-select-sm" id="dateRangeFilter" onchange="filterContacts()"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Time</option>
                                    <option value="today" style="background-color: #1a202c; color: white;">Today</option>
                                    <option value="yesterday" style="background-color: #1a202c; color: white;">Yesterday</option>
                                    <option value="last-7-days" style="background-color: #1a202c; color: white;">Last 7 Days</option>
                                    <option value="last-30-days" style="background-color: #1a202c; color: white;">Last 30 Days</option>
                                    <option value="no-activity" style="background-color: #1a202c; color: white;">No Recent Activity</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="d-flex gap-2 align-items-center">
                                    <button class="btn btn-outline-light btn-sm" onclick="clearFilters()" title="Clear All Filters">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="exportFilteredContacts()" title="Export Filtered Results">
                                        <i class="fas fa-download"></i> Export Filtered
                                    </button>
                                    <div class="ms-auto">
                                        <span id="filterResults" class="text-light small"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if contacts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Contact</th>
                                <th>Email Status</th>
                                <th>Chatbot Interaction</th>
                                <th>Sales Stage</th>
                                <th>Last Activity</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contact in contacts %}
                            <tr>
                                <td>
                                    <strong>{{ contact.full_name or contact.first_name + ' ' + contact.last_name or 'Unknown' }}</strong>
                                    <br><small class="text-muted">{{ contact.email }}</small>
                                    {% if contact.company %}
                                    <br><small class="text-info">{{ contact.company }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contact.email_sent_at %}
                                    <span class="badge bg-success">Sent</span>
                                    <br><small class="text-muted">{{ contact.email_sent_at.strftime('%m/%d %H:%M') }}</small>
                                    {% else %}
                                    <span class="badge bg-secondary">Not Sent</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contact.chatbot_link_clicked %}
                                    <span class="badge bg-info">Clicked</span>
                                    <br><small class="text-muted">{{ contact.chatbot_link_clicked.strftime('%m/%d %H:%M') }}</small>
                                    {% else %}
                                    <span class="badge bg-light text-dark">No Click</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contact.current_sales_stage %}
                                    <span class="badge
                                        {% if contact.current_sales_stage == 'converted' %}bg-success
                                        {% elif contact.current_sales_stage in ['demonstration', 'pricing'] %}bg-warning
                                        {% elif contact.current_sales_stage == 'link_clicked' %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ contact.current_sales_stage.replace('_', ' ').title() }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">No Stage</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if contact.last_interaction_at %}
                                    <small>{{ contact.last_interaction_at.strftime('%m/%d %H:%M') }}</small>
                                    {% elif contact.last_activity %}
                                    <small>{{ contact.last_activity.strftime('%m/%d %H:%M') }}</small>
                                    {% else %}
                                    <small class="text-muted">No activity</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('view_contact', contact_id=contact.id) }}" class="btn btn-outline-primary btn-sm" title="View Contact">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if contact.chatbot_session_id %}
                                    <a href="{{ url_for('chatbot_entry', session_id=contact.chatbot_session_id) }}" class="btn btn-outline-info btn-sm" title="View Chat">
                                        <i class="fas fa-comments"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Controls -->
                {% if contacts_pagination and contacts_pagination.pages > 1 %}
                <nav aria-label="Contacts pagination" class="mt-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                Showing {{ contacts_pagination.per_page * (contacts_pagination.page - 1) + 1 }} to
                                {{ contacts_pagination.per_page * (contacts_pagination.page - 1) + contacts|length }}
                                of {{ total_contacts }} contacts
                            </small>
                        </div>
                        <ul class="pagination pagination-sm mb-0">
                            <!-- First Page -->
                            {% if contacts_pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_campaign', campaign_id=campaign.id, page=1, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <!-- Previous Page -->
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_campaign', campaign_id=campaign.id, page=contacts_pagination.prev_num, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-left"></i></span>
                            </li>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for page_num in contacts_pagination.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num != contacts_pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('view_campaign', campaign_id=campaign.id, page=page_num, per_page=request.args.get('per_page', 20)) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if contacts_pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_campaign', campaign_id=campaign.id, page=contacts_pagination.next_num, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <!-- Last Page -->
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_campaign', campaign_id=campaign.id, page=contacts_pagination.pages, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-right"></i></span>
                            </li>
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Contacts Found</h5>
                    <p class="text-muted">This campaign doesn't have any contacts assigned yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Activities -->
{% if activities %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activities</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% for activity in activities %}
                    <div class="timeline-item mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-circle text-primary" style="font-size: 8px; margin-top: 8px;"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">{{ activity.activity_type.replace('_', ' ').title() }}</h6>
                                <p class="mb-1">{{ activity.description }}</p>
                                <small class="text-muted">{{ activity.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh if campaign is still sending
{% if campaign.status == 'sending' %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}

// Handle per-page dropdown change
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', '1'); // Reset to first page
    window.location.href = url.toString();
}

// Enhanced filter contacts function with multiple criteria
function filterContacts() {
    const searchTerm = document.getElementById('contactSearch').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const engagementFilter = document.getElementById('engagementFilter') ? document.getElementById('engagementFilter').value : '';
    const salesStageFilter = document.getElementById('salesStageFilter') ? document.getElementById('salesStageFilter').value : '';
    const dateRangeFilter = document.getElementById('dateRangeFilter') ? document.getElementById('dateRangeFilter').value : '';

    const tableRows = document.querySelectorAll('tbody tr');
    let visibleCount = 0;

    tableRows.forEach(row => {
        const contactText = row.cells[0].textContent.toLowerCase(); // Name, email, company
        const emailStatus = row.cells[1].textContent.toLowerCase();
        const chatbotStatus = row.cells[2].textContent.toLowerCase();
        const salesStage = row.cells[3].textContent.toLowerCase();
        const lastActivity = row.cells[4].textContent.toLowerCase();

        // Check search term match
        const matchesSearch = contactText.includes(searchTerm);

        // Check email status filter match
        let matchesStatus = true;
        if (statusFilter) {
            switch (statusFilter) {
                case 'sent':
                    matchesStatus = emailStatus.includes('sent') && !emailStatus.includes('not sent');
                    break;
                case 'not-sent':
                    matchesStatus = emailStatus.includes('not sent');
                    break;
                case 'failed':
                    matchesStatus = emailStatus.includes('failed');
                    break;
                case 'bounced':
                    matchesStatus = emailStatus.includes('bounced');
                    break;
            }
        }

        // Check engagement filter match
        let matchesEngagement = true;
        if (engagementFilter) {
            switch (engagementFilter) {
                case 'opened':
                    matchesEngagement = emailStatus.includes('opened');
                    break;
                case 'clicked':
                    matchesEngagement = chatbotStatus.includes('clicked');
                    break;
                case 'chatbot-started':
                    matchesEngagement = chatbotStatus.includes('started') || salesStage.includes('link_clicked');
                    break;
                case 'no-engagement':
                    matchesEngagement = chatbotStatus.includes('no click') && !emailStatus.includes('opened');
                    break;
            }
        }

        // Check sales stage filter match
        let matchesSalesStage = true;
        if (salesStageFilter) {
            matchesSalesStage = salesStage.includes(salesStageFilter);
        }

        // Check date range filter match
        let matchesDateRange = true;
        if (dateRangeFilter) {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            switch (dateRangeFilter) {
                case 'today':
                    matchesDateRange = lastActivity.includes(today.toISOString().split('T')[0].replace(/-/g, '/'));
                    break;
                case 'yesterday':
                    matchesDateRange = lastActivity.includes(yesterday.toISOString().split('T')[0].replace(/-/g, '/'));
                    break;
                case 'last-7-days':
                    const sevenDaysAgo = new Date(today);
                    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                    // This is a simplified check - in a real implementation you'd parse the date properly
                    matchesDateRange = !lastActivity.includes('no activity');
                    break;
                case 'last-30-days':
                    const thirtyDaysAgo = new Date(today);
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                    matchesDateRange = !lastActivity.includes('no activity');
                    break;
                case 'no-activity':
                    matchesDateRange = lastActivity.includes('no activity');
                    break;
            }
        }

        // Show/hide row based on all filters
        if (matchesSearch && matchesStatus && matchesEngagement && matchesSalesStage && matchesDateRange) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update visible count display
    updateFilterResults(visibleCount, tableRows.length);
}

// Clear all filters
function clearFilters() {
    document.getElementById('contactSearch').value = '';
    document.getElementById('statusFilter').value = '';

    // Clear additional filters if they exist
    const engagementFilter = document.getElementById('engagementFilter');
    if (engagementFilter) engagementFilter.value = '';

    const salesStageFilter = document.getElementById('salesStageFilter');
    if (salesStageFilter) salesStageFilter.value = '';

    const dateRangeFilter = document.getElementById('dateRangeFilter');
    if (dateRangeFilter) dateRangeFilter.value = '';

    filterContacts();
}

// Export contacts to CSV
function exportContacts() {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tr');
    let csvContent = '';

    // Add header row
    const headers = ['Name', 'Email', 'Company', 'Email Status', 'Chatbot Interaction', 'Sales Stage', 'Last Activity'];
    csvContent += headers.join(',') + '\n';

    // Add data rows (skip header row)
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        if (row.style.display !== 'none') { // Only export visible rows
            const cells = row.querySelectorAll('td');
            const rowData = [];

            // Extract contact info (name, email, company)
            const contactCell = cells[0].textContent.trim();
            const contactLines = contactCell.split('\n').map(line => line.trim()).filter(line => line);
            const name = contactLines[0] || '';
            const email = contactLines[1] || '';
            const company = contactLines[2] || '';

            rowData.push('"' + name + '"');
            rowData.push('"' + email + '"');
            rowData.push('"' + company + '"');

            // Extract other data
            for (let j = 1; j < cells.length - 1; j++) { // Skip actions column
                const cellText = cells[j].textContent.trim().replace(/\n/g, ' ').replace(/"/g, '""');
                rowData.push('"' + cellText + '"');
            }

            csvContent += rowData.join(',') + '\n';
        }
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'campaign_{{ campaign.id }}_contacts.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Export filtered contacts to CSV
function exportFilteredContacts() {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tr');
    let csvContent = '';

    // Add header row
    const headers = ['Name', 'Email', 'Company', 'Email Status', 'Chatbot Interaction', 'Sales Stage', 'Last Activity'];
    csvContent += headers.join(',') + '\n';

    // Add data rows (skip header row, only export visible rows)
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        if (row.style.display !== 'none') { // Only export visible rows
            const cells = row.querySelectorAll('td');
            const rowData = [];

            // Extract contact info (name, email, company)
            const contactCell = cells[0].textContent.trim();
            const contactLines = contactCell.split('\n').map(line => line.trim()).filter(line => line);
            const name = contactLines[0] || '';
            const email = contactLines[1] || '';
            const company = contactLines[2] || '';

            rowData.push('"' + name + '"');
            rowData.push('"' + email + '"');
            rowData.push('"' + company + '"');

            // Extract other data
            for (let j = 1; j < cells.length - 1; j++) { // Skip actions column
                const cellText = cells[j].textContent.trim().replace(/\n/g, ' ').replace(/"/g, '""');
                rowData.push('"' + cellText + '"');
            }

            csvContent += rowData.join(',') + '\n';
        }
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'campaign_{{ campaign.id }}_filtered_contacts.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Update filter results display
function updateFilterResults(visible, total) {
    const resultSpan = document.getElementById('filterResults');
    if (resultSpan) {
        if (visible < total) {
            resultSpan.innerHTML = `<i class="fas fa-filter"></i> Showing ${visible} of ${total} contacts`;
            resultSpan.style.display = 'inline';
        } else {
            resultSpan.innerHTML = `Showing all ${total} contacts`;
            resultSpan.style.display = 'inline';
        }
    }
}

// Add smooth scrolling to pagination links
document.addEventListener('DOMContentLoaded', function() {
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Scroll to top of contacts section after page change
            setTimeout(() => {
                const contactsSection = document.querySelector('.card-header h5');
                if (contactsSection) {
                    contactsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 100);
        });
    });

    // Initialize filter functionality
    const searchInput = document.getElementById('contactSearch');
    if (searchInput) {
        // Add debounce to search input
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(filterContacts, 300);
        });
    }
});
</script>
{% endblock %}
