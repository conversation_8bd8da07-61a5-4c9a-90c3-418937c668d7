#!/usr/bin/env python3
"""
Fix Campaign Group Selection
============================
Fix the "24seven Assistants" campaign to only send to the 100ppda group.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_campaign_group():
    """Fix the campaign to only send to 100ppda group"""
    try:
        from unified_sales_system import app, db, EmailCampaign, ContactGroup, ContactGroupMembership, Contact
        import json
        
        with app.app_context():
            # Find the "24seven Assistants" campaign
            campaign = EmailCampaign.query.filter_by(name="24seven Assistants").first()
            
            if not campaign:
                print("❌ Campaign '24seven Assistants' not found")
                return False
            
            print(f"🔍 Found campaign: {campaign.name} (ID: {campaign.id})")
            print(f"   Current status: {campaign.status}")
            print(f"   Current recipient criteria: {campaign.recipient_criteria}")
            
            # Find the 100ppda group
            group_100ppda = ContactGroup.query.filter_by(name="100ppda").first()
            
            if not group_100ppda:
                print("❌ Group '100ppda' not found")
                print("\n📊 Available groups:")
                groups = ContactGroup.query.all()
                for group in groups:
                    member_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()
                    print(f"   - {group.name} (ID: {group.id}) - {member_count} members")
                return False
            
            print(f"✅ Found group: {group_100ppda.name} (ID: {group_100ppda.id})")
            
            # Count contacts in this group
            contacts_in_group = db.session.query(ContactGroupMembership.contact_id).filter_by(
                group_id=group_100ppda.id
            ).distinct().all()
            contact_count = len(contacts_in_group)
            
            print(f"👥 Contacts in group: {contact_count}")
            
            if contact_count == 0:
                print("⚠️ No contacts found in the 100ppda group!")
                return False
            
            # Show contacts in the group
            contact_ids = [id[0] for id in contacts_in_group]
            contacts = Contact.query.filter(Contact.id.in_(contact_ids)).all()
            
            print(f"📧 Contacts that will receive emails:")
            for contact in contacts:
                print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
            
            # Update campaign recipient criteria
            recipient_criteria = {
                'type': 'groups',
                'group_ids': [group_100ppda.id]
            }
            
            campaign.recipient_criteria = json.dumps(recipient_criteria)
            
            # If campaign is currently sending, pause it first
            if campaign.status == 'sending':
                print("⏸️ Pausing campaign to apply changes...")
                campaign.status = 'paused'
            
            db.session.commit()
            
            print(f"✅ Campaign updated successfully!")
            print(f"   New recipient criteria: {campaign.recipient_criteria}")
            print(f"   Campaign will now send to ONLY the {contact_count} contact(s) in the '100ppda' group")
            
            # Verify the fix
            print(f"\n🔍 Verification:")
            from unified_sales_system import get_campaign_contacts
            target_contacts = get_campaign_contacts(campaign)
            print(f"   Target contacts after fix: {len(target_contacts)}")
            
            for contact in target_contacts:
                print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
            
            if len(target_contacts) == contact_count:
                print("🎉 SUCCESS! Campaign is now properly configured.")
                
                if campaign.status == 'paused':
                    print("\n💡 To resume sending:")
                    print("   1. Go to your campaigns page")
                    print("   2. Click on '24seven Assistants' campaign")
                    print("   3. Click 'Resume Campaign' or 'Send Campaign'")
                
                return True
            else:
                print("❌ Something went wrong. Contact counts don't match.")
                return False
            
    except Exception as e:
        print(f"❌ Error fixing campaign: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 24Seven Assistants - Fix Campaign Group Selection")
    print("=" * 60)
    fix_campaign_group()
