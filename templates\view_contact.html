{% extends "base.html" %}

{% block title %}{{ contact.full_name }} - Contact Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user"></i> {{ contact.full_name }}</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('contacts_list') }}">Contacts</a></li>
                    <li class="breadcrumb-item active">{{ contact.full_name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('edit_contact', contact_id=contact.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Contact
            </a>
            <a href="{{ url_for('contacts_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Contacts
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Contact Information -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Email:</strong><br>
                        <a href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                    </div>
                    {% if contact.phone %}
                    <div class="mb-3">
                        <strong>Phone:</strong><br>
                        <a href="tel:{{ contact.phone }}">{{ contact.phone }}</a>
                    </div>
                    {% endif %}
                    {% if contact.company %}
                    <div class="mb-3">
                        <strong>Company:</strong><br>
                        {{ contact.company }}
                    </div>
                    {% endif %}
                    {% if contact.job_title %}
                    <div class="mb-3">
                        <strong>Job Title:</strong><br>
                        {{ contact.job_title }}
                    </div>
                    {% endif %}
                    <div class="mb-3">
                        <strong>Source:</strong><br>
                        <span class="badge bg-info">{{ contact.source or 'Unknown' }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong><br>
                        {% if contact.is_customer %}
                            <span class="badge bg-success">Customer</span>
                        {% elif contact.do_not_email %}
                            <span class="badge bg-danger">Do Not Email</span>
                        {% elif contact.is_active %}
                            <span class="badge bg-primary">Active</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small>{{ contact.created_at.strftime('%Y-%m-%d %H:%M') if contact.created_at else '-' }}</small>
                    </div>
                </div>
            </div>

            <!-- Sales Progress -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Sales Progress</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Stage:</strong><br>
                        {% set stage_colors = {
                            'email_sent': 'primary',
                            'email_opened': 'info',
                            'link_clicked': 'warning',
                            'opening': 'secondary',
                            'trust': 'info',
                            'discovery': 'primary',
                            'demonstration': 'warning',
                            'close': 'danger',
                            'converted': 'success'
                        } %}
                        <span class="badge bg-{{ stage_colors.get(contact.current_sales_stage, 'secondary') }}">
                            {{ (contact.current_sales_stage or 'new').replace('_', ' ').title() }}
                        </span>
                    </div>
                    {% if contact.first_email_sent %}
                    <div class="mb-2">
                        <strong>Email Sent:</strong><br>
                        <small>{{ contact.first_email_sent.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                    {% if contact.email_opened %}
                    <div class="mb-2">
                        <strong>Email Opened:</strong><br>
                        <small>{{ contact.email_opened.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                    {% if contact.chatbot_link_clicked %}
                    <div class="mb-2">
                        <strong>Link Clicked:</strong><br>
                        <small>{{ contact.chatbot_link_clicked.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                    {% if contact.chatbot_conversation_started %}
                    <div class="mb-2">
                        <strong>Conversation Started:</strong><br>
                        <small>{{ contact.chatbot_conversation_started.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                    {% if contact.conversion_completed %}
                    <div class="mb-2">
                        <strong>Conversion Completed:</strong><br>
                        <small>{{ contact.conversion_completed.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Chatbot Sessions -->
            {% if contact.chatbot_session_id %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-robot"></i> Chatbot Session</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Session ID:</strong><br>
                        <code>{{ contact.chatbot_session_id }}</code>
                    </div>
                    <a href="{{ url_for('chat_page', session_id=contact.chatbot_session_id) }}"
                       target="_blank" class="btn btn-success btn-sm">
                        <i class="fas fa-external-link-alt"></i> Open Chatbot
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Activities and Timeline -->
        <div class="col-md-8">
            <!-- Stage Progression -->
            {% if contact.stage_history %}
            <div class="card mb-3">
                <div class="card-header">
                    <h5><i class="fas fa-timeline"></i> Stage Progression</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for stage_info in contact.stage_history %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ stage_colors.get(stage_info.stage, 'secondary') }}"></div>
                            <div class="timeline-content">
                                <h6>{{ (stage_info.stage or 'new').replace('_', ' ').title() }}</h6>
                                <small class="text-muted">{{ stage_info.timestamp }}</small>
                                {% if stage_info.duration_from_previous > 0 %}
                                <br><small class="text-info">Duration: {{ "%.1f"|format(stage_info.duration_from_previous / 60) }} minutes</small>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Recent Activities -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history"></i> Recent Activities</h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                    <div class="list-group list-group-flush">
                        {% for activity in activities %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ activity.subject }}</h6>
                                <small>{{ activity.created_at.strftime('%Y-%m-%d %H:%M') if activity.created_at else '-' }}</small>
                            </div>
                            <p class="mb-1">{{ activity.description }}</p>
                            <small class="text-muted">
                                <span class="badge bg-secondary">{{ activity.activity_type.replace('_', ' ').title() }}</span>
                                {% if activity.session_id %}
                                | Session: <code>{{ activity.session_id[:8] }}...</code>
                                {% endif %}
                            </small>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-history fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No activities recorded yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Chatbot Sessions Details -->
            {% if chatbot_sessions %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-comments"></i> Chatbot Sessions</h5>
                </div>
                <div class="card-body">
                    {% for session in chatbot_sessions %}
                    <div class="border rounded p-3 mb-3 chatbot-session-details">
                        <div class="row session-info">
                            <div class="col-md-6">
                                <strong>Session ID:</strong> <code>{{ session.session_id }}</code><br>
                                <strong>Started:</strong> <span class="chatbot-session-text">{{ session.started_at.strftime('%Y-%m-%d %H:%M') if session.started_at else '-' }}</span><br>
                                {% if session.ended_at %}
                                <strong>Ended:</strong> <span class="chatbot-session-text">{{ session.ended_at.strftime('%Y-%m-%d %H:%M') }}</span><br>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <strong>Current Stage:</strong>
                                <span class="badge bg-{{ stage_colors.get(session.current_stage, 'secondary') }}">
                                    {{ (session.current_stage or 'new').replace('_', ' ').title() }}
                                </span><br>
                                <strong>Messages:</strong> <span class="chatbot-session-text">{{ session.total_messages or 0 }}</span><br>
                                <strong>Status:</strong>
                                {% if session.conversion_achieved %}
                                    <span class="badge bg-success">Converted</span>
                                {% elif session.completed_successfully %}
                                    <span class="badge bg-primary">Completed</span>
                                {% else %}
                                    <span class="badge bg-warning">In Progress</span>
                                {% endif %}
                            </div>
                        </div>
                        {% if session.current_task %}
                        <div class="mt-2">
                            <strong>Current Task:</strong> <span class="chatbot-session-text">{{ session.current_task }}</span>
                        </div>
                        {% endif %}
                        <div class="mt-2">
                            <a href="{{ url_for('chat_page', session_id=session.session_id) }}"
                               target="_blank" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-external-link-alt"></i> Open Session
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}
</style>
{% endblock %}
