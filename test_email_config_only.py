#!/usr/bin/env python3
"""
Simple Email Configuration Test
===============================
Test just the email configuration without Flask dependencies
"""

def test_config():
    """Test email configuration"""
    try:
        from email_system.config import get_email_config, validate_email_config, print_email_config
        
        print("✅ Email config modules imported successfully")
        
        # Get configuration
        config = get_email_config()
        print("✅ Configuration loaded")
        
        # Print configuration
        print_email_config(config)
        
        # Validate configuration
        is_valid, error_msg = validate_email_config(config)
        if is_valid:
            print("✅ Configuration validation passed")
        else:
            print(f"❌ Configuration validation failed: {error_msg}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_smtp_service():
    """Test SMTP service without Flask dependencies"""
    try:
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        from email_system.config import get_email_config
        
        print("✅ SMTP service modules imported successfully")
        
        # Get configuration
        config = get_email_config()
        
        # Create SMTP service
        smtp_service = EnhancedSMTPService(config)
        print("✅ SMTP service created")
        
        # Test connection
        success, message = smtp_service.test_connection()
        if success:
            print(f"✅ SMTP connection test passed: {message}")
        else:
            print(f"❌ SMTP connection test failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SMTP test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_templates():
    """Test email templates"""
    try:
        from email_system.email_templates import EmailTemplateManager
        
        print("✅ Template manager imported successfully")
        
        # Create template manager
        template_manager = EmailTemplateManager()
        print("✅ Template manager created")
        
        # Get template list
        templates = template_manager.get_template_list()
        print(f"✅ Found {len(templates)} templates:")
        for template in templates:
            print(f"   • {template['name']}: {template['display_name']}")
        
        # Test template rendering
        template_data = template_manager.render_template('introduction', {
            'contact_name': 'Test User',
            'company_name': 'Test Company',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+****************',
            'industry': 'Technology'
        })
        
        print("✅ Template rendering successful")
        print(f"   Subject: {template_data['subject']}")
        print(f"   HTML length: {len(template_data['html_body'])} chars")
        print(f"   Text length: {len(template_data['text_body'])} chars")
        
        return True
        
    except Exception as e:
        print(f"❌ Template test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Simple Email System Component Testing")
    print("=" * 50)
    
    # Test 1: Configuration
    print("\n📧 Test 1: Email Configuration")
    print("-" * 30)
    config_success = test_config()
    
    # Test 2: SMTP Service
    print("\n🔌 Test 2: SMTP Service")
    print("-" * 30)
    if config_success:
        smtp_success = test_smtp_service()
    else:
        smtp_success = False
        print("⏭️ Skipping SMTP test due to config failure")
    
    # Test 3: Templates
    print("\n📝 Test 3: Email Templates")
    print("-" * 30)
    template_success = test_templates()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Configuration: {'✅ PASSED' if config_success else '❌ FAILED'}")
    print(f"SMTP Service:  {'✅ PASSED' if smtp_success else '❌ FAILED'}")
    print(f"Templates:     {'✅ PASSED' if template_success else '❌ FAILED'}")
    
    if all([config_success, smtp_success, template_success]):
        print("\n🎉 ALL BASIC TESTS PASSED!")
        print("The email system components are working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
