#!/usr/bin/env python3
"""
Simple Campaigns Test
====================
Simple test to check if campaigns page loads at all.
"""

import requests
import time

def test_campaigns_basic():
    """Basic test to see if campaigns page responds"""
    print("🧪 Testing Basic Campaigns Functionality")
    print("=" * 45)
    
    base_url = 'http://localhost:5000'
    
    try:
        print("1. Testing if server is running...")
        response = requests.get(f'{base_url}/', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server issue: {response.status_code}")
            return False
        
        print("\n2. Testing campaigns page...")
        response = requests.get(f'{base_url}/campaigns', timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ Campaigns page loads successfully")
            
            # Check for basic content
            content = response.text.lower()
            
            if 'email campaigns' in content:
                print("✅ Found 'Email Campaigns' in page")
            else:
                print("❌ Missing 'Email Campaigns' header")
            
            if 'create campaign' in content:
                print("✅ Found 'Create Campaign' button")
            else:
                print("❌ Missing 'Create Campaign' button")
            
            if 'error' in content or 'exception' in content:
                print("⚠️ Page contains error messages")
                # Find error messages
                lines = response.text.split('\n')
                for i, line in enumerate(lines):
                    if 'error' in line.lower() or 'exception' in line.lower():
                        print(f"   Line {i}: {line.strip()[:100]}...")
                        break
            else:
                print("✅ No obvious errors in page content")
            
            return True
        else:
            print(f"❌ Campaigns page failed with status: {response.status_code}")
            print(f"   Response preview: {response.text[:200]}...")
            return False
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - is Flask app running?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out - server might be overloaded")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_create_campaign_basic():
    """Basic test for create campaign page"""
    print("\n3. Testing create campaign page...")
    
    base_url = 'http://localhost:5000'
    
    try:
        response = requests.get(f'{base_url}/campaigns/create', timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Create campaign page loads")
            
            content = response.text.lower()
            if 'campaign name' in content:
                print("✅ Found campaign name field")
            else:
                print("❌ Missing campaign name field")
            
            return True
        else:
            print(f"❌ Create campaign page failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing create campaign: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 SIMPLE CAMPAIGNS TEST")
    print("=" * 50)
    print("Testing basic campaigns functionality...")
    print("=" * 50)
    
    # Run basic tests
    campaigns_works = test_campaigns_basic()
    create_works = test_create_campaign_basic()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SIMPLE TEST SUMMARY")
    print("=" * 50)
    
    if campaigns_works and create_works:
        print("✅ BASIC CAMPAIGNS FUNCTIONALITY WORKS!")
        print("\nThe campaigns feature is accessible:")
        print("• Campaigns page loads")
        print("• Create campaign page loads")
        print("• No major server errors")
        print("\n💡 If you're having issues, try:")
        print("• Refreshing the browser page")
        print("• Clearing browser cache")
        print("• Checking browser console for JavaScript errors")
    elif campaigns_works:
        print("⚠️ CAMPAIGNS PAGE WORKS, CREATE PAGE HAS ISSUES")
        print("\nThe main campaigns page loads but create campaign has problems.")
    elif create_works:
        print("⚠️ CREATE PAGE WORKS, CAMPAIGNS LIST HAS ISSUES")
        print("\nThe create campaign page works but the main list has problems.")
    else:
        print("❌ CAMPAIGNS FUNCTIONALITY HAS MAJOR ISSUES!")
        print("\nPossible problems:")
        print("• Flask application not running")
        print("• Database connection issues")
        print("• Template rendering errors")
        print("• Analytics function failures")
        print("\n🔧 Try restarting the Flask application")
    
    return campaigns_works and create_works

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
