"""
Email Templates
==============
Manages email templates for 24Seven Assistants introduction campaigns.
"""

from jinja2 import Template, Environment, BaseLoader
from typing import Dict, Any, Optional, List
import os
import json

class EmailTemplateManager:
    """Manages email templates for campaigns"""

    def __init__(self):
        """Initialize template manager"""
        self.templates = {}
        self.load_default_templates()

    def load_default_templates(self):
        """Load default 24Seven Assistants email templates"""

        # Introduction Email Template – Meet <PERSON> with pricing & interactive chat
        self.templates['introduction'] = {
            'name': '24Seven Assistants - Meet <PERSON>, Your AI Sales Assistant',
            'subject': 'Meet <PERSON> - Our Personal AI Sales Assistant',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24Seven Assistants - Meet <PERSON></title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="margin: 0; font-size: 28px;">👋 Meet Sarah</h1>
        <p style="margin: 10px 0 0 0; font-size: 18px;">Our Personal AI Sales Assistant</p>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
        <p style="margin-top: 0;">Hi {{ contact_name }},</p>

        <p>I hope this email finds you well. My name is Sarah, and I'm reaching out from <strong>24Seven Assistants</strong> because I believe we can help you streamline your business operations and achieve remarkable growth and efficiency.</p>

        <p>Many businesses struggle with providing round the clock customer service. That's where we come in: we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.</p>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">🤖 Why 24Seven Assistants?</h3>
            <p style="margin-bottom: 0;">We provide world-class virtual assistant services that work around the clock to support your business operations, allowing you to focus on what matters most - growing your business.</p>
        </div>

        <h3 style="color: #ff9800;">📋 Our Core Services:</h3>

        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #4caf50;">
            <strong>📞 Administrative Support</strong><br>
            Email management, scheduling, data entry, and document preparation
        </div>

        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #2196f3;">
            <strong>📞 Customer Service</strong><br>
            24/7 customer support, live chat, and phone assistance
        </div>

        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <strong>📊 Lead qualification and follow-up</strong><br>
            Appointment scheduling
        </div>

        <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #9c27b0;">
            <strong>📧 Email and phone support</strong><br>
            Professional communication management
        </div>

        <h3 style="color: #4caf50;">💡 What Makes Us Different:</h3>
        <ul style="background: white; padding: 20px; border-radius: 8px;">
            <li><strong>Available 24/7:</strong> We work around the clock for maximum productivity</li>
            <li><strong>Flexible plans that grow with your business</strong></li>
            <li><strong>Easy integration with your current systems</strong></li>
            <li><strong>Competitive pricing in UGX currency</strong></li>
        </ul>

        <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0;">
            <h3 style="margin-top: 0; text-align: center;">💰 Our Pricing Plans</h3>

            <div style="display: flex; justify-content: space-between; flex-wrap: wrap; gap: 15px;">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; flex: 1; min-width: 150px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0;">Small Business</h4>
                    <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">UGX 250K setup</p>
                    <p style="margin: 5px 0;">UGX 100K/month</p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; flex: 1; min-width: 150px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0;">Medium Business</h4>
                    <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">UGX 500K setup</p>
                    <p style="margin: 5px 0;">UGX 250K/month</p>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; flex: 1; min-width: 150px; text-align: center;">
                    <h4 style="margin: 0 0 10px 0;">Large Enterprise</h4>
                    <p style="margin: 5px 0; font-size: 18px; font-weight: bold;">UGX 3M setup</p>
                    <p style="margin: 5px 0;">UGX 1M/month</p>
                </div>
            </div>
        </div>

        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 25px 0;">
            <h3 style="margin-top: 0; text-align: center;">💬 Try Our AI Assistant Demo</h3>
            <p style="text-align: center; margin-bottom: 20px;">Experience our AI assistant in action! Click the chat button below to start a conversation:</p>

            <div style="background: white; border-radius: 25px; padding: 15px; margin: 15px 0;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <input type="text"
                           style="flex: 1; padding: 12px; border: 2px solid #007bff; border-radius: 25px; font-size: 14px; background: white; color: #333; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                           placeholder="Type your message here..."
                           readonly>
                    <button style="background: #007bff; color: white; border: none; border-radius: 50%; width: 45px; height: 45px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"
                            onclick="window.open('{{ chat_url }}', '_blank')">
                        ➤
                    </button>
                </div>
            </div>

            <p style="text-align: center; margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">
                <a href="{{ chat_url }}" style="color: white; text-decoration: underline;">Click here to start chatting with Sarah →</a>
            </p>
        </div>

        <p>I'd love to schedule a brief call to discuss how 24Seven Assistants can specifically help {{ company_name }} achieve its goals. Are you available for a 15-minute conversation this week?</p>

        <div style="text-align: center; margin: 25px 0;">
            <a href="mailto:{{ reply_email }}" style="background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                📞 Schedule a Call
            </a>
        </div>

        <div style="border-top: 2px solid #e0e0e0; padding-top: 20px; margin-top: 30px;">
            <p style="margin-bottom: 5px;"><strong>Best regards,</strong></p>
            <p style="margin: 5px 0;"><strong>{{ agent_name }}</strong></p>
            <p style="margin: 5px 0;">24Seven Assistants</p>
            <p style="margin: 5px 0;">📧 {{ reply_email }}</p>
            <p style="margin: 5px 0;">📞 {{ phone_number }}</p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
        <p>© 2024 24Seven Assistants. Professional Virtual Assistant Services.</p>
    </div>
</body>
</html>
            ''',
            'text_template': '''
Hi {{ contact_name }},

I hope this email finds you well. My name is Sarah, and I'm reaching out from 24Seven Assistants because I believe we can help you streamline your business operations and achieve remarkable growth and efficiency.

Many businesses struggle with providing round the clock customer service. That's where we come in: we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.

Why 24Seven Assistants?
We provide world-class virtual assistant services that work around the clock to support your business operations, allowing you to focus on what matters most - growing your business.

Our Core Services:
• Administrative Support: Email management, scheduling, data entry, and document preparation
• Customer Service: 24/7 customer support, live chat, and phone assistance
• Lead qualification and follow-up: Appointment scheduling
• Email and phone support: Professional communication management

What Makes Us Different:
• Available 24/7: We work around the clock for maximum productivity
• Flexible plans that grow with your business
• Easy integration with your current systems
• Competitive pricing in UGX currency

Our Pricing Plans:
• Small Business: UGX 250K setup, UGX 100K/month
• Medium Business: UGX 500K setup, UGX 250K/month
• Large Enterprise: UGX 3M setup, UGX 1M/month

Try Our AI Assistant Demo:
Visit {{ chat_url }} to chat with Sarah and experience our AI assistant in action!

I'd love to schedule a brief call to discuss how 24Seven Assistants can specifically help {{ company_name }} achieve its goals. Are you available for a 15-minute conversation this week?

Best regards,
{{ agent_name }}
24Seven Assistants
{{ reply_email }}
{{ phone_number }}

© 2024 24Seven Assistants. Professional Virtual Assistant Services.
            '''
        }



        # Customer Support Solutions Email Template (using Meet Sarah format)
        self.templates['customer_support'] = {
            'name': '24Seven Assistants - Meet Sarah, Your AI Sales Assistant',
            'subject': 'Customer Support Solutions for {{ contact_name }}',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24Seven Assistants - Meet Sarah</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="margin: 0; font-size: 28px;">🤖 Meet Sarah</h1>
        <p style="margin: 10px 0 0 0; font-size: 18px;">Our Personal AI Sales Assistant</p>
    </div>

    <div style="background: white; padding: 30px; border: 1px solid #ddd; border-top: none;">
        <p>Hi {{ contact_name }},</p>

        <p>I hope this email finds you well! I'm Sarah from 24Seven Assistants, and I wanted to personally reach out about our customer support solutions.</p>

        <p>Many businesses struggle with providing round-the-clock customer service. That's where we come in - we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.</p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2c3e50; margin-top: 0;">Our Services Include:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Customer support and live chat</li>
                <li>Administrative task management</li>
                <li>Lead qualification and follow-up</li>
                <li>Appointment scheduling</li>
                <li>Email and phone support</li>
            </ul>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2c3e50; margin-top: 0;">What Makes Us Different:</h3>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Assistants trained specifically for your industry</li>
                <li>Flexible plans that grow with your business</li>
                <li>Easy integration with your current systems</li>
                <li>Competitive pricing in UGX currency</li>
            </ul>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2c3e50; margin-top: 0;">Our Pricing Plans:</h3>

            <div style="margin: 15px 0;">
                <h4 style="color: #2c3e50; margin-bottom: 5px;">Small Business Package:</h4>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li>Setup: UGX 250,000</li>
                    <li>Monthly: UGX 100,000</li>
                    <li>Perfect for startups and small businesses</li>
                </ul>
            </div>

            <div style="margin: 15px 0;">
                <h4 style="color: #2c3e50; margin-bottom: 5px;">Medium Business Package:</h4>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li>Setup: UGX 500,000</li>
                    <li>Monthly: UGX 250,000</li>
                    <li>Ideal for growing companies</li>
                </ul>
            </div>

            <div style="margin: 15px 0;">
                <h4 style="color: #2c3e50; margin-bottom: 5px;">Large Enterprise Package:</h4>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li>Setup: UGX 3,000,000</li>
                    <li>Monthly: UGX 1,000,000</li>
                    <li>Comprehensive solution for large organizations</li>
                </ul>
            </div>
        </div>

        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 15px; padding: 25px; margin: 30px 0; border: 2px solid #dee2e6;">
            <div style="font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 20px; text-align: center;">💬 Start Chat with Sarah</div>

            <div style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 15px;">
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 10px; border-left: 4px solid #2196f3;">
                        <strong>Sarah:</strong> Hello! I'm Sarah from 24Seven Assistants. We create virtual assistants that never sleep - helping businesses serve customers 24/7 even when everyone goes home. Think of it like having a helpful employee who works nights, weekends, and holidays without ever getting tired. Would you be interested in learning how this could help your business serve customers around the clock?
                    </div>
                </div>

                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="text" style="flex: 1; padding: 12px; border: 2px solid #007bff; border-radius: 25px; font-size: 14px; background: white; color: #333; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" placeholder="Type your message here..." readonly>
                    <a href="{{ chat_url }}" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 12px 25px; border: none; border-radius: 25px; font-weight: bold; text-decoration: none; display: inline-block; transition: all 0.3s ease;" target="_blank">Send</a>
                </div>

                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 15px; border-left: 4px solid #ffc107; font-size: 14px;">
                    💡 <strong>This will open our full chat interface where you can have a complete conversation with Sarah</strong>
                </div>
            </div>
        </div>

        <p>Sarah will introduce herself, learn about your business needs, and show you exactly how 24Seven Assistants can help you scale your operations. The conversation takes just 5-10 minutes.</p>

        <p><strong>Pro Tip:</strong> Sarah is powered by advanced AI and has helped hundreds of businesses. She's particularly great at understanding unique challenges and providing tailored solutions.</p>

        <p>Ready to see how AI can transform your business operations?</p>

        <p>Best regards,<br>
        <strong>Sarah</strong><br>
        24Seven Assistants<br>
        📧 <EMAIL><br>
        📞 +256 **********<br>
        🌐 <a href="http://www.24seven.site">http://www.24seven.site</a></p>
    </div>

    <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 12px; color: #666;">
        <p style="margin: 0;">This email was sent as part of our AI sales assistant demonstration.</p>
        <p style="margin: 5px 0 0 0;">Click tracking and conversation analytics are enabled for performance optimization.</p>
    </div>

    <!-- Email Open Tracking Pixel -->
    <img src="http://localhost:5000/track/open/{{ session_id }}" width="1" height="1" style="display:none;" alt="">
</body>
</html>
            ''',
            'text_template': '''
Customer Support Solutions for {{ contact_name }}

Hi {{ contact_name }},

I hope this message finds you well. I'm Sarah from 24Seven Assistants, and I wanted to personally reach out about our customer support solutions.

Many businesses struggle with providing round-the-clock customer service. That's where we come in - we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.

OUR SERVICES INCLUDE:
• Customer support and live chat
• Administrative task management
• Lead qualification and follow-up
• Appointment scheduling
• Email and phone support

WHAT MAKES US DIFFERENT:
• Assistants trained specifically for your industry
• Flexible plans that grow with your business
• Easy integration with your current systems
• Competitive pricing in UGX currency

OUR PRICING PLANS:

Small Business Package:
• Setup: UGX 250,000
• Monthly: UGX 100,000
• Perfect for startups and small businesses

Medium Business Package:
• Setup: UGX 500,000
• Monthly: UGX 250,000
• Ideal for growing companies

Large Enterprise Package:
• Setup: UGX 3,000,000
• Monthly: UGX 1,000,000
• Comprehensive solution for large organizations

I'd love to show you how this works. Our assistant Sarah can demonstrate our capabilities and help determine which package would be best for your business.

Start a conversation with Sarah: {{ chat_url }}

Sarah will introduce herself and explain how we can help your specific business needs.

Best regards,
Sarah
24Seven Assistants

Email: <EMAIL>
Website: http://www.24seven.site
Phone: +256 **********

P.S. If you'd prefer to schedule a call instead, Sarah can arrange that too.
            '''
        }

        # Follow-up Email Template
        self.templates['followup'] = {
            'name': '24Seven Assistants Follow-up',
            'subject': 'Quick Follow-up: Virtual Assistant Services for {{ company_name }}',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Follow-up: 24Seven Assistants</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2196F3; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .highlight { background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3; margin: 20px 0; }
        .cta-button { display: inline-block; background: #2196F3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>24Seven Assistants</h2>
        </div>

        <div class="content">
            <p>Hi {{ contact_name }},</p>

            <p>I wanted to follow up on my previous email about how 24Seven Assistants can help {{ company_name }} streamline operations and boost productivity.</p>

            <div class="highlight">
                <h3>🚀 Quick Reminder - What We Offer:</h3>
                <p><strong>24/7 Virtual Assistant Services</strong> that can save {{ company_name }} up to 70% on operational costs while providing round-the-clock support.</p>
            </div>

            <p>I understand you're busy, so I'll keep this brief. Many of our clients initially hesitated but later told us that partnering with 24Seven Assistants was one of their best business decisions.</p>

            <p><strong>Would a quick 10-minute call work for you this week?</strong> I can show you exactly how we've helped similar companies in {{ industry }} achieve remarkable results.</p>

            <a href="mailto:{{ reply_email }}?subject=Yes, let's schedule a call - {{ company_name }}" class="cta-button">
                📞 Let's Talk
            </a>

            <p>No pressure - just a friendly conversation about your business needs and how we might be able to help.</p>

            <p>Best regards,<br>
            <strong>{{ agent_name }}</strong><br>
            24Seven Assistants<br>
            {{ reply_email }}</p>
        </div>

        <div class="footer">
            <p><small>If you'd prefer not to receive these emails, <a href="#">click here to unsubscribe</a></small></p>
        </div>
    </div>
</body>
</html>
            ''',
            'text_template': '''
Hi {{ contact_name }},

I wanted to follow up on my previous email about how 24Seven Assistants can help {{ company_name }} streamline operations and boost productivity.

QUICK REMINDER - WHAT WE OFFER:
24/7 Virtual Assistant Services that can save {{ company_name }} up to 70% on operational costs while providing round-the-clock support.

I understand you're busy, so I'll keep this brief. Many of our clients initially hesitated but later told us that partnering with 24Seven Assistants was one of their best business decisions.

Would a quick 10-minute call work for you this week? I can show you exactly how we've helped similar companies in {{ industry }} achieve remarkable results.

No pressure - just a friendly conversation about your business needs and how we might be able to help.

Best regards,
{{ agent_name }}
24Seven Assistants
{{ reply_email }}
            '''
        }

    def render_template(self, template_name: str, context: Dict[str, Any]) -> Dict[str, str]:
        """
        Render email template with context variables

        Args:
            template_name: Name of the template to render
            context: Dictionary of variables to substitute

        Returns:
            Dictionary with rendered subject, html_body, and text_body
        """
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")

        template_data = self.templates[template_name]

        # Set default context values
        default_context = {
            'agent_name': 'Sarah',
            'company_name': 'Your Company',
            'contact_name': 'there',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'industry': 'your industry',
            'chat_url': 'http://localhost:5000/chat/session_id_placeholder',
            'session_id': 'session_id_placeholder'
        }

        # Merge with provided context
        render_context = {**default_context, **context}

        # Render templates
        subject_template = Template(template_data['subject'])
        html_template = Template(template_data['html_template'])
        text_template = Template(template_data['text_template'])

        return {
            'subject': subject_template.render(**render_context),
            'html_body': html_template.render(**render_context),
            'text_body': text_template.render(**render_context)
        }

    def get_template_list(self) -> List[Dict[str, str]]:
        """Get list of available templates"""
        return [
            {
                'name': template_name,
                'display_name': template_data['name'],
                'subject': template_data['subject']
            }
            for template_name, template_data in self.templates.items()
        ]

    def add_custom_template(self, name: str, display_name: str, subject: str,
                           html_template: str, text_template: str):
        """Add a custom email template"""
        self.templates[name] = {
            'name': display_name,
            'subject': subject,
            'html_template': html_template,
            'text_template': text_template
        }
