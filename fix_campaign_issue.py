#!/usr/bin/env python3
"""
Fix Campaign Issue
==================
Quick fix for campaign sending issues based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md
"""

def fix_campaign_recipient_criteria():
    """Fix campaign recipient criteria that might be causing no contacts to be selected"""
    try:
        print("🔧 Fixing Campaign Recipient Criteria")
        print("=" * 50)
        
        from unified_sales_system import app, db, Contact, EmailCampaign
        import json
        
        with app.app_context():
            # Get all campaigns
            campaigns = EmailCampaign.query.all()
            print(f"Found {len(campaigns)} campaigns")
            
            for campaign in campaigns:
                print(f"\n📧 Checking campaign: {campaign.name}")
                print(f"   Current recipient criteria: {campaign.recipient_criteria}")
                
                # Check if recipient criteria is problematic
                if campaign.recipient_criteria:
                    try:
                        criteria = json.loads(campaign.recipient_criteria)
                        recipient_type = criteria.get('type', 'all')
                        
                        if recipient_type == 'specific':
                            contact_ids = criteria.get('contact_ids', [])
                            if not contact_ids:
                                print("   ⚠️ Issue found: 'specific' type but no contact IDs")
                                # Fix by changing to 'all'
                                new_criteria = {'type': 'all'}
                                campaign.recipient_criteria = json.dumps(new_criteria)
                                print("   ✅ Fixed: Changed to 'all' contacts")
                            else:
                                print(f"   ✅ OK: Has {len(contact_ids)} specific contact IDs")
                        else:
                            print(f"   ✅ OK: Type is '{recipient_type}'")
                            
                    except json.JSONDecodeError:
                        print("   ⚠️ Issue found: Invalid JSON in recipient criteria")
                        # Fix by setting to 'all'
                        new_criteria = {'type': 'all'}
                        campaign.recipient_criteria = json.dumps(new_criteria)
                        print("   ✅ Fixed: Set to 'all' contacts")
                else:
                    print("   ✅ OK: No recipient criteria (defaults to all)")
            
            # Commit changes
            db.session.commit()
            print("\n✅ All campaign recipient criteria fixed!")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing recipient criteria: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_contacts_exist():
    """Verify that email-enabled contacts exist"""
    try:
        print("\n👥 Verifying Email-Enabled Contacts")
        print("=" * 40)
        
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            total_contacts = Contact.query.count()
            active_contacts = Contact.query.filter_by(is_active=True).count()
            email_enabled = Contact.query.filter_by(is_active=True, do_not_email=False).count()
            
            print(f"Total contacts: {total_contacts}")
            print(f"Active contacts: {active_contacts}")
            print(f"Email-enabled contacts: {email_enabled}")
            
            if email_enabled == 0:
                print("❌ No email-enabled contacts found!")
                
                # Try to fix by enabling email for active contacts
                if active_contacts > 0:
                    print("🔧 Attempting to fix by enabling email for active contacts...")
                    active_contacts_list = Contact.query.filter_by(is_active=True).all()
                    fixed_count = 0
                    
                    for contact in active_contacts_list:
                        if contact.do_not_email:
                            contact.do_not_email = False
                            fixed_count += 1
                    
                    if fixed_count > 0:
                        db.session.commit()
                        print(f"✅ Fixed: Enabled email for {fixed_count} contacts")
                        return True
                    else:
                        print("⚠️ All active contacts already have email enabled")
                        return True
                else:
                    print("❌ No active contacts found!")
                    return False
            else:
                print("✅ Email-enabled contacts found!")
                
                # Show sample contacts
                sample_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).limit(3).all()
                print("\n📋 Sample email-enabled contacts:")
                for contact in sample_contacts:
                    print(f"   • {contact.first_name} {contact.last_name} <{contact.email}>")
                
                return True
                
    except Exception as e:
        print(f"❌ Error verifying contacts: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_smtp_and_fix():
    """Test SMTP connection and provide fix suggestions"""
    try:
        print("\n📤 Testing SMTP Connection")
        print("=" * 40)
        
        from unified_sales_system import app, test_smtp_connection
        
        with app.app_context():
            success, message = test_smtp_connection()
            
            if success:
                print(f"✅ SMTP test passed: {message}")
                return True
            else:
                print(f"❌ SMTP test failed: {message}")
                
                # Provide fix suggestions
                print("\n🔧 SMTP Fix Suggestions:")
                print("1. Check your email configuration in email_system/config.py")
                print("2. Verify SMTP server: mail.24seven.site")
                print("3. Verify SMTP port: 465 (SSL)")
                print("4. Verify username: <EMAIL>")
                print("5. Verify password is correct")
                print("6. Check firewall/network connectivity")
                
                return False
                
    except Exception as e:
        print(f"❌ SMTP test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def reset_campaign_daily_limits():
    """Reset daily send limits for campaigns"""
    try:
        print("\n📊 Resetting Campaign Daily Limits")
        print("=" * 40)
        
        from unified_sales_system import app, db, EmailCampaign
        from datetime import date
        
        with app.app_context():
            campaigns = EmailCampaign.query.all()
            
            for campaign in campaigns:
                print(f"📧 Campaign: {campaign.name}")
                print(f"   Emails sent today: {campaign.emails_sent_today}")
                print(f"   Last send date: {campaign.last_send_date}")
                print(f"   Daily limit: {campaign.daily_send_limit}")
                
                # Reset daily counter if needed
                today = date.today()
                if campaign.last_send_date and campaign.last_send_date.date() != today:
                    campaign.emails_sent_today = 0
                    print("   ✅ Reset daily counter (new day)")
                elif not campaign.last_send_date:
                    campaign.emails_sent_today = 0
                    print("   ✅ Reset daily counter (no previous sends)")
                
                remaining = campaign.get_remaining_sends_today()
                print(f"   Remaining sends today: {remaining}")
            
            db.session.commit()
            print("\n✅ Daily limits reset!")
            return True
            
    except Exception as e:
        print(f"❌ Error resetting daily limits: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main fix function"""
    print("🚀 Campaign Issue Fix Tool")
    print("Based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    print("=" * 60)
    
    fixes_applied = []
    
    # Fix 1: Campaign recipient criteria
    if fix_campaign_recipient_criteria():
        fixes_applied.append("✅ Campaign recipient criteria")
    else:
        fixes_applied.append("❌ Campaign recipient criteria")
    
    # Fix 2: Verify contacts
    if verify_contacts_exist():
        fixes_applied.append("✅ Email-enabled contacts")
    else:
        fixes_applied.append("❌ Email-enabled contacts")
    
    # Fix 3: Reset daily limits
    if reset_campaign_daily_limits():
        fixes_applied.append("✅ Daily send limits")
    else:
        fixes_applied.append("❌ Daily send limits")
    
    # Fix 4: Test SMTP
    if test_smtp_and_fix():
        fixes_applied.append("✅ SMTP connection")
    else:
        fixes_applied.append("❌ SMTP connection")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIX SUMMARY")
    print("=" * 60)
    for fix in fixes_applied:
        print(f"  {fix}")
    
    success_count = len([f for f in fixes_applied if f.startswith("✅")])
    total_count = len(fixes_applied)
    
    if success_count == total_count:
        print(f"\n🎉 ALL FIXES SUCCESSFUL! ({success_count}/{total_count})")
        print("\nNext steps:")
        print("1. Go to http://localhost:5000/campaigns")
        print("2. Try sending your campaign again")
        print("3. Check the application logs for detailed information")
    else:
        print(f"\n⚠️ SOME FIXES FAILED ({success_count}/{total_count})")
        print("Please address the failed items above before trying to send campaigns.")
    
    return success_count == total_count

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
