#!/usr/bin/env python3
"""
Send Updated Template Test
=========================
Send a test email using the updated template to verify the clickable input works.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def send_test_email():
    """Send a test email with the updated template"""
    print("📧 Sending Test Email with Updated Template")
    print("=" * 50)
    
    try:
        # Import email system components
        from email_system.email_templates import EmailTemplateManager
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        from email_system.config import get_email_config
        
        print("✅ Email system components imported")
        
        # Create services
        template_manager = EmailTemplateManager()
        email_config = get_email_config()
        smtp_service = EnhancedSMTPService(email_config)
        
        print("✅ Services created")
        
        # Test context
        test_context = {
            'contact_name': 'Test User',
            'company_name': 'Test Company Ltd',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/updated-template-test',
            'session_id': 'updated-template-test'
        }
        
        print("📝 Rendering template...")
        
        # Render the template
        rendered = template_manager.render_template('introduction', test_context)
        
        print("✅ Template rendered successfully")
        print(f"   Subject: {rendered['subject']}")
        
        # Check if our changes are in the rendered content
        html_content = rendered['html_body']
        has_table = '<table cellpadding="0" cellspacing="0"' in html_content
        has_emoji_text = '💬 Click here to start typing your message...' in html_content
        has_clickable_link = 'href="http://localhost:5000/chat/updated-template-test"' in html_content
        
        print(f"\n🔍 Template verification:")
        print(f"   Table layout: {'✅' if has_table else '❌'}")
        print(f"   Emoji button text: {'✅' if has_emoji_text else '❌'}")
        print(f"   Clickable link: {'✅' if has_clickable_link else '❌'}")
        
        if not all([has_table, has_emoji_text, has_clickable_link]):
            print("❌ Template verification failed!")
            return False
        
        print("\n📤 Sending test email...")
        
        # Send the email
        success, message_id, error_msg = smtp_service.send_email(
            to_email="<EMAIL>",
            subject=f"[UPDATED TEMPLATE TEST] {rendered['subject']}",
            html_body=html_content,
            text_body=rendered['text_body'],
            from_name="24Seven Assistants - Updated Template Test"
        )
        
        if success:
            print("✅ Test email sent successfully!")
            print(f"📧 Message ID: {message_id}")
            print("📬 Check your <NAME_EMAIL>")
            print("\n🖱️ In the email, look for:")
            print("   • Table-based input field layout")
            print("   • '💬 Click here to start typing your message...' text")
            print("   • Entire input area should be clickable")
            print("   • Should open: http://localhost:5000/chat/updated-template-test")
            
            # Save a copy for reference
            with open('sent_email_template.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n📁 Email content saved to: sent_email_template.html")
            
            return True
        else:
            print(f"❌ Failed to send test email: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test email: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = send_test_email()
    if success:
        print("\n🎉 SUCCESS! Updated template email sent!")
        print("Check your Gmail inbox to test the clickable input field.")
    else:
        print("\n❌ FAILED! Could not send updated template email.")
    
    sys.exit(0 if success else 1)
