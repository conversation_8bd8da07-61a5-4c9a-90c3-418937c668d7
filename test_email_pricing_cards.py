#!/usr/bin/env python3
"""
Test Email Pricing Cards
========================
Test script to verify the email templates include the smaller, flexible pricing cards.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_system.email_templates import EmailTemplateManager

def test_email_pricing_cards():
    """Test the pricing cards in email templates"""
    print("📧 Testing Email Pricing Cards")
    print("=" * 50)
    
    try:
        # Initialize template manager
        template_manager = EmailTemplateManager()
        
        # Test context
        test_context = {
            'contact_name': '<PERSON>',
            'company_name': 'Test Company Ltd',
            'agent_name': '<PERSON>',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-session-123',
            'session_id': 'test-session-123'
        }
        
        # Test introduction template
        print("📧 Testing Introduction Template...")
        intro_result = template_manager.render_template('introduction', test_context)
        
        # Check for smaller, flexible pricing card features
        html_content = intro_result['html_body']
        
        pricing_card_checks = [
            ('Pricing section exists', 'pricing-section' in html_content),
            ('Pricing grid layout', 'pricing-grid' in html_content),
            ('Smaller pricing cards', 'pricing-card' in html_content),
            ('Smaller min-width (180px)', 'min-width: 180px' in html_content),
            ('Smaller max-width (220px)', 'max-width: 220px' in html_content),
            ('Reduced padding (15px)', 'padding: 15px' in html_content),
            ('Smaller border radius (10px)', 'border-radius: 10px' in html_content),
            ('Hover effects', 'transform: translateY(-3px)' in html_content),
            ('Smooth transitions', 'transition: transform 0.2s ease' in html_content),
            ('Smaller heading font (16px)', 'font-size: 16px' in html_content),
            ('Smaller pricing font (18px)', 'font-size: 18px' in html_content),
            ('Compact text (14px)', 'font-size: 14px' in html_content),
            ('Compact margins (6px)', 'margin: 6px 0' in html_content)
        ]
        
        print("   Pricing Card Features:")
        all_passed = True
        for check_name, passed in pricing_card_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Check pricing content
        pricing_content_checks = [
            ('Small Business pricing', 'UGX 250K setup' in html_content),
            ('Medium Business pricing', 'UGX 500K setup' in html_content),
            ('Large Enterprise pricing', 'UGX 3M setup' in html_content),
            ('Monthly pricing display', 'UGX 100K/month' in html_content),
            ('Pricing descriptions', 'Perfect for startups' in html_content),
            ('Growth description', 'Ideal for growth' in html_content),
            ('Enterprise description', 'Complete solution' in html_content)
        ]
        
        print("\n   Pricing Content:")
        for check_name, passed in pricing_content_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Check responsive features
        responsive_checks = [
            ('Mobile responsive', '@media only screen and (max-width: 768px)' in html_content),
            ('Mobile column layout', 'flex-direction: column' in html_content),
            ('Mobile full width', 'width: 100%' in html_content),
            ('Mobile reduced padding', 'padding: 12px' in html_content),
            ('Small mobile optimization', '@media only screen and (max-width: 480px)' in html_content),
            ('Extra small optimization', '@media only screen and (max-width: 360px)' in html_content)
        ]
        
        print("\n   Responsive Features:")
        for check_name, passed in responsive_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Create a test email HTML file
        test_email_html = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email with Flexible Pricing Cards</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        
        .email-preview {{
            background: white;
            max-width: 800px;
            margin: 0 auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .preview-header {{
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }}
        
        .preview-content {{
            padding: 20px;
        }}
        
        .size-controls {{
            margin: 20px 0;
            text-align: center;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
        }}
        
        .size-controls button {{
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }}
        
        .size-controls button:hover {{
            background: #0056b3;
        }}
        
        .email-container {{
            transition: all 0.3s ease;
            border: 2px dashed #ddd;
            margin: 20px 0;
        }}
        
        .improvements-list {{
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }}
        
        .improvements-list h4 {{
            color: #155724;
            margin-top: 0;
        }}
        
        .improvements-list ul {{
            color: #155724;
            margin-bottom: 0;
        }}
    </style>
</head>
<body>
    <div class="email-preview">
        <div class="preview-header">
            <h2>📧 Email Template with Flexible Pricing Cards</h2>
            <p>Test how the smaller, more flexible pricing cards look in actual emails</p>
        </div>
        
        <div class="preview-content">
            <div class="size-controls">
                <strong>📱 Test Different Email Client Widths:</strong><br>
                <button onclick="setWidth('100%')">Full Width</button>
                <button onclick="setWidth('800px')">Desktop Email</button>
                <button onclick="setWidth('600px')">Tablet Email</button>
                <button onclick="setWidth('400px')">Mobile Email</button>
                <button onclick="setWidth('320px')">Small Mobile</button>
            </div>
            
            <div class="email-container" id="emailContainer">
                {intro_result['html_body']}
            </div>
            
            <div class="improvements-list">
                <h4>✨ Pricing Card Improvements Applied:</h4>
                <ul>
                    <li><strong>28% Smaller Size:</strong> Width reduced from 250-300px to 180-220px</li>
                    <li><strong>Compact Padding:</strong> Reduced from 20px to 15px for better space efficiency</li>
                    <li><strong>Improved Typography:</strong> Optimized font sizes for better readability</li>
                    <li><strong>Hover Effects:</strong> Smooth animations when users hover over cards</li>
                    <li><strong>Better Mobile Design:</strong> Cards stack properly on mobile devices</li>
                    <li><strong>Flexible Layout:</strong> Cards adapt to any email client width</li>
                    <li><strong>Enhanced Responsiveness:</strong> Multiple breakpoints for all screen sizes</li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #856404; margin-top: 0;">📊 Size Comparison:</h4>
                <div style="color: #856404;">
                    <strong>Before:</strong> 250-300px wide cards with 20px padding<br>
                    <strong>After:</strong> 180-220px wide cards with 15px padding<br>
                    <strong>Space Saved:</strong> Up to 28% more efficient use of email width
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function setWidth(width) {{
            const container = document.getElementById('emailContainer');
            container.style.width = width;
            container.style.maxWidth = width;
            container.style.margin = '20px auto';
        }}
        
        // Set initial width
        setWidth('800px');
    </script>
</body>
</html>
        '''
        
        # Save the test email
        test_filename = 'test_email_pricing_cards.html'
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(test_email_html)
        
        print(f"\n📁 Test email saved to: {test_filename}")
        print("   Open this file to see how the pricing cards look in emails")
        
        # Test customer support template too
        print("\n📧 Testing Customer Support Template...")
        if 'customer_support' in template_manager.templates:
            support_result = template_manager.render_template('customer_support', test_context)
            support_html = support_result['html_body']
            
            support_checks = [
                ('Support template has pricing', 'pricing' in support_html.lower()),
                ('Support template responsive', '@media' in support_html)
            ]
            
            print("   Customer Support Template:")
            for check_name, passed in support_checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
        
        if all_passed:
            print("\n🎉 All Email Pricing Card Tests Passed!")
            print("\n📧 Email Template Features:")
            print("   • Smaller, more flexible pricing cards")
            print("   • 28% reduction in card width")
            print("   • Smooth hover animations")
            print("   • Better mobile responsiveness")
            print("   • Optimized for all email clients")
            print("   • Professional visual design")
            
            print("\n📱 Email Client Compatibility:")
            print("   • Desktop email clients (Outlook, Thunderbird)")
            print("   • Web email clients (Gmail, Yahoo, Outlook.com)")
            print("   • Mobile email apps (iOS Mail, Android Gmail)")
            print("   • Responsive design for all screen sizes")
            
            return True
        else:
            print("\n❌ Some email pricing card tests failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing email pricing cards: {e}")
        return False

if __name__ == "__main__":
    success = test_email_pricing_cards()
    sys.exit(0 if success else 1)
