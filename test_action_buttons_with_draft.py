#!/usr/bin/env python3
"""
Test Campaign Action Buttons with Draft Campaign
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def create_draft_campaign():
    """Create a draft campaign for testing"""
    print("📝 Creating draft campaign for testing...")
    
    data = {
        'name': 'Action Buttons Test Campaign',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate'
    }
    
    response = requests.post(f"{BASE_URL}/campaigns/create", data=data)
    if response.status_code in [200, 302]:
        print("✅ Draft campaign created successfully")
        return True
    else:
        print(f"❌ Failed to create draft campaign: {response.status_code}")
        return False

def test_action_buttons_functionality():
    """Test the actual functionality of action buttons"""
    print("\n🎯 Testing Action Button Functionality")
    print("=" * 40)
    
    # Get campaigns page
    response = requests.get(f"{BASE_URL}/campaigns")
    if response.status_code != 200:
        print(f"❌ Failed to get campaigns: {response.status_code}")
        return False
    
    content = response.text
    
    # Check for different types of action buttons based on campaign status
    action_elements = {
        'View Button': 'btn-outline-primary',
        'Edit Button': 'btn-outline-secondary', 
        'Send Button': 'btn-success',
        'Duplicate Form': 'duplicate_campaign',
        'Delete Form': 'delete_campaign',
        'Bulk Delete': 'bulkDeleteForm'
    }
    
    found_elements = 0
    for element_name, element_identifier in action_elements.items():
        if element_identifier in content:
            print(f"✅ {element_name} found")
            found_elements += 1
        else:
            print(f"❌ {element_name} missing")
    
    print(f"\n📊 Elements Found: {found_elements}/{len(action_elements)}")
    
    # Check for specific form actions
    form_actions = [
        'action="/campaigns/',
        'method="POST"',
        'onclick="return confirm'
    ]
    
    form_features = 0
    for feature in form_actions:
        if feature in content:
            form_features += 1
            print(f"✅ Form feature found: {feature}")
        else:
            print(f"❌ Form feature missing: {feature}")
    
    print(f"\n📊 Form Features: {form_features}/{len(form_actions)}")
    
    return found_elements >= 4 and form_features >= 2

def test_individual_actions():
    """Test individual action button functionality"""
    print("\n🔧 Testing Individual Actions")
    print("=" * 30)
    
    # Get campaign ID from the page
    response = requests.get(f"{BASE_URL}/campaigns")
    content = response.text
    
    import re
    campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', content)
    
    if not campaign_ids:
        print("❌ No campaign IDs found")
        return False
    
    campaign_id = campaign_ids[0]
    print(f"🎯 Testing with campaign ID: {campaign_id}")
    
    # Test view campaign
    view_url = f"{BASE_URL}/campaigns/{campaign_id}"
    view_response = requests.get(view_url)
    if view_response.status_code == 200:
        print("✅ View campaign action works")
    else:
        print(f"❌ View campaign failed: {view_response.status_code}")
    
    # Test edit campaign
    edit_url = f"{BASE_URL}/campaigns/{campaign_id}/edit"
    edit_response = requests.get(edit_url)
    if edit_response.status_code == 200:
        print("✅ Edit campaign action works")
    else:
        print(f"❌ Edit campaign failed: {edit_response.status_code}")
    
    # Test duplicate campaign (POST request)
    duplicate_url = f"{BASE_URL}/campaigns/{campaign_id}/duplicate"
    try:
        duplicate_response = requests.post(duplicate_url)
        if duplicate_response.status_code in [200, 302]:
            print("✅ Duplicate campaign action works")
        else:
            print(f"❌ Duplicate campaign failed: {duplicate_response.status_code}")
    except Exception as e:
        print(f"❌ Duplicate campaign error: {str(e)}")
    
    return True

def test_javascript_functionality():
    """Test JavaScript functions in the page"""
    print("\n🔧 Testing JavaScript Functionality")
    print("=" * 35)
    
    response = requests.get(f"{BASE_URL}/campaigns")
    content = response.text
    
    js_functions = [
        'function bulkDeleteCampaigns',
        'function toggleSelectAll',
        'function updateBulkDeleteButtons',
        'addEventListener',
        'document.getElementById'
    ]
    
    js_found = 0
    for func in js_functions:
        if func in content:
            js_found += 1
            print(f"✅ JavaScript feature: {func}")
        else:
            print(f"❌ Missing JavaScript: {func}")
    
    print(f"\n📊 JavaScript Features: {js_found}/{len(js_functions)}")
    return js_found >= 3

def main():
    print("🚀 Comprehensive Action Buttons Test")
    print("=" * 50)
    
    # Step 1: Create a draft campaign for testing
    draft_created = create_draft_campaign()
    
    # Step 2: Test action buttons presence and functionality
    buttons_test = test_action_buttons_functionality()
    
    # Step 3: Test individual actions
    actions_test = test_individual_actions()
    
    # Step 4: Test JavaScript functionality
    js_test = test_javascript_functionality()
    
    print("\n" + "=" * 50)
    print("📋 COMPREHENSIVE TEST RESULTS")
    print("=" * 50)
    print(f"Draft Campaign Created:     {'✅ PASSED' if draft_created else '❌ FAILED'}")
    print(f"Action Buttons Present:     {'✅ PASSED' if buttons_test else '❌ FAILED'}")
    print(f"Individual Actions Work:    {'✅ PASSED' if actions_test else '❌ FAILED'}")
    print(f"JavaScript Functions:       {'✅ PASSED' if js_test else '❌ FAILED'}")
    
    overall_result = buttons_test and actions_test and js_test
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if overall_result:
        print("\n🎉 Campaign action buttons are working correctly!")
        print("\n📝 Available Actions:")
        print("   • 👁️  View Campaign Details")
        print("   • ✏️  Edit Campaign Settings")
        print("   • 📧 Send Campaign (draft status)")
        print("   • 📋 Duplicate Campaign")
        print("   • 🗑️  Delete Campaign")
        print("   • 🔄 Retry Failed Emails")
        print("   • 📦 Bulk Operations")
        print("\n💡 Tips:")
        print("   • Click the eye icon to view campaign details")
        print("   • Use the edit icon to modify campaign settings")
        print("   • Send button appears for draft campaigns")
        print("   • Duplicate creates a copy in draft status")
        print("   • Delete has confirmation dialogs")
        print("   • Bulk operations use checkboxes")
    else:
        print("\n⚠️ Some issues detected:")
        if not buttons_test:
            print("   • Action buttons may not be properly rendered")
        if not actions_test:
            print("   • Individual actions may not be working")
        if not js_test:
            print("   • JavaScript functionality may be limited")
        print("\n🔧 Troubleshooting:")
        print("   • Check browser console for JavaScript errors")
        print("   • Verify Flask routes are accessible")
        print("   • Ensure database is properly connected")

if __name__ == "__main__":
    main()
