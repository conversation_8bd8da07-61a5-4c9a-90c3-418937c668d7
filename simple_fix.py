import os
os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
os.environ['MAIL_PORT'] = '587'
os.environ['MAIL_USE_TLS'] = 'true'
os.environ['MAIL_USERNAME'] = '<EMAIL>'
os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

try:
    from unified_sales_system import app, db, Contact
    with app.app_context():
        db.create_all()
        contact = Contact(first_name='<PERSON>', last_name='Scof', email='<EMAIL>', phone='(*************', company='Test Company', job_title='CEO', source='manual_entry', status='new')
        db.session.add(contact)
        db.session.commit()
        print(f"SUCCESS: {contact.full_name} created")
except Exception as e:
    print(f"ERROR: {e}")
