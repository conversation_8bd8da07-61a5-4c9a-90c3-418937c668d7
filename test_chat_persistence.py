#!/usr/bin/env python3
"""
Chat Persistence Test Script
============================
This script tests the chat persistence functionality by simulating
chat sessions and verifying that messages are saved and retrieved correctly.
"""

import requests
import json
import uuid
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_SESSION_ID = str(uuid.uuid4())

def test_save_message(session_id, message_type, content, stage=None, task=None, message_order=0):
    """Test saving a chat message"""
    url = f"{BASE_URL}/api/save-chat-message"
    data = {
        'session_id': session_id,
        'message_type': message_type,
        'content': content,
        'stage': stage,
        'task': task,
        'message_order': message_order
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Saved {message_type} message: {content[:50]}...")
            return result.get('message_id')
        else:
            print(f"❌ Failed to save message: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error saving message: {e}")
        return None

def test_get_history(session_id):
    """Test retrieving chat history"""
    url = f"{BASE_URL}/api/get-chat-history/{session_id}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                history = result.get('history', [])
                print(f"✅ Retrieved {len(history)} messages from history")
                return history
            else:
                print(f"❌ Failed to get history: {result.get('message')}")
                return []
        else:
            print(f"❌ Failed to get history: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error getting history: {e}")
        return []

def test_reset_session(session_id):
    """Test resetting a chat session"""
    url = f"{BASE_URL}/api/reset-chat-session"
    data = {'session_id': session_id}
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Successfully reset session")
                return True
            else:
                print(f"❌ Failed to reset session: {result.get('message')}")
                return False
        else:
            print(f"❌ Failed to reset session: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error resetting session: {e}")
        return False

def run_tests():
    """Run all chat persistence tests"""
    print("🧪 Chat Persistence Test Suite")
    print("=" * 50)
    print(f"Test Session ID: {TEST_SESSION_ID}")
    print()
    
    # Test 1: Save initial assistant message
    print("📝 Test 1: Save initial assistant message")
    msg_id_1 = test_save_message(
        TEST_SESSION_ID, 
        'assistant', 
        "Hello! I'm Sarah, your AI sales assistant. What's your name?",
        'opening',
        'greeting',
        0
    )
    
    # Test 2: Save user response
    print("\n📝 Test 2: Save user response")
    msg_id_2 = test_save_message(
        TEST_SESSION_ID,
        'user',
        "Hi Sarah, my name is John Smith",
        'opening',
        'greeting',
        1
    )
    
    # Test 3: Save bot response
    print("\n📝 Test 3: Save bot response")
    msg_id_3 = test_save_message(
        TEST_SESSION_ID,
        'assistant',
        "Nice to meet you, John! I'd love to learn about your business. What industry are you in?",
        'opening',
        'business_inquiry',
        2
    )
    
    # Test 4: Retrieve chat history
    print("\n📚 Test 4: Retrieve chat history")
    history = test_get_history(TEST_SESSION_ID)
    
    if history:
        print("   Chat History:")
        for i, msg in enumerate(history):
            role = msg['role'].upper()
            content = msg['content'][:60] + "..." if len(msg['content']) > 60 else msg['content']
            print(f"   {i+1}. {role}: {content}")
    
    # Test 5: Verify message order
    print("\n🔢 Test 5: Verify message order")
    if len(history) == 3:
        orders = [msg['message_order'] for msg in history]
        if orders == [0, 1, 2]:
            print("✅ Message order is correct")
        else:
            print(f"❌ Message order is incorrect: {orders}")
    else:
        print(f"❌ Expected 3 messages, got {len(history)}")
    
    # Test 6: Reset session
    print("\n🗑️ Test 6: Reset session")
    reset_success = test_reset_session(TEST_SESSION_ID)
    
    # Test 7: Verify history is cleared
    print("\n🧹 Test 7: Verify history is cleared after reset")
    history_after_reset = test_get_history(TEST_SESSION_ID)
    
    if len(history_after_reset) == 0:
        print("✅ History successfully cleared after reset")
    else:
        print(f"❌ History not cleared: {len(history_after_reset)} messages remain")
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    tests_passed = 0
    total_tests = 7
    
    if msg_id_1: tests_passed += 1
    if msg_id_2: tests_passed += 1
    if msg_id_3: tests_passed += 1
    if len(history) == 3: tests_passed += 1
    if len(history) == 3 and [msg['message_order'] for msg in history] == [0, 1, 2]: tests_passed += 1
    if reset_success: tests_passed += 1
    if len(history_after_reset) == 0: tests_passed += 1
    
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Chat persistence is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return tests_passed == total_tests

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(f"{BASE_URL}/")
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("🔍 Checking server status...")
    if not check_server_status():
        print(f"❌ Server is not running at {BASE_URL}")
        print("Please start the unified sales system first:")
        print("   python unified_sales_system.py")
        exit(1)
    
    print(f"✅ Server is running at {BASE_URL}")
    print()
    
    success = run_tests()
    exit(0 if success else 1)
