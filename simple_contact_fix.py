#!/usr/bin/env python3
"""
Simple Contact Addition Fix
===========================
Create a simplified contact addition route that works reliably
"""

def create_simple_contact_route():
    """Create a simplified, working contact addition route"""
    
    route_code = '''
@app.route('/contacts/add', methods=['GET', 'POST'])
def add_contact():
    """Add new contact - SIMPLIFIED VERSION"""
    if request.method == 'POST':
        try:
            # Get basic required fields
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            email = request.form.get('email', '').strip().lower()
            
            # Basic validation
            if not first_name:
                flash('First name is required.', 'error')
                return render_template('add_contact.html')
            
            if not last_name:
                flash('Last name is required.', 'error')
                return render_template('add_contact.html')
            
            if not email:
                flash('Email is required.', 'error')
                return render_template('add_contact.html')
            
            # Check for existing contact
            existing_contact = Contact.query.filter_by(email=email).first()
            if existing_contact:
                flash('A contact with this email already exists.', 'error')
                return render_template('add_contact.html')
            
            # Create contact with minimal required fields
            contact = Contact(
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=request.form.get('phone', '').strip() or None,
                company=request.form.get('company', '').strip() or None,
                job_title=request.form.get('job_title', '').strip() or None,
                source=request.form.get('source', 'manual_entry'),
                status='new',
                is_active=True,
                do_not_email=False
            )
            
            # Add and commit
            db.session.add(contact)
            db.session.commit()
            
            flash(f'Contact "{contact.full_name}" added successfully!', 'success')
            return redirect(url_for('contacts_list'))
            
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Add contact error: {str(e)}")
            flash('Error adding contact. Please try again.', 'error')
    
    return render_template('add_contact.html')
'''
    
    return route_code

def create_manual_contact_script():
    """Create a script to manually add the test contact"""
    
    script_code = '''#!/usr/bin/env python3
"""
Manual Contact Addition
======================
Manually add Alex Scof contact to the database
"""

import os
from datetime import datetime

# Set up environment
os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
os.environ['MAIL_PORT'] = '587'
os.environ['MAIL_USE_TLS'] = 'true'
os.environ['MAIL_USERNAME'] = '<EMAIL>'
os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

try:
    from unified_sales_system import app, db, Contact
    
    with app.app_context():
        # Check if contact already exists
        existing = Contact.query.filter_by(email='<EMAIL>').first()
        
        if existing:
            print(f"Contact already exists: {existing.full_name}")
            print("Updating existing contact...")
            existing.first_name = 'Alex'
            existing.last_name = 'Scof'
            existing.company = 'Test Company'
            existing.job_title = 'CEO'
            existing.phone = '(*************'
            existing.is_active = True
            existing.do_not_email = False
            db.session.commit()
            print("✅ Contact updated successfully!")
        else:
            print("Creating new contact...")
            contact = Contact(
                first_name='Alex',
                last_name='Scof',
                email='<EMAIL>',
                phone='(*************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new',
                is_active=True,
                do_not_email=False,
                created_at=datetime.utcnow()
            )
            
            db.session.add(contact)
            db.session.commit()
            print("✅ Contact created successfully!")
            print(f"   ID: {contact.id}")
            print(f"   Name: {contact.full_name}")
            print(f"   Email: {contact.email}")
        
        # Verify contact exists
        test_contact = Contact.query.filter_by(email='<EMAIL>').first()
        if test_contact:
            print("\\n✅ Contact verification successful!")
            print(f"   Contact ID: {test_contact.id}")
            print(f"   Full Name: {test_contact.full_name}")
            print(f"   Email: {test_contact.email}")
            print(f"   Company: {test_contact.company}")
            print(f"   Active: {test_contact.is_active}")
            print(f"   Do Not Email: {test_contact.do_not_email}")
        else:
            print("❌ Contact verification failed!")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
'''
    
    return script_code

def main():
    """Main function to provide fixes"""
    print("🔧 SIMPLE CONTACT ADDITION FIX")
    print("=" * 60)
    
    print("📝 Option 1: Simplified Contact Route")
    print("-" * 40)
    print("Replace the add_contact route in unified_sales_system.py with:")
    print()
    print(create_simple_contact_route())
    
    print("\n📝 Option 2: Manual Contact Addition Script")
    print("-" * 40)
    
    # Save the manual script
    manual_script = create_manual_contact_script()
    
    try:
        with open('add_contact_manually.py', 'w') as f:
            f.write(manual_script)
        print("✅ Created add_contact_manually.py")
        print("   Run with: python add_contact_manually.py")
    except Exception as e:
        print(f"❌ Failed to create script: {e}")
    
    print("\n📋 RECOMMENDED SOLUTION")
    print("=" * 60)
    print("Since the web form is having issues, use the manual approach:")
    print()
    print("1. Run the manual contact addition script:")
    print("   python add_contact_manually.py")
    print()
    print("2. This will add Alex Scof as a contact directly to the database")
    print()
    print("3. Then you can proceed with campaign testing:")
    print("   • Go to http://localhost:5000/campaigns")
    print("   • Create a new campaign")
    print("   • Select 'All contacts' as recipients")
    print("   • Send the <NAME_EMAIL>")
    print()
    print("4. The email system is already working (confirmed by SMTP test)")
    print("   so the campaign should send successfully!")
    
    return True

if __name__ == "__main__":
    main()
