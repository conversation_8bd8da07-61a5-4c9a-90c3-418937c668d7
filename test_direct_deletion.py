#!/usr/bin/env python3
"""
Direct Database Deletion Test
============================
Test deletion functionality directly on the database to identify issues.
"""

import sqlite3
import os

def test_direct_deletion():
    """Test deletion directly on the database"""
    
    print("🧪 Testing Direct Database Deletion")
    print("=" * 50)
    
    db_path = 'unified_sales.db'
    if not os.path.exists(db_path):
        print("❌ Database not found")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 1. Show current contacts
        print("\n1. Current contacts:")
        cursor.execute('SELECT id, first_name, last_name, email FROM contacts ORDER BY id')
        contacts = cursor.fetchall()
        
        if contacts:
            for contact in contacts:
                print(f"  ID: {contact[0]}, Name: {contact[1]} {contact[2]}, Email: {contact[3]}")
        else:
            print("  No contacts found")
            return True
        
        # 2. Test deletion of contact ID 1
        contact_id_to_delete = 1
        print(f"\n2. Testing deletion of contact ID {contact_id_to_delete}:")
        
        # Check related data first
        related_tables = [
            ('chatbot_sessions', 'contact_id'),
            ('activities', 'contact_id'),
            ('email_logs', 'contact_id'),
            ('email_failures', 'contact_id'),
            ('contact_group_memberships', 'contact_id')
        ]
        
        for table, column in related_tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE {column} = ?', (contact_id_to_delete,))
                count = cursor.fetchone()[0]
                print(f"  Related {table}: {count} records")
            except Exception as e:
                print(f"  Related {table}: Error - {str(e)}")
        
        # 3. Try to delete related data first
        print(f"\n3. Deleting related data for contact {contact_id_to_delete}:")
        
        # Delete chat messages for sessions related to this contact
        try:
            cursor.execute('''
                DELETE FROM chat_messages 
                WHERE session_id IN (
                    SELECT session_id FROM chatbot_sessions WHERE contact_id = ?
                )
            ''', (contact_id_to_delete,))
            deleted_messages = cursor.rowcount
            print(f"  Deleted {deleted_messages} chat messages")
        except Exception as e:
            print(f"  Error deleting chat messages: {str(e)}")
        
        # Delete chatbot sessions
        try:
            cursor.execute('DELETE FROM chatbot_sessions WHERE contact_id = ?', (contact_id_to_delete,))
            deleted_sessions = cursor.rowcount
            print(f"  Deleted {deleted_sessions} chatbot sessions")
        except Exception as e:
            print(f"  Error deleting chatbot sessions: {str(e)}")
        
        # Delete activities
        try:
            cursor.execute('DELETE FROM activities WHERE contact_id = ?', (contact_id_to_delete,))
            deleted_activities = cursor.rowcount
            print(f"  Deleted {deleted_activities} activities")
        except Exception as e:
            print(f"  Error deleting activities: {str(e)}")
        
        # Delete email logs
        try:
            cursor.execute('DELETE FROM email_logs WHERE contact_id = ?', (contact_id_to_delete,))
            deleted_logs = cursor.rowcount
            print(f"  Deleted {deleted_logs} email logs")
        except Exception as e:
            print(f"  Error deleting email logs: {str(e)}")
        
        # Delete email failures
        try:
            cursor.execute('DELETE FROM email_failures WHERE contact_id = ?', (contact_id_to_delete,))
            deleted_failures = cursor.rowcount
            print(f"  Deleted {deleted_failures} email failures")
        except Exception as e:
            print(f"  Error deleting email failures: {str(e)}")
        
        # Delete group memberships
        try:
            cursor.execute('DELETE FROM contact_group_memberships WHERE contact_id = ?', (contact_id_to_delete,))
            deleted_memberships = cursor.rowcount
            print(f"  Deleted {deleted_memberships} group memberships")
        except Exception as e:
            print(f"  Error deleting group memberships: {str(e)}")
        
        # 4. Delete the contact itself
        print(f"\n4. Deleting contact {contact_id_to_delete}:")
        try:
            cursor.execute('DELETE FROM contacts WHERE id = ?', (contact_id_to_delete,))
            deleted_contact = cursor.rowcount
            print(f"  Deleted {deleted_contact} contact")
        except Exception as e:
            print(f"  Error deleting contact: {str(e)}")
        
        # 5. Commit changes
        print(f"\n5. Committing changes:")
        conn.commit()
        print("  ✅ Changes committed successfully")
        
        # 6. Verify deletion
        print(f"\n6. Verifying deletion:")
        cursor.execute('SELECT id, first_name, last_name, email FROM contacts ORDER BY id')
        remaining_contacts = cursor.fetchall()
        
        if remaining_contacts:
            print("  Remaining contacts:")
            for contact in remaining_contacts:
                print(f"    ID: {contact[0]}, Name: {contact[1]} {contact[2]}, Email: {contact[3]}")
        else:
            print("  ✅ All contacts deleted!")
        
        # Check if the specific contact was deleted
        cursor.execute('SELECT COUNT(*) FROM contacts WHERE id = ?', (contact_id_to_delete,))
        contact_exists = cursor.fetchone()[0]
        
        if contact_exists == 0:
            print(f"  ✅ Contact {contact_id_to_delete} successfully deleted!")
            return True
        else:
            print(f"  ❌ Contact {contact_id_to_delete} still exists!")
            return False
        
    except Exception as e:
        print(f"❌ Error during deletion test: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = test_direct_deletion()
    if success:
        print("\n🎉 Direct deletion test SUCCESSFUL!")
    else:
        print("\n❌ Direct deletion test FAILED!")
