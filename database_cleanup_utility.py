#!/usr/bin/env python3
"""
Database Cleanup Utility
========================
Comprehensive database cleanup and deletion utilities for the sales department system.
Provides safe, permanent deletion of data with proper foreign key handling.
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseCleanupUtility:
    """Comprehensive database cleanup utility with permanent deletion capabilities"""
    
    def __init__(self, db_path: str = 'sales_department.db'):
        """Initialize the cleanup utility with database path"""
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """Connect to the database"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.execute("PRAGMA foreign_keys = ON")
            logger.info(f"Connected to database: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from the database"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Disconnected from database")
    
    def execute_with_foreign_keys_disabled(self, queries: List[str]) -> Dict[str, int]:
        """Execute queries with foreign keys temporarily disabled"""
        if not self.connection:
            raise Exception("Not connected to database")
        
        cursor = self.connection.cursor()
        deleted_counts = {}
        
        try:
            # Disable foreign key constraints
            cursor.execute("PRAGMA foreign_keys = OFF")
            logger.info("Foreign key constraints disabled")
            
            # Execute all queries
            for query in queries:
                try:
                    result = cursor.execute(query)
                    count = result.rowcount
                    table_name = self._extract_table_name(query)
                    if count > 0:
                        deleted_counts[table_name] = count
                        logger.info(f"Deleted {count} records from {table_name}")
                except Exception as e:
                    logger.error(f"Error executing query '{query}': {str(e)}")
                    continue
            
            # Commit all changes
            self.connection.commit()
            logger.info("All deletion queries committed successfully")
            
        except Exception as e:
            logger.error(f"Error during bulk deletion: {str(e)}")
            self.connection.rollback()
            raise
        finally:
            # Re-enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys = ON")
            logger.info("Foreign key constraints re-enabled")
        
        return deleted_counts
    
    def _extract_table_name(self, query: str) -> str:
        """Extract table name from DELETE query"""
        try:
            # Simple extraction for DELETE FROM table_name
            parts = query.upper().split()
            if 'DELETE' in parts and 'FROM' in parts:
                from_index = parts.index('FROM')
                if from_index + 1 < len(parts):
                    return parts[from_index + 1].lower()
        except:
            pass
        return "unknown_table"
    
    def delete_contact_completely(self, contact_id: int) -> Dict[str, int]:
        """Permanently delete a contact and all related data"""
        logger.info(f"Starting complete deletion for contact ID: {contact_id}")
        
        deletion_queries = [
            # Delete chat messages for sessions related to this contact
            f"""DELETE FROM chat_messages 
                WHERE session_id IN (
                    SELECT session_id FROM chatbot_sessions WHERE contact_id = {contact_id}
                )""",
            
            # Delete chatbot sessions
            f"DELETE FROM chatbot_sessions WHERE contact_id = {contact_id}",
            
            # Delete activities
            f"DELETE FROM activities WHERE contact_id = {contact_id}",
            
            # Delete email logs
            f"DELETE FROM email_logs WHERE contact_id = {contact_id}",
            
            # Delete email failures
            f"DELETE FROM email_failures WHERE contact_id = {contact_id}",
            
            # Delete group memberships
            f"DELETE FROM contact_group_memberships WHERE contact_id = {contact_id}",
            
            # Delete opportunities
            f"DELETE FROM opportunities WHERE contact_id = {contact_id}",
            
            # Delete sales stages
            f"DELETE FROM sales_stages WHERE contact_id = {contact_id}",
            
            # Delete the contact itself
            f"DELETE FROM contacts WHERE id = {contact_id}"
        ]
        
        return self.execute_with_foreign_keys_disabled(deletion_queries)
    
    def delete_campaign_completely(self, campaign_id: int) -> Dict[str, int]:
        """Permanently delete a campaign and all related data"""
        logger.info(f"Starting complete deletion for campaign ID: {campaign_id}")
        
        deletion_queries = [
            # Delete chat messages for sessions related to this campaign
            f"""DELETE FROM chat_messages 
                WHERE session_id IN (
                    SELECT session_id FROM chatbot_sessions WHERE email_campaign_id = {campaign_id}
                )""",
            
            # Delete chatbot sessions
            f"DELETE FROM chatbot_sessions WHERE email_campaign_id = {campaign_id}",
            
            # Delete activities
            f"DELETE FROM activities WHERE session_id = '{campaign_id}'",
            
            # Delete email logs
            f"DELETE FROM email_logs WHERE campaign_id = {campaign_id}",
            
            # Delete email failures
            f"DELETE FROM email_failures WHERE campaign_id = {campaign_id}",
            
            # Delete campaign group associations
            f"DELETE FROM campaign_groups WHERE campaign_id = {campaign_id}",
            
            # Delete the campaign itself
            f"DELETE FROM email_campaigns WHERE id = {campaign_id}"
        ]
        
        return self.execute_with_foreign_keys_disabled(deletion_queries)
    
    def delete_group_completely(self, group_id: int) -> Dict[str, int]:
        """Permanently delete a group and all related data"""
        logger.info(f"Starting complete deletion for group ID: {group_id}")
        
        deletion_queries = [
            # Delete contact group memberships
            f"DELETE FROM contact_group_memberships WHERE group_id = {group_id}",
            
            # Delete campaign group associations
            f"DELETE FROM campaign_groups WHERE group_id = {group_id}",
            
            # Delete the group itself
            f"DELETE FROM contact_groups WHERE id = {group_id}"
        ]
        
        return self.execute_with_foreign_keys_disabled(deletion_queries)
    
    def delete_all_data(self) -> Dict[str, int]:
        """Nuclear option: Delete ALL data from the system"""
        logger.warning("STARTING COMPLETE SYSTEM DATA DELETION - ALL DATA WILL BE PERMANENTLY LOST")
        
        deletion_queries = [
            # Delete in order to avoid foreign key issues
            "DELETE FROM chat_messages",
            "DELETE FROM chatbot_sessions", 
            "DELETE FROM activities",
            "DELETE FROM email_logs",
            "DELETE FROM email_failures",
            "DELETE FROM contact_group_memberships",
            "DELETE FROM campaign_groups",
            "DELETE FROM opportunities",
            "DELETE FROM sales_stages",
            "DELETE FROM contacts",
            "DELETE FROM contact_groups",
            "DELETE FROM email_campaigns",
            "DELETE FROM sqlite_sequence"  # Reset auto-increment
        ]
        
        result = self.execute_with_foreign_keys_disabled(deletion_queries)
        logger.warning(f"COMPLETE SYSTEM RESET COMPLETED: {result}")
        return result
    
    def get_database_statistics(self) -> Dict[str, int]:
        """Get current database statistics"""
        if not self.connection:
            raise Exception("Not connected to database")
        
        cursor = self.connection.cursor()
        stats = {}
        
        tables = [
            'contacts', 'email_campaigns', 'contact_groups', 'chatbot_sessions',
            'chat_messages', 'activities', 'email_logs', 'email_failures',
            'contact_group_memberships', 'campaign_groups', 'opportunities', 'sales_stages'
        ]
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                stats[table] = count
            except Exception as e:
                logger.warning(f"Could not get count for table {table}: {str(e)}")
                stats[table] = 0
        
        return stats
    
    def vacuum_database(self) -> bool:
        """Vacuum the database to reclaim space after deletions"""
        if not self.connection:
            raise Exception("Not connected to database")
        
        try:
            logger.info("Starting database vacuum operation...")
            self.connection.execute("VACUUM")
            logger.info("Database vacuum completed successfully")
            return True
        except Exception as e:
            logger.error(f"Database vacuum failed: {str(e)}")
            return False
    
    def verify_referential_integrity(self) -> List[str]:
        """Verify database referential integrity"""
        if not self.connection:
            raise Exception("Not connected to database")
        
        cursor = self.connection.cursor()
        issues = []
        
        try:
            cursor.execute("PRAGMA foreign_key_check")
            violations = cursor.fetchall()
            
            if violations:
                for violation in violations:
                    issues.append(f"Foreign key violation: {violation}")
            else:
                logger.info("No foreign key violations found")
                
        except Exception as e:
            issues.append(f"Error checking referential integrity: {str(e)}")
        
        return issues

# Convenience functions for direct use
def cleanup_contact(contact_id: int, db_path: str = 'sales_department.db') -> Dict[str, int]:
    """Convenience function to delete a contact completely"""
    utility = DatabaseCleanupUtility(db_path)
    try:
        if utility.connect():
            return utility.delete_contact_completely(contact_id)
        else:
            return {}
    finally:
        utility.disconnect()

def cleanup_campaign(campaign_id: int, db_path: str = 'sales_department.db') -> Dict[str, int]:
    """Convenience function to delete a campaign completely"""
    utility = DatabaseCleanupUtility(db_path)
    try:
        if utility.connect():
            return utility.delete_campaign_completely(campaign_id)
        else:
            return {}
    finally:
        utility.disconnect()

def cleanup_group(group_id: int, db_path: str = 'sales_department.db') -> Dict[str, int]:
    """Convenience function to delete a group completely"""
    utility = DatabaseCleanupUtility(db_path)
    try:
        if utility.connect():
            return utility.delete_group_completely(group_id)
        else:
            return {}
    finally:
        utility.disconnect()

def nuclear_cleanup(db_path: str = 'sales_department.db') -> Dict[str, int]:
    """Convenience function to delete ALL data"""
    utility = DatabaseCleanupUtility(db_path)
    try:
        if utility.connect():
            return utility.delete_all_data()
        else:
            return {}
    finally:
        utility.disconnect()

if __name__ == "__main__":
    # Example usage
    utility = DatabaseCleanupUtility()
    
    if utility.connect():
        try:
            # Show current statistics
            print("Current database statistics:")
            stats = utility.get_database_statistics()
            for table, count in stats.items():
                print(f"  {table}: {count} records")
            
            # Check integrity
            print("\nChecking referential integrity...")
            issues = utility.verify_referential_integrity()
            if issues:
                print("Issues found:")
                for issue in issues:
                    print(f"  - {issue}")
            else:
                print("  No issues found")
                
        finally:
            utility.disconnect()
