#!/usr/bin/env python3
"""
Test Campaign Email Sending
===========================
Comprehensive test script to create a campaign and verify that emails are being sent properly.
Uses the user's SMTP configuration: mail.24seven.site with SSL on port 465.
"""

import sys
import os
import time
import smtplib
import ssl
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_smtp_connection():
    """Test SMTP connection independently"""
    print("🔌 Testing SMTP Connection...")
    print("-" * 40)

    # User's SMTP configuration
    smtp_config = {
        'server': 'mail.24seven.site',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'use_ssl': True
    }

    try:
        if smtp_config['use_ssl']:
            # Create SSL context
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(smtp_config['server'], smtp_config['port'], context=context)
        else:
            server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            server.starttls()

        # Login
        server.login(smtp_config['username'], smtp_config['password'])
        print("✅ SMTP connection successful!")
        print(f"   Server: {smtp_config['server']}:{smtp_config['port']}")
        print(f"   Username: {smtp_config['username']}")
        print(f"   SSL: {smtp_config['use_ssl']}")

        server.quit()
        return True

    except Exception as e:
        print(f"❌ SMTP connection failed: {str(e)}")
        return False

def send_test_email():
    """Send a simple test email to verify SMTP works"""
    print("\n📧 Sending Test Email...")
    print("-" * 40)

    smtp_config = {
        'server': 'mail.24seven.site',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'use_ssl': True
    }

    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = 'Test Email from 24Seven Assistants Sales System'
        msg['From'] = f"24Seven Assistants Sales Team <{smtp_config['username']}>"
        msg['To'] = smtp_config['username']  # Send to self for testing

        # Create HTML and text versions
        text_body = """
        Hello!

        This is a test email from the 24Seven Assistants Sales System.
        If you receive this email, the SMTP configuration is working correctly.

        Test sent at: {timestamp}

        Best regards,
        24Seven Assistants Sales Team
        """.format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        html_body = """
        <html>
        <body>
            <h2>Test Email from 24Seven Assistants Sales System</h2>
            <p>Hello!</p>
            <p>This is a test email from the 24Seven Assistants Sales System.</p>
            <p>If you receive this email, the SMTP configuration is working correctly.</p>
            <p><strong>Test sent at:</strong> {timestamp}</p>
            <br>
            <p>Best regards,<br>
            24Seven Assistants Sales Team</p>
        </body>
        </html>
        """.format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

        # Attach parts
        text_part = MIMEText(text_body, 'plain', 'utf-8')
        html_part = MIMEText(html_body, 'html', 'utf-8')
        msg.attach(text_part)
        msg.attach(html_part)

        # Send email
        if smtp_config['use_ssl']:
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(smtp_config['server'], smtp_config['port'], context=context)
        else:
            server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            server.starttls()

        server.login(smtp_config['username'], smtp_config['password'])
        server.send_message(msg)
        server.quit()

        print("✅ Test email sent successfully!")
        print(f"   To: {smtp_config['username']}")
        print(f"   Subject: {msg['Subject']}")
        return True

    except Exception as e:
        print(f"❌ Failed to send test email: {str(e)}")
        return False

def test_campaign_system():
    """Test the campaign system using the Flask application"""
    print("\n🚀 Testing Campaign System...")
    print("-" * 40)

    try:
        # Import Flask app and models
        from unified_sales_system import app, db, EmailCampaign, EmailLog, Contact
        from email_system import create_email_system, get_email_config

        with app.app_context():
            print("✅ Flask app context created")

            # Initialize enhanced email system
            email_system = create_email_system(db_session=db.session)
            campaign_manager = email_system['campaign_manager']
            smtp_service = email_system['smtp_service']
            template_manager = email_system['template_manager']

            print("✅ Services initialized")

            # Create test contact if it doesn't exist
            test_email = "<EMAIL>"  # Send to ourselves for testing
            test_contact = Contact.query.filter_by(email=test_email).first()

            if not test_contact:
                test_contact = Contact(
                    first_name='Test',
                    last_name='Contact',
                    email=test_email,
                    company='24Seven Assistants',
                    job_title='Test Contact',
                    source='test_campaign',
                    status='new'
                )
                db.session.add(test_contact)
                db.session.commit()
                print("✅ Test contact created")
            else:
                print("✅ Test contact already exists")

            # Create test campaign
            campaign_name = f"Test Campaign {datetime.now().strftime('%Y%m%d_%H%M%S')}"

            campaign = campaign_manager.create_campaign(
                name=campaign_name,
                template_name='introduction',
                target_filters={'status': 'new'},
                created_by='test_system'
            )

            print(f"✅ Campaign created: {campaign.name} (ID: {campaign.id})")

            # Prepare campaign
            success, message = campaign_manager.prepare_campaign(campaign.id)
            if success:
                print(f"✅ Campaign prepared: {message}")
            else:
                print(f"❌ Failed to prepare campaign: {message}")
                return False

            # Send campaign
            print("📤 Sending campaign...")
            success, message = campaign_manager.send_campaign(campaign.id, batch_size=5)

            if success:
                print(f"✅ Campaign sent: {message}")

                # Check email logs
                email_logs = EmailLog.query.filter_by(campaign_id=campaign.id).all()
                print(f"📊 Email logs created: {len(email_logs)}")

                for log in email_logs:
                    print(f"   📧 {log.recipient_email}: {log.status}")

                return True
            else:
                print(f"❌ Failed to send campaign: {message}")
                return False

    except Exception as e:
        print(f"❌ Campaign system test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 24Seven Assistants - Email Campaign Testing")
    print("=" * 60)
    print("Testing SMTP configuration and campaign sending...")
    print()

    # Test 1: SMTP Connection
    smtp_success = test_smtp_connection()

    # Test 2: Simple Email Send
    if smtp_success:
        email_success = send_test_email()
    else:
        email_success = False
        print("⏭️ Skipping email test due to SMTP connection failure")

    # Test 3: Campaign System
    if smtp_success:
        campaign_success = test_campaign_system()
    else:
        campaign_success = False
        print("⏭️ Skipping campaign test due to SMTP connection failure")

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"SMTP Connection: {'✅ PASS' if smtp_success else '❌ FAIL'}")
    print(f"Simple Email:    {'✅ PASS' if email_success else '❌ FAIL'}")
    print(f"Campaign System: {'✅ PASS' if campaign_success else '❌ FAIL'}")

    if all([smtp_success, email_success, campaign_success]):
        print("\n🎉 All tests passed! Email system is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")

    print("\n💡 Check your email inbox for test messages.")

if __name__ == "__main__":
    main()
