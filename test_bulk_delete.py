#!/usr/bin/env python3
"""
Test Bulk Delete Functionality
==============================
Direct test of the bulk delete functionality to identify issues.
"""

import requests
import json

def test_bulk_delete():
    """Test the bulk delete functionality directly"""
    
    print("🧪 Testing Bulk Delete Functionality")
    print("=" * 50)
    
    # Test 1: Check if contacts exist
    print("\n1. Checking existing contacts...")
    try:
        response = requests.get('http://localhost:5000/contacts')
        if response.status_code == 200:
            print("✅ Contacts page accessible")
        else:
            print(f"❌ Contacts page error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing contacts page: {str(e)}")
        return False
    
    # Test 2: Create test contacts
    print("\n2. Creating test contacts...")
    try:
        response = requests.get('http://localhost:5000/admin/create-test-contacts')
        if response.status_code == 200:
            print("✅ Test contacts created")
        else:
            print(f"❌ Error creating test contacts: {response.status_code}")
    except Exception as e:
        print(f"❌ Error creating test contacts: {str(e)}")
    
    # Test 3: Test bulk delete with form data
    print("\n3. Testing bulk delete with form data...")
    try:
        # Simulate form submission
        form_data = {
            'contact_ids': ['1', '2'],  # Test with contact IDs 1 and 2
            'force_delete': 'false'
        }
        
        print(f"   Sending form data: {form_data}")
        
        response = requests.post(
            'http://localhost:5000/contacts/bulk-delete',
            data=form_data,
            allow_redirects=False  # Don't follow redirects to see the response
        )
        
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code in [302, 303]:  # Redirect expected
            print("✅ Bulk delete request processed (redirect received)")
            location = response.headers.get('Location', '')
            print(f"   Redirect location: {location}")
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"   Response text: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Error testing bulk delete: {str(e)}")
    
    # Test 4: Test debug endpoint
    print("\n4. Testing debug endpoint...")
    try:
        # Test GET first
        response = requests.get('http://localhost:5000/debug/test-bulk-delete')
        if response.status_code == 200:
            print("✅ Debug endpoint accessible")
            
            # Test POST
            form_data = {
                'contact_ids': ['1', '2'],
                'force_delete': 'true'
            }
            
            response = requests.post(
                'http://localhost:5000/debug/test-bulk-delete',
                data=form_data,
                allow_redirects=False
            )
            
            print(f"   Debug POST response: {response.status_code}")
            
        else:
            print(f"❌ Debug endpoint error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing debug endpoint: {str(e)}")
    
    # Test 5: Check application logs
    print("\n5. Application should be logging detailed information.")
    print("   Check the terminal where you're running unified_sales_system.py")
    print("   Look for log messages starting with '=== BULK DELETE CONTACTS STARTED ==='")
    
    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("1. If you see detailed logs in the application terminal, the function is being called")
    print("2. If no logs appear, there might be a routing or form submission issue")
    print("3. Check the browser developer tools for JavaScript errors")
    print("4. Try the debug endpoint manually in the browser")
    
    return True

if __name__ == "__main__":
    test_bulk_delete()
