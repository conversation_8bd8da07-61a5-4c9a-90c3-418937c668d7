#!/usr/bin/env python3
"""
Manual Contact Addition
======================
Manually add Alex Scof contact to the database
"""

import os
from datetime import datetime

# Set up environment
os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
os.environ['MAIL_PORT'] = '587'
os.environ['MAIL_USE_TLS'] = 'true'
os.environ['MAIL_USERNAME'] = '<EMAIL>'
os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

try:
    print("🔧 Manual Contact Addition")
    print("=" * 40)
    
    from unified_sales_system import app, db, Contact
    
    with app.app_context():
        print("✅ Application context created")
        
        # Check if contact already exists
        existing = Contact.query.filter_by(email='<EMAIL>').first()
        
        if existing:
            print(f"📝 Contact already exists: {existing.full_name}")
            print("   Updating existing contact...")
            existing.first_name = 'Alex'
            existing.last_name = 'Scof'
            existing.company = 'Test Company'
            existing.job_title = 'CEO'
            existing.phone = '(*************'
            existing.is_active = True
            existing.do_not_email = False
            existing.updated_at = datetime.utcnow()
            db.session.commit()
            print("✅ Contact updated successfully!")
        else:
            print("📝 Creating new contact...")
            contact = Contact(
                first_name='Alex',
                last_name='Scof',
                email='<EMAIL>',
                phone='(*************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new',
                is_active=True,
                do_not_email=False,
                created_at=datetime.utcnow()
            )
            
            db.session.add(contact)
            db.session.commit()
            print("✅ Contact created successfully!")
            print(f"   ID: {contact.id}")
            print(f"   Name: {contact.full_name}")
            print(f"   Email: {contact.email}")
        
        # Verify contact exists
        test_contact = Contact.query.filter_by(email='<EMAIL>').first()
        if test_contact:
            print("\n✅ Contact verification successful!")
            print(f"   Contact ID: {test_contact.id}")
            print(f"   Full Name: {test_contact.full_name}")
            print(f"   Email: {test_contact.email}")
            print(f"   Company: {test_contact.company}")
            print(f"   Job Title: {test_contact.job_title}")
            print(f"   Phone: {test_contact.phone}")
            print(f"   Active: {test_contact.is_active}")
            print(f"   Do Not Email: {test_contact.do_not_email}")
            
            print("\n🎉 CONTACT READY FOR CAMPAIGNS!")
            print("=" * 40)
            print("Next steps:")
            print("1. Go to: http://localhost:5000/campaigns")
            print("2. Create a new campaign")
            print("3. Select 'All contacts' as recipients")
            print("4. Send the campaign")
            print("5. Check <EMAIL> for the email")
        else:
            print("❌ Contact verification failed!")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
