#!/usr/bin/env python3
"""
Fix Contact Addition Issue
==========================
Diagnose and fix the contact addition problem
"""

import os
import sys
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USE_SSL'] = 'false'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'
    os.environ['SECRET_KEY'] = 'your-secret-key-here'
    os.environ['DATABASE_URL'] = 'sqlite:///sales_system.db'

def test_contact_creation():
    """Test contact creation directly"""
    try:
        print("🧪 Testing Contact Creation...")
        print("-" * 40)
        
        # Set up environment
        setup_environment()
        
        # Import the application
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            print("✅ Application context created")
            
            # Check if contacts table exists
            try:
                contact_count = Contact.query.count()
                print(f"✅ Contacts table exists with {contact_count} contacts")
            except Exception as e:
                print(f"❌ Contacts table issue: {e}")
                return False
            
            # Test creating a contact directly
            print("\n📝 Creating test contact...")
            
            test_contact = Contact(
                first_name='Alex',
                last_name='Scof',
                email='<EMAIL>',
                phone='************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new',
                is_active=True,
                do_not_email=False,
                created_at=datetime.utcnow()
            )
            
            try:
                # Check if contact already exists
                existing_contact = Contact.query.filter_by(email='<EMAIL>').first()
                if existing_contact:
                    print(f"⚠️ Contact already exists: {existing_contact.full_name}")
                    print("   Updating existing contact...")
                    existing_contact.first_name = 'Alex'
                    existing_contact.last_name = 'Scof'
                    existing_contact.phone = '************'
                    existing_contact.company = 'Test Company'
                    existing_contact.job_title = 'CEO'
                    existing_contact.is_active = True
                    existing_contact.do_not_email = False
                    db.session.commit()
                    print("✅ Existing contact updated successfully")
                    return True
                else:
                    db.session.add(test_contact)
                    db.session.commit()
                    print("✅ Test contact created successfully")
                    print(f"   ID: {test_contact.id}")
                    print(f"   Name: {test_contact.full_name}")
                    print(f"   Email: {test_contact.email}")
                    return True
                    
            except Exception as e:
                db.session.rollback()
                print(f"❌ Contact creation failed: {e}")
                
                # Check specific error types
                if "UNIQUE constraint failed" in str(e):
                    print("   Error: Email already exists")
                elif "NOT NULL constraint failed" in str(e):
                    print("   Error: Missing required field")
                else:
                    print(f"   Error: {str(e)}")
                
                return False
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_schema():
    """Check database schema for contacts table"""
    try:
        print("\n🔍 Checking Database Schema...")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db
        
        with app.app_context():
            # Get database connection
            connection = db.engine.connect()
            
            # Check if contacts table exists
            result = connection.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='contacts'")
            table_exists = result.fetchone() is not None
            
            if table_exists:
                print("✅ Contacts table exists")
                
                # Get table schema
                result = connection.execute("PRAGMA table_info(contacts)")
                columns = result.fetchall()
                
                print("\n📋 Table Schema:")
                required_fields = ['first_name', 'last_name', 'email']
                missing_fields = []
                
                for column in columns:
                    column_name = column[1]
                    column_type = column[2]
                    not_null = column[3]
                    print(f"   {column_name}: {column_type} {'(NOT NULL)' if not_null else ''}")
                
                # Check for required fields
                existing_fields = [col[1] for col in columns]
                for field in required_fields:
                    if field not in existing_fields:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ Missing required fields: {missing_fields}")
                    return False
                else:
                    print("✅ All required fields present")
                    return True
                    
            else:
                print("❌ Contacts table does not exist")
                return False
                
            connection.close()
            
    except Exception as e:
        print(f"❌ Schema check failed: {e}")
        return False

def fix_contact_route():
    """Create a fixed contact addition route"""
    try:
        print("\n🔧 Creating Fixed Contact Route...")
        print("-" * 40)
        
        fixed_route_code = '''
@app.route('/contacts/add', methods=['GET', 'POST'])
def add_contact():
    """Add new contact with comprehensive validation"""
    if request.method == 'POST':
        try:
            # Get and validate form data
            first_name = request.form.get('first_name', '').strip()
            last_name = request.form.get('last_name', '').strip()
            email = request.form.get('email', '').strip().lower()
            
            # Validate required fields
            if not first_name:
                flash('First name is required.', 'error')
                return render_template('add_contact.html')
            
            if not last_name:
                flash('Last name is required.', 'error')
                return render_template('add_contact.html')
            
            if not email:
                flash('Email is required.', 'error')
                return render_template('add_contact.html')
            
            # Validate email format
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                flash('Please enter a valid email address.', 'error')
                return render_template('add_contact.html')
            
            # Check if email already exists
            existing_contact = Contact.query.filter_by(email=email).first()
            if existing_contact:
                flash('A contact with this email already exists.', 'error')
                return render_template('add_contact.html')
            
            # Create new contact
            contact = Contact(
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=request.form.get('phone', '').strip() or None,
                company=request.form.get('company', '').strip() or None,
                job_title=request.form.get('job_title', '').strip() or None,
                source=request.form.get('source', 'manual_entry'),
                status='new',
                is_active=True,
                do_not_email=False,
                created_at=datetime.utcnow()
            )
            
            db.session.add(contact)
            db.session.commit()
            
            flash(f'Contact "{contact.full_name}" added successfully!', 'success')
            return redirect(url_for('contacts_list'))
            
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Add contact error: {str(e)}")
            flash('Error adding contact. Please try again.', 'error')
    
    return render_template('add_contact.html')
'''
        
        print("✅ Fixed contact route code generated")
        print("\nTo apply this fix:")
        print("1. Replace the add_contact route in unified_sales_system.py")
        print("2. Restart the application")
        print("3. Try adding a contact again")
        
        return True
        
    except Exception as e:
        print(f"❌ Route fix failed: {e}")
        return False

def test_web_form_submission():
    """Test web form submission"""
    try:
        print("\n🌐 Testing Web Form Submission...")
        print("-" * 40)
        
        import requests
        
        # Test form data
        form_data = {
            'first_name': 'Alex',
            'last_name': 'Scof',
            'email': '<EMAIL>',
            'phone': '************',
            'company': 'Test Company',
            'job_title': 'CEO',
            'source': 'manual_entry'
        }
        
        try:
            response = requests.post('http://localhost:5000/contacts/add', data=form_data, timeout=10)
            
            if response.status_code == 200:
                if 'successfully' in response.text.lower():
                    print("✅ Web form submission successful")
                    return True
                else:
                    print("⚠️ Form submitted but may have validation errors")
                    return False
            else:
                print(f"❌ Web form submission failed: Status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to web application")
            print("   Make sure the application is running on http://localhost:5000")
            return False
            
    except Exception as e:
        print(f"❌ Web form test failed: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔧 CONTACT ADDITION DIAGNOSTIC & FIX")
    print("=" * 60)
    
    # Test 1: Database schema
    schema_ok = check_database_schema()
    
    # Test 2: Direct contact creation
    if schema_ok:
        creation_ok = test_contact_creation()
    else:
        creation_ok = False
    
    # Test 3: Web form submission
    if creation_ok:
        web_ok = test_web_form_submission()
    else:
        web_ok = False
    
    # Test 4: Generate fix
    fix_generated = fix_contact_route()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    print(f"Database Schema: {'✅ OK' if schema_ok else '❌ ISSUES'}")
    print(f"Direct Creation: {'✅ OK' if creation_ok else '❌ FAILED'}")
    print(f"Web Form Test: {'✅ OK' if web_ok else '❌ FAILED'}")
    print(f"Fix Generated: {'✅ YES' if fix_generated else '❌ NO'}")
    
    if all([schema_ok, creation_ok]):
        print("\n🎉 CONTACT SYSTEM IS WORKING!")
        print("\n📋 To add the test contact:")
        print("1. Go to: http://localhost:5000/contacts/add")
        print("2. Fill in the form:")
        print("   - First Name: Alex")
        print("   - Last Name: Scof")
        print("   - Email: <EMAIL>")
        print("   - Company: Test Company")
        print("   - Job Title: CEO")
        print("3. Click 'Add Contact'")
        print("4. The contact should be added successfully")
    else:
        print("\n❌ ISSUES FOUND")
        if not schema_ok:
            print("• Database schema issues detected")
        if not creation_ok:
            print("• Contact creation failed")
        if not web_ok:
            print("• Web form submission issues")
        
        print("\n🔧 Recommended fixes:")
        print("1. Check database initialization")
        print("2. Verify all required fields are present")
        print("3. Check for unique constraint violations")
        print("4. Review application logs for detailed errors")
    
    return all([schema_ok, creation_ok])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
