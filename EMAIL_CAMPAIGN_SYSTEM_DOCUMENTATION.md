# Email Campaign System Documentation

## Overview

The 24Seven Assistants email campaign system is a comprehensive solution for managing and sending bulk email campaigns with advanced tracking, personalization, and automation capabilities. The system is designed for sales teams to efficiently reach prospects with personalized email sequences.

## System Architecture

### Core Components

1. **Campaign Manager** (`email_system/campaign_manager.py`)
   - Central orchestrator for email campaigns
   - Handles campaign creation, preparation, and execution
   - Manages contact targeting and filtering

2. **SMTP Service** (`email_system/smtp_service.py` & `email_system/enhanced_smtp_service.py`)
   - Email delivery engine with SMTP/IMAP integration
   - Bulk email sending with rate limiting
   - Automatic saving to sent folder

3. **Email Templates** (`email_system/email_templates.py`)
   - Template management and rendering system
   - Jinja2-based personalization
   - Pre-built templates for different campaign types

4. **Email Tracking** (`email_system/email_tracker.py`)
   - Open and click tracking
   - Campaign performance analytics
   - Contact engagement monitoring

5. **Database Models** (`models/email_campaign.py`)
   - EmailCampaign: Campaign metadata and settings
   - EmailLog: Individual email tracking records

## Configuration

### Environment Variables

```bash
# SMTP Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>

# IMAP Configuration (for sent folder)
IMAP_SERVER=mail.24seven.site
IMAP_PORT=993
IMAP_USE_SSL=true
IMAP_SENT_FOLDER=INBOX.Sent

# Campaign Settings
EMAIL_BATCH_SIZE=50
EMAIL_DELAY_SECONDS=2
```

### Configuration Object

```python
config = {
    'MAIL_SERVER': 'mail.24seven.site',
    'MAIL_PORT': 465,
    'MAIL_USE_SSL': True,
    'MAIL_USERNAME': '<EMAIL>',
    'MAIL_PASSWORD': 'M@kerere1',
    'MAIL_DEFAULT_SENDER': '<EMAIL>',
    'IMAP_SERVER': 'mail.24seven.site',
    'IMAP_PORT': 993,
    'IMAP_USE_SSL': True,
    'IMAP_SENT_FOLDER': 'INBOX.Sent',
    'SAVE_TO_SENT_FOLDER': True,
    'EMAIL_DELAY_SECONDS': 2
}
```

## Email Campaign Workflow

### 1. Campaign Creation

```python
from email_system.campaign_manager import CampaignManager
from email_system.smtp_service import SMTPService
from email_system.email_templates import EmailTemplateManager

# Initialize services
smtp_service = SMTPService(config)
template_manager = EmailTemplateManager()
campaign_manager = CampaignManager(db_session, smtp_service, template_manager)

# Create campaign
campaign = campaign_manager.create_campaign(
    name="Q1 2024 Introduction Campaign",
    template_name="introduction",
    target_filters={
        'industry': 'technology',
        'min_lead_score': 50
    },
    scheduled_at=datetime(2024, 1, 15, 9, 0),
    created_by="sales_manager"
)
```

### 2. Campaign Preparation

```python
# Prepare campaign (creates EmailLog records for each target contact)
success, message = campaign_manager.prepare_campaign(campaign.id)
if success:
    print(f"Campaign prepared: {message}")
else:
    print(f"Preparation failed: {message}")
```

### 3. Campaign Execution

```python
# Send campaign in batches
success, message = campaign_manager.send_campaign(
    campaign_id=campaign.id,
    batch_size=50
)
```

## Email Templates

### Template Structure

Templates use Jinja2 syntax for personalization:

```python
template = {
    'name': 'Introduction Email',
    'subject': 'Transform Your Business with {{ company_name }}',
    'html_template': '''
    <html>
    <body>
        <h1>Hello {{ contact_name }}!</h1>
        <p>I'm {{ agent_name }} from 24Seven Assistants...</p>
        <p>We can help {{ company_name }} in {{ industry }}...</p>
    </body>
    </html>
    ''',
    'text_template': '''
    Hello {{ contact_name }}!
    
    I'm {{ agent_name }} from 24Seven Assistants...
    '''
}
```

### Available Variables

- `contact_name`: Contact's first name or "there"
- `company_name`: Contact's company name
- `agent_name`: Sales agent name (default: "Sarah")
- `reply_email`: Reply-to email address
- `phone_number`: Contact phone number
- `industry`: Contact's industry

### Pre-built Templates

1. **Introduction Template** (`introduction`)
   - Initial outreach to new prospects
   - Highlights 24Seven Assistants services
   - Professional design with call-to-action

2. **Follow-up Template** (`followup`)
   - Follow-up for non-responders
   - Shorter, more direct messaging
   - Emphasizes value proposition

## Contact Targeting

### Filter Options

```python
target_filters = {
    'industry': 'technology',           # Filter by industry
    'min_lead_score': 50,              # Minimum lead score
    'company_size': 'medium',          # Company size category
    'location': 'United States',       # Geographic filter
    'tags': ['qualified', 'warm']      # Contact tags
}
```

### Contact Selection Process

1. Query contacts based on filters
2. Exclude contacts with recent email activity
3. Validate email addresses
4. Create personalized EmailLog records

## Email Tracking & Analytics

### Tracking Capabilities

1. **Email Delivery Status**
   - Sent, delivered, bounced, failed
   - SMTP response codes and errors

2. **Engagement Tracking**
   - Email opens (pixel tracking)
   - Link clicks (redirect tracking)
   - Reply detection

3. **Campaign Analytics**
   - Total recipients, sent, delivered
   - Open rates, click rates, reply rates
   - Bounce rates, unsubscribe rates

### Tracking Implementation

```python
from email_system.email_tracker import EmailTracker

tracker = EmailTracker(db_session)

# Track email open
tracker.track_email_open(
    message_id="<email-message-id>",
    user_agent="Mozilla/5.0...",
    ip_address="***********"
)

# Track email click
tracker.track_email_click(
    message_id="<email-message-id>",
    clicked_url="https://24sevenassistants.com/contact"
)
```

## Database Schema

### EmailCampaign Model

```python
class EmailCampaign(db.Model):
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=False)
    template_name = Column(String(100), nullable=False)
    
    # Content
    email_body_html = Column(Text)
    email_body_text = Column(Text)
    
    # Settings
    sender_name = Column(String(100))
    sender_email = Column(String(255))
    reply_to_email = Column(String(255))
    
    # Targeting
    contact_filters = Column(JSON)
    
    # Scheduling
    scheduled_at = Column(DateTime)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Status
    status = Column(String(50))  # draft, ready, running, completed, paused
    
    # Statistics
    total_recipients = Column(Integer, default=0)
    emails_sent = Column(Integer, default=0)
    emails_delivered = Column(Integer, default=0)
    emails_opened = Column(Integer, default=0)
    emails_clicked = Column(Integer, default=0)
    emails_replied = Column(Integer, default=0)
    emails_bounced = Column(Integer, default=0)
    
    # Configuration
    send_delay_seconds = Column(Integer, default=2)
    max_emails_per_hour = Column(Integer, default=100)
```

### EmailLog Model

```python
class EmailLog(db.Model):
    id = Column(Integer, primary_key=True)
    campaign_id = Column(Integer, ForeignKey('email_campaigns.id'))
    contact_id = Column(Integer, ForeignKey('contacts.id'))
    
    # Email Details
    recipient_email = Column(String(255))
    recipient_name = Column(String(255))
    subject = Column(String(255))
    email_body_html = Column(Text)
    email_body_text = Column(Text)
    
    # Status Tracking
    status = Column(String(50))  # pending, sent, delivered, bounced, failed
    sent_at = Column(DateTime)
    delivered_at = Column(DateTime)
    opened_at = Column(DateTime)
    first_clicked_at = Column(DateTime)
    bounced_at = Column(DateTime)
    
    # Tracking Data
    open_count = Column(Integer, default=0)
    click_count = Column(Integer, default=0)
    user_agent = Column(String(500))
    ip_address = Column(String(45))
    
    # Message IDs
    message_id = Column(String(255))
    thread_id = Column(String(255))
```

## Advanced Features

### Rate Limiting

- Configurable delay between emails (default: 2 seconds)
- Maximum emails per hour limits
- Batch processing to prevent server overload

### Error Handling

- Automatic retry for failed sends
- Detailed error logging and reporting
- Graceful handling of SMTP errors

### Personalization

- Dynamic content based on contact data
- Industry-specific messaging
- Custom variable substitution

### Integration Points

- Contact management system integration
- CRM activity logging
- Analytics dashboard integration
- Webhook support for external systems

## Usage Examples

### Complete Campaign Setup

```python
# 1. Initialize services
smtp_service = SMTPService(config)
template_manager = EmailTemplateManager()
campaign_manager = CampaignManager(db_session, smtp_service, template_manager)

# 2. Create campaign
campaign = campaign_manager.create_campaign(
    name="Technology Sector Outreach",
    template_name="introduction",
    target_filters={'industry': 'technology'},
    created_by="sales_team"
)

# 3. Prepare and send
success, msg = campaign_manager.prepare_campaign(campaign.id)
if success:
    success, msg = campaign_manager.send_campaign(campaign.id, batch_size=25)
    print(f"Campaign status: {msg}")
```

### Custom Template Creation

```python
template_manager = EmailTemplateManager()
template_manager.add_custom_template(
    name="holiday_greeting",
    display_name="Holiday Greeting",
    subject="Season's Greetings from {{ company_name }}",
    html_template="<h1>Happy Holidays {{ contact_name }}!</h1>",
    text_template="Happy Holidays {{ contact_name }}!"
)
```

### Bulk Email Sending

```python
smtp_service = SMTPService(config)

emails = [
    {
        'to_email': '<EMAIL>',
        'subject': 'Introduction to 24Seven Assistants',
        'html_body': '<h1>Hello!</h1>',
        'text_body': 'Hello!',
        'from_name': '24Seven Sales Team',
        'from_email': '<EMAIL>'
    }
    # ... more emails
]

results = smtp_service.send_bulk_emails(emails, delay_between_emails=2)
for result in results:
    print(f"Email to {result['to_email']}: {'Success' if result['success'] else 'Failed'}")
```

## Best Practices

1. **Template Design**
   - Use responsive HTML templates
   - Include both HTML and text versions
   - Test templates across email clients

2. **Contact Management**
   - Maintain clean contact lists
   - Respect unsubscribe requests
   - Segment contacts for targeted campaigns

3. **Delivery Optimization**
   - Use appropriate sending rates
   - Monitor bounce rates and reputation
   - Implement proper authentication (SPF, DKIM, DMARC)

4. **Compliance**
   - Include unsubscribe links
   - Honor opt-out requests
   - Follow CAN-SPAM and GDPR guidelines

5. **Performance Monitoring**
   - Track key metrics (open, click, reply rates)
   - A/B test subject lines and content
   - Monitor deliverability scores

## Troubleshooting

### Common Issues

1. **SMTP Authentication Errors**
   - Verify credentials and server settings
   - Check for 2FA requirements
   - Ensure proper SSL/TLS configuration

2. **High Bounce Rates**
   - Validate email addresses before sending
   - Clean contact lists regularly
   - Check domain reputation

3. **Low Open Rates**
   - Improve subject lines
   - Check sender reputation
   - Verify email content quality

4. **Template Rendering Issues**
   - Validate Jinja2 syntax
   - Check variable availability
   - Test with sample data

### Debugging Tools

- Enable detailed SMTP logging
- Use email testing services
- Monitor campaign analytics dashboard
- Check server logs for errors

This documentation provides a comprehensive guide for replicating and extending the email campaign system. The modular architecture allows for easy customization and integration with existing sales workflows.
