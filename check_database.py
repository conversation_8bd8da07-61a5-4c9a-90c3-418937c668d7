#!/usr/bin/env python3
"""
Check Database Structure
"""

import sqlite3
import os

def check_database():
    """Check what tables exist in the database"""
    db_path = 'sales_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print(f"🔍 Checking database: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Tables found: {tables}")
        
        # Check if chatbot_sessions table exists
        if 'chatbot_sessions' in tables:
            print("\n✅ chatbot_sessions table exists")
            cursor.execute("PRAGMA table_info(chatbot_sessions)")
            columns = cursor.fetchall()
            print("📋 Columns:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            print("\n❌ chatbot_sessions table does not exist")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
