#!/usr/bin/env python3
"""
Debug Specific Campaign Issue
"""

import requests
import sqlite3
import os

def check_campaign_in_database(campaign_id):
    """Check if campaign exists in database"""
    print(f"🗄️ Checking Campaign {campaign_id} in Database")
    print("=" * 40)
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if campaign exists
        cursor.execute("SELECT * FROM email_campaigns WHERE id = ?", (campaign_id,))
        campaign = cursor.fetchone()
        
        if campaign:
            print(f"✅ Campaign {campaign_id} found in database")
            print(f"   Name: {campaign[1] if len(campaign) > 1 else 'Unknown'}")
            print(f"   Status: {campaign[4] if len(campaign) > 4 else 'Unknown'}")
        else:
            print(f"❌ Campaign {campaign_id} NOT found in database")
            
            # Show all campaigns
            cursor.execute("SELECT id, name, status FROM email_campaigns ORDER BY id")
            all_campaigns = cursor.fetchall()
            print(f"\n📋 All campaigns in database ({len(all_campaigns)}):")
            for camp in all_campaigns:
                print(f"   ID: {camp[0]}, Name: {camp[1]}, Status: {camp[2]}")
        
        # Check email logs for this campaign
        cursor.execute("SELECT COUNT(*) FROM email_logs WHERE campaign_id = ?", (campaign_id,))
        email_logs_count = cursor.fetchone()[0]
        print(f"   📧 Email logs: {email_logs_count}")
        
        # Check activities for this campaign
        cursor.execute("SELECT COUNT(*) FROM activities WHERE session_id = ?", (str(campaign_id),))
        activities_count = cursor.fetchone()[0]
        print(f"   📝 Activities: {activities_count}")
        
        conn.close()
        return campaign is not None
        
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        return False

def test_specific_campaigns():
    """Test specific campaign IDs"""
    print("🎯 Testing Specific Campaign IDs")
    print("=" * 35)
    
    # Get all campaign IDs from web interface
    try:
        response = requests.get("http://localhost:5000/campaigns")
        if response.status_code == 200:
            import re
            campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', response.text)
            print(f"📋 Found {len(campaign_ids)} campaigns in web interface: {campaign_ids}")
            
            # Test each campaign
            working_campaigns = []
            failing_campaigns = []
            
            for campaign_id in campaign_ids:
                print(f"\n🔍 Testing campaign {campaign_id}...")
                
                # Check in database first
                in_db = check_campaign_in_database(campaign_id)
                
                # Test view campaign
                try:
                    view_response = requests.get(f"http://localhost:5000/campaigns/{campaign_id}")
                    print(f"   Web Status: {view_response.status_code}")
                    
                    if view_response.status_code == 200:
                        if "Campaign Details" in view_response.text or "Campaign Information" in view_response.text:
                            print(f"   ✅ Campaign {campaign_id}: WORKING")
                            working_campaigns.append(campaign_id)
                        else:
                            print(f"   ⚠️ Campaign {campaign_id}: Page loads but wrong content")
                            failing_campaigns.append(campaign_id)
                    elif view_response.status_code == 302:
                        print(f"   ❌ Campaign {campaign_id}: REDIRECTING (error)")
                        failing_campaigns.append(campaign_id)
                    else:
                        print(f"   ❌ Campaign {campaign_id}: Status {view_response.status_code}")
                        failing_campaigns.append(campaign_id)
                        
                except Exception as e:
                    print(f"   ❌ Campaign {campaign_id}: Error - {str(e)}")
                    failing_campaigns.append(campaign_id)
            
            print(f"\n📊 SUMMARY:")
            print(f"   ✅ Working campaigns: {working_campaigns}")
            print(f"   ❌ Failing campaigns: {failing_campaigns}")
            
            return working_campaigns, failing_campaigns
            
    except Exception as e:
        print(f"❌ Error getting campaigns: {str(e)}")
        return [], []

def fix_failing_campaigns(failing_campaigns):
    """Try to fix failing campaigns"""
    print(f"\n🔧 Attempting to Fix Failing Campaigns")
    print("=" * 40)
    
    if not failing_campaigns:
        print("✅ No failing campaigns to fix!")
        return
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        for campaign_id in failing_campaigns:
            print(f"\n🔧 Fixing campaign {campaign_id}...")
            
            # Check if campaign exists
            cursor.execute("SELECT id, name FROM email_campaigns WHERE id = ?", (campaign_id,))
            campaign = cursor.fetchone()
            
            if campaign:
                print(f"   ✅ Campaign exists: {campaign[1]}")
                
                # Ensure basic fields are set
                cursor.execute("""
                    UPDATE email_campaigns 
                    SET 
                        total_recipients = COALESCE(total_recipients, 0),
                        emails_sent = COALESCE(emails_sent, 0),
                        emails_delivered = COALESCE(emails_delivered, 0),
                        emails_opened = COALESCE(emails_opened, 0),
                        chatbot_links_clicked = COALESCE(chatbot_links_clicked, 0),
                        conversions_achieved = COALESCE(conversions_achieved, 0)
                    WHERE id = ?
                """, (campaign_id,))
                
                print(f"   ✅ Updated campaign {campaign_id} fields")
            else:
                print(f"   ❌ Campaign {campaign_id} not found in database")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ Attempted to fix {len(failing_campaigns)} campaigns")
        print("🔄 Testing campaigns again...")
        
        # Test again
        for campaign_id in failing_campaigns:
            try:
                view_response = requests.get(f"http://localhost:5000/campaigns/{campaign_id}")
                if view_response.status_code == 200 and ("Campaign Details" in view_response.text or "Campaign Information" in view_response.text):
                    print(f"   ✅ Campaign {campaign_id}: NOW WORKING!")
                else:
                    print(f"   ❌ Campaign {campaign_id}: Still failing")
            except Exception as e:
                print(f"   ❌ Campaign {campaign_id}: Error - {str(e)}")
        
    except Exception as e:
        print(f"❌ Error fixing campaigns: {str(e)}")

if __name__ == "__main__":
    print("🔍 Campaign-Specific Debug Tool")
    print("=" * 50)
    
    # Test all campaigns
    working, failing = test_specific_campaigns()
    
    # Try to fix failing campaigns
    if failing:
        fix_failing_campaigns(failing)
    
    print("\n" + "=" * 50)
    print("📋 FINAL SUMMARY")
    print("=" * 50)
    
    if not failing:
        print("🎉 ALL CAMPAIGNS ARE WORKING!")
        print("💡 All view buttons should work in the web interface.")
    else:
        print(f"⚠️ Some campaigns may still have issues: {failing}")
        print("🔧 Possible solutions:")
        print("   • Restart the Flask application")
        print("   • Check Flask application logs")
        print("   • Create new campaigns (they should work)")
        print("   • Delete problematic old campaigns if not needed")
    
    print(f"\n✅ Working campaigns: {working}")
    print("💡 These campaigns' view buttons should work perfectly!")
