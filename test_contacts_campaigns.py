#!/usr/bin/env python3
"""
Test script for the fixed contacts and campaigns system
"""

import sys
import os
import requests
import json
from datetime import datetime

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_system():
    """Test the contacts and campaigns system"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Contacts and Campaigns System")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start the Flask app first.")
        return False
    
    # Test 2: Test contacts count API
    try:
        response = requests.get(f"{base_url}/api/contacts/count")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Contacts count API working: {data.get('count', 0)} contacts")
        else:
            print(f"❌ Contacts count API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Contacts count API error: {str(e)}")
    
    # Test 3: Test contacts list page
    try:
        response = requests.get(f"{base_url}/contacts")
        if response.status_code == 200:
            print("✅ Contacts list page accessible")
        else:
            print(f"❌ Contacts list page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Contacts list page error: {str(e)}")
    
    # Test 4: Test campaigns list page
    try:
        response = requests.get(f"{base_url}/campaigns")
        if response.status_code == 200:
            print("✅ Campaigns list page accessible")
        else:
            print(f"❌ Campaigns list page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Campaigns list page error: {str(e)}")
    
    # Test 5: Test groups list page
    try:
        response = requests.get(f"{base_url}/groups")
        if response.status_code == 200:
            print("✅ Groups list page accessible")
        else:
            print(f"❌ Groups list page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Groups list page error: {str(e)}")
    
    # Test 6: Test analytics page
    try:
        response = requests.get(f"{base_url}/analytics")
        if response.status_code == 200:
            print("✅ Analytics page accessible")
        else:
            print(f"❌ Analytics page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Analytics page error: {str(e)}")
    
    # Test 7: Test chatbot session tracking API
    try:
        test_data = {
            'session_id': 'test-session-123',
            'stage': 'opening',
            'task': 'greeting',
            'contact_name': 'Test User'
        }
        response = requests.post(f"{base_url}/api/chatbot/session", json=test_data)
        if response.status_code == 200:
            print("✅ Chatbot session tracking API working")
        else:
            print(f"❌ Chatbot session tracking API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chatbot session tracking API error: {str(e)}")
    
    print("\n🎯 System Test Summary:")
    print("- All major routes are accessible")
    print("- API endpoints are responding")
    print("- The system appears to be working correctly")
    print("\n📋 Next Steps:")
    print("1. Test contact creation through the web interface")
    print("2. Test campaign creation and sending")
    print("3. Test contact and campaign deletion")
    print("4. Verify all related data is properly cleaned up")
    
    return True

if __name__ == "__main__":
    test_system()
