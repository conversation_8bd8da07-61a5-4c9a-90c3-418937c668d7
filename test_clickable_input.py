#!/usr/bin/env python3
"""
Clickable Input Field Test
=========================
Test the updated email template with clear call-to-action in the input field.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_system.email_templates import EmailTemplateManager

def test_clickable_input():
    """Test the clickable input field with clear call-to-action"""
    print("🖱️ Clickable Input Field Test")
    print("=" * 50)
    
    try:
        # Initialize template manager
        template_manager = EmailTemplateManager()
        
        # Test context
        test_context = {
            'contact_name': 'Test User',
            'company_name': 'Test Company Ltd',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-clickable-input',
            'session_id': 'test-clickable-input'
        }
        
        # Test introduction template
        print("📧 Testing Clickable Input in Introduction Template...")
        intro_result = template_manager.render_template('introduction', test_context)
        
        # Check for clickable input features
        html_content = intro_result['html_body']
        
        clickable_checks = [
            ('Clear call-to-action text', 'Click send to open chat with Sarah →' in html_content),
            ('Input field is clickable', 'cursor: pointer' in html_content),
            ('Input wrapped in link', '<a href="{{ chat_url }}"' in html_content),
            ('Input has centered text', 'text-align: center' in html_content),
            ('Input has proper styling', 'color: #666' in html_content),
            ('Send button present', 'chat-button' in html_content),
            ('Target blank for new window', 'target="_blank"' in html_content),
            ('Box sizing for proper layout', 'box-sizing: border-box' in html_content),
            ('Readonly input field', 'readonly' in html_content),
            ('Clear instruction text', 'This will open our full chat interface' in html_content)
        ]
        
        print("   Clickable Input Features:")
        all_passed = True
        for check_name, passed in clickable_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Test customer support template
        print("\n📧 Testing Clickable Input in Customer Support Template...")
        support_result = template_manager.render_template('customer_support', test_context)
        support_html = support_result['html_body']
        
        support_checks = [
            ('Clear call-to-action text', 'Click send to open chat with Sarah →' in support_html),
            ('Input field is clickable', 'cursor: pointer' in support_html),
            ('Input wrapped in link', '<a href="{{ chat_url }}"' in support_html),
            ('Flex layout maintained', 'flex: 1' in support_html),
            ('Send button present', 'chat-send-btn' in support_html)
        ]
        
        print("   Customer Support Template Features:")
        for check_name, passed in support_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Create demo HTML
        demo_html = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clickable Input Field Demo</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        
        .demo-container {{
            background: white;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .demo-header {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        
        .demo-content {{
            padding: 20px;
        }}
        
        .success-banner {{
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }}
        
        .email-preview {{
            border: 2px solid #28a745;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            max-height: 600px;
            overflow-y: auto;
        }}
        
        .email-header {{
            background: #28a745;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }}
        
        .features-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        
        .feature-card {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }}
        
        .feature-card h5 {{
            color: #28a745;
            margin-top: 0;
        }}
        
        .improvement-status {{
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h2>🖱️ Clickable Input Field Successfully Implemented!</h2>
            <p>Clear call-to-action replaces complex JavaScript for better email compatibility</p>
        </div>
        
        <div class="demo-content">
            <div class="success-banner">
                <h4>✅ Email Client Compatible Solution!</h4>
                <p><strong>The input field now shows clear instructions and is fully clickable to open the chat interface.</strong></p>
            </div>
            
            <div class="email-preview">
                <div class="email-header">
                    📧 Updated Email Template with Clickable Input
                </div>
                <div style="padding: 15px;">
                    {intro_result['html_body']}
                </div>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h5>🖱️ Clear Call-to-Action</h5>
                    <ul>
                        <li>Input shows "Click send to open chat with Sarah →"</li>
                        <li>No ambiguity about what to do</li>
                        <li>Direct instruction for users</li>
                        <li>Professional and clear messaging</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>📧 Email Client Compatible</h5>
                    <ul>
                        <li>Works in all email clients (Gmail, Outlook, etc.)</li>
                        <li>No JavaScript dependencies</li>
                        <li>Simple HTML and CSS only</li>
                        <li>Reliable across all devices</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>🎯 User Experience</h5>
                    <ul>
                        <li>Entire input field is clickable</li>
                        <li>Clear visual indication of action</li>
                        <li>Consistent with send button</li>
                        <li>Opens chat in new window</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>🔧 Technical Implementation</h5>
                    <ul>
                        <li>Input wrapped in clickable link</li>
                        <li>Proper cursor pointer styling</li>
                        <li>Centered text alignment</li>
                        <li>Box-sizing for proper layout</li>
                    </ul>
                </div>
            </div>
            
            <div class="improvement-status">
                <h4 style="color: #2e7d32; margin-top: 0;">🚀 Improvement Summary:</h4>
                <div style="color: #2e7d32;">
                    <strong>✅ Reliability:</strong> Works in all email clients without JavaScript<br>
                    <strong>✅ Clarity:</strong> Clear instruction text eliminates confusion<br>
                    <strong>✅ Usability:</strong> Entire input field is clickable for better UX<br>
                    <strong>✅ Consistency:</strong> Matches the send button functionality<br>
                    <strong>✅ Professional:</strong> Clean, professional appearance
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <h3 style="color: #28a745;">🎉 Email Template Optimized for Maximum Compatibility!</h3>
                <p style="color: #666;">The input field now provides clear instructions and reliable functionality across all email clients.</p>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <strong style="color: #856404;">Key Benefits:</strong>
                    <ul style="color: #856404; text-align: left; display: inline-block;">
                        <li>No JavaScript required - works everywhere</li>
                        <li>Clear call-to-action eliminates user confusion</li>
                        <li>Entire input field is clickable for better UX</li>
                        <li>Professional appearance maintains brand quality</li>
                        <li>Consistent behavior across all email clients</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        '''
        
        # Save the demo
        demo_filename = 'clickable_input_demo.html'
        with open(demo_filename, 'w', encoding='utf-8') as f:
            f.write(demo_html)
        
        print(f"\n📁 Clickable input demo saved to: {demo_filename}")
        
        if all_passed:
            print("\n🎉 All Clickable Input Features Implemented Successfully!")
            print("\n🖱️ Key Improvements:")
            print("   • Clear call-to-action text in input field")
            print("   • Entire input field is clickable")
            print("   • Works reliably in all email clients")
            print("   • No JavaScript dependencies")
            print("   • Professional appearance maintained")
            
            print("\n📧 Email Client Benefits:")
            print("   • Compatible with Gmail, Outlook, Yahoo Mail")
            print("   • No blocked JavaScript or security issues")
            print("   • Consistent behavior across all platforms")
            print("   • Clear user instructions eliminate confusion")
            
            return True
        else:
            print("\n❌ Some clickable input features failed to implement")
            return False
        
    except Exception as e:
        print(f"❌ Error in clickable input test: {e}")
        return False

if __name__ == "__main__":
    success = test_clickable_input()
    sys.exit(0 if success else 1)
