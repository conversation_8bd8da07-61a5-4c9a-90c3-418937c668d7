"""
Stage Analytics
==============
Analytics for tracking sales process stages and conversions.
"""

from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple
from sqlalchemy import func, and_, or_
from models.opportunity import Opportunity
from models.sales_stage import SalesStage, SalesStageHistory
from models.contact import Contact
from models.analytics import SalesAnalytics

class StageAnalytics:
    """Analytics for sales stage tracking and conversion"""
    
    def __init__(self, db_session):
        """Initialize with database session"""
        self.db = db_session
    
    def get_stage_funnel_data(self, days: int = 30) -> Dict:
        """
        Get sales funnel data showing progression through stages
        
        Args:
            days: Number of days to look back
            
        Returns:
            Dictionary with stage funnel metrics
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get all active stages ordered by sequence
        stages = self.db.query(SalesStage).filter(
            SalesStage.is_active == True
        ).order_by(SalesStage.order).all()
        
        funnel_data = {
            'stages': [],
            'total_opportunities': 0,
            'conversion_rates': {},
            'stage_metrics': {}
        }
        
        previous_count = 0
        
        for stage in stages:
            # Count opportunities that entered this stage in the time period
            stage_entries = self.db.query(SalesStageHistory).filter(
                and_(
                    SalesStageHistory.stage_id == stage.id,
                    SalesStageHistory.entered_at >= start_date,
                    SalesStageHistory.entered_at <= end_date
                )
            ).count()
            
            # Count opportunities currently in this stage
            current_in_stage = self.db.query(Opportunity).filter(
                and_(
                    Opportunity.current_stage_id == stage.id,
                    Opportunity.status == 'open'
                )
            ).count()
            
            # Calculate conversion rate from previous stage
            conversion_rate = 0.0
            if previous_count > 0:
                conversion_rate = (stage_entries / previous_count) * 100
            
            stage_data = {
                'id': stage.id,
                'name': stage.name,
                'order': stage.order,
                'entries': stage_entries,
                'current_count': current_in_stage,
                'conversion_rate': round(conversion_rate, 2),
                'probability_percent': stage.probability_percent
            }
            
            funnel_data['stages'].append(stage_data)
            funnel_data['conversion_rates'][stage.name] = conversion_rate
            funnel_data['stage_metrics'][stage.name] = stage_data
            
            previous_count = stage_entries
            
            if stage.order == 1:  # First stage
                funnel_data['total_opportunities'] = stage_entries
        
        return funnel_data
    
    def get_stage_duration_analysis(self, days: int = 90) -> Dict:
        """
        Analyze time spent in each sales stage
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with stage duration metrics
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get completed stage histories (with exit dates)
        stage_histories = self.db.query(SalesStageHistory).join(SalesStage).filter(
            and_(
                SalesStageHistory.entered_at >= start_date,
                SalesStageHistory.exited_at.isnot(None),
                SalesStageHistory.duration_hours.isnot(None)
            )
        ).all()
        
        # Group by stage and calculate metrics
        stage_durations = {}
        
        for history in stage_histories:
            stage_name = history.stage.name
            duration_days = history.duration_hours / 24
            
            if stage_name not in stage_durations:
                stage_durations[stage_name] = {
                    'durations': [],
                    'stage_id': history.stage_id,
                    'stage_order': history.stage.order
                }
            
            stage_durations[stage_name]['durations'].append(duration_days)
        
        # Calculate statistics for each stage
        duration_analysis = {}
        
        for stage_name, data in stage_durations.items():
            durations = data['durations']
            
            if durations:
                duration_analysis[stage_name] = {
                    'stage_id': data['stage_id'],
                    'stage_order': data['stage_order'],
                    'count': len(durations),
                    'avg_days': round(sum(durations) / len(durations), 1),
                    'min_days': round(min(durations), 1),
                    'max_days': round(max(durations), 1),
                    'median_days': round(sorted(durations)[len(durations)//2], 1)
                }
        
        return duration_analysis
    
    def get_stage_conversion_matrix(self, days: int = 60) -> Dict:
        """
        Get conversion matrix showing movement between stages
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with stage-to-stage conversion data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get all stage transitions in the period
        transitions = self.db.query(SalesStageHistory).join(
            Opportunity, SalesStageHistory.opportunity_id == Opportunity.id
        ).filter(
            SalesStageHistory.entered_at >= start_date
        ).order_by(
            SalesStageHistory.opportunity_id,
            SalesStageHistory.entered_at
        ).all()
        
        # Group transitions by opportunity
        opportunity_transitions = {}
        for transition in transitions:
            opp_id = transition.opportunity_id
            if opp_id not in opportunity_transitions:
                opportunity_transitions[opp_id] = []
            opportunity_transitions[opp_id].append(transition)
        
        # Build conversion matrix
        conversion_matrix = {}
        stage_names = [stage.name for stage in self.db.query(SalesStage).order_by(SalesStage.order).all()]
        
        # Initialize matrix
        for from_stage in stage_names:
            conversion_matrix[from_stage] = {}
            for to_stage in stage_names:
                conversion_matrix[from_stage][to_stage] = 0
        
        # Count transitions
        for opp_id, opp_transitions in opportunity_transitions.items():
            for i in range(len(opp_transitions) - 1):
                current_stage = opp_transitions[i].stage.name
                next_stage = opp_transitions[i + 1].stage.name
                conversion_matrix[current_stage][next_stage] += 1
        
        return {
            'matrix': conversion_matrix,
            'stage_names': stage_names,
            'total_transitions': sum(sum(row.values()) for row in conversion_matrix.values())
        }
    
    def get_stage_performance_trends(self, days: int = 30) -> Dict:
        """
        Get performance trends for each stage over time
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with daily stage performance data
        """
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)
        
        # Get daily stage entries
        daily_data = {}
        current_date = start_date
        
        while current_date <= end_date:
            daily_data[current_date.isoformat()] = {}
            
            # Get stage entries for this day
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            stage_entries = self.db.query(
                SalesStage.name,
                func.count(SalesStageHistory.id).label('entries')
            ).join(
                SalesStageHistory, SalesStage.id == SalesStageHistory.stage_id
            ).filter(
                and_(
                    SalesStageHistory.entered_at >= day_start,
                    SalesStageHistory.entered_at <= day_end
                )
            ).group_by(SalesStage.name).all()
            
            for stage_name, entries in stage_entries:
                daily_data[current_date.isoformat()][stage_name] = entries
            
            current_date += timedelta(days=1)
        
        return daily_data
    
    def get_stage_bottlenecks(self, threshold_days: int = 14) -> List[Dict]:
        """
        Identify stage bottlenecks where opportunities are stuck
        
        Args:
            threshold_days: Days threshold to consider as bottleneck
            
        Returns:
            List of bottleneck information
        """
        # Get opportunities currently in stages longer than threshold
        threshold_date = datetime.utcnow() - timedelta(days=threshold_days)
        
        bottlenecks = self.db.query(
            Opportunity,
            SalesStage,
            SalesStageHistory
        ).join(
            SalesStage, Opportunity.current_stage_id == SalesStage.id
        ).join(
            SalesStageHistory, and_(
                SalesStageHistory.opportunity_id == Opportunity.id,
                SalesStageHistory.stage_id == SalesStage.id,
                SalesStageHistory.exited_at.is_(None)
            )
        ).filter(
            and_(
                Opportunity.status == 'open',
                SalesStageHistory.entered_at <= threshold_date
            )
        ).all()
        
        bottleneck_data = []
        
        for opportunity, stage, history in bottlenecks:
            days_in_stage = (datetime.utcnow() - history.entered_at).days
            
            bottleneck_data.append({
                'opportunity_id': opportunity.id,
                'opportunity_name': opportunity.name,
                'contact_name': opportunity.contact.full_name if opportunity.contact else 'Unknown',
                'contact_email': opportunity.contact.email if opportunity.contact else 'Unknown',
                'stage_name': stage.name,
                'days_in_stage': days_in_stage,
                'estimated_value': opportunity.estimated_value,
                'probability_percent': opportunity.probability_percent,
                'last_activity': opportunity.last_activity_at.isoformat() if opportunity.last_activity_at else None
            })
        
        # Sort by days in stage (descending)
        bottleneck_data.sort(key=lambda x: x['days_in_stage'], reverse=True)
        
        return bottleneck_data
    
    def calculate_stage_metrics_summary(self, days: int = 30) -> Dict:
        """
        Calculate comprehensive stage metrics summary
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with comprehensive stage metrics
        """
        funnel_data = self.get_stage_funnel_data(days)
        duration_analysis = self.get_stage_duration_analysis(days)
        bottlenecks = self.get_stage_bottlenecks()
        
        return {
            'funnel': funnel_data,
            'durations': duration_analysis,
            'bottlenecks': {
                'count': len(bottlenecks),
                'opportunities': bottlenecks[:10]  # Top 10 bottlenecks
            },
            'summary': {
                'total_opportunities': funnel_data['total_opportunities'],
                'total_stages': len(funnel_data['stages']),
                'avg_conversion_rate': round(
                    sum(funnel_data['conversion_rates'].values()) / len(funnel_data['conversion_rates'])
                    if funnel_data['conversion_rates'] else 0, 2
                ),
                'bottleneck_count': len(bottlenecks)
            }
        }
