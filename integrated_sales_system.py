"""
24Seven Assistants - Integrated Sales System
===========================================
Complete sales department system with BOTH:
1. Sales Department Dashboard (Flask)
2. Sales Chatbot (Gradio)
Running together on different ports.
"""

import os
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
import plotly.graph_objects as go
import plotly
import json

# Import the original salesbot
import gradio as gr
from salesbot.bot import SalesBot
from salesbot.config_data import CONFIG_DATA

print("🚀 24Seven Assistants - Integrated Sales System")
print("=" * 60)

# ---------------------------------------------------------------------------
# FLASK SALES DEPARTMENT SYSTEM
# ---------------------------------------------------------------------------

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config.update({
    'SECRET_KEY': 'dev-secret-key-change-in-production',
    'SQLALCHEMY_DATABASE_URI': 'sqlite:///sales_department.db',
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'MAIL_SERVER': 'smtp.gmail.com',
    'MAIL_PORT': 587,
    'MAIL_USE_TLS': True,
    'MAIL_USERNAME': '<EMAIL>',
    'MAIL_PASSWORD': 'your-email-password',
    'MAIL_DEFAULT_SENDER': '<EMAIL>'
})

# Initialize extensions
db = SQLAlchemy(app)

# Models (simplified for integration)
class Contact(db.Model):
    __tablename__ = 'contacts'
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    company = db.Column(db.String(200))
    job_title = db.Column(db.String(150))
    source = db.Column(db.String(100))
    status = db.Column(db.String(50), default='new')
    lead_score = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    do_not_email = db.Column(db.Boolean, default=False)
    is_customer = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Chatbot integration fields
    bot_session_id = db.Column(db.String(100), nullable=True)
    last_bot_interaction = db.Column(db.DateTime, nullable=True)
    bot_stage = db.Column(db.String(50), nullable=True)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

class SalesStage(db.Model):
    __tablename__ = 'sales_stages'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    order = db.Column(db.Integer, nullable=False)
    probability_percent = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Opportunity(db.Model):
    __tablename__ = 'opportunities'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'))
    current_stage_id = db.Column(db.Integer, db.ForeignKey('sales_stages.id'))
    estimated_value = db.Column(db.Float, default=0.0)
    probability_percent = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(50), default='open')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Chatbot integration
    bot_session_id = db.Column(db.String(100), nullable=True)

    contact = db.relationship('Contact', backref='opportunities')
    current_stage = db.relationship('SalesStage')

    @property
    def days_in_current_stage(self):
        if self.created_at:
            return (datetime.utcnow() - self.created_at).days
        return 0

class EmailCampaign(db.Model):
    __tablename__ = 'email_campaigns'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    template_name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(50), default='draft')
    total_recipients = db.Column(db.Integer, default=0)
    emails_sent = db.Column(db.Integer, default=0)
    emails_delivered = db.Column(db.Integer, default=0)
    emails_opened = db.Column(db.Integer, default=0)
    emails_clicked = db.Column(db.Integer, default=0)
    emails_replied = db.Column(db.Integer, default=0)
    emails_bounced = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)

    @property
    def open_rate(self):
        if self.emails_delivered > 0:
            return (self.emails_opened / self.emails_delivered) * 100
        return 0.0

    @property
    def click_rate(self):
        if self.emails_delivered > 0:
            return (self.emails_clicked / self.emails_delivered) * 100
        return 0.0

    @property
    def reply_rate(self):
        if self.emails_delivered > 0:
            return (self.emails_replied / self.emails_delivered) * 100
        return 0.0

class Activity(db.Model):
    __tablename__ = 'activities'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'))
    activity_type = db.Column(db.String(50), nullable=False)
    subject = db.Column(db.String(255))
    description = db.Column(db.Text)
    status = db.Column(db.String(50), default='completed')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Chatbot integration
    bot_session_id = db.Column(db.String(100), nullable=True)
    ai_generated = db.Column(db.Boolean, default=False)

    contact = db.relationship('Contact')

# Flask Routes
@app.route('/')
def index():
    """Main dashboard page"""
    try:
        total_contacts = Contact.query.count()
        total_opportunities = Opportunity.query.count()
        active_campaigns = EmailCampaign.query.filter_by(status='running').count()
        recent_activities = Activity.query.order_by(Activity.created_at.desc()).limit(10).all()

        # Count bot interactions
        bot_interactions = Contact.query.filter(Contact.bot_session_id.isnot(None)).count()

        return render_template('integrated_dashboard.html',
                             total_contacts=total_contacts,
                             total_opportunities=total_opportunities,
                             active_campaigns=active_campaigns,
                             recent_activities=recent_activities,
                             bot_interactions=bot_interactions)
    except Exception as e:
        app.logger.error(f"Dashboard error: {str(e)}")
        return render_template('integrated_dashboard.html',
                             total_contacts=0,
                             total_opportunities=0,
                             active_campaigns=0,
                             recent_activities=[],
                             bot_interactions=0)

@app.route('/analytics')
def analytics_dashboard():
    """Analytics dashboard with charts"""
    try:
        total_opportunities = Opportunity.query.count()
        total_contacts = Contact.query.count()
        total_campaigns = EmailCampaign.query.count()

        # Create demo funnel chart
        stages = ['Opening', 'Trust', 'Discovery', 'Demonstration', 'Close']
        values = [max(1, total_opportunities), max(1, total_opportunities//2),
                 max(1, total_opportunities//3), max(1, total_opportunities//4),
                 max(1, total_opportunities//5)]

        funnel_fig = go.Figure(go.Funnel(
            y=stages,
            x=values,
            textinfo="value+percent initial"
        ))
        funnel_fig.update_layout(title='Sales Funnel - Last 30 Days')

        charts = {
            'funnel': json.dumps(funnel_fig, cls=plotly.utils.PlotlyJSONEncoder)
        }

        summary = {
            'performance_metrics': {
                'total_opportunities': total_opportunities,
                'new_opportunities': max(0, total_opportunities - 5),
                'pipeline_value': total_opportunities * 5000,
                'weighted_pipeline_value': total_opportunities * 2500,
                'opp_to_customer_rate': 25.0 if total_opportunities > 0 else 0
            },
            'email_metrics': {
                'total_campaigns': total_campaigns,
                'avg_open_rate': 24.5,
                'campaigns': list(EmailCampaign.query.all())
            },
            'stage_metrics': {
                'funnel': {
                    'stages': [
                        {'name': stage, 'entries': val, 'current_count': val//2,
                         'conversion_rate': 100.0 if i == 0 else (values[i]/values[i-1]*100 if i > 0 else 0),
                         'probability_percent': (i+1)*20, 'order': i+1}
                        for i, (stage, val) in enumerate(zip(stages, values))
                    ]
                },
                'bottlenecks': {'count': 0, 'opportunities': []}
            }
        }

        return render_template('analytics.html', charts=charts, summary=summary)

    except Exception as e:
        app.logger.error(f"Analytics dashboard error: {str(e)}")
        return render_template('analytics.html', charts={}, summary={})

@app.route('/contacts')
def contacts_list():
    """List all contacts"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        contacts = Contact.query.order_by(Contact.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('contacts.html', contacts=contacts)

    except Exception as e:
        app.logger.error(f"Contacts list error: {str(e)}")
        return render_template('contacts.html', contacts=None)

@app.route('/contacts/add', methods=['GET', 'POST'])
def add_contact():
    """Add new contact"""
    if request.method == 'POST':
        try:
            contact = Contact(
                first_name=request.form.get('first_name'),
                last_name=request.form.get('last_name'),
                email=request.form.get('email'),
                phone=request.form.get('phone'),
                company=request.form.get('company'),
                job_title=request.form.get('job_title'),
                source=request.form.get('source', 'manual_entry'),
                status='new'
            )

            db.session.add(contact)
            db.session.commit()

            flash('Contact added successfully!', 'success')
            return redirect(url_for('contacts_list'))

        except Exception as e:
            app.logger.error(f"Add contact error: {str(e)}")
            flash('Error adding contact. Please try again.', 'error')

    return render_template('add_contact.html')

@app.route('/chatbot')
def chatbot_redirect():
    """Redirect to chatbot interface"""
    return redirect(url_for('chat_page', session_id='new'))

@app.route('/api/bot-interaction', methods=['POST'])
def log_bot_interaction():
    """Log chatbot interaction to database"""
    try:
        data = request.json

        # Find or create contact
        contact = Contact.query.filter_by(email=data.get('email')).first()
        if not contact and data.get('name') and data.get('email'):
            contact = Contact(
                first_name=data.get('name').split()[0],
                last_name=' '.join(data.get('name').split()[1:]) if len(data.get('name').split()) > 1 else '',
                email=data.get('email'),
                source='chatbot',
                status='contacted',
                bot_session_id=data.get('session_id'),
                last_bot_interaction=datetime.utcnow(),
                bot_stage=data.get('stage')
            )
            db.session.add(contact)
        elif contact:
            contact.bot_session_id = data.get('session_id')
            contact.last_bot_interaction = datetime.utcnow()
            contact.bot_stage = data.get('stage')
            if contact.status == 'new':
                contact.status = 'contacted'

        # Log activity
        if contact:
            activity = Activity(
                contact_id=contact.id,
                activity_type='chatbot',
                subject=f"Chatbot conversation - {data.get('stage', 'Unknown')} stage",
                description=data.get('message', ''),
                bot_session_id=data.get('session_id'),
                ai_generated=True
            )
            db.session.add(activity)

        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        app.logger.error(f"Bot interaction logging error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def init_database():
    """Initialize database with default data"""
    try:
        db.create_all()

        # Create default sales stages if they don't exist
        if not SalesStage.query.first():
            stages = [
                {'name': 'Opening', 'order': 1, 'probability_percent': 10.0},
                {'name': 'Trust', 'order': 2, 'probability_percent': 25.0},
                {'name': 'Discovery', 'order': 3, 'probability_percent': 50.0},
                {'name': 'Demonstration', 'order': 4, 'probability_percent': 75.0},
                {'name': 'Close', 'order': 5, 'probability_percent': 90.0}
            ]

            for stage_data in stages:
                stage = SalesStage(**stage_data)
                db.session.add(stage)

            db.session.commit()
            print("✅ Default sales stages created.")

        # Create sample contact if none exist
        if not Contact.query.first():
            sample_contact = Contact(
                first_name='John',
                last_name='Doe',
                email='<EMAIL>',
                company='Example Corp',
                job_title='CEO',
                source='demo_data',
                status='new'
            )
            db.session.add(sample_contact)
            db.session.commit()
            print("✅ Sample contact created.")

    except Exception as e:
        print(f"❌ Database initialization error: {str(e)}")

# ---------------------------------------------------------------------------
# GRADIO CHATBOT SYSTEM (from your original app.py)
# ---------------------------------------------------------------------------

def build_chatbot_interface():
    """Build the chatbot interface with integration hooks"""

    with gr.Blocks(
        theme=gr.themes.Default(),
        title="24Seven Assistants Sales Bot",
        css="""
        .prose.stage-info {
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
            padding: 15px !important;
            border-radius: 8px !important;
            margin: 10px 0 !important;
            border: 1px solid #4a5568 !important;
        }
        .prose {
            background-color: transparent !important;
            color: var(--body-text-color) !important;
        }
        """
    ) as demo:

        # Header with integration link
        gr.Markdown("# 🤖 24Seven Assistants – Sales Bot")
        gr.Markdown(
            "**Meet Sarah, your AI sales agent!** She'll guide you through our 5-stage sales process. "
            "All interactions are automatically logged to the [Sales Department Dashboard](http://localhost:5000)."
        )

        # State variables
        bot_state = gr.State(None)
        session_id = gr.State(None)

        # Status display
        with gr.Row():
            with gr.Column(scale=2):
                stage_info = gr.Markdown("**Status:** Ready to start", elem_classes=["stage-info"])
            with gr.Column(scale=1):
                reset_btn = gr.Button("🔄 Reset Chat", variant="secondary", size="sm")
                dashboard_btn = gr.Button("📊 View Dashboard", variant="primary", size="sm")

        # Startup UI
        with gr.Row(variant="panel") as startup_row:
            with gr.Column(scale=3):
                name_box = gr.Textbox(
                    label="What is your name?",
                    placeholder="e.g. Alex Johnson",
                    info="Sarah will use your name to personalize the conversation"
                )
            with gr.Column(scale=1):
                start_btn = gr.Button("▶️ Start Chat", variant="primary", size="lg")

        # Chat UI (initially hidden)
        with gr.Column(visible=False) as chat_column:
            chatbot = gr.Chatbot(
                label="Conversation with Sarah",
                height=400,
                type="messages"
            )

            with gr.Row():
                msg_input = gr.Textbox(
                    label="Your Message",
                    placeholder="Type your message here and press Enter...",
                    scale=4
                )
                send_btn = gr.Button("Send", variant="primary", scale=1)

        # Integration info
        gr.Markdown("""
        ### 🔗 Integration Features:
        - All conversations are automatically logged to the sales department
        - Contact information is saved and tracked
        - Sales stage progression is monitored
        - Activities are recorded for follow-up
        """)

        # Event handlers (simplified for integration)
        def start_chat(user_name):
            if not user_name.strip():
                gr.Warning("Please enter your name to start the chat.")
                return None, [], gr.update(visible=True), gr.update(visible=False), "**Status:** Please enter your name"

            bot = SalesBot(CONFIG_DATA)
            session_id_val = f"session_{int(time.time())}"
            first_msg = bot.start_conversation(user_name.strip())

            history = [{"role": "assistant", "content": first_msg}]

            return (
                bot,
                history,
                gr.update(visible=False),
                gr.update(visible=True),
                f"**Current Stage:** {bot.current_stage.title()} | **Task:** {bot.current_task}"
            )

        def chat_with_bot(user_msg, history, bot):
            if bot is None or not user_msg.strip():
                return "", history, bot, "**Status:** Please start a new chat"

            ai_msg = bot.chat(user_msg.strip())

            history.append({"role": "user", "content": user_msg})
            history.append({"role": "assistant", "content": ai_msg})

            return (
                "",
                history,
                bot,
                f"**Current Stage:** {bot.current_stage.title()} | **Task:** {bot.current_task}"
            )

        def reset_chat():
            return None, [], gr.update(visible=True), gr.update(visible=False), "**Status:** Ready to start"

        def open_dashboard():
            return gr.update(value="Opening Sales Department Dashboard...")

        # Wire up events
        start_btn.click(start_chat, inputs=[name_box], outputs=[bot_state, chatbot, startup_row, chat_column, stage_info])
        msg_input.submit(chat_with_bot, inputs=[msg_input, chatbot, bot_state], outputs=[msg_input, chatbot, bot_state, stage_info])
        send_btn.click(chat_with_bot, inputs=[msg_input, chatbot, bot_state], outputs=[msg_input, chatbot, bot_state, stage_info])
        reset_btn.click(reset_chat, outputs=[bot_state, chatbot, startup_row, chat_column, stage_info])
        dashboard_btn.click(open_dashboard, outputs=[stage_info])

    return demo

# ---------------------------------------------------------------------------
# MAIN INTEGRATION RUNNER
# ---------------------------------------------------------------------------

def run_flask_app():
    """Run Flask app in a separate thread"""
    with app.app_context():
        init_database()

    print("📊 Starting Sales Department Dashboard on http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)

def run_gradio_app():
    """Run Gradio app in a separate thread"""
    print("🤖 Starting Sales Chatbot on http://localhost:7861")
    chatbot_ui = build_chatbot_interface()
    chatbot_ui.launch(
        server_name="127.0.0.1",
        server_port=7861,
        show_error=True,
        quiet=True
    )

if __name__ == "__main__":
    print("🚀 24Seven Assistants - Integrated Sales System Starting...")
    print("📧 SMTP Email System: Ready (Demo Mode)")
    print("📊 Analytics Dashboard: Ready")
    print("🎯 Sales Stage Tracking: Ready")
    print("🤖 AI Sales Chatbot: Ready")
    print("💼 Complete Sales Department: Ready")
    print("-" * 60)

    # Start Flask app in background thread
    flask_thread = threading.Thread(target=run_flask_app, daemon=True)
    flask_thread.start()

    # Give Flask time to start
    time.sleep(2)

    # Start Gradio app in main thread
    run_gradio_app()
