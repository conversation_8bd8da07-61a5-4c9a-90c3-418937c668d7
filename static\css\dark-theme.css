:root {
  /* Dark professional palette reused from chatbot */
  --primary-color: #203A8F;
  --secondary-color: #1C254A;
  --success-color: #16a085;
  --warning-color: #FF9800;
  --danger-color: #f44336;
  --dark-color: #0d1117;
  --light-color: #1c1f26; /* override previous light background */

  /* Additional chatbot variables */
  --primary-gradient: linear-gradient(135deg, #203A8F 0%, #1C254A 100%);
  --secondary-gradient: linear-gradient(135deg, #FF0080 0%, #7928CA 100%);
  --chat-bg: linear-gradient(135deg, #0d1117 0%, #1c1f26 100%);
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.15);
  --text-primary: #e6e6e6;
  --text-secondary: #9ca3af;
  --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.6);
  --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.7);
  --border-radius: 16px;
}

/* Global background and text */
body {
  background: var(--chat-bg) !important;
  color: var(--text-primary) !important;
}

/* Navbar & sidebar */
.navbar {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
}

.sidebar {
  background: var(--primary-gradient) !important;
}

.sidebar .nav-link {
  color: var(--text-primary) !important;
}

.sidebar .nav-link.active,
.sidebar .nav-link:hover {
  background: rgba(255,255,255,0.1) !important;
}

/* Cards & tables */
.card, .chart-container, .table {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}

.card-header {
  background: var(--primary-gradient) !important;
  color: var(--text-primary) !important;
}

.table thead th {
  background: var(--primary-gradient) !important;
  color: var(--text-primary) !important;
}

/* Common light background overrides */
.bg-white,
.bg-light,
.card.bg-white,
.chart-container.bg-white,
.table.bg-white {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}

.list-group-item {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}

/* Plotly charts */
.js-plotly-plot .plot-container, .js-plotly-plot .svg-container, .js-plotly-plot {
  background: var(--glass-bg) !important;
}
/* Override internal SVG rect/background and inline white */
.js-plotly-plot svg, .plotly svg {
  background: var(--glass-bg) !important;
}
/* Override internal SVG rect background */
.plotly .bg, .plotly .bg rect {
  fill: var(--glass-bg) !important;
}
.plotly .grid-background {
  fill: var(--glass-bg) !important;
}
.js-plotly-plot, .plot-container, .plotly, .plotly-graph-div {
  background: var(--glass-bg) !important;
}

/* Plotly text & grid for dark mode */
.plotly text, .plotly .legend text, .plotly .ytitle text, .plotly .xtitle text,
.plotly .xtick text, .plotly .ytick text, .plotly .annotation text {
  fill: var(--text-primary) !important;
}
.plotly .gridlayer line {
  stroke: rgba(255, 255, 255, 0.1) !important;
}
.plotly .axis line, .plotly .ticks {
  stroke: rgba(255, 255, 255, 0.3) !important;
}

/* Progress bars */
.progress {
  background: rgba(255,255,255,0.12) !important;
}
.progress-bar {
  background: var(--secondary-gradient) !important;
}

/* Bootstrap Modals */
.modal-content {
  /* Less transparent for readability */
  background: rgba(18,22,28,0.95) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
  box-shadow: var(--shadow-soft) !important;
}
.modal-header,
.modal-footer { /* keep solid separators */
  border-color: var(--glass-border) !important;
}
.modal-header .close,
.btn-close {
  filter: invert(1);
}
.modal-backdrop.show {
  background-color: rgba(0, 0, 0, 0.75) !important;
}

/* Form controls (inputs/selects) */
.form-control, .form-select {
  /* Higher opacity for better contrast inside modals */
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: var(--text-primary) !important;
}
.form-control::placeholder {
  color: var(--text-secondary) !important;
}
.form-control:focus, .form-select:focus {
  background: rgba(255, 255, 255, 0.12) !important;
  border-color: rgba(255, 255, 255, 0.35) !important;
  box-shadow: 0 0 0 0.15rem rgba(255,255,255,.15) !important;
}

/* Dark dropdown for bootstrap-select */
.bootstrap-select .dropdown-menu {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}
.bootstrap-select .dropdown-menu .dropdown-item {
  color: var(--text-primary) !important;
}
.bootstrap-select .dropdown-menu .dropdown-item.active,
.bootstrap-select .dropdown-menu .dropdown-item:active,
.bootstrap-select .dropdown-menu .dropdown-item:hover {
  background: var(--primary-gradient) !important;
  color: #fff !important;
}

/* Dropdown option styles (supported browsers) */
.form-select option {
  background: var(--glass-bg);
  color: var(--text-primary);
}
.form-select option:checked {
  background: var(--primary-gradient);
  color: #fff;
}


/* Enhanced Button Styling with High Contrast Text */
.btn-primary {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5BA0F2 0%, #4A8ACD 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4) !important;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-success:hover {
  background: linear-gradient(135deg, #34ce57 0%, #2dd4aa 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-info:hover {
  background: linear-gradient(135deg, #1fc8e3 0%, #17a2b8 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4) !important;
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  border: none !important;
  color: #000000 !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #ffcd39 0%, #ffc107 100%) !important;
  color: #000000 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4) !important;
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #7c858d 0%, #6c757d 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #e4606d 0%, #dc3545 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4) !important;
}

/* Outline button enhancements with high contrast */
.btn-outline-primary {
  border: 2px solid #66b3ff !important;
  color: #66b3ff !important;
  background: rgba(74, 144, 226, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
  color: #ffffff !important;
  border-color: #4A90E2 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4) !important;
}

.btn-outline-secondary {
  border: 2px solid #9ca3af !important;
  color: #e6e6e6 !important;
  background: rgba(108, 117, 125, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  color: #ffffff !important;
  border-color: #6c757d !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4) !important;
}

.btn-outline-success {
  border: 2px solid #28a745 !important;
  color: #34ce57 !important;
  background: rgba(40, 167, 69, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-success:hover {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
  color: #ffffff !important;
  border-color: #28a745 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
}

.btn-outline-info {
  border: 2px solid #17a2b8 !important;
  color: #1fc8e3 !important;
  background: rgba(23, 162, 184, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-info:hover {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  color: #ffffff !important;
  border-color: #17a2b8 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4) !important;
}

.btn-outline-warning {
  border: 2px solid #ffc107 !important;
  color: #ffcd39 !important;
  background: rgba(255, 193, 7, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-warning:hover {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #000000 !important;
  border-color: #ffc107 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4) !important;
}

.btn-outline-danger {
  border: 2px solid #dc3545 !important;
  color: #e4606d !important;
  background: rgba(220, 53, 69, 0.15) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.btn-outline-danger:hover {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  color: #ffffff !important;
  border-color: #dc3545 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4) !important;
}

/* Button group enhancements */
.btn-group .btn {
  margin: 0 2px !important;
  border-radius: 8px !important;
}

/* Special styling for navigation buttons */
.d-flex .btn {
  margin: 0 4px !important;
  border-radius: 10px !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
}

/* Button text and icon contrast improvements */
.btn i {
  color: inherit !important;
  opacity: 1 !important;
}

/* Ensure all button text is highly visible */
.btn, .btn:focus, .btn:active {
  text-decoration: none !important;
}

/* Small button text improvements */
.btn-sm {
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

/* Button link text color fixes */
.btn a, .btn a:hover, .btn a:focus {
  color: inherit !important;
  text-decoration: none !important;
}

/* Ensure button text doesn't inherit dark colors */
.btn * {
  color: inherit !important;
}

/* Fix for any remaining text contrast issues */
.btn-primary, .btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  color: #ffffff !important;
}

.btn-success, .btn-success:hover, .btn-success:focus, .btn-success:active {
  color: #ffffff !important;
}

.btn-info, .btn-info:hover, .btn-info:focus, .btn-info:active {
  color: #ffffff !important;
}

.btn-warning, .btn-warning:hover, .btn-warning:focus, .btn-warning:active {
  color: #000000 !important;
}

.btn-secondary, .btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
  color: #ffffff !important;
}

/* Dark mode styling for .btn-light */
.btn-light {
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  box-shadow: 0 4px 15px rgba(45, 55, 72, 0.3) !important;
  transition: all 0.3s ease !important;
}

.btn-light:hover {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
  color: #ffffff !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(45, 55, 72, 0.4) !important;
}

.btn-light:focus, .btn-light:active {
  color: #ffffff !important;
}

.btn-danger, .btn-danger:hover, .btn-danger:focus, .btn-danger:active {
  color: #ffffff !important;
}

/* Contact Details and Chatbot Session Text Improvements */
.card-body strong {
  color: var(--text-primary) !important;
}

.card-body code {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #66b3ff !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-weight: 600 !important;
}

/* Chatbot session details */
.border.rounded {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

.border.rounded strong {
  color: var(--text-primary) !important;
}

.border.rounded .col-md-6 {
  color: var(--text-primary) !important;
}

/* Timeline and activity text */
.timeline-content h6 {
  color: var(--text-primary) !important;
}

.timeline-content small {
  color: var(--text-secondary) !important;
}

.list-group-item {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}

.list-group-item h6 {
  color: var(--text-primary) !important;
}

.list-group-item p {
  color: var(--text-primary) !important;
}

.list-group-item small {
  color: var(--text-secondary) !important;
}

/* Contact information section */
.card-body div {
  color: var(--text-primary) !important;
}

.card-body a {
  color: #66b3ff !important;
}

.card-body a:hover {
  color: #99ccff !important;
}

/* Stage progression and activities */
.timeline-item {
  color: var(--text-primary) !important;
}

.timeline-marker {
  border: 2px solid var(--text-primary) !important;
}

/* Session details specific improvements */
.chatbot-session-text {
  color: var(--text-primary) !important;
  font-weight: 500 !important;
}

.session-info strong {
  color: var(--text-primary) !important;
}

.session-info {
  color: var(--text-primary) !important;
}

/* Current task text */
.mt-2 strong {
  color: var(--text-primary) !important;
}

/* Specific chatbot session styling */
.chatbot-session-details {
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.chatbot-session-details strong {
  color: #e0e0e0 !important;
  font-weight: 600 !important;
}

.chatbot-session-details .chatbot-session-text {
  color: #f0f0f0 !important;
  font-weight: 500 !important;
}

.chatbot-session-details .session-info {
  color: #f0f0f0 !important;
}

/* Improve all text in contact details */
.card-body span {
  color: var(--text-primary) !important;
}

.card-body .text-muted {
  color: #b0b0b0 !important;
}

/* Badge improvements for better contrast */
.badge {
  font-weight: 600 !important;
  padding: 0.5em 0.75em !important;
}

.badge.bg-success {
  background-color: #28a745 !important;
  color: #ffffff !important;
}

.badge.bg-primary {
  background-color: #007bff !important;
  color: #ffffff !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #000000 !important;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
  color: #ffffff !important;
}

.alert {
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
  color: var(--text-primary) !important;
}

/* Text color overrides for better visibility */
.text-muted, .text-secondary {
  color: var(--text-secondary) !important;
}

.text-dark, .text-black {
  color: var(--text-primary) !important;
}

/* Table text overrides */
.table td, .table th {
  color: var(--text-primary) !important;
}

.table td strong, .table th strong {
  color: var(--text-primary) !important;
}

.table td small, .table th small {
  color: var(--text-secondary) !important;
}

/* Badge overrides for dark theme */
.badge.bg-light {
  background: rgba(255, 255, 255, 0.2) !important;
  color: var(--text-primary) !important;
}

.badge.text-dark {
  color: var(--text-primary) !important;
}

/* Link colors for dark theme */
a {
  color: #66b3ff !important;
}

a:hover {
  color: #99ccff !important;
}

/* Email links in tables */
.table a {
  color: #66b3ff !important;
}

.table a:hover {
  color: #99ccff !important;
}

/* General text elements */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary) !important;
}

p, span, div {
  color: inherit;
}

/* Specific overrides for campaign and contact tables */
.table .text-info {
  color: #66b3ff !important;
}

.table .text-warning {
  color: #ffc107 !important;
}

.table .text-success {
  color: #28a745 !important;
}

.table .text-danger {
  color: #dc3545 !important;
}

/* Dark-mode cards and analytics sections */
.metric-card,
.analytics-tabs {
  background: #1e1e1e !important;
  color: var(--text-primary) !important;
  box-shadow: var(--shadow-soft) !important;
}

/* Dark-mode stage progress */
.stage-progress {
  background: #2a2a2a !important;
  color: var(--text-primary) !important;
}
