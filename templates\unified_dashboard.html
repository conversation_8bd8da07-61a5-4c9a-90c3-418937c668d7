{% extends "base.html" %}
{% block title %}Unified Sales System - 24Seven Assistants{% endblock %}
{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line text-primary"></i> Unified Sales System Dashboard</h1>
    <div class="text-muted">
        <i class="fas fa-sync-alt"></i> Real-time tracking: Email → Chatbot → Conversion
    </div>
</div>
<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4><i class="fas fa-rocket"></i> Complete Sales Funnel Tracking</h4>
                        <p class="mb-0">Email Campaigns → Chatbot Links → AI Sales Conversations → Conversions</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-chart-bar"></i> View Full Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Conversion Funnel Metrics -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body text-center">
                <div class="metric-value">{{ emails_sent }}</div>
                <div class="metric-label">Emails Sent</div>
                <small>{{ total_campaigns }} campaigns</small>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-secondary text-white">
            <div class="card-body text-center">
                <div class="metric-value">{{ emails_opened }}</div>
                <div class="metric-label">Emails Opened</div>
                <small>
                    {% if conversion_rates %}
                        {{ conversion_rates.email_open_rate }}% open rate
                    {% else %}
                        0% open rate
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body text-center">
                <div class="metric-value">{{ links_clicked }}</div>
                <div class="metric-label">Links Clicked</div>
                <small>
                    {% if conversion_rates %}
                        {{ conversion_rates.click_rate }}% click rate
                    {% else %}
                        0% click rate
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body text-center">
                <div class="metric-value">{{ conversations_started }}</div>
                <div class="metric-label">Conversations</div>
                <small>
                    {% if conversion_rates %}
                        {{ conversion_rates.conversation_rate }}% conversion
                    {% else %}
                        0% conversion
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body text-center">
                <div class="metric-value">{{ conversions }}</div>
                <div class="metric-label">Conversions</div>
                <small>
                    {% if conversion_rates %}
                        {{ conversion_rates.conversion_rate }}% close rate
                    {% else %}
                        0% close rate
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card metric-card bg-dark text-white">
            <div class="card-body text-center">
                <div class="metric-value">
                    {% if conversion_rates %}
                        {{ conversion_rates.overall_rate }}%
                    {% else %}
                        0%
                    {% endif %}
                </div>
                <div class="metric-label">Overall Rate</div>
                <small>Email to Sale</small>
            </div>
        </div>
    </div>
</div>
<!-- Sales Stage Distribution -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-funnel-dollar"></i> Sales Funnel Progress (Cumulative)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-1 text-center">
                        <div class="p-2 bg-light rounded">
                            <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                            <h6>Email Sent</h6>
                            <span class="badge bg-primary">{{ emails_sent }}</span>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <div class="p-2 bg-light rounded">
                            <i class="fas fa-envelope-open fa-2x text-info mb-2"></i>
                            <h6>Opened</h6>
                            <span class="badge bg-info">{{ emails_opened }}</span>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <div class="p-2 bg-light rounded">
                            <i class="fas fa-mouse-pointer fa-2x text-warning mb-2"></i>
                            <h6>Clicked</h6>
                            <span class="badge bg-warning">{{ links_clicked }}</span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-2 bg-light rounded">
                            <i class="fas fa-comments fa-2x text-success mb-2"></i>
                            <h6>Conversations</h6>
                            <span class="badge bg-success">{{ conversations_started }}</span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-2 bg-light rounded">
                            <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                            <h6>Conversions</h6>
                            <span class="badge bg-warning">{{ conversions }}</span>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="p-2 bg-success text-white rounded">
                            <i class="fas fa-percentage fa-2x mb-2"></i>
                            <h6>Overall Success Rate</h6>
                            <span class="badge bg-light text-dark">
                                {% if conversion_rates %}
                                    {{ conversion_rates.overall_rate }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Current Stage Distribution (Where contacts are stuck) -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> Current Contact Distribution (Where contacts are now)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for stage_name, count in stage_counts.items() %}
                        {% if count > 0 %}
                        <div class="col-md-2 text-center mb-3">
                            <div class="p-2 bg-light rounded">
                                {% if stage_name == 'email_sent' %}
                                    <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                    <h6>Awaiting Open</h6>
                                {% elif stage_name == 'email_opened' %}
                                    <i class="fas fa-envelope-open fa-2x text-info mb-2"></i>
                                    <h6>Awaiting Click</h6>
                                {% elif stage_name == 'link_clicked' %}
                                    <i class="fas fa-mouse-pointer fa-2x text-warning mb-2"></i>
                                    <h6>Ready for Chat</h6>
                                {% elif stage_name == 'opening' %}
                                    <i class="fas fa-door-open fa-2x text-success mb-2"></i>
                                    <h6>Opening Stage</h6>
                                {% elif stage_name == 'trust' %}
                                    <i class="fas fa-handshake fa-2x text-primary mb-2"></i>
                                    <h6>Building Trust</h6>
                                {% elif stage_name == 'discovery' %}
                                    <i class="fas fa-search fa-2x text-info mb-2"></i>
                                    <h6>Discovery</h6>
                                {% elif stage_name == 'demonstration' %}
                                    <i class="fas fa-presentation fa-2x text-warning mb-2"></i>
                                    <h6>Demonstration</h6>
                                {% elif stage_name == 'close' %}
                                    <i class="fas fa-handshake fa-2x text-danger mb-2"></i>
                                    <h6>Closing</h6>
                                {% elif stage_name == 'converted' %}
                                    <i class="fas fa-trophy fa-2x text-success mb-2"></i>
                                    <h6>Converted!</h6>
                                {% else %}
                                    <i class="fas fa-circle fa-2x text-muted mb-2"></i>
                                    <h6>{{ stage_name.title() }}</h6>
                                {% endif %}
                                <span class="badge bg-primary">{{ count }}</span>
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                <small class="text-muted">This shows where each contact is currently in the sales process. Use this to identify bottlenecks.</small>
            </div>
        </div>
    </div>
</div>
<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('create_campaign') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-envelope-open-text"></i> Create Email Campaign
                        </a>
                        <small class="text-muted">Send emails with chatbot links</small>
                    </div>
                    <div class="col-md-3">
                        <a href="#" onclick="openChatbot(event)" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-robot"></i> Open Sales Chatbot
                        </a>
                        <small class="text-muted">Direct access to Sarah</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-chart-line"></i> View Analytics
                        </a>
                        <small class="text-muted">Complete funnel analysis</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('contacts_list') }}" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-users"></i> Manage Contacts
                        </a>
                        <small class="text-muted">Track customer journey</small>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6 text-center">
                        <button id="syncStatsBtn" class="btn btn-outline-primary">
                            <i class="fas fa-sync-alt"></i> Sync All Statistics
                        </button>
                        <small class="text-muted d-block mt-1">Ensure all analytics are synchronized</small>
                    </div>
                    <div class="col-md-6 text-center">
                        <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteAllDataModal">
                            <i class="fas fa-exclamation-triangle"></i> Delete All Data
                        </button>
                        <small class="text-muted d-block mt-1">⚠️ Nuclear option - removes everything</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Recent Activities -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Real-time Activity Feed</h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Activity</th>
                                    <th>Contact</th>
                                    <th>Details</th>
                                    <th>Time</th>
                                </tr>
                            </thead>





                            <tbody>
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>
                                        {% if activity.activity_type == 'email_sent' %}
                                            <i class="fas fa-envelope text-primary"></i> Email Sent
                                        {% elif activity.activity_type == 'email_opened' %}
                                            <i class="fas fa-envelope-open text-info"></i> Email Opened
                                        {% elif activity.activity_type == 'link_clicked' %}
                                            <i class="fas fa-mouse-pointer text-warning"></i> Link Clicked
                                        {% elif activity.activity_type == 'stage_progression' %}
                                            <i class="fas fa-arrow-right text-success"></i> Stage Progress
                                        {% elif activity.activity_type == 'conversion' %}
                                            <i class="fas fa-trophy text-danger"></i> Conversion!
                                        {% else %}
                                            <i class="fas fa-circle text-muted"></i> {{ activity.activity_type.title() }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if activity.contact %}
                                            {{ activity.contact.full_name }}
                                            <br><small class="text-muted">{{ activity.contact.company or 'No company' }}</small>
                                        {% else %}
                                            <span class="text-muted">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ activity.subject or activity.description or 'No details' }}
                                        {% if activity.session_id %}
                                            <br><small class="text-muted">Session: {{ activity.session_id[:8] }}...</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ activity.created_at.strftime('%H:%M') }}
                                        <br><small class="text-muted">{{ activity.created_at.strftime('%m/%d') }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No activities yet. Start by creating an email campaign!</p>
                        <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
                            <i class="fas fa-envelope-open-text"></i> Create Your First Campaign
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> System Integration</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-link text-primary"></i> How It Works</h6>
                    <ol class="small">
                        <li><strong>Email Campaign</strong> - Send emails with chatbot links</li>
                        <li><strong>Link Tracking</strong> - Track when recipients click</li>
                        <li><strong>Chatbot Engagement</strong> - Sarah guides through sales process</li>
                        <li><strong>Stage Progression</strong> - Real-time tracking of sales stages</li>
                        <li><strong>Conversion Tracking</strong> - Monitor completed sales</li>
                    </ol>
                </div>
                <div class="mb-3">
                    <h6><i class="fas fa-chart-bar text-success"></i> Current Performance</h6>
                    <ul class="list-unstyled small">
                        <li>Total Contacts: {{ total_contacts }}</li>
                        <li>Active Campaigns: {{ total_campaigns }}</li>
                        <li>Chatbot Sessions: {{ total_sessions }}</li>
                        <li>Live Conversations: {{ active_sessions }}</li>
                    </ul>
                </div>
                <div class="mb-3">
                    <h6><i class="fas fa-cogs text-info"></i> System Status</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success"></i> Email System: Active</li>
                        <li><i class="fas fa-check text-success"></i> Chatbot: Active</li>
                        <li><i class="fas fa-check text-success"></i> Analytics: Real-time</li>
                        <li><i class="fas fa-check text-success"></i> Tracking: Unified</li>
                    </ul>
                </div>
                <div class="mb-3">
                    <h6><i class="fas fa-chart-bar text-warning"></i> Advanced Analytics Dashboard</h6>
                    <div class="row g-2">
                        <div class="col-6">
                            <a href="{{ url_for('sales_pipeline_analytics') }}" class="btn btn-sm w-100" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none;">
                                <i class="fas fa-funnel-dollar"></i><br>
                                <small>Sales Pipeline</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('sales_cycle_analytics') }}" class="btn btn-sm w-100" style="background: linear-gradient(135deg, #007bff, #6610f2); color: white; border: none;">
                                <i class="fas fa-clock"></i><br>
                                <small>Sales Cycle</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('session_analytics') }}" class="btn btn-sm w-100" style="background: linear-gradient(135deg, #6f42c1, #e83e8c); color: white; border: none;">
                                <i class="fas fa-comments"></i><br>
                                <small>Session Analytics</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{{ url_for('comprehensive_analytics') }}" class="btn btn-sm w-100" style="background: linear-gradient(135deg, #fd7e14, #dc3545); color: white; border: none;">
                                <i class="fas fa-chart-pie"></i><br>
                                <small>Complete Dashboard</small>
                            </a>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">Pipeline funnel • Cycle metrics • Session insights • Complete overview</small>
                    </div>
                </div>
                <div class="text-center">
                    <a href="#" onclick="openChatbot(event)" class="btn btn-success btn-sm w-100 mb-2">
                        <i class="fas fa-robot"></i> Test Sales Chatbot
                    </a>
                    <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-chart-line"></i> View Detailed Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Delete All Data Modal -->
<div class="modal fade" id="deleteAllDataModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">🚨 DANGER: Delete ALL System Data</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> ⚠️ NUCLEAR OPTION WARNING ⚠️</h6>
                    <p><strong>This will permanently delete EVERYTHING from the system:</strong></p>
                    <ul>
                        <li>🗑️ All email campaigns and templates</li>
                        <li>🗑️ All contacts and their profiles</li>
                        <li>🗑️ All chatbot conversations and sessions</li>
                        <li>🗑️ All activity history and tracking data</li>
                        <li>🗑️ All analytics and performance metrics</li>
                        <li>🗑️ All sales stage progression data</li>
                    </ul>
                    <p class="text-danger"><strong>THIS ACTION CANNOT BE UNDONE!</strong></p>
                </div>
                <div class="alert alert-warning">
                    <h6>Use this only when:</h6>
                    <ul class="mb-0">
                        <li>Starting completely fresh</li>
                        <li>Clearing test data</li>
                        <li>System reset required</li>
                    </ul>
                </div>
                <form action="{{ url_for('delete_all_data') }}" method="POST" id="deleteAllDataForm">
                    <div class="mb-3">
                        <label for="confirmationText" class="form-label">
                            <strong>Type exactly "DELETE ALL DATA" to confirm:</strong>
                        </label>
                        <input type="text" class="form-control" id="confirmationText" name="confirmation"
                               placeholder="Type: DELETE ALL DATA" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="deleteAllDataForm" class="btn btn-danger" id="confirmDeleteAllBtn" disabled>
                    <i class="fas fa-trash"></i> DELETE ALL DATA
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_js %}
<script>
// Open Sales Chatbot button handler
function openChatbot(event){
    if(event) event.preventDefault();
    const id = (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') ?
        crypto.randomUUID() : Math.random().toString(36).substring(2,10);
    window.open('/chat/' + id, '_blank');
}

// Auto-refresh every 30 s if there are active live chat sessions
setInterval(() => {
    if ({{ active_sessions }} > 0) location.reload();
}, 30000);

document.addEventListener('DOMContentLoaded', () => {
    /* Highlight active sessions metric */
    const activeCard = document.querySelector('.bg-danger');
    if (activeCard && {{ active_sessions }} > 0) activeCard.style.animation = 'pulse 2s infinite';

    /* Sync statistics button */
    const syncBtn = document.getElementById('syncStatsBtn');
    if (syncBtn){
        syncBtn.addEventListener('click', () => {
            const orig = syncBtn.innerHTML;
            syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
            syncBtn.disabled = true;
            fetch('/api/sync-statistics',{method:'POST',headers:{'Content-Type':'application/json'}})
                .then(r=>r.json())
                .then(d=>{
                    if(d.success){
                        syncBtn.innerHTML='<i class="fas fa-check text-success"></i> Synced!';
                        setTimeout(()=>location.reload(),1000);
                    }else{throw new Error(d.message||'Sync failed');}
                })
                .catch(err=>{
                    console.error(err);
                    syncBtn.innerHTML='<i class="fas fa-exclamation-triangle text-danger"></i> Error';
                    setTimeout(()=>{syncBtn.innerHTML=orig;syncBtn.disabled=false;},3000);
                });
        });
    }

    /* Delete-all confirmation */
    const confirmInput=document.getElementById('confirmationText');
    const confirmBtn=document.getElementById('confirmDeleteAllBtn');
    if(confirmInput && confirmBtn){
        confirmInput.addEventListener('input',()=>{
            const ok = confirmInput.value === 'DELETE ALL DATA';
            confirmBtn.disabled = !ok;
            confirmBtn.classList.toggle('btn-danger', !ok);
            confirmBtn.classList.toggle('btn-outline-danger', ok);
        });
    }
});
</script>
<style>
@keyframes pulse{0%{opacity:1}50%{opacity:.7}100%{opacity:1}}
</style>
<script>
function openChatbot(event){
    if(event) event.preventDefault();
    const id = (typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function')
        ? crypto.randomUUID()
        : Math.random().toString(36).substring(2,10);
    window.open('/chat/' + id, '_blank');
}

// Auto-refresh every 30 seconds for real-time updates
setInterval(() => {
    if ({{ active_sessions }} > 0) {
        location.reload();
    }
}, 30000);

document.addEventListener('DOMContentLoaded', () => {
    // Pulsing effect on active sessions metric
    const activeSessionsCard = document.querySelector('.bg-danger');
    if (activeSessionsCard && {{ active_sessions }} > 0) {
        activeSessionsCard.style.animation = 'pulse 2s infinite';
    }

    // Sync statistics button handler
    const syncBtn = document.getElementById('syncStatsBtn');
    if (syncBtn) {
        syncBtn.addEventListener('click', () => {
            const originalText = syncBtn.innerHTML;
            syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
            syncBtn.disabled = true;

            fetch('/api/sync-statistics', {method:'POST', headers:{'Content-Type':'application/json'}})
                .then(r => r.json())
                .then(data => {
                    if (data.success) {
                        syncBtn.innerHTML = '<i class="fas fa-check text-success"></i> Synced!';
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        throw new Error(data.message || 'Sync failed');
                    }
                })
                .catch(err => {
                    console.error('Sync error:', err);
                    syncBtn.innerHTML = '<i class="fas fa-exclamation-triangle text-danger"></i> Error';
                    setTimeout(() => {
                        syncBtn.innerHTML = originalText;
                        syncBtn.disabled = false;
                    }, 3000);
                });
        });
    }

    // Delete all data confirmation
    const confirmationInput = document.getElementById('confirmationText');
    if (confirmationInput) {
        confirmationInput.addEventListener('input', () => {
            const confirmBtn = document.getElementById('confirmDeleteAllBtn');
            if (!confirmBtn) return;
            if (confirmationInput.value === 'DELETE ALL DATA') {
                confirmBtn.disabled = false;
                confirmBtn.classList.remove('btn-danger');
                confirmBtn.classList.add('btn-outline-danger');
            } else {
                confirmBtn.disabled = true;
                confirmBtn.classList.remove('btn-outline-danger');
                confirmBtn.classList.add('btn-danger');
            }
        });
    }
});
</script>

<style>
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>
{% endblock %}
