#!/usr/bin/env python3
"""
Fix Contacts Now
================
Complete fix for contact creation issue
"""

import os
import sys
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

def main():
    """Main fix function"""
    print("🔧 FIXING CONTACT CREATION ISSUE")
    print("=" * 50)
    
    try:
        # Set up environment
        setup_environment()
        
        # Import after environment setup
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            print("✅ Application context created")
            
            # Remove old database file if it exists
            db_files = ['unified_sales.db', 'instance/unified_sales.db']
            for db_file in db_files:
                if os.path.exists(db_file):
                    try:
                        os.remove(db_file)
                        print(f"✅ Removed old database: {db_file}")
                    except Exception as e:
                        print(f"⚠️ Could not remove {db_file}: {e}")
            
            # Create all tables with correct schema
            print("📋 Creating database with correct schema...")
            db.create_all()
            print("✅ Database tables created")
            
            # Create test contact exactly as per documentation
            print("👤 Creating test contact...")
            contact = Contact(
                first_name='Alex',
                last_name='Scof',
                email='<EMAIL>',
                phone='(*************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new'
            )
            
            db.session.add(contact)
            db.session.commit()
            
            print(f"✅ Test contact created: {contact.full_name}")
            print(f"   ID: {contact.id}")
            print(f"   Email: {contact.email}")
            print(f"   Company: {contact.company}")
            print(f"   Active: {contact.is_active}")
            print(f"   Do Not Email: {contact.do_not_email}")
            
            # Verify contact can be queried
            test_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if test_contact:
                print("\n✅ Contact verification successful!")
                print(f"   Query result: {test_contact.full_name}")
                
                # Test contact operations
                print("\n🧪 Testing contact operations...")
                
                # Count contacts
                total = Contact.query.count()
                print(f"✅ Total contacts: {total}")
                
                # Count active contacts
                active = Contact.query.filter_by(is_active=True).count()
                print(f"✅ Active contacts: {active}")
                
                # Count email-enabled contacts
                email_enabled = Contact.query.filter_by(is_active=True, do_not_email=False).count()
                print(f"✅ Email-enabled contacts: {email_enabled}")
                
                # Test creating another contact (simulate form)
                print("\n📝 Testing form simulation...")
                form_contact = Contact(
                    first_name='John',
                    last_name='Doe',
                    email='<EMAIL>',
                    phone='(*************',
                    company='Example Corp',
                    job_title='Manager',
                    source='manual_entry',
                    status='new'
                )
                
                db.session.add(form_contact)
                db.session.commit()
                
                print(f"✅ Form simulation successful: {form_contact.full_name}")
                
                # Final verification
                final_count = Contact.query.count()
                print(f"✅ Final contact count: {final_count}")
                
                print("\n🎉 CONTACT SYSTEM FIXED SUCCESSFULLY!")
                print("=" * 50)
                print("✅ Database schema is correct")
                print("✅ Contact model matches database")
                print("✅ Contact creation works")
                print("✅ Contact queries work")
                print("✅ Form simulation successful")
                
                print("\n🚀 READY FOR EMAIL CAMPAIGNS!")
                print("=" * 30)
                print("Next steps:")
                print("1. Start application: python start_app_simple.py")
                print("2. Go to: http://localhost:5000/contacts/add")
                print("3. Add a contact (should work now)")
                print("4. Go to: http://localhost:5000/campaigns")
                print("5. Create and send campaign")
                print("6. Check <EMAIL> for email")
                
                return True
            else:
                print("❌ Contact verification failed")
                return False
                
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ CONTACT CREATION ISSUE RESOLVED!")
    else:
        print("\n❌ CONTACT CREATION ISSUE PERSISTS")
    sys.exit(0 if success else 1)
