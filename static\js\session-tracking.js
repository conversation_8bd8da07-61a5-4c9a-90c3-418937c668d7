/**
 * Enhanced Session Tracking for 24Seven Assistants Sales Chatbot
 * Tracks complete funnel from email campaigns through all sales stages
 */

class SessionTracker {
    constructor() {
        this.baseUrl = 'http://localhost:5000';
        this.sessionId = this.getSessionIdFromUrl() || this.generateSessionId();
        this.trackingEnabled = true;
        this.debugMode = true;
        
        this.init();
    }
    
    init() {
        // Track email-to-session if session ID is from URL
        if (this.getSessionIdFromUrl()) {
            this.trackEmailToSession();
        }
        
        // Set up periodic session heartbeat
        this.startHeartbeat();
        
        // Track page visibility changes
        this.setupVisibilityTracking();
        
        this.log('Session tracker initialized', { sessionId: this.sessionId });
    }
    
    getSessionIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('session_id') || urlParams.get('sid');
    }
    
    generateSessionId() {
        return 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    async trackEmailToSession() {
        try {
            const response = await fetch(`${this.baseUrl}/api/session-from-email/${this.sessionId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.log('Email-to-session tracking successful', result);
            } else {
                this.log('Email-to-session tracking failed', result);
            }
        } catch (error) {
            this.log('Email-to-session tracking error', error);
        }
    }
    
    async trackStageProgression(stage, task, additionalData = {}) {
        if (!this.trackingEnabled) return;
        
        const trackingData = {
            session_id: this.sessionId,
            stage: stage,
            task: task,
            action: 'stage_progression',
            timestamp: new Date().toISOString(),
            ...additionalData
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/api/track-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trackingData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.log('Stage progression tracked', { stage, task, result });
            } else {
                this.log('Stage progression tracking failed', result);
            }
            
            return result;
        } catch (error) {
            this.log('Stage progression tracking error', error);
            return { success: false, error: error.message };
        }
    }
    
    async trackMessage(userMessage, botResponse, messageCount) {
        if (!this.trackingEnabled) return;
        
        const trackingData = {
            session_id: this.sessionId,
            action: 'message_exchange',
            user_message: userMessage,
            bot_response: botResponse,
            message_count: messageCount,
            timestamp: new Date().toISOString()
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/api/track-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trackingData)
            });
            
            const result = await response.json();
            this.log('Message tracked', { messageCount, result });
            return result;
        } catch (error) {
            this.log('Message tracking error', error);
            return { success: false, error: error.message };
        }
    }
    
    async trackConversion(conversionData = {}) {
        if (!this.trackingEnabled) return;
        
        const trackingData = {
            session_id: this.sessionId,
            stage: 'close',
            action: 'conversion_completed',
            conversion_completed: true,
            timestamp: new Date().toISOString(),
            ...conversionData
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/api/track-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trackingData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.log('Conversion tracked successfully', result);
                this.showConversionSuccess();
            } else {
                this.log('Conversion tracking failed', result);
            }
            
            return result;
        } catch (error) {
            this.log('Conversion tracking error', error);
            return { success: false, error: error.message };
        }
    }
    
    async trackSessionEnd(reason = 'user_left') {
        if (!this.trackingEnabled) return;
        
        const trackingData = {
            session_id: this.sessionId,
            action: 'session_abandoned',
            abandonment_reason: reason,
            timestamp: new Date().toISOString()
        };
        
        try {
            const response = await fetch(`${this.baseUrl}/api/track-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(trackingData)
            });
            
            const result = await response.json();
            this.log('Session end tracked', { reason, result });
            return result;
        } catch (error) {
            this.log('Session end tracking error', error);
            return { success: false, error: error.message };
        }
    }
    
    startHeartbeat() {
        // Send heartbeat every 30 seconds to track active sessions
        setInterval(() => {
            if (this.trackingEnabled && document.visibilityState === 'visible') {
                this.sendHeartbeat();
            }
        }, 30000);
    }
    
    async sendHeartbeat() {
        try {
            const response = await fetch(`${this.baseUrl}/api/track-session`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    action: 'heartbeat',
                    timestamp: new Date().toISOString()
                })
            });
            
            // Don't log heartbeats unless in debug mode
            if (this.debugMode) {
                const result = await response.json();
                this.log('Heartbeat sent', result);
            }
        } catch (error) {
            if (this.debugMode) {
                this.log('Heartbeat error', error);
            }
        }
    }
    
    setupVisibilityTracking() {
        // Track when user leaves/returns to page
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                this.trackSessionEnd('page_hidden');
            }
        });
        
        // Track when user closes window/tab
        window.addEventListener('beforeunload', () => {
            this.trackSessionEnd('page_unload');
        });
    }
    
    showConversionSuccess() {
        // Show a success message when conversion is tracked
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        message.innerHTML = '🎉 Conversion tracked successfully!';
        document.body.appendChild(message);
        
        setTimeout(() => {
            document.body.removeChild(message);
        }, 3000);
    }
    
    log(message, data = null) {
        if (this.debugMode) {
            console.log(`[SessionTracker] ${message}`, data);
        }
    }
    
    // Public methods for external use
    getSessionId() {
        return this.sessionId;
    }
    
    enableTracking() {
        this.trackingEnabled = true;
        this.log('Tracking enabled');
    }
    
    disableTracking() {
        this.trackingEnabled = false;
        this.log('Tracking disabled');
    }
    
    setDebugMode(enabled) {
        this.debugMode = enabled;
        this.log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
}

// Initialize session tracker when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.sessionTracker = new SessionTracker();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionTracker;
}
