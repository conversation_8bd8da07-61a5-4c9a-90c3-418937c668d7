#!/usr/bin/env python3
"""
Test script to verify dashboard and sales pipeline data synchronization
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_sync():
    """Test that dashboard and sales pipeline use the same data sources"""
    try:
        print("🧪 Testing Dashboard and Sales Pipeline Data Synchronization")
        print("=" * 60)
        
        # Import the application
        import unified_sales_system
        from unified_sales_system import app, db, get_unified_analytics, get_sales_pipeline_analytics
        
        with app.app_context():
            print("📊 Initializing database...")
            
            # Initialize database
            db.drop_all()
            db.create_all()
            print("✅ Database tables created")
            
            # Create sample data
            print("📝 Creating sample data...")
            success = unified_sales_system.create_sample_data()
            if success:
                print("✅ Sample data created successfully")
            else:
                print("❌ Sample data creation failed")
                return False
            
            # Test unified analytics
            print("\n📈 Testing Unified Analytics...")
            analytics = get_unified_analytics()
            print(f"  • Total contacts: {analytics['basic_metrics']['total_contacts']}")
            print(f"  • Total campaigns: {analytics['basic_metrics']['total_campaigns']}")
            print(f"  • Emails sent: {analytics['funnel_metrics']['emails_sent']}")
            print(f"  • Emails opened: {analytics['funnel_metrics']['emails_opened']}")
            print(f"  • Links clicked: {analytics['funnel_metrics']['links_clicked']}")
            print(f"  • Conversations: {analytics['funnel_metrics']['conversations_started']}")
            print(f"  • Conversions: {analytics['funnel_metrics']['conversions']}")
            
            # Test sales pipeline analytics
            print("\n🔄 Testing Sales Pipeline Analytics...")
            pipeline = get_sales_pipeline_analytics()
            print(f"  • Sessions started: {pipeline['sessions_started']}")
            print(f"  • Opening stage: {pipeline['opening_entered']}")
            print(f"  • Trust stage: {pipeline['trust_entered']}")
            print(f"  • Discovery stage: {pipeline['discovery_entered']}")
            print(f"  • Demo stage: {pipeline['demonstration_entered']}")
            print(f"  • Close stage: {pipeline['close_entered']}")
            print(f"  • Conversions: {pipeline['conversions_completed']}")
            
            # Compare data sources
            print("\n🔍 Comparing Data Sources...")
            dashboard_conversations = analytics['funnel_metrics']['conversations_started']
            pipeline_conversations = pipeline['sessions_started']
            
            dashboard_conversions = analytics['funnel_metrics']['conversions']
            pipeline_conversions = pipeline['conversions_completed']
            
            print(f"  • Dashboard conversations: {dashboard_conversations}")
            print(f"  • Pipeline conversations: {pipeline_conversations}")
            print(f"  • Conversations match: {'✅' if dashboard_conversations == pipeline_conversations else '❌'}")
            
            print(f"  • Dashboard conversions: {dashboard_conversions}")
            print(f"  • Pipeline conversions: {pipeline_conversions}")
            print(f"  • Conversions match: {'✅' if dashboard_conversions == pipeline_conversions else '❌'}")
            
            # Check if data is synchronized
            if (dashboard_conversations == pipeline_conversations and 
                dashboard_conversions == pipeline_conversions and
                analytics['basic_metrics']['total_contacts'] > 0):
                print("\n🎉 SUCCESS: Dashboard and Sales Pipeline are synchronized!")
                print("   Both systems are using the same data sources.")
                return True
            else:
                print("\n⚠️  WARNING: Data synchronization issues detected!")
                print("   Dashboard and Sales Pipeline may be using different data sources.")
                return False
                
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_sync()
    if success:
        print("\n✅ All tests passed! The dashboard should now show synchronized data.")
        print("🌐 You can start the application with: python unified_sales_system.py")
    else:
        print("\n❌ Tests failed! Please check the error messages above.")
    
    sys.exit(0 if success else 1)
