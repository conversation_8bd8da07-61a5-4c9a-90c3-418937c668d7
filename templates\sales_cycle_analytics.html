{% extends "base.html" %}

{% block title %}Sales Cycle Analytics - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-clock text-primary"></i> Sales Cycle Analytics</h1>
    <div>
        <a href="{{ url_for('sales_pipeline_analytics') }}" class="btn btn-info me-2">
            <i class="fas fa-chart-line"></i> Pipeline Analytics
        </a>
        <a href="{{ url_for('session_analytics') }}" class="btn btn-warning me-2">
            <i class="fas fa-comments"></i> Session Analytics
        </a>
        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Analytics
        </a>
    </div>
</div>

<!-- Cycle Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="metric-value">{{ cycle_data.total_sessions }}</div>
                <div class="metric-label">Total Sessions</div>
                <small><i class="fas fa-play"></i> All chat sessions</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">{{ cycle_data.final_conversions }}</div>
                <div class="metric-label">Conversions</div>
                <small><i class="fas fa-trophy"></i> Successful closes</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ "%.1f"|format(cycle_data.avg_conversion_time) }}m</div>
                <div class="metric-label">Avg Cycle Time</div>
                <small><i class="fas fa-stopwatch"></i> Time to convert</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">{{ "%.1f"|format(cycle_data.fastest_conversion) }}m</div>
                <div class="metric-label">Fastest Conversion</div>
                <small><i class="fas fa-rocket"></i> Best performance</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-bar"></i> Average Cycle Time by Stage</h5>
            <div id="timelineChart"></div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-line"></i> Sales Cycle Trends</h5>
            <div id="trendsChart"></div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-area"></i> Conversion Time Distribution</h5>
            <div id="distributionChart"></div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-waterfall"></i> Stage Drop-off Analysis</h5>
            <div id="dropoffChart"></div>
        </div>
    </div>
</div>

<!-- Detailed Cycle Metrics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-stopwatch"></i> Stage Duration Analysis</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Sales Stage</th>
                                <th>Avg Duration</th>
                                <th>Performance</th>
                                <th>Optimization</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage, duration in cycle_data.avg_cycle_times.items() %}
                            <tr>
                                <td><strong>{{ stage.title() }}</strong></td>
                                <td>{{ "%.1f"|format(duration) }} minutes</td>
                                <td>
                                    {% if duration <= 3 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif duration <= 7 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% elif duration <= 15 %}
                                        <span class="badge bg-warning">Fair</span>
                                    {% else %}
                                        <span class="badge bg-danger">Slow</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {% if duration > 15 %}
                                            Streamline process
                                        {% elif duration > 7 %}
                                            Reduce complexity
                                        {% else %}
                                            Performing well
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Drop-off Analysis</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Stage</th>
                                <th>Drop-offs</th>
                                <th>Drop-off Rate</th>
                                <th>Priority</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage, dropoffs in cycle_data.stage_dropoffs.items() %}
                            <tr>
                                <td><strong>{{ stage.title() }}</strong></td>
                                <td>{{ dropoffs }}</td>
                                <td>
                                    {% set dropoff_rate = (dropoffs / cycle_data.total_sessions * 100) if cycle_data.total_sessions > 0 else 0 %}
                                    {{ "%.1f"|format(dropoff_rate) }}%
                                </td>
                                <td>
                                    {% if dropoff_rate > 50 %}
                                        <span class="badge bg-danger">High Priority</span>
                                    {% elif dropoff_rate > 30 %}
                                        <span class="badge bg-warning">Medium Priority</span>
                                    {% else %}
                                        <span class="badge bg-success">Low Priority</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conversion Performance Insights -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb"></i> Sales Cycle Insights & Recommendations</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-trophy text-success"></i> Top Performers</h6>
                        <ul class="list-unstyled">
                            {% if cycle_data.fastest_conversion > 0 %}
                            <li><i class="fas fa-check text-success"></i> Fastest conversion: {{ "%.1f"|format(cycle_data.fastest_conversion) }} minutes</li>
                            {% endif %}
                            {% for stage, duration in cycle_data.avg_cycle_times.items() %}
                                {% if duration <= 5 %}
                                <li><i class="fas fa-check text-success"></i> {{ stage.title() }} stage: Efficient ({{ "%.1f"|format(duration) }}m)</li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6><i class="fas fa-exclamation-triangle text-warning"></i> Areas for Improvement</h6>
                        <ul class="list-unstyled">
                            {% for stage, duration in cycle_data.avg_cycle_times.items() %}
                                {% if duration > 10 %}
                                <li><i class="fas fa-times text-warning"></i> {{ stage.title() }} stage: Too slow ({{ "%.1f"|format(duration) }}m)</li>
                                {% endif %}
                            {% endfor %}
                            {% for stage, dropoffs in cycle_data.stage_dropoffs.items() %}
                                {% set dropoff_rate = (dropoffs / cycle_data.total_sessions * 100) if cycle_data.total_sessions > 0 else 0 %}
                                {% if dropoff_rate > 40 %}
                                <li><i class="fas fa-times text-warning"></i> High {{ stage }} drop-off: {{ "%.1f"|format(dropoff_rate) }}%</li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    
                    <div class="col-md-4">
                        <h6><i class="fas fa-chart-line text-info"></i> Optimization Tips</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-info"></i> Focus on stages with highest drop-offs</li>
                            <li><i class="fas fa-lightbulb text-info"></i> Reduce time in slow-performing stages</li>
                            <li><i class="fas fa-lightbulb text-info"></i> Analyze successful conversion patterns</li>
                            <li><i class="fas fa-lightbulb text-info"></i> A/B test different approaches</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Benchmarks -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-target"></i> Performance Benchmarks</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">{{ "%.1f"|format((cycle_data.final_conversions / cycle_data.total_sessions * 100) if cycle_data.total_sessions > 0 else 0) }}%</h4>
                            <p class="text-muted">Overall Conversion Rate</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ "%.1f"|format(cycle_data.avg_conversion_time) }}m</h4>
                            <p class="text-muted">Average Cycle Time</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ "%.1f"|format(cycle_data.slowest_conversion) }}m</h4>
                            <p class="text-muted">Longest Conversion</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ cycle_data.conversion_times|length }}</h4>
                            <p class="text-muted">Total Conversions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.metric-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.chart-container h5 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 10px;
}
</style>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Render timeline chart
        {% if charts.timeline %}
        var timelineData = {{ charts.timeline|safe }};
        Plotly.newPlot('timelineChart', timelineData.data, timelineData.layout, {responsive: true});
        {% endif %}

        // Render trends chart
        {% if charts.trends %}
        var trendsData = {{ charts.trends|safe }};
        Plotly.newPlot('trendsChart', trendsData.data, trendsData.layout, {responsive: true});
        {% endif %}

        // Render distribution chart
        {% if charts.distribution %}
        var distributionData = {{ charts.distribution|safe }};
        Plotly.newPlot('distributionChart', distributionData.data, distributionData.layout, {responsive: true});
        {% endif %}

        // Render dropoff chart
        {% if charts.dropoff %}
        var dropoffData = {{ charts.dropoff|safe }};
        Plotly.newPlot('dropoffChart', dropoffData.data, dropoffData.layout, {responsive: true});
        {% endif %}
    });

    // Auto-refresh every 2 minutes
    setInterval(function() {
        location.reload();
    }, 120000);
</script>
{% endblock %}
