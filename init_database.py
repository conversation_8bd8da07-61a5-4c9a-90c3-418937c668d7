#!/usr/bin/env python3
"""
Initialize Database
==================
Initialize the database with the correct schema and create test contact
"""

import os
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

def init_database():
    """Initialize database with correct schema"""
    try:
        print("🔧 Initializing Database")
        print("=" * 50)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            print("✅ Application context created")
            
            # Create all tables
            print("📋 Creating database tables...")
            db.create_all()
            print("✅ Database tables created")
            
            # Check if test contact exists
            existing_contact = Contact.query.filter_by(email='<EMAIL>').first()
            
            if existing_contact:
                print(f"📝 Contact already exists: {existing_contact.full_name}")
                print("   Updating existing contact...")
                existing_contact.first_name = 'Alex'
                existing_contact.last_name = 'Scof'
                existing_contact.phone = '(*************'
                existing_contact.company = 'Test Company'
                existing_contact.job_title = 'CEO'
                existing_contact.source = 'manual_entry'
                existing_contact.status = 'new'
                existing_contact.is_active = True
                existing_contact.do_not_email = False
                existing_contact.updated_at = datetime.utcnow()
                db.session.commit()
                print("✅ Contact updated successfully")
            else:
                print("📝 Creating new test contact...")
                contact = Contact(
                    first_name='Alex',
                    last_name='Scof',
                    email='<EMAIL>',
                    phone='(*************',
                    company='Test Company',
                    job_title='CEO',
                    source='manual_entry',
                    status='new'
                )
                
                db.session.add(contact)
                db.session.commit()
                print("✅ Test contact created successfully")
                print(f"   ID: {contact.id}")
                print(f"   Name: {contact.full_name}")
                print(f"   Email: {contact.email}")
            
            # Verify contact exists and can be queried
            test_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if test_contact:
                print("\n✅ Contact verification successful!")
                print(f"   Contact ID: {test_contact.id}")
                print(f"   Full Name: {test_contact.full_name}")
                print(f"   Email: {test_contact.email}")
                print(f"   Company: {test_contact.company}")
                print(f"   Job Title: {test_contact.job_title}")
                print(f"   Phone: {test_contact.phone}")
                print(f"   Status: {test_contact.status}")
                print(f"   Active: {test_contact.is_active}")
                print(f"   Do Not Email: {test_contact.do_not_email}")
                return True
            else:
                print("❌ Contact verification failed!")
                return False
                
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contact_operations():
    """Test basic contact operations"""
    try:
        print("\n🧪 Testing Contact Operations")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Test 1: Count contacts
            total_contacts = Contact.query.count()
            print(f"✅ Total contacts: {total_contacts}")
            
            # Test 2: Count active contacts
            active_contacts = Contact.query.filter_by(is_active=True).count()
            print(f"✅ Active contacts: {active_contacts}")
            
            # Test 3: Count email-enabled contacts
            email_enabled = Contact.query.filter_by(is_active=True, do_not_email=False).count()
            print(f"✅ Email-enabled contacts: {email_enabled}")
            
            # Test 4: Get our test contact
            test_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if test_contact:
                print(f"✅ Test contact found: {test_contact.full_name}")
                return True
            else:
                print("❌ Test contact not found")
                return False
                
    except Exception as e:
        print(f"❌ Contact operations test failed: {e}")
        return False

def main():
    """Main initialization function"""
    print("🚀 DATABASE INITIALIZATION")
    print("=" * 60)
    print("Initializing database with simplified Contact model")
    print("=" * 60)
    
    # Step 1: Initialize database
    init_success = init_database()
    
    # Step 2: Test contact operations
    if init_success:
        test_success = test_contact_operations()
    else:
        test_success = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 INITIALIZATION SUMMARY")
    print("=" * 60)
    print(f"Database Initialization: {'✅ SUCCESS' if init_success else '❌ FAILED'}")
    print(f"Contact Operations: {'✅ WORKING' if test_success else '❌ FAILED'}")
    
    if all([init_success, test_success]):
        print("\n🎉 DATABASE INITIALIZATION COMPLETED!")
        print("\n✅ Database schema is correct")
        print("✅ Test contact created")
        print("✅ Contact operations working")
        
        print("\n🚀 READY FOR EMAIL CAMPAIGNS!")
        print("=" * 40)
        print("Next steps:")
        print("1. Start the application: python start_app_simple.py")
        print("2. Go to: http://localhost:5000/campaigns")
        print("3. Create a new campaign")
        print("4. Select 'All contacts' as recipients")
        print("5. Send the campaign")
        print("6. Check <EMAIL> for the email")
        
        print("\n✅ Email system confirmed working (SMTP test passed)")
        print("✅ Contact system now fixed")
        print("✅ Database schema matches Contact model")
        print("✅ Ready to test complete campaign flow!")
    else:
        print("\n❌ INITIALIZATION HAD ISSUES")
        print("Check the error messages above")
    
    return all([init_success, test_success])

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
