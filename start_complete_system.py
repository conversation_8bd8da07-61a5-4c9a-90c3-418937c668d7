#!/usr/bin/env python3
"""
24Seven Assistants - Complete Sales System Launcher
===================================================
This script launches both the Sales Department Dashboard and the Sales Chatbot
in separate processes so they run together seamlessly.

Usage:
    python start_complete_system.py

This will start:
- Sales Department Dashboard on http://localhost:5000
- Sales Chatbot (Sarah) on http://localhost:7860

Press Ctrl+C to stop both systems.
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

def print_banner():
    """Print startup banner"""
    print("=" * 80)
    print("🚀 24Seven Assistants - Complete Sales System")
    print("=" * 80)
    print("📊 Sales Department Dashboard: Full CRM and Analytics")
    print("🤖 AI Sales Chatbot (Sarah): 5-Stage Sales Process Automation")
    print("📧 SMTP Email Campaigns: Automated Follow-up System")
    print("🎯 Sales Stage Tracking: Real-time Pipeline Management")
    print("📈 Analytics Dashboard: Performance Metrics and Insights")
    print("=" * 80)

def check_dependencies():
    """Check if required files exist"""
    required_files = [
        'simple_sales_app.py',
        'app.py',
        'salesbot/bot.py',
        'salesbot/config_data.py',
        'templates/dashboard.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all files are in the correct location.")
        return False
    
    print("✅ All required files found.")
    return True

def start_sales_dashboard():
    """Start the Sales Department Dashboard"""
    print("📊 Starting Sales Department Dashboard...")
    try:
        process = subprocess.Popen(
            [sys.executable, 'simple_sales_app.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Read and display output
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print(f"[DASHBOARD] {line.strip()}")
                if "Running on" in line:
                    print("✅ Sales Department Dashboard is ready!")
                    break
        
        return process
    except Exception as e:
        print(f"❌ Failed to start Sales Department Dashboard: {e}")
        return None

def start_sales_chatbot():
    """Start the Sales Chatbot"""
    print("🤖 Starting Sales Chatbot (Sarah)...")
    try:
        process = subprocess.Popen(
            [sys.executable, 'app.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Give it time to start
        time.sleep(3)
        print("✅ Sales Chatbot (Sarah) is ready!")
        
        return process
    except Exception as e:
        print(f"❌ Failed to start Sales Chatbot: {e}")
        return None

def monitor_process(process, name):
    """Monitor a process and restart if it crashes"""
    while True:
        if process.poll() is not None:
            print(f"⚠️  {name} has stopped. Exit code: {process.returncode}")
            break
        time.sleep(1)

def main():
    """Main function to start both systems"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    print("\n🔧 Starting system components...")
    
    # Start Sales Department Dashboard
    dashboard_process = start_sales_dashboard()
    if not dashboard_process:
        print("❌ Failed to start Sales Department Dashboard")
        sys.exit(1)
    
    # Wait a moment for dashboard to fully start
    time.sleep(3)
    
    # Start Sales Chatbot
    chatbot_process = start_sales_chatbot()
    if not chatbot_process:
        print("❌ Failed to start Sales Chatbot")
        dashboard_process.terminate()
        sys.exit(1)
    
    # Start monitoring threads
    dashboard_monitor = threading.Thread(
        target=monitor_process, 
        args=(dashboard_process, "Sales Department Dashboard"),
        daemon=True
    )
    chatbot_monitor = threading.Thread(
        target=monitor_process, 
        args=(chatbot_process, "Sales Chatbot"),
        daemon=True
    )
    
    dashboard_monitor.start()
    chatbot_monitor.start()
    
    # Print success message
    print("\n" + "=" * 80)
    print("🎉 24Seven Assistants Sales System is now running!")
    print("=" * 80)
    print("📊 Sales Department Dashboard: http://localhost:5000")
    print("   - Contact Management")
    print("   - Opportunity Tracking") 
    print("   - Email Campaigns")
    print("   - Analytics Dashboard")
    print("   - Sales Pipeline Management")
    print()
    print("🤖 AI Sales Chatbot (Sarah): http://localhost:7860")
    print("   - 5-Stage Sales Process")
    print("   - Objection Handling")
    print("   - Trust Building")
    print("   - Real-time Stage Progression")
    print("   - SambaNova AI Integration")
    print()
    print("🔗 Integration Features:")
    print("   - Shared contact database")
    print("   - Automated activity logging")
    print("   - Sales stage synchronization")
    print("   - Performance analytics")
    print("=" * 80)
    print("⏹️  Press Ctrl+C to stop both systems")
    print("=" * 80)
    
    # Set up signal handler for graceful shutdown
    def signal_handler(sig, frame):
        print("\n\n🛑 Shutting down 24Seven Assistants Sales System...")
        print("📊 Stopping Sales Department Dashboard...")
        dashboard_process.terminate()
        print("🤖 Stopping Sales Chatbot...")
        chatbot_process.terminate()
        
        # Wait for processes to terminate
        dashboard_process.wait()
        chatbot_process.wait()
        
        print("✅ All systems stopped successfully.")
        print("👋 Thank you for using 24Seven Assistants Sales System!")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Keep the main thread alive
    try:
        while True:
            time.sleep(1)
            # Check if both processes are still running
            if dashboard_process.poll() is not None:
                print("❌ Sales Department Dashboard has stopped unexpectedly")
                break
            if chatbot_process.poll() is not None:
                print("❌ Sales Chatbot has stopped unexpectedly")
                break
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)

if __name__ == "__main__":
    main()
