"""
Enhanced SMTP Service with IMAP Sent Folder Integration
======================================================
SMTP service that automatically saves sent emails to IMAP sent folder.
"""

import smtplib
import imaplib
import ssl
import time
import logging
from datetime import datetime
from typing import Dict, List, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

logger = logging.getLogger(__name__)

class EnhancedSMTPService:
    """Enhanced SMTP service that saves sent emails to IMAP sent folder"""

    def __init__(self, config: Dict):
        """Initialize enhanced SMTP service with IMAP integration"""
        # SMTP configuration
        self.smtp_server = config.get('MAIL_SERVER', 'smtp.gmail.com')
        self.smtp_port = config.get('MAIL_PORT', 587)
        self.use_tls = config.get('MAIL_USE_TLS', True)
        self.use_ssl = config.get('MAIL_USE_SSL', False)
        self.username = config.get('MAIL_USERNAME')
        self.password = config.get('MAIL_PASSWORD')
        self.default_sender = config.get('MAIL_DEFAULT_SENDER')

        # IMAP configuration for saving sent emails
        self.imap_server = config.get('IMAP_SERVER', self.smtp_server)
        self.imap_port = config.get('IMAP_PORT', 993)
        self.imap_use_ssl = config.get('IMAP_USE_SSL', True)
        self.sent_folder = config.get('IMAP_SENT_FOLDER', 'INBOX.Sent')

        # Connection settings
        self.timeout = config.get('SMTP_TIMEOUT', 30)
        self.retry_count = config.get('SMTP_RETRY_COUNT', 3)
        self.retry_delay = config.get('SMTP_RETRY_DELAY', 5)

        # Fallback configurations
        self.fallback_configs = config.get('FALLBACK_CONFIGS', [])

        # Settings
        self.save_to_sent = config.get('SAVE_TO_SENT_FOLDER', True)
        self.delay_seconds = config.get('EMAIL_DELAY_SECONDS', 2)

        # Validate configuration
        if not all([self.username, self.password, self.default_sender]):
            raise ValueError("SMTP configuration incomplete. Need username, password, and default sender.")

    def create_smtp_connection(self) -> smtplib.SMTP:
        """Create and authenticate SMTP connection with fallback support"""
        # Try primary configuration first
        primary_config = {
            'server': self.smtp_server,
            'port': self.smtp_port,
            'use_ssl': self.use_ssl,
            'use_tls': self.use_tls,
            'name': 'Primary Configuration'
        }

        # Combine primary with fallback configurations
        all_configs = [primary_config] + self.fallback_configs

        last_error = None
        for config in all_configs:
            try:
                logger.info(f"Attempting SMTP connection: {config['name']} - {config['server']}:{config['port']}")

                if config['use_ssl']:
                    # Use SSL connection (port 465)
                    context = ssl.create_default_context()
                    server = smtplib.SMTP_SSL(
                        config['server'],
                        config['port'],
                        context=context,
                        timeout=self.timeout
                    )
                else:
                    # Use regular connection
                    server = smtplib.SMTP(config['server'], config['port'], timeout=self.timeout)
                    if config['use_tls']:
                        context = ssl.create_default_context()
                        server.starttls(context=context)

                # Authenticate
                server.login(self.username, self.password)

                logger.info(f"SMTP connection successful: {config['name']} - {config['server']}:{config['port']}")
                return server

            except Exception as e:
                last_error = e
                logger.warning(f"SMTP connection failed for {config['name']}: {str(e)}")
                continue

        # If all configurations failed
        error_msg = f"All SMTP configurations failed. Last error: {str(last_error)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    def create_imap_connection(self) -> imaplib.IMAP4:
        """Create and authenticate IMAP connection with timeout"""
        try:
            if self.imap_use_ssl:
                context = ssl.create_default_context()
                # Note: imaplib doesn't support timeout parameter directly
                server = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
            else:
                server = imaplib.IMAP4(self.imap_server, self.imap_port)
                server.starttls()

            # Set socket timeout
            server.sock.settimeout(self.timeout)

            # Authenticate
            server.login(self.username, self.password)

            logger.info(f"IMAP connection established to {self.imap_server}:{self.imap_port}")
            return server

        except Exception as e:
            logger.error(f"Failed to create IMAP connection: {str(e)}")
            raise

    def save_to_sent_folder(self, message: MIMEMultipart) -> bool:
        """Save email message to IMAP sent folder"""
        if not self.save_to_sent:
            return True

        try:
            with self.create_imap_connection() as imap_server:
                # Select sent folder
                status, response = imap_server.select(self.sent_folder)
                if status != 'OK':
                    # Try to create the folder if it doesn't exist
                    imap_server.create(self.sent_folder)
                    status, response = imap_server.select(self.sent_folder)

                if status == 'OK':
                    # Add message to sent folder
                    message_bytes = message.as_bytes()
                    imap_server.append(self.sent_folder, '\\Seen', None, message_bytes)
                    logger.info(f"Email saved to sent folder: {self.sent_folder}")
                    return True
                else:
                    logger.warning(f"Could not access sent folder: {self.sent_folder}")
                    return False

        except Exception as e:
            logger.error(f"Failed to save email to sent folder: {str(e)}")
            return False

    def send_email(self,
                   to_email: str,
                   subject: str,
                   html_body: str,
                   text_body: str = None,
                   from_email: str = None,
                   from_name: str = None,
                   reply_to: str = None,
                   attachments: List[Dict] = None) -> Tuple[bool, str, str]:
        """
        Send email and save to sent folder

        Returns:
            Tuple of (success: bool, message_id: str, error_message: str)
        """
        try:
            # Set defaults
            from_email = from_email or self.default_sender
            from_name = from_name or "24Seven Assistants Sales Team"

            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = to_email
            msg['Date'] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S +0000')

            if reply_to:
                msg['Reply-To'] = reply_to

            # Add text version if provided
            if text_body:
                text_part = MIMEText(text_body, 'plain', 'utf-8')
                msg.attach(text_part)

            # Add HTML version
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)

            # Send email via SMTP
            with self.create_smtp_connection() as smtp_server:
                smtp_server.send_message(msg)
                message_id = msg['Message-ID']

                logger.info(f"Email sent successfully to {to_email}")

            # Save to sent folder via IMAP
            sent_saved = self.save_to_sent_folder(msg)
            if sent_saved:
                logger.info(f"Email saved to sent folder for {to_email}")
            else:
                logger.warning(f"Email sent but not saved to sent folder for {to_email}")

            return True, message_id, ""

        except Exception as e:
            error_msg = f"Failed to send email to {to_email}: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def send_bulk_emails(self,
                        email_list: List[Dict],
                        delay_between_emails: int = None) -> List[Dict]:
        """
        Send bulk emails with delay between sends and save to sent folder

        Args:
            email_list: List of email dictionaries
            delay_between_emails: Seconds to wait between emails

        Returns:
            List of result dictionaries
        """
        delay = delay_between_emails or self.delay_seconds
        results = []

        try:
            # Create single SMTP connection for all emails
            with self.create_smtp_connection() as smtp_server:
                # Create single IMAP connection for saving sent emails
                imap_server = None
                if self.save_to_sent:
                    try:
                        imap_server = self.create_imap_connection()
                        # Select sent folder
                        status, response = imap_server.select(self.sent_folder)
                        if status != 'OK':
                            imap_server.create(self.sent_folder)
                            imap_server.select(self.sent_folder)
                    except Exception as e:
                        logger.warning(f"Could not establish IMAP connection: {e}")
                        imap_server = None

                try:
                    for i, email_data in enumerate(email_list):
                        try:
                            # Send individual email
                            success, message_id, error_msg = self._send_single_email_with_servers(
                                smtp_server, imap_server, email_data
                            )

                            result = {
                                'index': i,
                                'to_email': email_data.get('to_email'),
                                'success': success,
                                'message_id': message_id,
                                'error_message': error_msg,
                                'sent_at': datetime.utcnow().isoformat(),
                                'saved_to_sent': success and imap_server is not None
                            }
                            results.append(result)

                            # Log progress
                            if (i + 1) % 10 == 0:
                                logger.info(f"Sent {i + 1}/{len(email_list)} emails")

                            # Delay between emails (except for last email)
                            if i < len(email_list) - 1 and delay > 0:
                                time.sleep(delay)

                        except Exception as e:
                            result = {
                                'index': i,
                                'to_email': email_data.get('to_email'),
                                'success': False,
                                'message_id': '',
                                'error_message': str(e),
                                'sent_at': datetime.utcnow().isoformat(),
                                'saved_to_sent': False
                            }
                            results.append(result)
                            logger.error(f"Failed to send email {i + 1}: {str(e)}")

                finally:
                    # Close IMAP connection
                    if imap_server:
                        try:
                            imap_server.close()
                            imap_server.logout()
                        except:
                            pass

            logger.info(f"Bulk email sending completed: {len(results)} emails processed")
            return results

        except Exception as e:
            logger.error(f"Bulk email sending failed: {str(e)}")
            return results

    def _send_single_email_with_servers(self, smtp_server: smtplib.SMTP, imap_server, email_data: Dict) -> Tuple[bool, str, str]:
        """Send single email using existing server connections"""
        try:
            # Extract email data
            to_email = email_data['to_email']
            subject = email_data['subject']
            html_body = email_data['html_body']
            text_body = email_data.get('text_body')
            from_email = email_data.get('from_email', self.default_sender)
            from_name = email_data.get('from_name', "24Seven Assistants Sales Team")
            reply_to = email_data.get('reply_to')

            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = to_email
            msg['Date'] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S +0000')

            if reply_to:
                msg['Reply-To'] = reply_to

            # Add content
            if text_body:
                text_part = MIMEText(text_body, 'plain', 'utf-8')
                msg.attach(text_part)

            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

            # Send via SMTP
            smtp_server.send_message(msg)
            message_id = msg['Message-ID']

            # Save to sent folder via IMAP
            if imap_server:
                try:
                    message_bytes = msg.as_bytes()
                    imap_server.append(self.sent_folder, '\\Seen', None, message_bytes)
                except Exception as e:
                    logger.warning(f"Could not save to sent folder: {e}")

            return True, message_id, ""

        except Exception as e:
            return False, "", str(e)

    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict):
        """Add attachment to email message"""
        try:
            filename = attachment['filename']
            content = attachment['content']
            content_type = attachment.get('content_type', 'application/octet-stream')

            part = MIMEBase(*content_type.split('/'))
            part.set_payload(content)
            encoders.encode_base64(part)

            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )

            msg.attach(part)

        except Exception as e:
            logger.error(f"Failed to add attachment {attachment.get('filename')}: {str(e)}")

    def test_connection(self) -> Tuple[bool, str]:
        """Test both SMTP and IMAP connections"""
        try:
            # Test SMTP
            with self.create_smtp_connection() as smtp_server:
                smtp_status = "SMTP connection successful"

            # Test IMAP
            with self.create_imap_connection() as imap_server:
                status, response = imap_server.select(self.sent_folder)
                if status == 'OK':
                    imap_status = f"IMAP connection successful, sent folder accessible"
                else:
                    imap_status = f"IMAP connection successful, but sent folder not accessible"

            message = f"{smtp_status}. {imap_status}."
            logger.info(message)
            return True, message

        except Exception as e:
            error_msg = f"Connection test failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
