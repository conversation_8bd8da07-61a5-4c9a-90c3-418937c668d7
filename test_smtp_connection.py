#!/usr/bin/env python3
"""
Test SMTP Connection with Updated Password
=========================================
This script tests the SMTP connection using the updated Gmail app password.
"""

import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dotenv import load_dotenv

def test_smtp_connection():
    """Test SMTP connection with environment variables"""
    
    # Load environment variables
    load_dotenv()
    
    print("🔍 Testing SMTP Connection with Environment Variables")
    print("=" * 60)
    
    # Get configuration from environment
    config = {
        'MAIL_SERVER': os.getenv('MAIL_SERVER', 'smtp.gmail.com'),
        'MAIL_PORT': int(os.getenv('MAIL_PORT', '587')),
        'MAIL_USE_TLS': os.getenv('MAIL_USE_TLS', 'true').lower() == 'true',
        'MAIL_USE_SSL': os.getenv('MAIL_USE_SSL', 'false').lower() == 'true',
        'MAIL_USERNAME': os.getenv('MAIL_USERNAME', '<EMAIL>'),
        'MAIL_PASSWORD': os.getenv('MAIL_PASSWORD', 'wkff biod dzyv obon'),
        'MAIL_DEFAULT_SENDER': os.getenv('MAIL_DEFAULT_SENDER', '<EMAIL>'),
    }
    
    print("📧 Configuration:")
    print(f"   Server: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
    print(f"   Username: {config['MAIL_USERNAME']}")
    print(f"   Password: {'*' * len(config['MAIL_PASSWORD'])}")
    print(f"   TLS: {config['MAIL_USE_TLS']}")
    print(f"   SSL: {config['MAIL_USE_SSL']}")
    print()
    
    try:
        print("🔗 Step 1: Creating SMTP connection...")
        
        if config['MAIL_USE_SSL']:
            # SSL connection (port 465)
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(config['MAIL_SERVER'], config['MAIL_PORT'], context=context, timeout=30)
            print("✅ SSL connection established")
        else:
            # Regular connection with STARTTLS (port 587)
            server = smtplib.SMTP(config['MAIL_SERVER'], config['MAIL_PORT'], timeout=30)
            if config['MAIL_USE_TLS']:
                context = ssl.create_default_context()
                server.starttls(context=context)
                print("✅ TLS connection established")
            else:
                print("✅ Plain connection established")
        
        print("🔐 Step 2: Authenticating...")
        server.login(config['MAIL_USERNAME'], config['MAIL_PASSWORD'])
        print("✅ Authentication successful!")
        
        print("📧 Step 3: Sending test email...")
        
        # Create test email
        msg = MIMEMultipart()
        msg['From'] = config['MAIL_DEFAULT_SENDER']
        msg['To'] = config['MAIL_USERNAME']  # Send to self
        msg['Subject'] = "SMTP Test - 24Seven Assistants"
        
        body = """
        <h2>🎉 SMTP Test Successful!</h2>
        <p>This email confirms that the SMTP configuration is working correctly with the updated Gmail app password.</p>
        <p><strong>Configuration Details:</strong></p>
        <ul>
            <li>Server: {server}:{port}</li>
            <li>Username: {username}</li>
            <li>TLS: {tls}</li>
            <li>SSL: {ssl}</li>
        </ul>
        <p>Email campaigns should now work properly!</p>
        <br>
        <p>Best regards,<br>24Seven Assistants Team</p>
        """.format(
            server=config['MAIL_SERVER'],
            port=config['MAIL_PORT'],
            username=config['MAIL_USERNAME'],
            tls=config['MAIL_USE_TLS'],
            ssl=config['MAIL_USE_SSL']
        )
        
        msg.attach(MIMEText(body, 'html'))
        
        # Send email
        server.send_message(msg)
        print("✅ Test email sent successfully!")
        
        # Close connection
        server.quit()
        print("✅ Connection closed")
        
        print("\n" + "=" * 60)
        print("🎉 SMTP TEST PASSED!")
        print("✅ Your email configuration is working correctly.")
        print("✅ Email campaigns should now send successfully.")
        print("=" * 60)
        
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check if the Gmail app password is correct")
        print("2. Verify 2-factor authentication is enabled")
        print("3. Make sure 'Less secure app access' is disabled (use app passwords)")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your internet connection")
        print("2. Verify SMTP server and port are correct")
        print("3. Check firewall settings")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    test_smtp_connection()
