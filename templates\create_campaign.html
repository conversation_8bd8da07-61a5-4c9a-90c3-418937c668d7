{% extends "base.html" %}

{% block title %}Create Campaign - 24Seven Assistants{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-envelope-open-text text-primary"></i> Create Email Campaign</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('create_campaign') }}">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Campaign Details -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Campaign Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Campaign Name *</label>
                                        <input type="text" class="form-control" id="name" name="name" required
                                               placeholder="e.g., Q1 2024 Introduction Campaign">
                                    </div>

                                    <div class="mb-3">
                                        <label for="template" class="form-label">Email Template *</label>
                                        <select class="form-select" id="template" name="template" required onchange="updateTemplatePreview()">
                                            <option value="">Select Template</option>
                                            {% for template in templates %}
                                            <option value="{{ template.name }}" data-subject="{{ template.subject }}">
                                                {{ template.display_name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <div class="form-text">Choose the email template for this campaign</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="target_audience" class="form-label">Target Audience Description</label>
                                        <input type="text" class="form-control" id="target_audience" name="target_audience"
                                               placeholder="e.g., New leads from website, Technology companies, etc.">
                                        <div class="form-text">Describe who this campaign is targeting</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Selection -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-users"></i> Contact Selection</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Choose Recipients</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="recipient_type" id="all_contacts" value="all" checked onchange="toggleRecipientOptions()">
                                            <label class="form-check-label" for="all_contacts">
                                                <strong>All Active Contacts</strong>
                                                <small class="text-muted d-block">Send to all active contacts (excluding do-not-email)</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="recipient_type" id="specific_contacts" value="specific" onchange="toggleRecipientOptions()">
                                            <label class="form-check-label" for="specific_contacts">
                                                <strong>Specific Contacts</strong>
                                                <small class="text-muted d-block">Choose individual contacts manually</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="recipient_type" id="contact_groups" value="groups" onchange="toggleRecipientOptions()">
                                            <label class="form-check-label" for="contact_groups">
                                                <strong>Contact Groups</strong>
                                                <small class="text-muted d-block">Send to one or more contact groups</small>
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="recipient_type" id="filtered_contacts" value="filtered" onchange="toggleRecipientOptions()">
                                            <label class="form-check-label" for="filtered_contacts">
                                                <strong>Filtered Contacts</strong>
                                                <small class="text-muted d-block">Use advanced filters to target specific contacts</small>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Specific Contacts Selection -->
                                    <div id="specific_contacts_section" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">Select Contacts</label>
                                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                                <div class="mb-2">
                                                    <input type="text" class="form-control form-control-sm" id="contact_search" placeholder="Search contacts..." onkeyup="filterContacts()">
                                                </div>
                                                <div id="contacts_list">
                                                    {% if contacts %}
                                                        {% for contact in contacts %}
                                                        <div class="form-check contact-item">
                                                            <input class="form-check-input" type="checkbox" name="selected_contacts" value="{{ contact.id }}" id="contact_{{ contact.id }}">
                                                            <label class="form-check-label" for="contact_{{ contact.id }}">
                                                                <strong>{{ contact.full_name }}</strong>
                                                                <small class="text-muted d-block">{{ contact.email }} - {{ contact.company or 'No company' }}</small>
                                                            </label>
                                                        </div>
                                                        {% endfor %}
                                                    {% else %}
                                                        <p class="text-muted">No contacts available</p>
                                                    {% endif %}
                                                </div>
                                                <div class="mt-2">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllContacts()">Select All</button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllContacts()">Clear All</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact Groups Selection -->
                                    <div id="contact_groups_section" style="display: none;">
                                        <div class="mb-3">
                                            <label class="form-label">Select Contact Groups</label>
                                            <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                                {% if groups %}
                                                    {% for group in groups %}
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="selected_groups" value="{{ group.id }}" id="group_{{ group.id }}" onchange="updateContactsFromGroups()">
                                                        <label class="form-check-label" for="group_{{ group.id }}">
                                                            <strong>{{ group.name }}</strong>
                                                            <small class="text-muted d-block">{{ group.description or 'No description' }} - {{ group.contact_count or 0 }} contacts</small>
                                                        </label>
                                                    </div>
                                                    {% endfor %}
                                                {% else %}
                                                    <p class="text-muted">No contact groups available. <a href="{{ url_for('create_group') }}">Create a group</a></p>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Group Contact Preview -->
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <label class="form-label mb-0">Contacts in Selected Groups</label>
                                                <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleGroupContactsPreview()" id="togglePreviewBtn">
                                                    <i class="fas fa-eye"></i> Show Contacts
                                                </button>
                                            </div>
                                            <div id="group_contacts_preview" style="display: none;">
                                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                                                    <div id="group_contacts_list">
                                                        <p class="text-muted text-center">Select groups to see contacts</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Filtered Contacts Section -->
                                    <div id="filtered_contacts_section" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="status_filter" class="form-label">Contact Status</label>
                                                    <select class="form-select" id="status_filter" name="status_filter" onchange="calculateRecipients()">
                                                        <option value="">All Statuses</option>
                                                        <option value="new">New Contacts</option>
                                                        <option value="contacted">Previously Contacted</option>
                                                        <option value="qualified">Qualified Leads</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="source_filter" class="form-label">Lead Source</label>
                                                    <select class="form-select" id="source_filter" name="source_filter" onchange="calculateRecipients()">
                                                        <option value="">All Sources</option>
                                                        <option value="website">Website</option>
                                                        <option value="referral">Referral</option>
                                                        <option value="social_media">Social Media</option>
                                                        <option value="trade_show">Trade Show</option>
                                                        <option value="cold_outreach">Cold Outreach</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="industry_filter" class="form-label">Industry</label>
                                                    <select class="form-select" id="industry_filter" name="industry_filter" onchange="calculateRecipients()">
                                                        <option value="">All Industries</option>
                                                        <option value="technology">Technology</option>
                                                        <option value="healthcare">Healthcare</option>
                                                        <option value="finance">Finance</option>
                                                        <option value="retail">Retail</option>
                                                        <option value="manufacturing">Manufacturing</option>
                                                        <option value="education">Education</option>
                                                        <option value="real_estate">Real Estate</option>
                                                        <option value="consulting">Consulting</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="company_size_filter" class="form-label">Company Size</label>
                                                    <select class="form-select" id="company_size_filter" name="company_size_filter" onchange="calculateRecipients()">
                                                        <option value="">All Sizes</option>
                                                        <option value="1-10">1-10 employees</option>
                                                        <option value="11-50">11-50 employees</option>
                                                        <option value="51-200">51-200 employees</option>
                                                        <option value="201-500">201-500 employees</option>
                                                        <option value="501-1000">501-1000 employees</option>
                                                        <option value="1000+">1000+ employees</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="min_lead_score" class="form-label">Minimum Lead Score</label>
                                                    <input type="range" class="form-range" id="min_lead_score" name="min_lead_score"
                                                           min="0" max="100" value="0" oninput="updateLeadScoreDisplay(); calculateRecipients();">
                                                    <div class="d-flex justify-content-between">
                                                        <small class="text-muted">0</small>
                                                        <small class="text-muted" id="leadScoreValue">0</small>
                                                        <small class="text-muted">100</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="decision_makers_only" name="decision_makers_only" onchange="calculateRecipients()">
                                                        <label class="form-check-label" for="decision_makers_only">
                                                            Decision Makers Only
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="exclude_customers" name="exclude_customers" checked onchange="calculateRecipients()">
                                                        <label class="form-check-label" for="exclude_customers">
                                                            Exclude Existing Customers
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Estimated Recipients:</strong> <span id="estimatedRecipients">Calculating...</span>
                                        <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="calculateRecipients()">
                                            <i class="fas fa-calculator"></i> Calculate
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Scheduling -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-clock"></i> Scheduling</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="send_option" id="send_now" value="now" checked>
                                            <label class="form-check-label" for="send_now">
                                                Send Immediately
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="send_option" id="send_later" value="later">
                                            <label class="form-check-label" for="send_later">
                                                Schedule for Later
                                            </label>
                                        </div>
                                    </div>

                                    <div id="scheduleOptions" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="schedule_date" class="form-label">Date</label>
                                                    <input type="date" class="form-control" id="schedule_date" name="schedule_date">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="schedule_time" class="form-label">Time</label>
                                                    <input type="time" class="form-control" id="schedule_time" name="schedule_time">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <!-- Template Preview -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-eye"></i> Template Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div id="templatePreview">
                                        <div class="text-center text-muted py-4">
                                            <i class="fas fa-envelope fa-3x mb-3"></i>
                                            <p>Select a template to see preview</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Daily Sending Limits -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-calendar-day"></i> Daily Sending Limits</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="daily_send_limit" class="form-label">Emails per Day</label>
                                        <select class="form-select" id="daily_send_limit" name="daily_send_limit" onchange="updateEstimatedDays()">
                                            <option value="50">50 emails per day</option>
                                            <option value="100" selected>100 emails per day</option>
                                            <option value="500">500 emails per day</option>
                                            <option value="1000">1,000 emails per day</option>
                                        </select>
                                        <div class="form-text">Choose your daily sending limit to control delivery rate</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="send_schedule" class="form-label">Sending Schedule</label>
                                        <select class="form-select" id="send_schedule" name="send_schedule" onchange="toggleScheduleOptions()">
                                            <option value="immediate">Send immediately (respects daily limits)</option>
                                            <option value="scheduled">Schedule for specific date/time</option>
                                            <option value="daily_batch">Daily batches (spread over multiple days)</option>
                                        </select>
                                    </div>

                                    <div id="scheduled_options" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="scheduled_start_date" class="form-label">Start Date</label>
                                                    <input type="date" class="form-control" id="scheduled_start_date" name="scheduled_start_date">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="scheduled_start_time" class="form-label">Start Time</label>
                                                    <input type="time" class="form-control" id="scheduled_start_time" name="scheduled_start_time" value="09:00">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert alert-info" id="sending_estimate">
                                        <i class="fas fa-info-circle"></i>
                                        <span id="estimated_days_text">Campaign will be sent based on daily limits.</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Campaign Settings -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-cogs"></i> Advanced Settings</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="sender_name" class="form-label">Sender Name</label>
                                        <input type="text" class="form-control" id="sender_name" name="sender_name"
                                               value="24Seven Assistants Sales Team">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="track_opens" name="track_opens" checked>
                                            <label class="form-check-label" for="track_opens">
                                                Track Email Opens
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="track_clicks" name="track_clicks" checked>
                                            <label class="form-check-label" for="track_clicks">
                                                Track Link Clicks
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('campaigns_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Campaigns
                        </a>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" onclick="previewCampaign()">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Campaign
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update template preview
    function updateTemplatePreview() {
        const select = document.getElementById('template');
        const preview = document.getElementById('templatePreview');
        const selectedOption = select.options[select.selectedIndex];

        if (selectedOption.value) {
            const subject = selectedOption.getAttribute('data-subject');
            preview.innerHTML = `
                <div class="border rounded p-3">
                    <h6 class="text-primary mb-2">Subject:</h6>
                    <p class="small mb-3">${subject}</p>
                    <h6 class="text-primary mb-2">Template:</h6>
                    <p class="small text-muted">${selectedOption.text}</p>
                    <div class="mt-3">
                        <span class="badge bg-info">Professional Design</span>
                        <span class="badge bg-success">Mobile Responsive</span>
                        <span class="badge bg-warning">Personalized</span>
                    </div>
                </div>
            `;
        } else {
            preview.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-envelope fa-3x mb-3"></i>
                    <p>Select a template to see preview</p>
                </div>
            `;
        }
    }

    // Update lead score display
    function updateLeadScoreDisplay() {
        const slider = document.getElementById('min_lead_score');
        const display = document.getElementById('leadScoreValue');
        display.textContent = slider.value;
    }

    // Toggle recipient options based on selection
    function toggleRecipientOptions() {
        const recipientType = document.querySelector('input[name="recipient_type"]:checked').value;

        // Hide all sections first
        document.getElementById('specific_contacts_section').style.display = 'none';
        document.getElementById('contact_groups_section').style.display = 'none';
        document.getElementById('filtered_contacts_section').style.display = 'none';

        // Show relevant section
        if (recipientType === 'specific') {
            document.getElementById('specific_contacts_section').style.display = 'block';
        } else if (recipientType === 'groups') {
            document.getElementById('contact_groups_section').style.display = 'block';
        } else if (recipientType === 'filtered') {
            document.getElementById('filtered_contacts_section').style.display = 'block';
        }

        // Recalculate recipients
        calculateRecipients();
    }

    // Filter contacts in the specific contacts list
    function filterContacts() {
        const searchTerm = document.getElementById('contact_search').value.toLowerCase();
        const contactItems = document.querySelectorAll('.contact-item');

        contactItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Select all contacts
    function selectAllContacts() {
        const checkboxes = document.querySelectorAll('input[name="selected_contacts"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.closest('.contact-item').style.display !== 'none') {
                checkbox.checked = true;
            }
        });
        calculateRecipients();
    }

    // Clear all contact selections
    function clearAllContacts() {
        const checkboxes = document.querySelectorAll('input[name="selected_contacts"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        calculateRecipients();
    }

    // Calculate estimated recipients
    function calculateRecipients() {
        const recipientType = document.querySelector('input[name="recipient_type"]:checked').value;
        let estimated = 0;
        let description = '';

        if (recipientType === 'all') {
            // Make API call to get all active contacts count
            fetch('/api/contacts/count?active=true&do_not_email=false')
                .then(response => response.json())
                .then(data => {
                    estimated = data.count || 0;
                    updateRecipientsDisplay(estimated, 'All active contacts');
                })
                .catch(() => {
                    // Fallback to estimated count
                    estimated = Math.floor(Math.random() * 50) + 20;
                    updateRecipientsDisplay(estimated, 'All active contacts (estimated)');
                });
        } else if (recipientType === 'specific') {
            const selectedContacts = document.querySelectorAll('input[name="selected_contacts"]:checked');
            estimated = selectedContacts.length;
            description = 'Manually selected contacts';
            updateRecipientsDisplay(estimated, description);
        } else if (recipientType === 'groups') {
            const selectedGroups = document.querySelectorAll('input[name="selected_groups"]:checked');
            estimated = 0;
            selectedGroups.forEach(checkbox => {
                const label = checkbox.closest('.form-check').querySelector('label');
                const text = label.textContent;
                const match = text.match(/(\d+) contacts/);
                if (match) {
                    estimated += parseInt(match[1]);
                }
            });
            description = `Contacts from ${selectedGroups.length} selected group(s)`;
            updateRecipientsDisplay(estimated, description);
        } else if (recipientType === 'filtered') {
            // Make API call with filters
            const filters = {
                status: document.getElementById('status_filter').value,
                source: document.getElementById('source_filter').value,
                industry: document.getElementById('industry_filter').value,
                company_size: document.getElementById('company_size_filter').value,
                min_lead_score: document.getElementById('min_lead_score').value,
                decision_makers_only: document.getElementById('decision_makers_only').checked,
                exclude_customers: document.getElementById('exclude_customers').checked
            };

            fetch('/api/contacts/count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(filters)
            })
            .then(response => response.json())
            .then(data => {
                estimated = data.count || 0;
                updateRecipientsDisplay(estimated, 'Contacts matching filters');
            })
            .catch(() => {
                // Fallback to estimated count
                estimated = Math.floor(Math.random() * 100) + 10;
                updateRecipientsDisplay(estimated, 'Contacts matching filters (estimated)');
            });
        }
    }

    // Update recipients display
    function updateRecipientsDisplay(count, description) {
        document.getElementById('estimatedRecipients').innerHTML = `
            <strong>${count} contacts</strong> - ${description}
            <small class="text-muted d-block">This count will be used for the campaign</small>
        `;
        updateEstimatedDays();
    }

    // Update estimated days based on daily limit and recipients
    function updateEstimatedDays() {
        const dailyLimit = parseInt(document.getElementById('daily_send_limit').value);
        const recipientsText = document.getElementById('estimatedRecipients').textContent;
        const recipientsMatch = recipientsText.match(/(\d+) contacts/);

        if (recipientsMatch) {
            const totalRecipients = parseInt(recipientsMatch[1]);
            const estimatedDays = Math.ceil(totalRecipients / dailyLimit);

            let message = '';
            if (estimatedDays === 1) {
                message = `Campaign will be sent in 1 day (${totalRecipients} emails at ${dailyLimit} per day).`;
            } else {
                message = `Campaign will be sent over ${estimatedDays} days (${totalRecipients} emails at ${dailyLimit} per day).`;
            }

            document.getElementById('estimated_days_text').textContent = message;
        }
    }

    // Toggle schedule options
    function toggleScheduleOptions() {
        const scheduleType = document.getElementById('send_schedule').value;
        const scheduledOptions = document.getElementById('scheduled_options');

        if (scheduleType === 'scheduled') {
            scheduledOptions.style.display = 'block';
            // Set default date to tomorrow
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('scheduled_start_date').value = tomorrow.toISOString().split('T')[0];
        } else {
            scheduledOptions.style.display = 'none';
        }
    }

    // Toggle schedule options
    document.querySelectorAll('input[name="send_option"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const scheduleOptions = document.getElementById('scheduleOptions');
            if (this.value === 'later') {
                scheduleOptions.style.display = 'block';
                // Set default date to tomorrow
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                document.getElementById('schedule_date').value = tomorrow.toISOString().split('T')[0];
                document.getElementById('schedule_time').value = '09:00';
            } else {
                scheduleOptions.style.display = 'none';
            }
        });
    });

    // Preview campaign
    function previewCampaign() {
        const formData = new FormData(document.querySelector('form'));
        const campaign = {};
        for (let [key, value] of formData.entries()) {
            campaign[key] = value;
        }

        const preview = `
Campaign Preview:
Name: ${campaign.name || 'Untitled Campaign'}
Template: ${campaign.template || 'None selected'}
Target Audience: ${campaign.target_audience || 'Not specified'}
Send Option: ${campaign.send_option === 'later' ? 'Scheduled' : 'Immediate'}
Estimated Recipients: ${document.getElementById('estimatedRecipients').textContent}
        `;

        alert(preview);
    }

    // Update contacts from selected groups
    function updateContactsFromGroups() {
        const selectedGroups = document.querySelectorAll('input[name="selected_groups"]:checked');
        const groupIds = Array.from(selectedGroups).map(checkbox => parseInt(checkbox.value));

        if (groupIds.length === 0) {
            // Clear the preview if no groups selected
            document.getElementById('group_contacts_list').innerHTML = '<p class="text-muted text-center">Select groups to see contacts</p>';

            // Remove any existing help text
            const existingHelp = document.querySelector('.group-help-text');
            if (existingHelp) {
                existingHelp.remove();
            }

            // Clear specific contacts filtering
            clearSpecificContactsFromGroups();
            calculateRecipients();
            return;
        }

        // Show loading state
        document.getElementById('group_contacts_list').innerHTML = '<p class="text-muted text-center"><i class="fas fa-spinner fa-spin"></i> Loading contacts...</p>';

        // Fetch contacts for selected groups
        fetch('/api/contacts/by-groups', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ group_ids: groupIds })
        })
        .then(response => response.json())
        .then(data => {
            const contactsList = document.getElementById('group_contacts_list');

            if (data.contacts && data.contacts.length > 0) {
                let contactsHtml = '';
                data.contacts.forEach(contact => {
                    contactsHtml += `
                        <div class="d-flex justify-content-between align-items-center py-1 border-bottom">
                            <div>
                                <strong>${contact.full_name}</strong>
                                <small class="text-muted d-block">${contact.email}</small>
                            </div>
                            <small class="text-muted">${contact.company}</small>
                        </div>
                    `;
                });
                contactsList.innerHTML = contactsHtml;

                // Update the specific contacts section to show only these contacts
                updateSpecificContactsFromGroups(data.contacts);

                // Add a helpful message about switching to specific contacts
                if (data.contacts.length > 0) {
                    const helpText = document.createElement('div');
                    helpText.className = 'alert alert-info mt-2';
                    helpText.innerHTML = `
                        <i class="fas fa-info-circle"></i>
                        <strong>Tip:</strong> Switch to "Specific Contacts" to see and modify which contacts from these groups will receive the campaign.
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="switchToSpecificContacts()">
                            <i class="fas fa-users"></i> View Contacts
                        </button>
                    `;

                    // Remove any existing help text
                    const existingHelp = document.querySelector('.group-help-text');
                    if (existingHelp) {
                        existingHelp.remove();
                    }

                    helpText.classList.add('group-help-text');
                    document.getElementById('group_contacts_preview').appendChild(helpText);
                }
            } else {
                contactsList.innerHTML = '<p class="text-muted text-center">No contacts found in selected groups</p>';
                // Clear specific contacts if no group contacts
                clearSpecificContactsFromGroups();
            }

            // Recalculate recipients
            calculateRecipients();
        })
        .catch(error => {
            console.error('Error fetching group contacts:', error);
            document.getElementById('group_contacts_list').innerHTML = '<p class="text-danger text-center">Error loading contacts</p>';
        });
    }

    // Update specific contacts section to show only contacts from selected groups
    function updateSpecificContactsFromGroups(groupContacts) {
        const contactsList = document.getElementById('contacts_list');
        const allContactItems = contactsList.querySelectorAll('.contact-item');

        // Create a set of contact IDs from groups for quick lookup
        const groupContactIds = new Set(groupContacts.map(contact => contact.id.toString()));

        // Show/hide contacts based on group membership
        allContactItems.forEach(item => {
            const checkbox = item.querySelector('input[name="selected_contacts"]');
            const contactId = checkbox.value;

            if (groupContactIds.has(contactId)) {
                item.style.display = 'block';
                // Add visual indicator that this contact is from selected groups
                if (!item.querySelector('.group-indicator')) {
                    const indicator = document.createElement('span');
                    indicator.className = 'badge bg-info group-indicator ms-2';
                    indicator.textContent = 'In Group';
                    item.querySelector('label').appendChild(indicator);
                }
            } else {
                item.style.display = 'none';
                // Remove group indicator if contact is not in groups
                const indicator = item.querySelector('.group-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }
        });

        // Update search functionality to work with filtered contacts
        const searchInput = document.getElementById('contact_search');
        if (searchInput.value) {
            filterContacts(); // Re-apply search filter
        }
    }

    // Clear specific contacts filtering when no groups are selected
    function clearSpecificContactsFromGroups() {
        const contactsList = document.getElementById('contacts_list');
        const allContactItems = contactsList.querySelectorAll('.contact-item');

        // Show all contacts and remove group indicators
        allContactItems.forEach(item => {
            item.style.display = 'block';
            const indicator = item.querySelector('.group-indicator');
            if (indicator) {
                indicator.remove();
            }
        });
    }

    // Toggle group contacts preview visibility
    function toggleGroupContactsPreview() {
        const preview = document.getElementById('group_contacts_preview');
        const button = document.getElementById('togglePreviewBtn');

        if (preview.style.display === 'none') {
            preview.style.display = 'block';
            button.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Contacts';
        } else {
            preview.style.display = 'none';
            button.innerHTML = '<i class="fas fa-eye"></i> Show Contacts';
        }
    }

    // Switch to specific contacts mode to show filtered contacts
    function switchToSpecificContacts() {
        // Select the specific contacts radio button
        document.getElementById('specific_contacts').checked = true;

        // Trigger the change event to show the specific contacts section
        toggleRecipientOptions();

        // Scroll to the specific contacts section
        document.getElementById('specific_contacts_section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Add a brief highlight effect
        const section = document.getElementById('specific_contacts_section');
        section.style.border = '2px solid #007bff';
        section.style.borderRadius = '5px';
        setTimeout(() => {
            section.style.border = '';
            section.style.borderRadius = '';
        }, 2000);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        calculateRecipients();
        updateLeadScoreDisplay();
        updateEstimatedDays();

        // Set default scheduled date to tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('scheduled_start_date').value = tomorrow.toISOString().split('T')[0];

        // Add event listener to recipient type changes to handle group filtering
        document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value !== 'groups') {
                    // Clear group filtering when switching away from groups
                    clearSpecificContactsFromGroups();
                }
            });
        });
    });
</script>
{% endblock %}
