#!/usr/bin/env python3
"""
Start the 24Seven Assistants Sales System with Enhanced Debug System
"""

import os
import sys
from datetime import datetime

def main():
    print("🚀 24Seven Assistants Sales System with Enhanced Debug System")
    print("=" * 70)
    print(f"📅 Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Set debug environment variables
    os.environ.setdefault('DEBUG_REQUESTS', 'true')
    os.environ.setdefault('DEBUG_SQL', 'true')
    os.environ.setdefault('DEBUG_PERFORMANCE', 'true')
    os.environ.setdefault('DEBUG_ERRORS', 'true')
    os.environ.setdefault('LOG_LEVEL', 'INFO')
    os.environ.setdefault('SLOW_REQUEST_THRESHOLD', '2.0')
    os.environ.setdefault('SLOW_QUERY_THRESHOLD', '1.0')
    
    print("🔧 Debug System Configuration:")
    print(f"   📡 Request Logging: {os.environ.get('DEBUG_REQUESTS', 'false')}")
    print(f"   🗄️  SQL Logging: {os.environ.get('DEBUG_SQL', 'false')}")
    print(f"   ⚡ Performance Monitoring: {os.environ.get('DEBUG_PERFORMANCE', 'false')}")
    print(f"   🚨 Error Tracking: {os.environ.get('DEBUG_ERRORS', 'false')}")
    print(f"   📊 Log Level: {os.environ.get('LOG_LEVEL', 'INFO')}")
    print(f"   🐌 Slow Request Threshold: {os.environ.get('SLOW_REQUEST_THRESHOLD', '2.0')}s")
    print(f"   🐌 Slow Query Threshold: {os.environ.get('SLOW_QUERY_THRESHOLD', '1.0')}s")
    print()
    
    try:
        print("📦 Loading debug system...")
        from debug_system import init_debug_system, DebugConfig
        print("   ✅ Debug system loaded successfully")
        
        print("📦 Loading debug routes...")
        from debug_routes import register_debug_routes
        print("   ✅ Debug routes loaded successfully")
        
        print("📦 Loading unified sales system...")
        from unified_sales_system import app
        print("   ✅ Unified sales system loaded successfully")
        
        print()
        print("🎯 System Ready!")
        print("=" * 70)
        print("📍 Main Application: http://localhost:5000")
        print("🔧 Debug Dashboard: http://localhost:5000/debug")
        print("📊 Analytics: http://localhost:5000/analytics")
        print("📧 Email Campaigns: http://localhost:5000/campaigns")
        print("👥 Contacts: http://localhost:5000/contacts")
        print()
        print("🔧 Debug Features Available:")
        print("   • Request/Response Logging")
        print("   • Error Tracking & Reporting")
        print("   • Performance Monitoring")
        print("   • Database Debugging")
        print("   • Email Testing Tools")
        print("   • System Health Monitoring")
        print()
        print("⏹️  Press Ctrl+C to stop the server")
        print("=" * 70)
        
        # Start the application
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n👋 Sales Department System stopped by user")
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure all required files are present:")
        print("   - debug_system.py")
        print("   - debug_routes.py")
        print("   - unified_sales_system.py")
        print("2. Check for syntax errors in the files")
        print("3. Verify all dependencies are installed")
        
    except Exception as e:
        print(f"❌ Startup Error: {e}")
        import traceback
        traceback.print_exc()
        print("\nPlease check the error details above and try again.")

if __name__ == '__main__':
    main()
