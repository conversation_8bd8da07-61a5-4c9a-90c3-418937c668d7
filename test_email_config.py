#!/usr/bin/env python3
"""
Test Email Configuration
========================
Test the email configuration with the .env file settings
"""

def test_email_config():
    """Test email configuration loading"""
    try:
        print("🧪 Testing Email Configuration")
        print("=" * 50)
        
        # Step 1: Load environment variables manually
        print("📁 Step 1: Loading .env file manually")
        print("-" * 30)
        
        env_vars = {}
        try:
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
            
            print(f"✅ Loaded {len(env_vars)} environment variables from .env")
            
            # Show email-related variables
            email_vars = {k: v for k, v in env_vars.items() if 'MAIL' in k}
            print("📧 Email configuration from .env:")
            for key, value in email_vars.items():
                if 'PASSWORD' in key:
                    print(f"   {key}: {'*' * len(value)}")
                else:
                    print(f"   {key}: {value}")
                    
        except FileNotFoundError:
            print("❌ .env file not found")
            return False
        
        # Step 2: Test SMTP connection with .env settings
        print("\n🔗 Step 2: Testing SMTP with .env settings")
        print("-" * 30)
        
        import smtplib
        import ssl
        
        # Get configuration from .env
        smtp_server = env_vars.get('MAIL_SERVER', 'smtp.gmail.com')
        smtp_port = int(env_vars.get('MAIL_PORT', '587'))
        use_tls = env_vars.get('MAIL_USE_TLS', 'true').lower() == 'true'
        use_ssl = env_vars.get('MAIL_USE_SSL', 'false').lower() == 'true'
        username = env_vars.get('MAIL_USERNAME', '')
        password = env_vars.get('MAIL_PASSWORD', '')
        
        print(f"Server: {smtp_server}:{smtp_port}")
        print(f"SSL: {use_ssl}, TLS: {use_tls}")
        print(f"Username: {username}")
        print(f"Password: {'*' * len(password)}")
        
        if not username or not password:
            print("❌ Missing username or password")
            return False
        
        try:
            if use_ssl:
                # SSL connection (port 465)
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(smtp_server, smtp_port, context=context, timeout=30)
            else:
                # Regular connection (port 587)
                server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
                if use_tls:
                    context = ssl.create_default_context()
                    server.starttls(context=context)
            
            print("✅ SMTP connection established")
            
            # Test authentication
            server.login(username, password)
            print("✅ SMTP authentication successful")
            
            server.quit()
            print("🎉 SMTP test passed!")
            
            return True
            
        except Exception as e:
            print(f"❌ SMTP test failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_flask_app():
    """Test email configuration within Flask app context"""
    try:
        print("\n🌐 Step 3: Testing with Flask App")
        print("-" * 30)
        
        # Manually set environment variables
        import os
        
        # Load .env manually
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        
        print("✅ Environment variables set")
        
        # Import and test Flask app
        from unified_sales_system import app
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Check Flask configuration
            print("📧 Flask email configuration:")
            email_config_keys = ['MAIL_SERVER', 'MAIL_PORT', 'MAIL_USE_SSL', 'MAIL_USE_TLS', 'MAIL_USERNAME', 'MAIL_PASSWORD']
            for key in email_config_keys:
                value = app.config.get(key, 'NOT SET')
                if 'PASSWORD' in key:
                    print(f"   {key}: {'*' * len(str(value)) if value else 'NOT SET'}")
                else:
                    print(f"   {key}: {value}")
            
            # Test email system
            from email_system.config import get_email_config
            config = get_email_config()
            
            print("\n📧 Email system configuration:")
            print(f"   Server: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
            print(f"   Username: {config['MAIL_USERNAME']}")
            print(f"   SSL: {config['MAIL_USE_SSL']}, TLS: {config['MAIL_USE_TLS']}")
            
            # Test SMTP service
            from email_system.enhanced_smtp_service import EnhancedSMTPService
            smtp_service = EnhancedSMTPService(config)
            
            success, message = smtp_service.test_connection()
            if success:
                print(f"✅ Enhanced SMTP service test passed: {message}")
                return True
            else:
                print(f"❌ Enhanced SMTP service test failed: {message}")
                return False
                
    except Exception as e:
        print(f"❌ Flask app test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Email Configuration Test")
    print("Testing Gmail SMTP with .env configuration")
    print("=" * 60)
    
    # Test 1: Direct SMTP test
    smtp_success = test_email_config()
    
    # Test 2: Flask app test
    if smtp_success:
        flask_success = test_with_flask_app()
    else:
        flask_success = False
        print("⏭️ Skipping Flask test due to SMTP failure")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 EMAIL CONFIGURATION TEST SUMMARY")
    print("=" * 60)
    print(f"Direct SMTP Test: {'✅ PASSED' if smtp_success else '❌ FAILED'}")
    print(f"Flask App Test: {'✅ PASSED' if flask_success else '❌ FAILED'}")
    
    if smtp_success and flask_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The email configuration is working correctly.")
        print("Email campaigns should now work properly.")
    elif smtp_success:
        print("\n⚠️ SMTP works but Flask integration needs attention")
        print("Check the Flask app configuration loading")
    else:
        print("\n❌ SMTP configuration failed")
        print("Check your .env file settings and Gmail credentials")
        print("\nTroubleshooting:")
        print("1. Verify Gmail username and app password")
        print("2. Ensure 2-factor authentication is enabled")
        print("3. Use app-specific password, not regular password")
        print("4. Check if 'Less secure app access' is enabled (if not using app password)")
    
    return smtp_success and flask_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
