#!/usr/bin/env python3
"""
Check Database Schema
"""

import sqlite3
import os

def check_database_schema():
    """Check the database schema for missing tables"""
    print("🗄️ Checking Database Schema")
    print("=" * 30)
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Found {len(tables)} tables:")
        for table in sorted(tables):
            print(f"   • {table}")
        
        # Check for required tables
        required_tables = [
            'contacts',
            'email_campaigns', 
            'email_logs',
            'activities',
            'sales_stages'
        ]
        
        missing_tables = []
        for table in required_tables:
            if table not in tables:
                missing_tables.append(table)
                print(f"❌ Missing table: {table}")
            else:
                print(f"✅ Found table: {table}")
        
        # Check activities table schema if it exists
        if 'activities' in tables:
            print(f"\n📊 Activities table schema:")
            cursor.execute("PRAGMA table_info(activities)")
            columns = cursor.fetchall()
            for column in columns:
                print(f"   {column[1]:<20} {column[2]:<15}")
        
        # Check email_logs table schema if it exists
        if 'email_logs' in tables:
            print(f"\n📧 Email logs table schema:")
            cursor.execute("PRAGMA table_info(email_logs)")
            columns = cursor.fetchall()
            for column in columns:
                print(f"   {column[1]:<20} {column[2]:<15}")
        
        conn.close()
        
        if missing_tables:
            print(f"\n❌ Missing {len(missing_tables)} required tables")
            return False
        else:
            print(f"\n✅ All required tables present")
            return True
            
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        return False

def test_view_campaign_query():
    """Test the specific query used in view_campaign"""
    print(f"\n🔍 Testing View Campaign Query")
    print("=" * 30)
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test campaign query
        cursor.execute("SELECT id, name, status FROM email_campaigns LIMIT 5")
        campaigns = cursor.fetchall()
        
        print(f"📊 Found {len(campaigns)} campaigns:")
        for campaign in campaigns:
            print(f"   ID: {campaign[0]}, Name: {campaign[1]}, Status: {campaign[2]}")
        
        if campaigns:
            test_campaign_id = campaigns[0][0]
            print(f"\n🎯 Testing queries for campaign ID: {test_campaign_id}")
            
            # Test email_logs query
            try:
                cursor.execute("SELECT contact_id FROM email_logs WHERE campaign_id = ? LIMIT 5", (test_campaign_id,))
                email_logs = cursor.fetchall()
                print(f"   📧 Email logs: {len(email_logs)} records")
            except Exception as e:
                print(f"   ❌ Email logs query failed: {str(e)}")
            
            # Test activities query
            try:
                cursor.execute("SELECT id, activity_type, subject FROM activities WHERE session_id = ? LIMIT 5", (str(test_campaign_id),))
                activities = cursor.fetchall()
                print(f"   📝 Activities: {len(activities)} records")
            except Exception as e:
                print(f"   ❌ Activities query failed: {str(e)}")
            
            # Test contacts query
            try:
                cursor.execute("SELECT id, first_name, last_name, email FROM contacts WHERE is_active = 1 LIMIT 5")
                contacts = cursor.fetchall()
                print(f"   👥 Active contacts: {len(contacts)} records")
            except Exception as e:
                print(f"   ❌ Contacts query failed: {str(e)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Query test error: {str(e)}")
        return False

def create_missing_tables():
    """Create any missing tables"""
    print(f"\n🔧 Creating Missing Tables")
    print("=" * 25)
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create activities table if missing
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contact_id INTEGER,
                session_id VARCHAR(100),
                activity_type VARCHAR(50) NOT NULL,
                subject VARCHAR(255),
                description TEXT,
                extra_data TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contact_id) REFERENCES contacts (id)
            )
        """)
        print("✅ Activities table created/verified")
        
        # Create email_logs table if missing
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS email_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id INTEGER NOT NULL,
                contact_id INTEGER NOT NULL,
                recipient_email VARCHAR(255) NOT NULL,
                recipient_name VARCHAR(255),
                subject VARCHAR(255) NOT NULL,
                email_body_html TEXT,
                email_body_text TEXT,
                sent_at DATETIME,
                delivered_at DATETIME,
                opened_at DATETIME,
                first_clicked_at DATETIME,
                replied_at DATETIME,
                bounced_at DATETIME,
                unsubscribed_at DATETIME,
                status VARCHAR(50) DEFAULT 'pending',
                error_message TEXT,
                open_count INTEGER DEFAULT 0,
                click_count INTEGER DEFAULT 0,
                user_agent VARCHAR(500),
                ip_address VARCHAR(45),
                message_id VARCHAR(255),
                thread_id VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
                FOREIGN KEY (contact_id) REFERENCES contacts (id)
            )
        """)
        print("✅ Email logs table created/verified")
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activities_contact_id ON activities(contact_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_activities_session_id ON activities(session_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_email_logs_campaign_id ON email_logs(campaign_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_email_logs_contact_id ON email_logs(contact_id)")
        print("✅ Indexes created/verified")
        
        conn.commit()
        conn.close()
        
        print("✅ Database schema updated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Schema update error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Database Schema Checker")
    print("=" * 40)
    
    # Check current schema
    schema_ok = check_database_schema()
    
    # Test queries
    query_ok = test_view_campaign_query()
    
    # Create missing tables if needed
    if not schema_ok:
        print("\n🔧 Attempting to fix schema...")
        create_missing_tables()
        
        # Re-check
        print("\n🔍 Re-checking schema...")
        schema_ok = check_database_schema()
    
    print("\n" + "=" * 40)
    print("📋 SUMMARY")
    print("=" * 40)
    print(f"Database Schema:    {'✅ OK' if schema_ok else '❌ ISSUES'}")
    print(f"Query Tests:        {'✅ OK' if query_ok else '❌ ISSUES'}")
    
    if schema_ok and query_ok:
        print("\n🎉 Database is ready for view campaign functionality!")
    else:
        print("\n⚠️ Database issues detected. Please check the errors above.")
