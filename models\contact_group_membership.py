"""
Contact Group Membership Model
=============================
Manages the many-to-many relationship between contacts and groups.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

# Import db from a separate module to avoid circular imports
try:
    from unified_sales_system import db
except ImportError:
    # Fallback for when this module is imported before the main app
    db = None

class ContactGroupMembership(db.Model if db else object):
    """Contact group membership model for many-to-many relationship"""
    
    __tablename__ = 'contact_group_memberships'
    
    id = Column(Integer, primary_key=True)
    
    # Foreign Keys
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=False, index=True)
    group_id = Column(Integer, ForeignKey('contact_groups.id'), nullable=False, index=True)
    
    # Tracking
    added_at = Column(DateTime, default=datetime.utcnow, index=True)
    added_by = Column(String(100), nullable=True)  # User or system that added the contact
    
    # Relationships
    contact = relationship("Contact", backref="group_memberships")
    group = relationship("ContactGroup", backref="contact_memberships")
    
    # Unique constraint to prevent duplicate memberships
    __table_args__ = (
        db.UniqueConstraint('contact_id', 'group_id', name='unique_contact_group'),
    ) if db else ()
    
    def __repr__(self):
        return f'<ContactGroupMembership contact_id={self.contact_id} group_id={self.group_id}>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'contact_id': self.contact_id,
            'group_id': self.group_id,
            'added_at': self.added_at.isoformat() if self.added_at else None,
            'added_by': self.added_by,
            'contact_name': self.contact.full_name if self.contact else None,
            'contact_email': self.contact.email if self.contact else None,
            'group_name': self.group.name if self.group else None
        }
