#!/usr/bin/env python3
"""
Test Input Contrast Fix
======================
Test to verify the input field contrast has been fixed in email templates.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_email_template_input_contrast():
    """Test that input field has proper contrast styling"""
    try:
        from email_system.email_templates import EmailTemplateManager
        
        print("✅ EmailTemplateManager imported successfully")
        
        # Create template manager
        template_manager = EmailTemplateManager()
        print("✅ Template manager created")
        
        # Test context
        test_context = {
            'contact_name': '<PERSON>',
            'company_name': 'Test Company',
            'agent_name': '<PERSON>',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-123',
            'session_id': 'test-123'
        }
        
        # Render customer_support template
        rendered = template_manager.render_template('customer_support', test_context)
        print("✅ Customer support template rendered")
        
        # Check if the new input styling is present
        html_content = rendered['html_body']
        
        # Look for the new styling
        if 'border: 2px solid #007bff' in html_content:
            print("✅ NEW STYLING FOUND: Blue border (#007bff)")
        else:
            print("❌ NEW STYLING NOT FOUND: Blue border missing")
            
        if 'background: white' in html_content:
            print("✅ NEW STYLING FOUND: White background")
        else:
            print("❌ NEW STYLING NOT FOUND: White background missing")
            
        if 'color: #333' in html_content:
            print("✅ NEW STYLING FOUND: Dark text (#333)")
        else:
            print("❌ NEW STYLING NOT FOUND: Dark text missing")
            
        if 'box-shadow: 0 2px 4px rgba(0,0,0,0.1)' in html_content:
            print("✅ NEW STYLING FOUND: Box shadow")
        else:
            print("❌ NEW STYLING NOT FOUND: Box shadow missing")
        
        # Check for old styling (should not be present)
        if 'border: 2px solid #dee2e6' in html_content:
            print("❌ OLD STYLING STILL PRESENT: Light gray border (#dee2e6)")
        else:
            print("✅ OLD STYLING REMOVED: Light gray border not found")
        
        # Save a sample HTML file to inspect
        with open('test_email_output.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print("✅ Sample HTML saved to 'test_email_output.html'")
        
        print("\n🎉 INPUT CONTRAST TEST COMPLETED!")
        print("📧 Check 'test_email_output.html' to see the actual email template")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing Input Field Contrast Fix...")
    print("=" * 50)
    test_email_template_input_contrast()
