<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Tracking Test - 24Seven Assistants</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .btn-test {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1><i class="fas fa-chart-line text-primary"></i> Session Tracking Frontend Test</h1>
                <p class="lead">Test the enhanced session tracking system from the frontend</p>

                <!-- Session Info -->
                <div class="test-section">
                    <h3><i class="fas fa-info-circle"></i> Session Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Session ID:</strong> <code id="sessionId">Loading...</code></p>
                            <p><strong>Current Stage:</strong> <span id="currentStage" class="badge bg-primary">opening</span></p>
                            <p><strong>Message Count:</strong> <span id="messageCount">0</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Engagement Level:</strong> <span id="engagementLevel" class="badge bg-secondary">low</span></p>
                            <p><strong>Tracking Status:</strong> <span class="status-indicator status-pending"></span><span id="trackingStatus">Initializing...</span></p>
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="test-section">
                    <h3><i class="fas fa-play-circle"></i> Test Controls</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Session Management</h5>
                            <button class="btn btn-success btn-test" onclick="startSession()">
                                <i class="fas fa-play"></i> Start Session
                            </button>
                            <button class="btn btn-warning btn-test" onclick="simulateEmailClick()">
                                <i class="fas fa-envelope"></i> Simulate Email Click
                            </button>
                            <button class="btn btn-danger btn-test" onclick="endSession()">
                                <i class="fas fa-stop"></i> End Session
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h5>Stage Progression</h5>
                            <button class="btn btn-primary btn-test" onclick="progressToStage('trust')">
                                <i class="fas fa-handshake"></i> Trust
                            </button>
                            <button class="btn btn-info btn-test" onclick="progressToStage('discovery')">
                                <i class="fas fa-search"></i> Discovery
                            </button>
                            <button class="btn btn-warning btn-test" onclick="progressToStage('demonstration')">
                                <i class="fas fa-presentation"></i> Demo
                            </button>
                            <button class="btn btn-success btn-test" onclick="progressToStage('close')">
                                <i class="fas fa-trophy"></i> Close
                            </button>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h5>Interaction Simulation</h5>
                            <button class="btn btn-secondary btn-test" onclick="simulateMessage()">
                                <i class="fas fa-comment"></i> Send Message
                            </button>
                            <button class="btn btn-warning btn-test" onclick="simulateObjection()">
                                <i class="fas fa-exclamation-triangle"></i> Handle Objection
                            </button>
                            <button class="btn btn-success btn-test" onclick="simulateConversion()">
                                <i class="fas fa-check-circle"></i> Complete Conversion
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Analytics Links -->
                <div class="test-section">
                    <h3><i class="fas fa-chart-bar"></i> View Analytics Dashboards</h3>
                    <div class="row">
                        <div class="col-md-3">
                            <a href="http://localhost:5000/analytics" target="_blank" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-chart-line"></i> Main Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="http://localhost:5000/analytics/sales-pipeline" target="_blank" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-funnel-dollar"></i> Sales Pipeline
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="http://localhost:5000/analytics/sales-cycle" target="_blank" class="btn btn-info w-100 mb-2">
                                <i class="fas fa-clock"></i> Sales Cycle
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="http://localhost:5000/analytics/sessions" target="_blank" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-comments"></i> Session Analytics
                            </a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <p class="text-muted text-center">
                                <i class="fas fa-info-circle"></i>
                                <strong>Sales Pipeline:</strong> Shows progression through opening → trust → discovery → demo → close stages<br>
                                <strong>Sales Cycle:</strong> Analyzes timing, drop-offs, and conversion patterns across all stages
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Log Output -->
                <div class="test-section">
                    <h3><i class="fas fa-terminal"></i> Tracking Log</h3>
                    <button class="btn btn-sm btn-secondary" onclick="clearLog()">
                        <i class="fas fa-trash"></i> Clear Log
                    </button>
                    <div id="logOutput" class="log-output">
                        <div>Session tracking test initialized...</div>
                        <div>Waiting for user interaction...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Session tracking variables
        let sessionId = null;
        let currentStage = 'opening';
        let messageCount = 0;
        let engagementLevel = 'low';

        // Initialize session tracking
        function initializeSession() {
            // Generate session ID (or get from URL)
            const urlParams = new URLSearchParams(window.location.search);
            sessionId = urlParams.get('session_id') || 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            document.getElementById('sessionId').textContent = sessionId;
            log('Session initialized with ID: ' + sessionId);

            updateUI();
        }

        // Update UI elements
        function updateUI() {
            document.getElementById('currentStage').textContent = currentStage;
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('engagementLevel').textContent = engagementLevel;

            // Update engagement badge color
            const engagementBadge = document.getElementById('engagementLevel');
            engagementBadge.className = 'badge bg-' + (
                engagementLevel === 'high' ? 'success' :
                engagementLevel === 'medium' ? 'warning' : 'secondary'
            );
        }

        // Logging function
        function log(message, data = null) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');

            if (data) {
                logEntry.innerHTML = `[${timestamp}] ${message}: ${JSON.stringify(data, null, 2)}`;
            } else {
                logEntry.innerHTML = `[${timestamp}] ${message}`;
            }

            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '';
        }

        // API call function
        async function trackSession(data) {
            try {
                const response = await fetch('http://localhost:5000/api/track-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        ...data
                    })
                });

                const result = await response.json();

                if (result.success) {
                    log('✅ Tracking successful', result);

                    // Update UI with response data
                    if (result.engagement_level) {
                        engagementLevel = result.engagement_level;
                    }
                    if (result.total_messages) {
                        messageCount = result.total_messages;
                    }
                    updateUI();

                    document.getElementById('trackingStatus').textContent = 'Active';
                    document.querySelector('.status-indicator').className = 'status-indicator status-success';
                } else {
                    log('❌ Tracking failed', result);
                    document.getElementById('trackingStatus').textContent = 'Error';
                    document.querySelector('.status-indicator').className = 'status-indicator status-error';
                }

                return result;
            } catch (error) {
                log('❌ Network error', error.message);
                document.getElementById('trackingStatus').textContent = 'Network Error';
                document.querySelector('.status-indicator').className = 'status-indicator status-error';
                return { success: false, error: error.message };
            }
        }

        // Test functions
        async function startSession() {
            log('🚀 Starting session...');
            await trackSession({
                stage: 'opening',
                task: 'greeting',
                contact_name: 'Frontend Test User',
                contact_email: '<EMAIL>',
                action: 'conversation_started',
                message_count: 1,
                bot_response: 'Hello! Welcome to 24Seven Assistants.'
            });
        }

        async function simulateEmailClick() {
            log('📧 Simulating email click...');
            try {
                const response = await fetch(`http://localhost:5000/api/session-from-email/${sessionId}`);
                const result = await response.json();
                log('Email click tracking', result);
            } catch (error) {
                log('❌ Email click tracking error', error.message);
            }
        }

        async function progressToStage(stage) {
            log(`🎯 Progressing to ${stage} stage...`);
            currentStage = stage;
            messageCount += 2;

            await trackSession({
                stage: stage,
                task: `${stage}_task`,
                action: 'stage_progression',
                message_count: messageCount,
                user_message: `User message for ${stage} stage`,
                bot_response: `Bot response for ${stage} stage`
            });
        }

        async function simulateMessage() {
            log('💬 Simulating message exchange...');
            messageCount += 2;

            await trackSession({
                stage: currentStage,
                task: `${currentStage}_interaction`,
                action: 'message_exchange',
                message_count: messageCount,
                user_message: 'This is a test user message',
                bot_response: 'This is a test bot response'
            });
        }

        async function simulateObjection() {
            log('⚠️ Simulating objection handling...');
            messageCount += 2;

            await trackSession({
                stage: currentStage,
                task: 'handling_objection',
                action: 'objection_handled',
                message_count: messageCount,
                user_message: 'This seems too expensive for our budget',
                bot_response: 'I understand your concern. Let me show you our ROI calculator...'
            });
        }

        async function simulateConversion() {
            log('🎉 Simulating conversion...');
            currentStage = 'close';
            messageCount += 2;

            await trackSession({
                stage: 'close',
                task: 'conversion_completed',
                action: 'stage_progression',
                contact_email: '<EMAIL>',
                conversion_completed: true,
                conversion_value: 2500.00,
                message_count: messageCount
            });
        }

        async function endSession() {
            log('🛑 Ending session...');
            await trackSession({
                action: 'session_abandoned',
                abandonment_reason: 'User ended session manually'
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
        });
    </script>
</body>
</html>
