#!/usr/bin/env python3
"""
Test script to debug CSV upload issues
"""

import requests
import os

def test_csv_upload():
    """Test CSV upload functionality"""

    # Check if files exist
    test_file = "test_contacts.csv"
    original_file = "100clean.csv"

    print("=== CSV Upload Test ===")

    # Test with small test file first
    if os.path.exists(test_file):
        print(f"✓ Test file exists: {test_file}")
        with open(test_file, 'r') as f:
            lines = f.readlines()
            print(f"  - Lines: {len(lines)}")
            print(f"  - Header: {lines[0].strip()}")
            if len(lines) > 1:
                print(f"  - First data row: {lines[1].strip()}")
    else:
        print(f"✗ Test file not found: {test_file}")

    if os.path.exists(original_file):
        print(f"✓ Original file exists: {original_file}")
        with open(original_file, 'r') as f:
            lines = f.readlines()
            print(f"  - Lines: {len(lines)}")
            print(f"  - Header: {lines[0].strip()}")
            if len(lines) > 1:
                print(f"  - First data row: {lines[1].strip()}")
    else:
        print(f"✗ Original file not found: {original_file}")

    # Test server connectivity
    try:
        response = requests.get("http://localhost:5000/contacts/upload")
        print(f"✓ Server is accessible: {response.status_code}")
    except Exception as e:
        print(f"✗ Server not accessible: {e}")
        return

    # Test upload with small test file first
    if os.path.exists(test_file):
        print(f"\n=== Testing upload with {test_file} ===")

        try:
            with open(test_file, 'rb') as f:
                files = {'contact_file': f}
                data = {
                    'file_format': 'csv',
                    'create_new_group': 'on',
                    'group_name': 'Test Group'
                }

                response = requests.post(
                    "http://localhost:5000/contacts/upload",
                    files=files,
                    data=data
                )

                print(f"Response status: {response.status_code}")
                print(f"Response headers: {dict(response.headers)}")

                if response.status_code == 302:
                    print("✓ Upload successful (redirected)")
                    print(f"Redirect location: {response.headers.get('Location', 'Not specified')}")
                elif response.status_code == 200:
                    print("Response content preview:")
                    print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
                else:
                    print(f"✗ Upload failed with status {response.status_code}")
                    print("Response content:")
                    print(response.text[:1000])

        except Exception as e:
            print(f"✗ Upload test failed: {e}")

    # Test upload with original file
    if os.path.exists(original_file):
        print(f"\n=== Testing upload with {original_file} ===")

        try:
            with open(original_file, 'rb') as f:
                files = {'contact_file': f}
                data = {
                    'file_format': 'csv',
                    'create_new_group': 'on',
                    'group_name': '100 Clean Companies'
                }

                response = requests.post(
                    "http://localhost:5000/contacts/upload",
                    files=files,
                    data=data
                )

                print(f"Response status: {response.status_code}")
                print(f"Response headers: {dict(response.headers)}")

                if response.status_code == 302:
                    print("✓ Upload successful (redirected)")
                    print(f"Redirect location: {response.headers.get('Location', 'Not specified')}")
                elif response.status_code == 200:
                    print("Response content preview:")
                    print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
                else:
                    print(f"✗ Upload failed with status {response.status_code}")
                    print("Response content:")
                    print(response.text[:1000])

        except Exception as e:
            print(f"✗ Upload test failed: {e}")

    print("\n=== Test completed ===")

if __name__ == "__main__":
    test_csv_upload()
