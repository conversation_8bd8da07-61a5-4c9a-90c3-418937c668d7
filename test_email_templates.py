#!/usr/bin/env python3
"""
Test Email Templates
===================
This script tests the email templates to ensure they're working correctly.
"""

from email_system.email_templates import EmailTemplateManager

def test_templates():
    """Test all email templates"""
    print("🧪 Testing Email Templates")
    print("=" * 50)

    # Create template manager
    template_manager = EmailTemplateManager()

    # Get available templates
    templates = template_manager.get_template_list()
    print(f"📧 Found {len(templates)} templates:")
    for template in templates:
        print(f"   • {template['name']}: {template['display_name']}")

    print("\n" + "=" * 50)

    # Test context
    test_context = {
        'contact_name': '<PERSON>',
        'company_name': 'Test Company Ltd',
        'agent_name': 'Sarah',
        'reply_email': '<EMAIL>',
        'phone_number': '+256 **********',
        'industry': 'Technology',
        'chat_url': 'http://localhost:5000/chat/test-session-123',
        'session_id': 'test-session-123'
    }

    # Test each template
    for template in templates:
        template_name = template['name']
        print(f"\n🔍 Testing template: {template_name}")
        print("-" * 30)

        try:
            # Render template
            rendered = template_manager.render_template(template_name, test_context)

            print(f"✅ Subject: {rendered['subject']}")
            print(f"✅ HTML body: {len(rendered['html_body'])} characters")
            print(f"✅ Text body: {len(rendered['text_body'])} characters")

            # Check for template variables that weren't replaced
            html_vars = [var for var in ['{{ contact_name }}', '{{ company_name }}', '{{ agent_name }}']
                        if var in rendered['html_body']]
            text_vars = [var for var in ['{{ contact_name }}', '{{ company_name }}', '{{ agent_name }}']
                        if var in rendered['text_body']]

            if html_vars:
                print(f"⚠️  Unreplaced HTML variables: {html_vars}")
            if text_vars:
                print(f"⚠️  Unreplaced text variables: {text_vars}")

            if not html_vars and not text_vars:
                print("✅ All variables properly replaced")

        except Exception as e:
            print(f"❌ Error rendering template: {e}")

    print("\n" + "=" * 50)
    print("🎉 Template testing complete!")

if __name__ == "__main__":
    test_templates()
