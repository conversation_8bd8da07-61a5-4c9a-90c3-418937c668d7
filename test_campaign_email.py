#!/usr/bin/env python3
"""
Test Campaign Email
==================
Send a test campaign <NAME_EMAIL> using the campaign system
"""

import os
import uuid
from datetime import datetime

def load_env_variables():
    """Load environment variables from .env file"""
    try:
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        
        print(f"✅ Loaded {len(env_vars)} environment variables from .env")
        return True
        
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")
        return False

def create_test_contact():
    """Create or update test contact"""
    try:
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Check if contact already exists
            contact = Contact.query.filter_by(email='<EMAIL>').first()
            
            if contact:
                print(f"✅ Found existing contact: {contact.first_name} {contact.last_name}")
                # Update contact to ensure it's active and email-enabled
                contact.is_active = True
                contact.do_not_email = False
                contact.current_sales_stage = 'new'
            else:
                print("📝 Creating new test contact...")
                contact = Contact(
                    first_name='Alex',
                    last_name='Scof',
                    email='<EMAIL>',
                    company='Test Company',
                    job_title='CEO',
                    source='campaign_test',
                    status='active',
                    is_active=True,
                    do_not_email=False,
                    current_sales_stage='new',
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.session.add(contact)
            
            db.session.commit()
            print(f"✅ Test contact ready: {contact.email}")
            return contact
            
    except Exception as e:
        print(f"❌ Failed to create test contact: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_test_campaign():
    """Create or get test campaign"""
    try:
        from unified_sales_system import app, db, EmailCampaign
        import json
        
        with app.app_context():
            # Check if test campaign already exists
            campaign = EmailCampaign.query.filter_by(name='Test Campaign - Gmail').first()
            
            if campaign:
                print(f"✅ Found existing test campaign: {campaign.name}")
                # Reset campaign status for testing
                campaign.status = 'draft'
                campaign.emails_sent = 0
                campaign.emails_sent_today = 0
                campaign.last_send_date = None
            else:
                print("📧 Creating new test campaign...")
                campaign = EmailCampaign(
                    name='Test Campaign - Gmail',
                    subject='🚀 Welcome to 24Seven Assistants - Your AI Sales Solution',
                    template_name='introduction',
                    sender_name='24Seven Assistants Sales Team',
                    sender_email='<EMAIL>',
                    reply_to_email='<EMAIL>',
                    target_audience='Test Contact',
                    recipient_criteria=json.dumps({'type': 'all'}),
                    status='draft',
                    daily_send_limit=100,
                    send_schedule='immediate',
                    batch_size=10,
                    max_retries=3,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    created_by='test_system'
                )
                db.session.add(campaign)
            
            db.session.commit()
            print(f"✅ Test campaign ready: {campaign.name}")
            return campaign
            
    except Exception as e:
        print(f"❌ Failed to create test campaign: {e}")
        import traceback
        traceback.print_exc()
        return None

def send_test_campaign():
    """Send test campaign email"""
    try:
        print("🚀 Sending Test Campaign Email")
        print("=" * 60)
        
        # Load environment variables
        if not load_env_variables():
            print("❌ Failed to load environment variables")
            return False
        
        from unified_sales_system import app, db, send_campaign_email
        
        with app.app_context():
            # Create test contact
            contact = create_test_contact()
            if not contact:
                return False
            
            # Create test campaign
            campaign = create_test_campaign()
            if not campaign:
                return False
            
            # Generate unique session ID for chatbot link
            session_id = str(uuid.uuid4())
            print(f"📱 Generated chatbot session ID: {session_id}")
            
            # Send campaign email
            print(f"\n📤 Sending campaign email to: {contact.email}")
            print(f"📧 Campaign: {campaign.name}")
            print(f"📋 Subject: {campaign.subject}")
            
            success, message = send_campaign_email(contact, campaign, session_id)
            
            if success:
                print("✅ Campaign email sent successfully!")
                print(f"📧 Message: {message}")
                
                # Update campaign statistics
                campaign.emails_sent += 1
                campaign.increment_daily_send_count()
                contact.first_email_sent = datetime.utcnow()
                contact.current_sales_stage = 'email_sent'
                contact.chatbot_session_id = session_id
                contact.email_campaign_id = campaign.id
                
                db.session.commit()
                print("✅ Campaign statistics updated")
                
                # Show chatbot link
                chatbot_link = f"http://localhost:5000/chat/{session_id}"
                print(f"🤖 Chatbot link: {chatbot_link}")
                
                return True
            else:
                print(f"❌ Campaign email failed: {message}")
                return False
                
    except Exception as e:
        print(f"❌ Test campaign failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 24Seven Assistants - Test Campaign Sender")
    print("=" * 60)
    
    success = send_test_campaign()
    
    print("\n" + "=" * 60)
    print("📊 TEST CAMPAIGN RESULT")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Test campaign email <NAME_EMAIL>")
        print("\n📬 Check your Gmail inbox for the campaign email")
        print("📧 Subject: 🚀 Welcome to 24Seven Assistants - Your AI Sales Solution")
        print("\n✅ The email campaign system is working correctly!")
        print("\n🔗 Features included in the campaign email:")
        print("   • Personalized greeting")
        print("   • Company introduction")
        print("   • Chatbot link for sales conversation")
        print("   • Professional email template")
        print("   • Tracking pixels for analytics")
        print("\n🤖 The email includes a unique chatbot link for sales conversations")
        print("📊 Campaign statistics will be tracked in the dashboard")
    else:
        print("❌ FAILED to send test campaign email")
        print("\nPlease check:")
        print("• Application is running with Gmail configuration")
        print("• Database is properly initialized")
        print("• Email system is working (previous SMTP test passed)")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
