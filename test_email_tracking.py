#!/usr/bin/env python3
"""
Test Email Open Tracking
========================
This script tests the email open tracking functionality to ensure
that email opens are properly detected and recorded in analytics.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_email_open_tracking():
    """Test email open tracking functionality"""
    print("🔍 TESTING EMAIL OPEN TRACKING")
    print("=" * 50)
    
    # Test session ID (from our existing contact)
    session_id = "4f3b1f41-38bc-4fef-bf04-9b802c77af6"
    
    print(f"📧 Testing email open for session: {session_id}")
    
    # Simulate email open by accessing tracking pixel
    try:
        response = requests.get(f"{BASE_URL}/track/open/{session_id}")
        if response.status_code == 200:
            print("✅ Email open tracking pixel accessed successfully")
            print(f"   Response type: {response.headers.get('Content-Type')}")
            print(f"   Response size: {len(response.content)} bytes")
        else:
            print(f"❌ Email open tracking failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error accessing tracking pixel: {e}")

def check_analytics_after_open():
    """Check analytics after email open"""
    print("\n🔍 CHECKING ANALYTICS AFTER EMAIL OPEN")
    print("=" * 50)
    
    # Wait a moment for database to update
    time.sleep(2)
    
    # Check comprehensive analytics
    try:
        response = requests.get(f"{BASE_URL}/analytics/comprehensive")
        if response.status_code == 200:
            content = response.text.lower()
            
            # Look for email open indicators
            if "1 open rate" in content or "opened" in content:
                print("✅ Email open data appears in comprehensive analytics")
            else:
                print("⚠️ Email open data not yet visible in comprehensive analytics")
                
            # Check for specific metrics
            if "email sent" in content:
                print("✅ Email sent metrics found")
            if "email opened" in content:
                print("✅ Email opened metrics found")
            else:
                print("⚠️ Email opened metrics not found")
        else:
            print(f"❌ Analytics check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking analytics: {e}")

def check_contact_details():
    """Check contact details for email open tracking"""
    print("\n🔍 CHECKING CONTACT DETAILS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/contacts/1")
        if response.status_code == 200:
            content = response.text.lower()
            
            # Check for email open indicators
            indicators = [
                ("email_opened", "Email opened timestamp"),
                ("opened", "Email opened status"),
                ("activity", "Activity tracking"),
                ("email", "Email tracking")
            ]
            
            for indicator, description in indicators:
                if indicator in content:
                    print(f"✅ {description} found in contact details")
                else:
                    print(f"⚠️ {description} not visible in contact details")
        else:
            print(f"❌ Contact details check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking contact details: {e}")

def test_multiple_opens():
    """Test multiple email opens (should only count first one)"""
    print("\n🔍 TESTING MULTIPLE EMAIL OPENS")
    print("=" * 50)
    
    session_id = "4f3b1f41-38bc-4fef-bf04-9b802c77af6"
    
    # Access tracking pixel multiple times
    for i in range(3):
        try:
            response = requests.get(f"{BASE_URL}/track/open/{session_id}")
            if response.status_code == 200:
                print(f"✅ Email open #{i+1} tracked")
            else:
                print(f"❌ Email open #{i+1} failed: {response.status_code}")
            time.sleep(1)
        except Exception as e:
            print(f"❌ Error on open #{i+1}: {e}")

def create_test_campaign():
    """Create a test campaign to verify email tracking"""
    print("\n🔍 CREATING TEST CAMPAIGN")
    print("=" * 50)
    
    # Create a simple test campaign
    campaign_data = {
        'name': 'Email Open Test Campaign',
        'subject': 'Test Email Open Tracking',
        'template_name': 'default',
        'daily_send_limit': 10,
        'send_schedule': 'immediate'
    }
    
    try:
        # Note: This would require proper form data and CSRF token
        # For now, just check if the campaign creation endpoint exists
        response = requests.get(f"{BASE_URL}/campaigns/new")
        if response.status_code == 200:
            print("✅ Campaign creation page accessible")
        else:
            print(f"⚠️ Campaign creation page status: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking campaign creation: {e}")

def check_email_logs():
    """Check if EmailLog entries are being created"""
    print("\n🔍 CHECKING EMAIL LOG ENTRIES")
    print("=" * 50)
    
    # This would require a direct database query or API endpoint
    # For now, check if we can access campaign details
    try:
        response = requests.get(f"{BASE_URL}/campaigns")
        if response.status_code == 200:
            content = response.text.lower()
            if "campaign" in content:
                print("✅ Campaigns page accessible")
                if "sent" in content:
                    print("✅ Email sending data found")
                if "opened" in content:
                    print("✅ Email open data found")
                else:
                    print("⚠️ Email open data not visible in campaigns")
        else:
            print(f"❌ Campaigns page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking campaigns: {e}")

def main():
    """Main test function"""
    print("🧪 EMAIL OPEN TRACKING TEST")
    print("=" * 60)
    print("Testing email open tracking for contact: <EMAIL>")
    print("Session ID: 4f3b1f41-38bc-4fef-bf04-9b802c77af6")
    print("=" * 60)
    
    # Run all tests
    test_email_open_tracking()
    check_analytics_after_open()
    check_contact_details()
    test_multiple_opens()
    create_test_campaign()
    check_email_logs()
    
    print("\n🎯 SUMMARY")
    print("=" * 50)
    print("1. Email open tracking pixel should be working")
    print("2. Check these URLs to see the results:")
    print(f"   📊 Analytics: {BASE_URL}/analytics/comprehensive")
    print(f"   👤 Contact: {BASE_URL}/contacts/1")
    print(f"   📧 Campaigns: {BASE_URL}/campaigns")
    print("\n3. Expected results:")
    print("   - Email open rate should be > 0%")
    print("   - Contact should show email_opened timestamp")
    print("   - Analytics should show 1 email opened")
    print("   - Multiple opens should only count once")

if __name__ == "__main__":
    main()
