{% extends "base.html" %}

{% block title %}Analytics Dashboard - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line text-primary"></i> Sales Analytics Dashboard</h1>
    <div>
        <button class="btn btn-outline-primary" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary" onclick="changePeriod(7)">7 Days</button>
            <button type="button" class="btn btn-primary" onclick="changePeriod(30)">30 Days</button>
            <button type="button" class="btn btn-outline-secondary" onclick="changePeriod(90)">90 Days</button>
        </div>
    </div>
</div>

<!-- Summary Cards -->
{% if summary %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="metric-value">{{ summary.performance_metrics.total_opportunities or 0 }}</div>
                <div class="metric-label">Total Opportunities</div>
                <small><i class="fas fa-arrow-up"></i> {{ summary.performance_metrics.new_opportunities or 0 }} new this period</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">${{ "%.0f"|format(summary.performance_metrics.pipeline_value or 0) }}</div>
                <div class="metric-label">Pipeline Value</div>
                <small><i class="fas fa-dollar-sign"></i> Weighted: ${{ "%.0f"|format(summary.performance_metrics.weighted_pipeline_value or 0) }}</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ "%.1f"|format(summary.performance_metrics.opp_to_customer_rate or 0) }}%</div>
                <div class="metric-label">Conversion Rate</div>
                <small><i class="fas fa-percentage"></i> Opportunity to Customer</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">{{ summary.email_metrics.total_campaigns or 0 }}</div>
                <div class="metric-label">Email Campaigns</div>
                <small><i class="fas fa-envelope"></i> {{ "%.1f"|format(summary.email_metrics.avg_open_rate or 0) }}% open rate</small>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-funnel-dollar"></i> Sales Funnel</h5>
            <div id="funnelChart"></div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-clock"></i> Stage Duration Analysis</h5>
            <div id="durationChart"></div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-envelope-open-text"></i> Email Campaign Performance</h5>
            <div id="emailChart"></div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="chart-container">
            <h5><i class="fas fa-chart-bar"></i> Performance Overview</h5>
            <div id="performanceChart"></div>
        </div>
    </div>
</div>

<!-- Stage Analytics Table -->
{% if summary and summary.stage_metrics %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table"></i> Stage Performance Details</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Stage</th>
                                <th>Entries</th>
                                <th>Current Count</th>
                                <th>Conversion Rate</th>
                                <th>Win Probability</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage in summary.stage_metrics.funnel.stages %}
                            <tr>
                                <td>
                                    <strong>{{ stage.name }}</strong>
                                    <small class="text-muted d-block">Order: {{ stage.order }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ stage.entries }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ stage.current_count }}</span>
                                </td>
                                <td>
                                    {% if stage.conversion_rate > 0 %}
                                        <span class="badge bg-success">{{ stage.conversion_rate }}%</span>
                                    {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" role="progressbar"
                                             style="width: {{ stage.probability_percent }}%"
                                             aria-valuenow="{{ stage.probability_percent }}"
                                             aria-valuemin="0" aria-valuemax="100">
                                            {{ stage.probability_percent }}%
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-open">Active</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Bottlenecks Alert -->
{% if summary and summary.stage_metrics.bottlenecks.count > 0 %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle"></i> Stage Bottlenecks Detected</h5>
            <p>{{ summary.stage_metrics.bottlenecks.count }} opportunities have been in their current stage for more than 14 days.</p>
            <button class="btn btn-warning btn-sm" onclick="showBottlenecks()">
                <i class="fas fa-eye"></i> View Details
            </button>
        </div>
    </div>
</div>
{% endif %}

<!-- Email Campaign Summary -->
{% if summary and summary.email_metrics.campaigns %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-envelope"></i> Recent Email Campaigns</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Campaign</th>
                                <th>Status</th>
                                <th>Recipients</th>
                                <th>Open Rate</th>
                                <th>Click Rate</th>
                                <th>Reply Rate</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for campaign in summary.email_metrics.campaigns[:5] %}
                            <tr>
                                <td>
                                    <strong>{{ campaign.name }}</strong>
                                    <small class="text-muted d-block">{{ campaign.subject }}</small>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ campaign.status }}">
                                        {{ campaign.status.title() }}
                                    </span>
                                </td>
                                <td>{{ campaign.total_recipients }}</td>
                                <td>
                                    {% if campaign.open_rate and campaign.open_rate > 0 %}
                                        <span class="badge bg-success">{{ "%.1f"|format(campaign.open_rate or 0) }}%</span>
                                    {% else %}
                                        <span class="badge bg-secondary">0%</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if campaign.click_rate and campaign.click_rate > 0 %}
                                        <span class="badge bg-info">{{ "%.1f"|format(campaign.click_rate or 0) }}%</span>
                                    {% else %}
                                        <span class="badge bg-secondary">0%</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if campaign.reply_rate and campaign.reply_rate > 0 %}
                                        <span class="badge bg-warning">{{ "%.1f"|format(campaign.reply_rate or 0) }}%</span>
                                    {% else %}
                                        <span class="badge bg-secondary">0%</span>
                                    {% endif %}
                                </td>
                                <td>{{ campaign.created_at[:10] if campaign.created_at else 'N/A' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // Render charts if data is available
    {% if charts %}
        {% if charts.funnel %}
            var funnelData = {{ charts.funnel|safe }};
            Plotly.newPlot('funnelChart', funnelData.data, funnelData.layout, {responsive: true});
        {% endif %}

        {% if charts.duration %}
            var durationData = {{ charts.duration|safe }};
            Plotly.newPlot('durationChart', durationData.data, durationData.layout, {responsive: true});
        {% endif %}

        {% if charts.email %}
            var emailData = {{ charts.email|safe }};
            Plotly.newPlot('emailChart', emailData.data, emailData.layout, {responsive: true});
        {% endif %}

        {% if charts.performance %}
            var performanceData = {{ charts.performance|safe }};
            Plotly.newPlot('performanceChart', performanceData.data, performanceData.layout, {responsive: true});
        {% endif %}
    {% else %}
        // Show placeholder messages if no data
        document.getElementById('funnelChart').innerHTML = '<div class="text-center py-4"><i class="fas fa-chart-line fa-3x text-muted mb-3"></i><p class="text-muted">No funnel data available</p></div>';
        document.getElementById('durationChart').innerHTML = '<div class="text-center py-4"><i class="fas fa-clock fa-3x text-muted mb-3"></i><p class="text-muted">No duration data available</p></div>';
        document.getElementById('emailChart').innerHTML = '<div class="text-center py-4"><i class="fas fa-envelope fa-3x text-muted mb-3"></i><p class="text-muted">No email data available</p></div>';
        document.getElementById('performanceChart').innerHTML = '<div class="text-center py-4"><i class="fas fa-chart-bar fa-3x text-muted mb-3"></i><p class="text-muted">No performance data available</p></div>';
    {% endif %}

    function refreshDashboard() {
        location.reload();
    }

    function changePeriod(days) {
        // In a real implementation, this would update the charts with new data
        alert('Period changed to ' + days + ' days. Refreshing dashboard...');
        location.reload();
    }

    function showBottlenecks() {
        // In a real implementation, this would show a modal with bottleneck details
        alert('Bottleneck details would be shown here in a modal.');
    }
</script>
{% endblock %}
