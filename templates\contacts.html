{% extends "base.html" %}

{% block title %}Contacts - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users text-primary"></i> Contact Management</h1>
    <div>
        <a href="{{ url_for('upload_contacts') }}" class="btn btn-success">
            <i class="fas fa-upload"></i> Upload Contacts
        </a>
        <a href="{{ url_for('add_contact') }}" class="btn btn-primary">
            <i class="fas fa-user-plus"></i> Add Contact
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" placeholder="Search contacts..." id="searchInput">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter">
                            <option value="">All Statuses</option>
                            <option value="new">New</option>
                            <option value="contacted">Contacted</option>
                            <option value="qualified">Qualified</option>
                            <option value="customer">Customer</option>
                            <option value="lost">Lost</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="sourceFilter">
                            <option value="">All Sources</option>
                            <option value="website">Website</option>
                            <option value="referral">Referral</option>
                            <option value="campaign">Campaign</option>
                            <option value="manual_entry">Manual Entry</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contacts Table -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Contacts List
                    {% if contacts and contacts.total %}
                        <span class="badge bg-primary ms-2">{{ contacts.total }} total</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if contacts and contacts.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Company</th>
                                    <th>Job Title</th>
                                    <th>Status</th>
                                    <th>Source</th>
                                    <th>Lead Score</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contact in contacts.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2">
                                                {{ contact.first_name[0] if contact.first_name else 'U' }}
                                            </div>
                                            <div>
                                                <strong>{{ contact.full_name }}</strong>
                                                {% if contact.phone %}
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-phone"></i> {{ contact.phone }}
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="mailto:{{ contact.email }}" class="text-decoration-none">
                                            {{ contact.email }}
                                        </a>
                                        {% if contact.do_not_email %}
                                            <i class="fas fa-ban text-danger ms-1" title="Do not email"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ contact.company or '-' }}
                                        {% if contact.website %}
                                            <a href="{{ contact.website }}" target="_blank" class="ms-1">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        {% endif %}
                                    </td>
                                    <td>{{ contact.job_title or '-' }}</td>
                                    <td>
                                        <span class="status-badge status-{{ contact.status }}">
                                            {{ contact.status.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ contact.source or 'Unknown' }}</small>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px; width: 60px;">
                                            <div class="progress-bar
                                                {% if contact.lead_score >= 80 %}bg-success
                                                {% elif contact.lead_score >= 60 %}bg-warning
                                                {% elif contact.lead_score >= 40 %}bg-info
                                                {% else %}bg-secondary{% endif %}"
                                                role="progressbar"
                                                style="width: {{ contact.lead_score }}%"
                                                title="{{ contact.lead_score }}/100">
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ contact.lead_score }}/100</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ contact.created_at.strftime('%m/%d/%Y') if contact.created_at else 'N/A' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="viewContact({{ contact.id }})" title="View">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="emailContact('{{ contact.email }}')" title="Email">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="createOpportunity({{ contact.id }})" title="Create Opportunity">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if contacts.pages > 1 %}
                    <nav aria-label="Contacts pagination">
                        <ul class="pagination justify-content-center">
                            {% if contacts.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('contacts_list', page=contacts.prev_num) }}">Previous</a>
                                </li>
                            {% endif %}

                            {% for page_num in contacts.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != contacts.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('contacts_list', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if contacts.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('contacts_list', page=contacts.next_num) }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Contacts Found</h4>
                        <p class="text-muted">Start building your contact database by adding your first contact.</p>
                        <a href="{{ url_for('add_contact') }}" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Add Your First Contact
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }

    .progress {
        border-radius: 10px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function viewContact(contactId) {
        // In a real implementation, this would open a contact detail modal or page
        alert('View contact details for ID: ' + contactId);
    }

    function emailContact(email) {
        window.location.href = 'mailto:' + email + '?subject=24Seven Assistants - Professional Virtual Assistant Services';
    }

    function createOpportunity(contactId) {
        // In a real implementation, this would open a create opportunity form
        alert('Create opportunity for contact ID: ' + contactId);
    }



    function applyFilters() {
        const search = document.getElementById('searchInput').value;
        const status = document.getElementById('statusFilter').value;
        const source = document.getElementById('sourceFilter').value;

        // In a real implementation, this would filter the table or reload with filters
        console.log('Applying filters:', { search, status, source });
        alert('Filters applied: ' + JSON.stringify({ search, status, source }));
    }

    function clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('sourceFilter').value = '';

        // In a real implementation, this would clear filters and reload
        alert('Filters cleared');
    }

    // Real-time search (debounced)
    let searchTimeout;
    document.getElementById('searchInput').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // In a real implementation, this would perform live search
            console.log('Searching for:', document.getElementById('searchInput').value);
        }, 500);
    });
</script>
{% endblock %}
