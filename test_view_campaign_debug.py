#!/usr/bin/env python3
"""
Test script to debug the view_campaign route issue
"""

import requests
import sys

def test_view_campaign():
    """Test the view_campaign route directly"""
    
    print("🔍 Testing view_campaign route...")
    
    # First, get the campaigns list to see what campaigns exist
    print("\n1. Getting campaigns list...")
    try:
        response = requests.get('http://localhost:5000/campaigns')
        if response.status_code == 200:
            print(f"✅ Campaigns list loaded successfully")
            
            # Look for campaign IDs in the response
            import re
            campaign_ids = re.findall(r'/campaigns/(\d+)', response.text)
            if campaign_ids:
                print(f"📋 Found campaign IDs: {campaign_ids}")
                
                # Test the first campaign
                test_id = campaign_ids[0]
                print(f"\n2. Testing view campaign {test_id}...")
                
                # Test with verbose output
                view_response = requests.get(f'http://localhost:5000/campaigns/{test_id}', allow_redirects=False)
                
                print(f"   Status Code: {view_response.status_code}")
                print(f"   Headers: {dict(view_response.headers)}")
                
                if view_response.status_code == 302:
                    print(f"   Redirect Location: {view_response.headers.get('Location')}")
                    print("   ❌ REDIRECTED - This means there's an error in the route")
                    
                    # Check if there are any cookies with error messages
                    cookies = view_response.cookies
                    if cookies:
                        print(f"   Cookies: {dict(cookies)}")
                        
                        # Try to decode Flask session cookie
                        session_cookie = cookies.get('session')
                        if session_cookie:
                            print(f"   Session Cookie: {session_cookie}")
                            
                elif view_response.status_code == 200:
                    print("   ✅ SUCCESS - Campaign details loaded")
                    
                    # Check if it contains campaign details
                    if "Campaign Details" in view_response.text:
                        print("   ✅ Page contains 'Campaign Details'")
                    else:
                        print("   ❌ Page doesn't contain 'Campaign Details'")
                        print(f"   First 500 chars: {view_response.text[:500]}")
                        
                else:
                    print(f"   ❌ Unexpected status code: {view_response.status_code}")
                    print(f"   Response: {view_response.text[:500]}")
                    
            else:
                print("❌ No campaign IDs found in campaigns list")
                print(f"Response preview: {response.text[:500]}")
                
        else:
            print(f"❌ Failed to get campaigns list: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    # Test with a specific campaign ID that we know exists
    print(f"\n3. Testing specific campaign IDs...")
    for test_id in [7, 8, 9, 10, 11, 12, 13]:
        try:
            print(f"   Testing campaign {test_id}...")
            response = requests.get(f'http://localhost:5000/campaigns/{test_id}', allow_redirects=False)
            
            if response.status_code == 200:
                print(f"   ✅ Campaign {test_id} works!")
                return True
            elif response.status_code == 302:
                print(f"   ❌ Campaign {test_id} redirects")
            elif response.status_code == 404:
                print(f"   ⚠️  Campaign {test_id} not found")
            else:
                print(f"   ❌ Campaign {test_id} status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing campaign {test_id}: {e}")
            
    return False

def test_database_directly():
    """Test database queries directly"""
    print("\n🗄️  Testing database directly...")
    
    try:
        # Import the Flask app and models
        from unified_sales_system import app, db, EmailCampaign, EmailLog, Contact, Activity
        
        with app.app_context():
            print("   ✅ App context created")
            
            # Test EmailCampaign query
            campaigns = EmailCampaign.query.all()
            print(f"   📊 Found {len(campaigns)} campaigns")
            
            if campaigns:
                campaign = campaigns[0]
                print(f"   📋 Testing campaign: {campaign.id} - {campaign.name}")
                
                # Test the exact queries from view_campaign route
                print("   🔍 Testing EmailLog query...")
                contact_ids_query = db.session.query(EmailLog.contact_id).filter_by(campaign_id=campaign.id).distinct()
                contact_ids_list = [row[0] for row in contact_ids_query.all()]
                print(f"   📧 Found {len(contact_ids_list)} contact IDs with email logs")
                
                print("   🔍 Testing Contact query...")
                if not contact_ids_list:
                    contacts = Contact.query.filter_by(is_active=True, do_not_email=False).limit(5).all()
                    print(f"   👥 Found {len(contacts)} active contacts (no email logs)")
                else:
                    contacts = Contact.query.filter(Contact.id.in_(contact_ids_list)).limit(5).all()
                    print(f"   👥 Found {len(contacts)} contacts with email logs")
                
                print("   🔍 Testing Activity query...")
                activities = Activity.query.filter_by(session_id=str(campaign.id)).limit(5).all()
                print(f"   📝 Found {len(activities)} activities")
                
                print("   ✅ All database queries work!")
                return True
            else:
                print("   ❌ No campaigns found in database")
                return False
                
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting view_campaign debug test...")
    
    # Test HTTP requests
    http_success = test_view_campaign()
    
    # Test database directly
    db_success = test_database_directly()
    
    print(f"\n📊 Results:")
    print(f"   HTTP Test: {'✅ PASS' if http_success else '❌ FAIL'}")
    print(f"   Database Test: {'✅ PASS' if db_success else '❌ FAIL'}")
    
    if not http_success and db_success:
        print("\n💡 Conclusion: Database works but HTTP route fails")
        print("   This suggests an issue in the view_campaign route logic")
    elif not db_success:
        print("\n💡 Conclusion: Database issue detected")
    else:
        print("\n💡 Conclusion: Everything should be working!")
