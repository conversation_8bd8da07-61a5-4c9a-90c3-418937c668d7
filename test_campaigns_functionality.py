#!/usr/bin/env python3
"""
Test Campaigns Functionality
============================
Test that campaigns page and functionality are working properly.
"""

import requests
import json

def test_campaigns_page():
    """Test campaigns page loads and displays correctly"""
    print("🧪 Testing Campaigns Page")
    print("=" * 30)
    
    base_url = 'http://localhost:5000'
    
    try:
        # Test 1: Check campaigns page
        print("1. Testing campaigns page...")
        response = requests.get(f'{base_url}/campaigns', timeout=10)
        
        if response.status_code == 200:
            print("✅ Campaigns page loads successfully")
            
            # Check if page contains expected elements
            content = response.text.lower()
            
            # Check for key elements
            checks = [
                ('email campaigns', 'Email Campaigns header'),
                ('create campaign', 'Create Campaign button'),
                ('total campaigns', 'Campaign statistics'),
                ('no campaigns found', 'Empty state message')
            ]
            
            found_elements = []
            for check_text, description in checks:
                if check_text in content:
                    found_elements.append(description)
                    print(f"   ✅ Found: {description}")
                else:
                    print(f"   ❌ Missing: {description}")
            
            if len(found_elements) >= 2:
                print("✅ Campaigns page has expected content")
                return True
            else:
                print("❌ Campaigns page missing key elements")
                return False
        else:
            print(f"❌ Campaigns page failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_create_campaign_page():
    """Test create campaign page"""
    print("\n2. Testing create campaign page...")
    
    base_url = 'http://localhost:5000'
    
    try:
        response = requests.get(f'{base_url}/campaigns/create', timeout=10)
        
        if response.status_code == 200:
            print("✅ Create campaign page loads successfully")
            
            # Check for form elements
            content = response.text.lower()
            form_elements = [
                'campaign name',
                'template',
                'create campaign'
            ]
            
            found_forms = 0
            for element in form_elements:
                if element in content:
                    found_forms += 1
                    print(f"   ✅ Found: {element}")
                else:
                    print(f"   ❌ Missing: {element}")
            
            if found_forms >= 2:
                print("✅ Create campaign form has expected elements")
                return True
            else:
                print("❌ Create campaign form missing elements")
                return False
        else:
            print(f"❌ Create campaign page failed: {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_campaign_creation():
    """Test creating a campaign"""
    print("\n3. Testing campaign creation...")
    
    base_url = 'http://localhost:5000'
    
    try:
        # Create a test campaign
        campaign_data = {
            'name': 'Test Campaign',
            'template': 'introduction',
            'recipient_type': 'all',
            'daily_send_limit': '100',
            'send_schedule': 'immediate',
            'batch_size': '50',
            'batch_delay_minutes': '5'
        }
        
        response = requests.post(
            f'{base_url}/campaigns/create',
            data=campaign_data,
            allow_redirects=False,
            timeout=10
        )
        
        if response.status_code in [200, 302]:
            print("✅ Campaign creation request successful")
            
            # Check if redirected to campaigns list
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                if 'campaigns' in location:
                    print("✅ Redirected to campaigns list after creation")
                    return True
                else:
                    print(f"❌ Unexpected redirect: {location}")
                    return False
            else:
                print("✅ Campaign creation completed")
                return True
        else:
            print(f"❌ Campaign creation failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error creating campaign: {e}")
        return False

def test_campaign_actions():
    """Test campaign action buttons"""
    print("\n4. Testing campaign actions...")
    
    base_url = 'http://localhost:5000'
    
    try:
        # Get campaigns page to find campaign IDs
        response = requests.get(f'{base_url}/campaigns', timeout=10)
        
        if response.status_code != 200:
            print("❌ Cannot load campaigns page for testing")
            return False
        
        # Look for campaign IDs in view links
        import re
        view_links = re.findall(r'/campaigns/(\d+)', response.text)
        
        if view_links:
            campaign_id = view_links[0]
            print(f"✅ Found campaign ID {campaign_id} for testing")
            
            # Test view campaign
            view_response = requests.get(f'{base_url}/campaigns/{campaign_id}', timeout=10)
            
            if view_response.status_code == 200:
                print("✅ View campaign page works")
                return True
            else:
                print(f"❌ View campaign failed: {view_response.status_code}")
                return False
        else:
            print("ℹ️ No campaigns found to test actions")
            print("   Create a campaign first, then test the action buttons")
            return True
        
    except Exception as e:
        print(f"❌ Error testing campaign actions: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CAMPAIGNS FUNCTIONALITY TEST")
    print("=" * 50)
    print("This script tests that campaigns functionality is working properly.")
    print("=" * 50)
    
    # Run tests
    tests = [
        test_campaigns_page,
        test_create_campaign_page,
        test_campaign_creation,
        test_campaign_actions
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ ALL CAMPAIGNS TESTS PASSED!")
        print("\nCampaigns functionality is working correctly:")
        print("• Campaigns page loads properly")
        print("• Create campaign form works")
        print("• Campaign creation succeeds")
        print("• Campaign actions are functional")
        print("\n🎉 You can use the campaigns feature!")
    elif passed > total // 2:
        print("⚠️ CAMPAIGNS PARTIALLY WORKING")
        print(f"\n{passed}/{total} tests passed")
        print("\nSome issues found:")
        print("• Check server logs for errors")
        print("• Verify database connections")
        print("• Check template rendering")
    else:
        print("❌ CAMPAIGNS HAVE MAJOR ISSUES!")
        print(f"\n{passed}/{total} tests passed")
        print("\nPossible issues:")
        print("• Routes not properly defined")
        print("• Template rendering problems")
        print("• Database connection issues")
        print("• Missing dependencies")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
