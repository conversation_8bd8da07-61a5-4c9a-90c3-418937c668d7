"""
Sales Stage Model
================
Tracks the sales process stages for analytics and progression.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey
from sqlalchemy.orm import relationship



class SalesStage(db.Model):
    """Sales stage tracking model"""
    
    __tablename__ = 'sales_stages'
    
    id = Column(Integer, primary_key=True)
    
    # Stage Information
    name = Column(String(100), nullable=False, index=True)  # Opening, Trust, Discovery, Demonstration, Close
    description = Column(Text, nullable=True)
    order = Column(Integer, nullable=False, index=True)  # Order in the sales process
    
    # Stage Configuration
    is_active = Column(Boolean, default=True)
    probability_percent = Column(Float, default=0.0)  # Win probability at this stage
    expected_duration_days = Column(Integer, default=7)  # Expected time in this stage
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SalesStage {self.name} (Order: {self.order})>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'order': self.order,
            'is_active': self.is_active,
            'probability_percent': self.probability_percent,
            'expected_duration_days': self.expected_duration_days,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class SalesStageHistory(db.Model):
    """Track stage progression history for opportunities"""
    
    __tablename__ = 'sales_stage_history'
    
    id = Column(Integer, primary_key=True)
    
    # Relationships
    opportunity_id = Column(Integer, ForeignKey('opportunities.id'), nullable=False, index=True)
    stage_id = Column(Integer, ForeignKey('sales_stages.id'), nullable=False, index=True)
    
    # Stage Progression
    entered_at = Column(DateTime, default=datetime.utcnow, index=True)
    exited_at = Column(DateTime, nullable=True)
    duration_hours = Column(Float, nullable=True)  # Time spent in this stage
    
    # Context
    notes = Column(Text, nullable=True)
    changed_by = Column(String(100), nullable=True)  # User or AI agent
    reason = Column(String(255), nullable=True)  # Reason for stage change
    
    # Relationships
    opportunity = relationship("Opportunity", back_populates="stage_history")
    stage = relationship("SalesStage")
    
    def __repr__(self):
        return f'<SalesStageHistory Opp:{self.opportunity_id} Stage:{self.stage_id}>'
    
    def calculate_duration(self):
        """Calculate and update duration if stage is exited"""
        if self.exited_at and self.entered_at:
            delta = self.exited_at - self.entered_at
            self.duration_hours = delta.total_seconds() / 3600
            return self.duration_hours
        return None
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'opportunity_id': self.opportunity_id,
            'stage_id': self.stage_id,
            'stage_name': self.stage.name if self.stage else None,
            'entered_at': self.entered_at.isoformat() if self.entered_at else None,
            'exited_at': self.exited_at.isoformat() if self.exited_at else None,
            'duration_hours': self.duration_hours,
            'notes': self.notes,
            'changed_by': self.changed_by,
            'reason': self.reason
        }
