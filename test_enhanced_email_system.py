#!/usr/bin/env python3
"""
Enhanced Email System Test
==========================
Test the enhanced email campaign system based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md
"""

import sys
import os

def test_email_system_configuration():
    """Test email system configuration and connectivity"""
    print("🧪 Testing Enhanced Email System Configuration")
    print("=" * 60)
    
    try:
        # Import the email system
        from email_system import test_email_system, get_email_config, validate_email_config
        from email_system.config import print_email_config
        
        print("✅ Email system modules imported successfully")
        
        # Get and validate configuration
        config = get_email_config()
        print("\n📧 Current Email Configuration:")
        print_email_config(config)
        
        # Validate configuration
        is_valid, error_msg = validate_email_config(config)
        if is_valid:
            print("✅ Configuration validation passed")
        else:
            print(f"❌ Configuration validation failed: {error_msg}")
            return False
        
        # Test connectivity
        print("\n🔌 Testing Email System Connectivity...")
        success, message = test_email_system()
        if success:
            print(f"✅ Connectivity test passed: {message}")
        else:
            print(f"❌ Connectivity test failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Email system test failed: {str(e)}")
        return False


def test_campaign_creation():
    """Test campaign creation with the enhanced system"""
    print("\n🚀 Testing Campaign Creation")
    print("-" * 40)
    
    try:
        # Import Flask app and models
        from unified_sales_system import app, db, EmailCampaign, EmailLog, Contact
        from email_system import create_email_system
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Initialize enhanced email system
            email_system = create_email_system(db_session=db.session)
            campaign_manager = email_system['campaign_manager']
            
            print("✅ Enhanced email system initialized")
            
            # Create test contact if it doesn't exist
            test_email = "<EMAIL>"  # Send to ourselves for testing
            test_contact = Contact.query.filter_by(email=test_email).first()
            
            if not test_contact:
                test_contact = Contact(
                    first_name='Test',
                    last_name='Enhanced',
                    email=test_email,
                    company='24Seven Assistants',
                    job_title='Test Contact',
                    source='enhanced_test',
                    status='new'
                )
                db.session.add(test_contact)
                db.session.commit()
                print("✅ Test contact created")
            else:
                print("✅ Test contact already exists")
            
            # Create test campaign
            campaign = campaign_manager.create_campaign(
                name="Enhanced Email System Test Campaign",
                template_name="introduction",
                target_filters={'source': 'enhanced_test'},
                created_by="enhanced_test_system"
            )
            
            print(f"✅ Campaign created: {campaign.name} (ID: {campaign.id})")
            
            # Prepare campaign
            success, message = campaign_manager.prepare_campaign(campaign.id)
            if success:
                print(f"✅ Campaign prepared: {message}")
            else:
                print(f"❌ Campaign preparation failed: {message}")
                return False
            
            # Send campaign (small batch for testing)
            success, message = campaign_manager.send_campaign(campaign.id, batch_size=1)
            if success:
                print(f"✅ Campaign sent: {message}")
            else:
                print(f"❌ Campaign sending failed: {message}")
                return False
            
            # Check campaign status
            status = campaign_manager.get_campaign_status(campaign.id)
            if status:
                print(f"✅ Campaign status retrieved: {status['campaign']['status']}")
                print(f"   Total logs: {status['total_logs']}")
                print(f"   Pending logs: {status['pending_logs']}")
                print(f"   Progress: {status['progress_percent']:.1f}%")
            else:
                print("⚠️ Could not retrieve campaign status")
            
            return True
            
    except Exception as e:
        print(f"❌ Campaign creation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_smtp_service_directly():
    """Test the enhanced SMTP service directly"""
    print("\n📧 Testing Enhanced SMTP Service Directly")
    print("-" * 40)
    
    try:
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        from email_system import get_email_config
        
        # Create SMTP service
        config = get_email_config()
        smtp_service = EnhancedSMTPService(config)
        
        print("✅ Enhanced SMTP service created")
        
        # Test connection
        success, message = smtp_service.test_connection()
        if success:
            print(f"✅ SMTP connection test passed: {message}")
        else:
            print(f"❌ SMTP connection test failed: {message}")
            return False
        
        # Send test email
        success, message_id, error_msg = smtp_service.send_email(
            to_email=config['MAIL_DEFAULT_SENDER'],  # Send to ourselves
            subject="[TEST] Enhanced Email System Test",
            html_body="<h1>Test Email</h1><p>This is a test email from the enhanced email system.</p>",
            text_body="Test Email\n\nThis is a test email from the enhanced email system.",
            from_name="Enhanced Email System Test"
        )
        
        if success:
            print(f"✅ Test email sent successfully. Message ID: {message_id}")
        else:
            print(f"❌ Test email failed: {error_msg}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SMTP service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🧪 Enhanced Email Campaign System Testing")
    print("Based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    print("=" * 60)
    
    # Test 1: Configuration and connectivity
    config_success = test_email_system_configuration()
    
    # Test 2: SMTP service directly
    if config_success:
        smtp_success = test_smtp_service_directly()
    else:
        smtp_success = False
        print("⏭️ Skipping SMTP test due to configuration failure")
    
    # Test 3: Campaign system
    if smtp_success:
        campaign_success = test_campaign_creation()
    else:
        campaign_success = False
        print("⏭️ Skipping campaign test due to SMTP failure")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Configuration Test: {'✅ PASSED' if config_success else '❌ FAILED'}")
    print(f"SMTP Service Test:  {'✅ PASSED' if smtp_success else '❌ FAILED'}")
    print(f"Campaign Test:      {'✅ PASSED' if campaign_success else '❌ FAILED'}")
    
    if all([config_success, smtp_success, campaign_success]):
        print("\n🎉 ALL TESTS PASSED! Enhanced email system is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the configuration and fix any issues.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
