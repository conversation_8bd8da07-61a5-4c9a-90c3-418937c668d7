#!/usr/bin/env python3
"""
Quick Email System Test
=======================
Simple test to verify the enhanced email system is working
"""

def test_email_system():
    """Test the email system quickly"""
    try:
        print("🧪 Quick Email System Test")
        print("=" * 40)
        
        # Import Flask app
        from unified_sales_system import app, db
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Test SMTP connection
            from unified_sales_system import test_smtp_connection
            success, message = test_smtp_connection()
            
            if success:
                print(f"✅ SMTP Test: {message}")
                return True
            else:
                print(f"❌ SMTP Test Failed: {message}")
                return False
                
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_campaign_sending():
    """Test sending a campaign email"""
    try:
        print("\n📧 Testing Campaign Email Sending")
        print("-" * 40)
        
        from unified_sales_system import app, db, Contact, EmailCampaign
        
        with app.app_context():
            # Check if we have contacts
            contact_count = Contact.query.count()
            print(f"📊 Total contacts in database: {contact_count}")
            
            if contact_count == 0:
                print("⚠️ No contacts found. Please add a contact first.")
                return False
            
            # Check if we have campaigns
            campaign_count = EmailCampaign.query.count()
            print(f"📊 Total campaigns in database: {campaign_count}")
            
            if campaign_count == 0:
                print("⚠️ No campaigns found. Please create a campaign first.")
                return False
            
            # Get the first campaign
            campaign = EmailCampaign.query.first()
            print(f"📧 Found campaign: {campaign.name} (Status: {campaign.status})")
            
            # Check remaining sends
            remaining = campaign.get_remaining_sends_today()
            print(f"📊 Remaining sends today: {remaining}")
            
            if remaining > 0:
                print("✅ Campaign can send emails today")
                return True
            else:
                print("⚠️ Daily send limit reached")
                return False
                
    except Exception as e:
        print(f"❌ Campaign test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Enhanced Email System Quick Test")
    print("Based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    print("=" * 50)
    
    # Test 1: SMTP Connection
    smtp_success = test_email_system()
    
    # Test 2: Campaign System
    if smtp_success:
        campaign_success = test_campaign_sending()
    else:
        campaign_success = False
        print("⏭️ Skipping campaign test due to SMTP failure")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 QUICK TEST SUMMARY")
    print("=" * 50)
    print(f"SMTP Connection: {'✅ PASSED' if smtp_success else '❌ FAILED'}")
    print(f"Campaign System: {'✅ PASSED' if campaign_success else '❌ FAILED'}")
    
    if smtp_success and campaign_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The enhanced email system is ready to send campaigns.")
        print("\nNext steps:")
        print("1. Go to http://localhost:5000/campaigns")
        print("2. Click 'Send' on your campaign")
        print("3. Monitor the results in the dashboard")
    elif smtp_success:
        print("\n⚠️ SMTP works but campaign system needs attention.")
        print("Please check your contacts and campaigns.")
    else:
        print("\n❌ SMTP connection failed.")
        print("Please check your email configuration.")
        print("Visit http://localhost:5000/test-smtp for detailed testing.")
    
    return smtp_success and campaign_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
