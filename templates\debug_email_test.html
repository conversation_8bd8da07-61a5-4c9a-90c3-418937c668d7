{% extends "base.html" %}

{% block title %}Email Test - Debug Dashboard{% endblock %}

{% block extra_css %}
<style>
    .email-card {
        border-left: 4px solid #28a745;
    }
    .test-result {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 15px;
        margin-top: 15px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
    .smtp-config {
        background-color: #e9ecef;
        border-radius: 4px;
        padding: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-envelope text-success"></i> Email Test</h1>
    <div>
        <a href="{{ url_for('debug.debug_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Debug Dashboard
        </a>
    </div>
</div>

<!-- SMTP Configuration Display -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card email-card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> Current SMTP Configuration</h5>
            </div>
            <div class="card-body">
                <div class="smtp-config">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>SMTP Server:</strong> mail.24seven.site<br>
                            <strong>Port:</strong> 465<br>
                            <strong>Security:</strong> SSL/TLS<br>
                        </div>
                        <div class="col-md-6">
                            <strong>Username:</strong> <EMAIL><br>
                            <strong>From Address:</strong> <EMAIL><br>
                            <strong>Status:</strong> <span class="badge bg-success">Configured</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Test Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-paper-plane"></i> Send Test Email</h5>
            </div>
            <div class="card-body">
                <form id="emailTestForm">
                    <div class="mb-3">
                        <label for="testEmail" class="form-label">Test Email Address:</label>
                        <input type="email" class="form-control" id="testEmail" 
                               placeholder="Enter email address to test" required>
                        <div class="form-text">We'll send a test email to this address</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="testSubject" class="form-label">Subject:</label>
                        <input type="text" class="form-control" id="testSubject" 
                               value="24Seven Assistants - Email Test" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="testMessage" class="form-label">Message:</label>
                        <textarea class="form-control" id="testMessage" rows="4" readonly>This is a test email from the 24Seven Assistants Sales Department system.

If you receive this email, the SMTP configuration is working correctly.

Test Details:
- Timestamp: [Current Time]
- System: Sales Department Debug System
- Configuration: mail.24seven.site:465 (SSL)</textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success" id="sendTestBtn">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="testSMTPConnection()">
                        <i class="fas fa-plug"></i> Test SMTP Connection Only
                    </button>
                </form>
                
                <div id="testResults" class="test-result" style="display: none;">
                    <div id="testOutput"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Email Testing Guide</h5>
            </div>
            <div class="card-body">
                <h6>Test Steps:</h6>
                <ol class="small">
                    <li>Enter a valid email address</li>
                    <li>Click "Send Test Email"</li>
                    <li>Check the recipient's inbox</li>
                    <li>Verify email delivery</li>
                </ol>
                
                <h6 class="mt-3">Troubleshooting:</h6>
                <ul class="small">
                    <li><strong>Connection Failed:</strong> Check SMTP server settings</li>
                    <li><strong>Authentication Error:</strong> Verify username/password</li>
                    <li><strong>Email Not Received:</strong> Check spam folder</li>
                    <li><strong>SSL/TLS Error:</strong> Verify port and security settings</li>
                </ul>
                
                <h6 class="mt-3">Common Issues:</h6>
                <ul class="small">
                    <li>Firewall blocking SMTP port</li>
                    <li>ISP blocking outbound email</li>
                    <li>Incorrect server configuration</li>
                    <li>Rate limiting by email provider</li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Email Statistics</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <h4 class="text-success" id="emailsSentToday">0</h4>
                    <small class="text-muted">Emails Sent Today</small>
                </div>
                <hr>
                <div class="text-center">
                    <h4 class="text-info" id="emailsOpenedToday">0</h4>
                    <small class="text-muted">Emails Opened Today</small>
                </div>
                <hr>
                <div class="text-center">
                    <h4 class="text-warning" id="emailsFailedToday">0</h4>
                    <small class="text-muted">Failed Emails Today</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Preview -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-eye"></i> Email Preview</h5>
            </div>
            <div class="card-body">
                <div class="border rounded p-3" style="background-color: #fff;">
                    <div class="border-bottom pb-2 mb-3">
                        <strong>From:</strong> 24Seven Assistants Sales Team &lt;<EMAIL>&gt;<br>
                        <strong>To:</strong> <span id="previewTo">[Test Email Address]</span><br>
                        <strong>Subject:</strong> 24Seven Assistants - Email Test
                    </div>
                    <div>
                        <h4>Email Test</h4>
                        <p>This is a test email from the 24Seven Assistants Sales Department system.</p>
                        <p>If you receive this email, the SMTP configuration is working correctly.</p>
                        
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;">
                            <strong>Test Details:</strong><br>
                            • Timestamp: <span id="previewTimestamp">[Current Time]</span><br>
                            • System: Sales Department Debug System<br>
                            • Configuration: mail.24seven.site:465 (SSL)
                        </div>
                        
                        <p>Best regards,<br>
                        24Seven Assistants Sales Team</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Update preview when email changes
document.getElementById('testEmail').addEventListener('input', function() {
    document.getElementById('previewTo').textContent = this.value || '[Test Email Address]';
});

// Update timestamp in preview
document.getElementById('previewTimestamp').textContent = new Date().toLocaleString();

// Handle form submission
document.getElementById('emailTestForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('testEmail').value;
    const sendBtn = document.getElementById('sendTestBtn');
    const resultsDiv = document.getElementById('testResults');
    const outputDiv = document.getElementById('testOutput');
    
    if (!email) {
        alert('Please enter an email address');
        return;
    }
    
    // Disable button and show loading
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    
    // Show results area
    resultsDiv.style.display = 'block';
    outputDiv.innerHTML = '<div class="text-info">Sending test email...</div>';
    
    // Send test email
    fetch('/debug/api/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({email: email})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            outputDiv.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle"></i> SUCCESS: ${data.message}
                </div>
                <div class="mt-2 small text-muted">
                    Test email sent successfully. Please check the recipient's inbox (and spam folder).
                </div>
            `;
        } else {
            outputDiv.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-times-circle"></i> FAILED: ${data.message}
                </div>
                <div class="mt-2 small text-muted">
                    Please check your SMTP configuration and try again.
                </div>
            `;
        }
    })
    .catch(error => {
        outputDiv.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-times-circle"></i> ERROR: ${error}
            </div>
            <div class="mt-2 small text-muted">
                Network error or server issue. Please try again.
            </div>
        `;
    })
    .finally(() => {
        // Re-enable button
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Test Email';
    });
});

function testSMTPConnection() {
    const resultsDiv = document.getElementById('testResults');
    const outputDiv = document.getElementById('testOutput');
    
    resultsDiv.style.display = 'block';
    outputDiv.innerHTML = '<div class="text-info">Testing SMTP connection...</div>';
    
    fetch('/api/test-smtp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            outputDiv.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle"></i> CONNECTION SUCCESS: ${data.message}
                </div>
                <div class="mt-2 small text-muted">
                    SMTP server connection is working properly.
                </div>
            `;
        } else {
            outputDiv.innerHTML = `
                <div class="text-danger">
                    <i class="fas fa-times-circle"></i> CONNECTION FAILED: ${data.message}
                </div>
                <div class="mt-2 small text-muted">
                    Please check your SMTP configuration.
                </div>
            `;
        }
    })
    .catch(error => {
        outputDiv.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-times-circle"></i> ERROR: ${error}
            </div>
        `;
    });
}

// Load email statistics (mock data for demo)
document.getElementById('emailsSentToday').textContent = '42';
document.getElementById('emailsOpenedToday').textContent = '28';
document.getElementById('emailsFailedToday').textContent = '3';
</script>
{% endblock %}
