CONTACT DELETION: Starting deletion of contact 11
Route called: /contacts/11/delete
Request method: POST
Request URL: http://localhost:5000/contacts/11/delete
Timestamp: 2025-06-21 03:32:13.479395
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: /contacts/1/delete
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 03:35:45.122762
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: /contacts/1/delete
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 03:36:06.348773
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: /contacts/1/delete
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 03:39:15.989037
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 11:29:17.988966
==================================================
ERROR: Failed to delete contact 1
Error: no such table: chat_events
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2458, in delete_contact_and_content_enhanced
    deleted_items.append(f"{memberships_deleted} group memberships")
    
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2352, in delete_contact_and_content_enhanced
    # 11. Finally, delete the contact
sqlite3.OperationalError: no such table: chat_events

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 11:29:47.955831
==================================================
ERROR: Failed to delete contact 1
Error: no such table: chat_events
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2458, in delete_contact_and_content_enhanced
    deleted_items.append(f"{memberships_deleted} group memberships")
    
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2352, in delete_contact_and_content_enhanced
    # 11. Finally, delete the contact
sqlite3.OperationalError: no such table: chat_events

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 11:33:41.504432
==================================================
ERROR: Failed to delete contact 1
Error: no such table: chat_events
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2458, in delete_contact_and_content_enhanced
    if email_failures_deleted > 0:
    ^^^
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2352, in delete_contact_and_content_enhanced
    cursor.execute('DELETE FROM sales_stages WHERE contact_id = ?', (contact_id,))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: chat_events

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:03:27.183163
==================================================
ERROR: Failed to delete contact 1
Error: no such table: chatbot_sessions
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2421, in delete_contact_and_content_enhanced
    raise e
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2268, in delete_contact_and_content_enhanced
    cursor.execute('''
    ~~~~~~~~~~~~~~^^^^
        DELETE FROM chat_messages
        ^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        )
        ^
    ''', (contact_id,))
    ^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: chatbot_sessions

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:07:02.421233
==================================================
ERROR: Failed to delete contact 1
Error: no such table: contacts
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2426, in delete_contact_and_content_enhanced
    raise e
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2393, in delete_contact_and_content_enhanced
    cursor.execute('DELETE FROM contacts WHERE id = ?', (contact_id,))
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: contacts

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:12:08.772906
==================================================
ERROR: Failed to delete contact 1
Error: no such column: contact_id
Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2121, in delete_contact
    deleted_items = delete_contact_and_content_enhanced(contact_id)
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2431, in delete_contact_and_content_enhanced
    raise e
  File "C:\Users\<USER>\Downloads\testsales\unified_sales_system.py", line 2387, in delete_contact_and_content_enhanced
    cursor.execute('DELETE FROM sales_stages WHERE contact_id = ?', (contact_id,))
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: contact_id

==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:19:46.712105
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:20:05.721172
==================================================
CONTACT DELETION: Starting deletion of contact 1
Route called: delete_contact
Request method: POST
Request URL: http://localhost:5000/contacts/1/delete
Timestamp: 2025-06-21 12:25:05.868577
==================================================
