"""
Chat Event Model
================
Logs chat-related events such as chat_open and stage_progress for follow-up
logic.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship

# Local SQLAlchemy instance – patched by main Flask app at runtime



class ChatEvent(db.Model):
    """Represents a single chat-related event for a contact/opportunity."""

    __tablename__ = 'chat_events'

    id = Column(Integer, primary_key=True)

    # Relations
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=False, index=True)
    opportunity_id = Column(Integer, ForeignKey('opportunities.id'), nullable=True, index=True)

    # Chat session identifier (maps to bot_session_id)
    session_id = Column(String(100), nullable=False, index=True)

    # Event type: chat_open, stage_progress
    event_type = Column(String(50), nullable=False, index=True)
    stage_name = Column(String(100), nullable=True)  # filled for stage_progress

    event_metadata = Column(JSON, nullable=True)
    event_time = Column(DateTime, default=datetime.utcnow, index=True)

    processed = Column(Boolean, default=False)

    # Relationships for convenience
    contact = relationship("Contact")
    opportunity = relationship("Opportunity")

    def __repr__(self):
        return f'<ChatEvent {self.event_type} Session:{self.session_id}>'  # noqa: E501

    def to_dict(self):
        return {
            'id': self.id,
            'contact_id': self.contact_id,
            'opportunity_id': self.opportunity_id,
            'session_id': self.session_id,
            'event_type': self.event_type,
            'stage_name': self.stage_name,
            'event_metadata': self.event_metadata,
            'event_time': self.event_time.isoformat() if self.event_time else None,
            'processed': self.processed,
        }
