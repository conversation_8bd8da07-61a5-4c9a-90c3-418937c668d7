#!/usr/bin/env python3
"""
Test Contact Routes
==================
Test that contact view and edit routes are working.
"""

import requests

def test_contact_routes():
    """Test contact view and edit routes"""
    print("🧪 Testing Contact Action Buttons")
    print("=" * 40)
    
    base_url = 'http://localhost:5000'
    
    try:
        # Test 1: Check contacts page
        print("1. Testing contacts page...")
        response = requests.get(f'{base_url}/contacts', timeout=5)
        if response.status_code == 200:
            print("✅ Contacts page loads successfully")
        else:
            print(f"❌ Contacts page failed: {response.status_code}")
            return False
        
        # Test 2: Look for contact IDs in the page
        print("2. Looking for contact IDs...")
        import re
        
        # Look for contact IDs in view links
        view_links = re.findall(r'/contacts/(\d+)', response.text)
        edit_links = re.findall(r'/contacts/(\d+)/edit', response.text)
        
        if view_links:
            contact_id = view_links[0]
            print(f"✅ Found contact ID {contact_id} for testing")
            
            # Test 3: Try to access view contact page
            print("3. Testing view contact route...")
            view_response = requests.get(f'{base_url}/contacts/{contact_id}', timeout=10)
            
            if view_response.status_code == 200:
                print("✅ View contact page works!")
            else:
                print(f"❌ View contact failed: {view_response.status_code}")
                if view_response.status_code == 404:
                    print("   Contact not found - this is normal if no contacts exist")
                return False
            
            # Test 4: Try to access edit contact page
            print("4. Testing edit contact route...")
            edit_response = requests.get(f'{base_url}/contacts/{contact_id}/edit', timeout=10)
            
            if edit_response.status_code == 200:
                print("✅ Edit contact page works!")
                return True
            else:
                print(f"❌ Edit contact failed: {edit_response.status_code}")
                return False
        else:
            print("ℹ️ No contacts found to test")
            print("   Add some contacts first, then test the action buttons")
            return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_contact_creation():
    """Test creating a contact to have something to test with"""
    print("\n5. Testing contact creation...")
    
    base_url = 'http://localhost:5000'
    
    try:
        # Create a test contact
        contact_data = {
            'first_name': 'Test',
            'last_name': 'Contact',
            'email': f'test.contact.{hash("test") % 10000}@example.com',
            'phone': '************',
            'company': 'Test Company',
            'job_title': 'Test Manager',
            'source': 'manual_entry'
        }
        
        create_response = requests.post(
            f'{base_url}/contacts/add',
            data=contact_data,
            allow_redirects=False,
            timeout=10
        )
        
        if create_response.status_code in [200, 302]:
            print("✅ Test contact created successfully")
            return True
        else:
            print(f"❌ Failed to create test contact: {create_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ Error creating test contact: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CONTACT ACTION BUTTONS TEST")
    print("=" * 50)
    print("This script tests that contact view and edit buttons work.")
    print("=" * 50)
    
    # Test routes
    routes_work = test_contact_routes()
    
    # If no contacts found, try creating one
    if routes_work and "No contacts found" in str(routes_work):
        print("\nNo contacts found. Creating a test contact...")
        contact_created = test_contact_creation()
        if contact_created:
            print("Now testing routes with the new contact...")
            routes_work = test_contact_routes()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if routes_work:
        print("✅ CONTACT ACTION BUTTONS SHOULD WORK!")
        print("\nIf buttons still don't work in browser:")
        print("• Check browser console for JavaScript errors")
        print("• Try right-clicking and 'Open in new tab'")
        print("• Clear browser cache and refresh")
        print("• Check if there are any popup blockers")
    else:
        print("❌ CONTACT ACTION BUTTONS HAVE ISSUES!")
        print("\nPossible issues:")
        print("• Routes are not properly defined")
        print("• Database connection problems")
        print("• Template rendering issues")
    
    return routes_work

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
