#!/usr/bin/env python3
"""
Check Environment Variables
===========================
Check if the .env file is being loaded correctly.
"""

import os
from dotenv import load_dotenv

def check_env():
    """Check environment variables"""
    print("🔍 Checking Environment Variables...")
    
    # Load .env file
    load_dotenv()
    
    # Check email configuration
    mail_server = os.getenv('MAIL_SERVER')
    mail_port = os.getenv('MAIL_PORT')
    mail_username = os.getenv('MAIL_USERNAME')
    mail_password = os.getenv('MAIL_PASSWORD')
    
    print(f"MAIL_SERVER: {mail_server}")
    print(f"MAIL_PORT: {mail_port}")
    print(f"MAIL_USERNAME: {mail_username}")
    print(f"MAIL_PASSWORD: {'*' * len(mail_password) if mail_password else 'Not set'}")
    
    if all([mail_server, mail_port, mail_username, mail_password]):
        print("✅ All email environment variables are set!")
        return True
    else:
        print("❌ Some email environment variables are missing!")
        return False

if __name__ == "__main__":
    check_env()
