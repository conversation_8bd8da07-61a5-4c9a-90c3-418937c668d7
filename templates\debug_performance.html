{% extends "base.html" %}

{% block title %}Performance Monitor - Debug Dashboard{% endblock %}

{% block extra_css %}
<style>
    .performance-card {
        border-left: 4px solid #ffc107;
    }
    .slow-function {
        background-color: #fff3cd;
        border-left: 3px solid #ffc107;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 4px;
    }
    .function-stats {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt text-warning"></i> Performance Monitor</h1>
    <div>
        <a href="{{ url_for('debug.debug_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Debug Dashboard
        </a>
        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs('performance')">
            <i class="fas fa-trash"></i> Clear Performance Logs
        </button>
    </div>
</div>

<!-- Performance Summary -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card performance-card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ data.total_calls or 0 }}</h4>
                        <small class="text-muted">Total Function Calls</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">{{ data.successful_calls or 0 }}</h4>
                        <small class="text-muted">Successful Calls</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">{{ data.failed_calls or 0 }}</h4>
                        <small class="text-muted">Failed Calls</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ (data.slow_functions|length) or 0 }}</h4>
                        <small class="text-muted">Slow Functions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Function Statistics -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Function Statistics</h5>
            </div>
            <div class="card-body">
                {% if data.function_stats %}
                    {% for func_name, stats in data.function_stats %}
                    <div class="function-stats">
                        <div class="d-flex justify-content-between align-items-center">
                            <strong>{{ func_name }}</strong>
                            <span class="badge bg-{% if stats.avg_duration > 1.0 %}warning{% elif stats.failures > 0 %}danger{% else %}success{% endif %}">
                                {{ "%.3f"|format(stats.avg_duration) }}s avg
                            </span>
                        </div>
                        <div class="row mt-2">
                            <div class="col-sm-3">
                                <small class="text-muted">Calls:</small><br>
                                <strong>{{ stats.calls }}</strong>
                            </div>
                            <div class="col-sm-3">
                                <small class="text-muted">Max Duration:</small><br>
                                <strong>{{ "%.3f"|format(stats.max_duration) }}s</strong>
                            </div>
                            <div class="col-sm-3">
                                <small class="text-muted">Total Time:</small><br>
                                <strong>{{ "%.3f"|format(stats.total_duration) }}s</strong>
                            </div>
                            <div class="col-sm-3">
                                <small class="text-muted">Failures:</small><br>
                                <strong class="{% if stats.failures > 0 %}text-danger{% endif %}">{{ stats.failures }}</strong>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-bar fa-2x mb-3"></i>
                        <p>No function statistics available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Slow Functions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock text-warning"></i> Recent Slow Functions</h5>
            </div>
            <div class="card-body">
                {% if data.slow_functions %}
                    <div style="max-height: 500px; overflow-y: auto;">
                        {% for func in data.slow_functions %}
                        <div class="slow-function">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>{{ func.function_name }}</strong>
                                <span class="badge bg-{% if func.success %}warning{% else %}danger{% endif %}">
                                    {{ "%.3f"|format(func.duration or 0) }}s
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mt-1">
                                <small class="text-muted">{{ func.timestamp }}</small>
                                <span class="badge bg-{% if func.success %}success{% else %}danger{% endif %}">
                                    {{ 'Success' if func.success else 'Failed' }}
                                </span>
                            </div>
                            {% if func.error %}
                            <div class="mt-2">
                                <small class="text-danger">Error: {{ func.error }}</small>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-3"></i>
                        <p>No slow functions detected</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Performance Tips -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb text-info"></i> Performance Tips</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Database Optimization:</h6>
                        <ul class="small">
                            <li>Add indexes to frequently queried columns</li>
                            <li>Use pagination for large result sets</li>
                            <li>Consider database connection pooling</li>
                            <li>Optimize complex queries</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Application Optimization:</h6>
                        <ul class="small">
                            <li>Implement caching for expensive operations</li>
                            <li>Use background tasks for heavy processing</li>
                            <li>Optimize template rendering</li>
                            <li>Minimize external API calls</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearLogs(type) {
    if (confirm('Are you sure you want to clear ' + type + ' logs?')) {
        fetch('/debug/api/clear-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({type: type})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error clearing logs: ' + error);
        });
    }
}
</script>
{% endblock %}
