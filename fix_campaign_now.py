#!/usr/bin/env python3
"""
Fix Campaign Now
================
Quick fix for the campaign group selection issue.
"""

import sqlite3
import json
import os

def fix_campaign_now():
    """Fix the campaign to only send to the test group"""
    try:
        # Try different database files
        possible_dbs = ["unified_sales.db", "sales_system.db", "sales_department.db"]
        db_path = None

        for db_file in possible_dbs:
            if os.path.exists(db_file):
                db_path = db_file
                break

        if not db_path:
            print(f"❌ No database file found. Tried: {possible_dbs}")
            return False

        print(f"📊 Using database: {db_path}")

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔧 FIXING CAMPAIGN GROUP SELECTION")
        print("=" * 40)

        # 1. Find the test group
        cursor.execute("SELECT id, name FROM contact_groups WHERE name = 'test'")
        group = cursor.fetchone()

        if not group:
            print("❌ Group 'test' not found")
            print("\nAvailable groups:")
            cursor.execute("SELECT id, name FROM contact_groups")
            groups = cursor.fetchall()
            for g in groups:
                print(f"   - ID: {g[0]}, Name: {g[1]}")
            return False

        group_id, group_name = group
        print(f"✅ Found group: {group_name} (ID: {group_id})")

        # 2. Check contacts in this group
        cursor.execute("""
            SELECT c.id, c.first_name, c.last_name, c.email
            FROM contacts c
            JOIN contact_group_memberships cgm ON c.id = cgm.contact_id
            WHERE cgm.group_id = ? AND c.is_active = 1 AND c.do_not_email = 0
        """, (group_id,))
        contacts_in_group = cursor.fetchall()

        print(f"📧 Contacts in '{group_name}' group:")
        if contacts_in_group:
            for contact in contacts_in_group:
                print(f"   - {contact[1]} {contact[2]} ({contact[3]})")
        else:
            print("   No contacts found in group")
            return False

        # 3. Find campaigns to fix
        cursor.execute("SELECT id, name, recipient_criteria FROM email_campaigns")
        campaigns = cursor.fetchall()

        print(f"\n📋 Available campaigns:")
        for campaign in campaigns:
            print(f"   - ID: {campaign[0]}, Name: {campaign[1]}")
            print(f"     Current criteria: {campaign[2]}")

        # 4. Fix the specific campaign
        campaign_name = "24seven Assistants"
        cursor.execute("SELECT id, name FROM email_campaigns WHERE name = ?", (campaign_name,))
        campaign = cursor.fetchone()

        if not campaign:
            print(f"❌ Campaign '{campaign_name}' not found")
            return False

        campaign_id, campaign_name = campaign
        print(f"\n🎯 Fixing campaign: {campaign_name} (ID: {campaign_id})")

        # 5. Create proper recipient criteria
        new_criteria = {
            'type': 'groups',
            'group_ids': [group_id]
        }

        criteria_json = json.dumps(new_criteria)

        # 6. Update the campaign
        cursor.execute("""
            UPDATE email_campaigns
            SET recipient_criteria = ?, status = 'draft'
            WHERE id = ?
        """, (criteria_json, campaign_id))

        conn.commit()

        print(f"✅ Campaign updated successfully!")
        print(f"   New recipient criteria: {criteria_json}")
        print(f"   Status reset to 'draft'")
        print(f"   Campaign will now send to ONLY {len(contacts_in_group)} contact(s) in the '{group_name}' group")

        # 7. Verify the fix
        cursor.execute("SELECT recipient_criteria FROM email_campaigns WHERE id = ?", (campaign_id,))
        updated_criteria = cursor.fetchone()[0]
        print(f"\n🔍 Verification:")
        print(f"   Updated criteria in database: {updated_criteria}")

        conn.close()

        print(f"\n🎉 SUCCESS! Your campaign is now fixed.")
        print(f"💡 Next steps:")
        print(f"   1. Go to your campaigns page")
        print(f"   2. Click on '{campaign_name}' campaign")
        print(f"   3. Click 'Send Campaign'")
        print(f"   4. It will now send to ONLY the {len(contacts_in_group)} contact(s) in the '{group_name}' group")

        return True

    except Exception as e:
        print(f"❌ Error fixing campaign: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    fix_campaign_now()
