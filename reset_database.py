#!/usr/bin/env python3
"""
Reset Database
==============
Reset the database with correct schema and create test contact
"""

import os
import sqlite3
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

def reset_database():
    """Reset database with correct schema"""
    try:
        print("🔄 Resetting Database")
        print("=" * 50)
        
        # Remove old database if it exists
        db_path = 'unified_sales.db'
        if os.path.exists(db_path):
            os.remove(db_path)
            print("✅ Removed old database")
        
        setup_environment()
        from unified_sales_system import app, db
        
        with app.app_context():
            print("✅ Application context created")
            
            # Create all tables with correct schema
            print("📋 Creating database tables...")
            db.create_all()
            print("✅ Database tables created with correct schema")
            
            return True
            
    except Exception as e:
        print(f"❌ Database reset failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_contact():
    """Create test contact using Flask ORM"""
    try:
        print("\n👤 Creating Test Contact")
        print("-" * 30)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Create test contact exactly as per documentation
            contact = Contact(
                first_name='Alex',
                last_name='Scof',
                email='<EMAIL>',
                phone='(*************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new'
            )
            
            db.session.add(contact)
            db.session.commit()
            
            print(f"✅ Test contact created successfully")
            print(f"   ID: {contact.id}")
            print(f"   Name: {contact.full_name}")
            print(f"   Email: {contact.email}")
            print(f"   Company: {contact.company}")
            print(f"   Job Title: {contact.job_title}")
            print(f"   Status: {contact.status}")
            print(f"   Active: {contact.is_active}")
            print(f"   Do Not Email: {contact.do_not_email}")
            
            return True
            
    except Exception as e:
        print(f"❌ Test contact creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_contact_operations():
    """Verify all contact operations work"""
    try:
        print("\n🧪 Verifying Contact Operations")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Test 1: Query by email
            contact = Contact.query.filter_by(email='<EMAIL>').first()
            if not contact:
                print("❌ Contact query by email failed")
                return False
            print(f"✅ Query by email: {contact.full_name}")
            
            # Test 2: Count contacts
            total_contacts = Contact.query.count()
            print(f"✅ Total contacts: {total_contacts}")
            
            # Test 3: Count active contacts
            active_contacts = Contact.query.filter_by(is_active=True).count()
            print(f"✅ Active contacts: {active_contacts}")
            
            # Test 4: Count email-enabled contacts (for campaigns)
            email_enabled = Contact.query.filter_by(is_active=True, do_not_email=False).count()
            print(f"✅ Email-enabled contacts: {email_enabled}")
            
            # Test 5: Update contact
            contact.phone = '(*************'
            db.session.commit()
            print("✅ Contact update successful")
            
            # Test 6: Verify update
            updated_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if updated_contact.phone == '(*************':
                print("✅ Contact update verification successful")
            else:
                print("❌ Contact update verification failed")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Contact operations verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contact_form_simulation():
    """Simulate the contact form submission"""
    try:
        print("\n📝 Testing Contact Form Simulation")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Simulate form data
            form_data = {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'phone': '(*************',
                'company': 'Example Corp',
                'job_title': 'Manager',
                'source': 'manual_entry'
            }
            
            # Create contact exactly like the form does
            contact = Contact(
                first_name=form_data['first_name'],
                last_name=form_data['last_name'],
                email=form_data['email'],
                phone=form_data['phone'],
                company=form_data['company'],
                job_title=form_data['job_title'],
                source=form_data['source'],
                status='new'
            )
            
            db.session.add(contact)
            db.session.commit()
            
            print(f"✅ Form simulation successful: {contact.full_name}")
            
            # Verify it can be queried
            test_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if test_contact:
                print(f"✅ Form contact verification: {test_contact.full_name}")
                return True
            else:
                print("❌ Form contact verification failed")
                return False
                
    except Exception as e:
        print(f"❌ Contact form simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main reset function"""
    print("🚀 DATABASE RESET AND INITIALIZATION")
    print("=" * 60)
    print("Resetting database with correct schema for contact system")
    print("=" * 60)
    
    # Step 1: Reset database
    reset_success = reset_database()
    
    # Step 2: Create test contact
    if reset_success:
        contact_success = create_test_contact()
    else:
        contact_success = False
    
    # Step 3: Verify operations
    if contact_success:
        verify_success = verify_contact_operations()
    else:
        verify_success = False
    
    # Step 4: Test form simulation
    if verify_success:
        form_success = test_contact_form_simulation()
    else:
        form_success = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DATABASE RESET SUMMARY")
    print("=" * 60)
    print(f"Database Reset: {'✅ SUCCESS' if reset_success else '❌ FAILED'}")
    print(f"Test Contact: {'✅ CREATED' if contact_success else '❌ FAILED'}")
    print(f"Operations Verify: {'✅ WORKING' if verify_success else '❌ FAILED'}")
    print(f"Form Simulation: {'✅ WORKING' if form_success else '❌ FAILED'}")
    
    if all([reset_success, contact_success, verify_success, form_success]):
        print("\n🎉 DATABASE RESET COMPLETED SUCCESSFULLY!")
        print("\n✅ Database schema is correct")
        print("✅ Contact model matches database")
        print("✅ Test contacts created")
        print("✅ All operations working")
        print("✅ Form simulation successful")
        
        print("\n🚀 CONTACT SYSTEM IS NOW READY!")
        print("=" * 40)
        print("Next steps:")
        print("1. Start the application: python start_app_simple.py")
        print("2. Go to: http://localhost:5000/contacts/add")
        print("3. Add a contact (should work without errors)")
        print("4. Go to: http://localhost:5000/campaigns")
        print("5. Create and send a campaign")
        print("6. Check <EMAIL> for the email")
        
        print("\n✅ Email system confirmed working (SMTP test passed)")
        print("✅ Contact system now fully functional")
        print("✅ Database schema matches Contact model")
        print("✅ Ready for complete email campaign testing!")
    else:
        print("\n❌ DATABASE RESET HAD ISSUES")
        print("Check the error messages above")
    
    return all([reset_success, contact_success, verify_success, form_success])

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
