#!/usr/bin/env python3
"""
Web Campaign Test
================
Test campaign sending via web interface simulation
"""

import requests
import json
import time

def test_web_campaign():
    """Test campaign via web interface"""
    try:
        print("🌐 Testing Campaign via Web Interface")
        print("=" * 60)
        
        base_url = "http://localhost:5000"
        
        # Test 1: Check if application is running
        print("🔍 Step 1: Checking if application is running...")
        try:
            response = requests.get(f"{base_url}/", timeout=10)
            if response.status_code == 200:
                print("✅ Application is running")
            else:
                print(f"⚠️ Application returned status {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Application is not running")
            print("   Please start with: python start_with_gmail.py")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
        
        # Test 2: Check campaigns page
        print("\n📧 Step 2: Checking campaigns page...")
        try:
            response = requests.get(f"{base_url}/campaigns", timeout=10)
            if response.status_code == 200:
                print("✅ Campaigns page accessible")
            else:
                print(f"⚠️ Campaigns page returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Campaigns page error: {e}")
            return False
        
        # Test 3: Check SMTP test endpoint
        print("\n🔗 Step 3: Testing SMTP via web API...")
        try:
            response = requests.post(f"{base_url}/api/test-smtp", timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ SMTP test passed: {result.get('message')}")
                else:
                    print(f"❌ SMTP test failed: {result.get('message')}")
                    return False
            else:
                print(f"⚠️ SMTP test API returned status {response.status_code}")
        except Exception as e:
            print(f"❌ SMTP test API error: {e}")
            return False
        
        # Test 4: Check contacts
        print("\n👥 Step 4: Checking contacts...")
        try:
            response = requests.get(f"{base_url}/api/contacts/count?active=true&do_not_email=false", timeout=10)
            if response.status_code == 200:
                result = response.json()
                contact_count = result.get('count', 0)
                print(f"✅ Found {contact_count} email-enabled contacts")
                if contact_count == 0:
                    print("⚠️ No email-enabled contacts found")
                    print("   You may need to add a contact first")
            else:
                print(f"⚠️ Contacts API returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Contacts API error: {e}")
        
        print("\n🎉 Web interface tests completed successfully!")
        print("\n📋 Manual Steps to Send Campaign:")
        print("1. Go to: http://localhost:5000/campaigns")
        print("2. If no campaigns exist, click 'Create New Campaign'")
        print("3. Fill in campaign details:")
        print("   - Name: Test Campaign to Gmail")
        print("   - Subject: Test Email from 24Seven Assistants")
        print("   - Template: Introduction")
        print("   - Recipients: All contacts")
        print("4. Click 'Create Campaign'")
        print("5. Click 'Send' on the created campaign")
        print("6. Check <EMAIL> for the campaign email")
        
        return True
        
    except Exception as e:
        print(f"❌ Web campaign test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_contact_via_api():
    """Create test contact via API"""
    try:
        print("\n👤 Creating test contact via API...")
        
        base_url = "http://localhost:5000"
        
        contact_data = {
            'first_name': 'Alex',
            'last_name': 'Scof',
            'email': '<EMAIL>',
            'company': 'Test Company',
            'job_title': 'CEO',
            'source': 'api_test',
            'status': 'active'
        }
        
        response = requests.post(f"{base_url}/contacts/add", data=contact_data, timeout=10)
        
        if response.status_code in [200, 302]:  # 302 is redirect after successful creation
            print("✅ Test contact created successfully")
            return True
        else:
            print(f"⚠️ Contact creation returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Contact creation failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 24Seven Assistants - Web Campaign Test")
    print("Testing campaign system via web interface")
    print("=" * 60)
    
    # Test web interface
    web_success = test_web_campaign()
    
    # Create test contact if web interface is working
    if web_success:
        contact_success = create_test_contact_via_api()
    else:
        contact_success = False
    
    print("\n" + "=" * 60)
    print("📊 WEB CAMPAIGN TEST SUMMARY")
    print("=" * 60)
    print(f"Web Interface: {'✅ WORKING' if web_success else '❌ FAILED'}")
    print(f"Test Contact: {'✅ CREATED' if contact_success else '❌ FAILED'}")
    
    if web_success:
        print("\n🎉 SUCCESS! The campaign system is ready!")
        print("\n📧 To send a test <NAME_EMAIL>:")
        print("1. Open: http://localhost:5000/campaigns")
        print("2. Create a new campaign or use existing one")
        print("3. Click 'Send' to send the campaign")
        print("4. Check your Gmail inbox")
        print("\n✅ The SMTP system is working (confirmed by previous test)")
        print("✅ The web interface is accessible")
        print("✅ The campaign system is ready to send emails")
    else:
        print("\n❌ Web interface test failed")
        print("Please ensure the application is running with:")
        print("   python start_with_gmail.py")
    
    return web_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
