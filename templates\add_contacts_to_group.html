{% extends "base.html" %}

{% block title %}Add Contacts to {{ group.name }} - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-user-plus"></i> Add Contacts to Group</h2>
                    <p class="text-muted">
                        Group: <span class="badge" style="background-color: {{ group.color }}; color: white;">{{ group.name }}</span>
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Group
                    </a>
                </div>
            </div>

            {% if available_contacts %}
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users"></i> Available Contacts ({{ available_contacts|length }})</h5>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNone()">
                                <i class="fas fa-square"></i> Select None
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search Filter -->
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="contactSearch" placeholder="Search contacts by name, email, or company..." onkeyup="filterContacts()">
                        </div>
                    </div>

                    <form method="POST">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                        </th>
                                        <th>Contact</th>
                                        <th>Company</th>
                                        <th>Status</th>
                                        <th>Source</th>
                                    </tr>
                                </thead>
                                <tbody id="contactsTableBody">
                                    {% for contact in available_contacts %}
                                    <tr class="contact-row">
                                        <td>
                                            <input type="checkbox" name="contact_ids" value="{{ contact.id }}" class="contact-checkbox">
                                        </td>
                                        <td class="contact-info">
                                            <div>
                                                <strong>{{ contact.full_name }}</strong><br>
                                                <small class="text-muted">{{ contact.email }}</small>
                                                {% if contact.phone %}
                                                <br><small class="text-muted"><i class="fas fa-phone"></i> {{ contact.phone }}</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="company-info">
                                            {% if contact.company %}
                                            <div>
                                                <strong>{{ contact.company }}</strong>
                                                {% if contact.job_title %}
                                                <br><small class="text-muted">{{ contact.job_title }}</small>
                                                {% endif %}
                                            </div>
                                            {% else %}
                                            <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if contact.status == 'active' %}
                                            <span class="badge bg-success">Active</span>
                                            {% elif contact.status == 'new' %}
                                            <span class="badge bg-primary">New</span>
                                            {% else %}
                                            <span class="badge bg-secondary">{{ contact.status|title }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">{{ contact.source or 'Unknown' }}</small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span id="selectedCount">0</span> contact(s) selected
                            </div>
                            <div>
                                <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary" id="addContactsBtn" disabled>
                                    <i class="fas fa-user-plus"></i> Add Selected Contacts
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Available Contacts</h4>
                <p class="text-muted">All contacts are already in this group, or you don't have any contacts yet.</p>
                <div>
                    <a href="{{ url_for('add_contact') }}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> Add New Contact
                    </a>
                    <a href="{{ url_for('upload_contacts') }}" class="btn btn-success me-2">
                        <i class="fas fa-upload"></i> Upload Contacts
                    </a>
                    <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Group
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
let selectedContacts = new Set();

function updateSelectedCount() {
    const count = selectedContacts.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('addContactsBtn').disabled = count === 0;
}

function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.contact-row:not([style*="display: none"]) .contact-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
        selectedContacts.add(checkbox.value);
    });
    updateSelectedCount();
    updateSelectAllCheckbox();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        selectedContacts.delete(checkbox.value);
    });
    updateSelectedCount();
    updateSelectAllCheckbox();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const visibleCheckboxes = document.querySelectorAll('.contact-row:not([style*="display: none"]) .contact-checkbox');
    
    if (selectAllCheckbox.checked) {
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedContacts.add(checkbox.value);
        });
    } else {
        visibleCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            selectedContacts.delete(checkbox.value);
        });
    }
    updateSelectedCount();
}

function updateSelectAllCheckbox() {
    const visibleCheckboxes = document.querySelectorAll('.contact-row:not([style*="display: none"]) .contact-checkbox');
    const checkedVisibleCheckboxes = document.querySelectorAll('.contact-row:not([style*="display: none"]) .contact-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    
    if (visibleCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedVisibleCheckboxes.length === visibleCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else if (checkedVisibleCheckboxes.length > 0) {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    } else {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    }
}

function filterContacts() {
    const searchTerm = document.getElementById('contactSearch').value.toLowerCase();
    const rows = document.querySelectorAll('.contact-row');
    
    rows.forEach(row => {
        const contactInfo = row.querySelector('.contact-info').textContent.toLowerCase();
        const companyInfo = row.querySelector('.company-info').textContent.toLowerCase();
        
        if (contactInfo.includes(searchTerm) || companyInfo.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    updateSelectAllCheckbox();
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to contact checkboxes
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedContacts.add(this.value);
            } else {
                selectedContacts.delete(this.value);
            }
            updateSelectedCount();
            updateSelectAllCheckbox();
        });
    });
    
    // Initialize counts
    updateSelectedCount();
    updateSelectAllCheckbox();
    
    // Add debounce to search input
    let searchTimeout;
    document.getElementById('contactSearch').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(filterContacts, 300);
    });
});
</script>
{% endblock %}
