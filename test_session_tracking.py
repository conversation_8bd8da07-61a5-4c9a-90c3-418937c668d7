#!/usr/bin/env python3
"""
Test script for enhanced session tracking functionality
Tests the complete email-to-conversion funnel tracking
"""

import requests
import json
import time
import uuid
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_session_tracking():
    """Test the complete session tracking flow"""
    print("🧪 Testing Enhanced Session Tracking")
    print("=" * 50)
    
    # Generate test session ID
    session_id = str(uuid.uuid4())
    print(f"📝 Test Session ID: {session_id}")
    
    # Test 1: Start conversation
    print("\n1️⃣ Testing conversation start...")
    response = requests.post(f"{BASE_URL}/api/track-session", json={
        'session_id': session_id,
        'stage': 'opening',
        'task': 'greeting',
        'contact_name': 'Test User',
        'contact_email': '<EMAIL>',
        'action': 'conversation_started',
        'message_count': 1,
        'bot_response': 'Hello! Welcome to 24Seven Assistants.'
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Conversation start tracked: {result}")
        contact_id = result.get('contact_id')
    else:
        print(f"❌ Failed to track conversation start: {response.status_code}")
        return False
    
    # Test 2: Stage progression through all stages
    stages = [
        ('trust', 'building_rapport'),
        ('discovery', 'understanding_challenges'),
        ('demonstration', 'showing_pricing'),
        ('close', 'asking_for_commitment')
    ]
    
    for i, (stage, task) in enumerate(stages, 2):
        print(f"\n{i}️⃣ Testing stage progression to {stage}...")
        time.sleep(1)  # Small delay between requests
        
        response = requests.post(f"{BASE_URL}/api/track-session", json={
            'session_id': session_id,
            'stage': stage,
            'task': task,
            'action': 'stage_progression',
            'message_count': i * 3,
            'user_message': f'User response for {stage} stage',
            'bot_response': f'Bot response for {stage} stage'
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stage {stage} tracked: {result.get('message', 'Success')}")
        else:
            print(f"❌ Failed to track stage {stage}: {response.status_code}")
    
    # Test 3: Objection handling
    print(f"\n{len(stages) + 2}️⃣ Testing objection handling...")
    response = requests.post(f"{BASE_URL}/api/track-session", json={
        'session_id': session_id,
        'stage': 'demonstration',
        'task': 'handling_objection',
        'action': 'objection_handled',
        'message_count': 15,
        'user_message': 'This seems too expensive for our budget',
        'bot_response': 'I understand your concern about budget. Let me show you our ROI calculator...'
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Objection handling tracked: {result.get('message', 'Success')}")
    else:
        print(f"❌ Failed to track objection handling: {response.status_code}")
    
    # Test 4: Conversion
    print(f"\n{len(stages) + 3}️⃣ Testing conversion...")
    response = requests.post(f"{BASE_URL}/api/track-session", json={
        'session_id': session_id,
        'stage': 'close',
        'task': 'conversion_completed',
        'action': 'stage_progression',
        'contact_email': '<EMAIL>',
        'conversion_completed': True,
        'conversion_value': 2500.00,
        'message_count': 20
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Conversion tracked: {result.get('message', 'Success')}")
    else:
        print(f"❌ Failed to track conversion: {response.status_code}")
    
    # Test 5: Get session analytics
    print(f"\n{len(stages) + 4}️⃣ Testing session analytics...")
    try:
        response = requests.get(f"{BASE_URL}/analytics/sessions")
        if response.status_code == 200:
            print("✅ Session analytics page accessible")
        else:
            print(f"❌ Session analytics page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Session analytics error: {e}")
    
    print("\n📊 Testing complete! Check the analytics dashboard to see the tracked data.")
    print(f"🔗 Visit: {BASE_URL}/analytics/sessions")
    
    return True

def test_email_to_session_tracking():
    """Test email-to-session tracking"""
    print("\n🧪 Testing Email-to-Session Tracking")
    print("=" * 50)
    
    # Create a test contact with session ID first
    session_id = str(uuid.uuid4())
    
    # First, create a session
    response = requests.post(f"{BASE_URL}/api/track-session", json={
        'session_id': session_id,
        'stage': 'opening',
        'task': 'greeting',
        'contact_name': 'Email Test User',
        'contact_email': '<EMAIL>',
        'action': 'conversation_started'
    })
    
    if response.status_code == 200:
        print("✅ Test contact created")
        
        # Now test email-to-session tracking
        response = requests.get(f"{BASE_URL}/api/session-from-email/{session_id}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Email-to-session tracking: {result.get('message', 'Success')}")
        else:
            print(f"❌ Email-to-session tracking failed: {response.status_code}")
    else:
        print("❌ Failed to create test contact")

def test_analytics_data():
    """Test analytics data retrieval"""
    print("\n🧪 Testing Analytics Data")
    print("=" * 50)
    
    try:
        # Test main analytics
        response = requests.get(f"{BASE_URL}/analytics")
        if response.status_code == 200:
            print("✅ Main analytics dashboard accessible")
        else:
            print(f"❌ Main analytics failed: {response.status_code}")
        
        # Test session analytics
        response = requests.get(f"{BASE_URL}/analytics/sessions")
        if response.status_code == 200:
            print("✅ Session analytics dashboard accessible")
        else:
            print(f"❌ Session analytics failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Analytics test error: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Session Tracking Tests")
    print(f"🎯 Target URL: {BASE_URL}")
    print(f"⏰ Test started at: {datetime.now()}")
    print("=" * 60)
    
    try:
        # Test if server is running
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print(f"❌ Server not accessible at {BASE_URL}")
            return
        
        print("✅ Server is running")
        
        # Run tests
        test_session_tracking()
        test_email_to_session_tracking()
        test_analytics_data()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed!")
        print(f"📊 View results at: {BASE_URL}/analytics/sessions")
        print("💡 Tip: Refresh the analytics page to see the latest data")
        
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to server at {BASE_URL}")
        print("💡 Make sure the unified sales system is running:")
        print("   python unified_sales_system.py")
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    main()
