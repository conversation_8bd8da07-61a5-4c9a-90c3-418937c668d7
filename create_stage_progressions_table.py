#!/usr/bin/env python3
"""
Create Stage Progressions Table
Adds the stage_progressions table for tracking contact stage progression
"""

import sqlite3
import os

def create_stage_progressions_table():
    """Create the stage_progressions table"""
    db_path = 'sales_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print(f"🔄 Adding stage_progressions table to: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if table already exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stage_progressions'")
        if cursor.fetchone():
            print("⏭️  stage_progressions table already exists")
            return True
        
        # Create stage_progressions table
        cursor.execute('''
            CREATE TABLE stage_progressions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                contact_id INTEGER NOT NULL,
                stage_name VARCHAR(50) NOT NULL,
                occurred_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (contact_id) REFERENCES contacts (id)
            )
        ''')
        
        print("✅ stage_progressions table created successfully")
        
        # Create index for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stage_progressions_contact_id ON stage_progressions(contact_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stage_progressions_stage_name ON stage_progressions(stage_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stage_progressions_occurred_at ON stage_progressions(occurred_at)')
        
        print("✅ Indexes created for stage_progressions table")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Error creating table: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 Stage Progressions Table Creation Tool")
    print("=" * 50)
    
    success = create_stage_progressions_table()
    if success:
        print("\n✅ Table creation completed successfully!")
        print("You can now restart the Flask application.")
    else:
        print("\n❌ Table creation failed.")
