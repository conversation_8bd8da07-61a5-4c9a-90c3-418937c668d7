"""initial schema

Revision ID: 9a7b1c7bc97f
Revises: 
Create Date: 2025-06-22 13:02:24.982045

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9a7b1c7bc97f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stage_progressions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('contact_id', sa.Integer(), nullable=False),
    sa.Column('stage_name', sa.String(length=50), nullable=False),
    sa.Column('occurred_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('sales_stages')
    op.drop_table('opportunities')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('opportunities',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), nullable=False),
    sa.Column('contact_id', sa.INTEGER(), nullable=True),
    sa.Column('current_stage_id', sa.INTEGER(), nullable=True),
    sa.Column('estimated_value', sa.FLOAT(), nullable=True),
    sa.Column('probability_percent', sa.FLOAT(), nullable=True),
    sa.Column('status', sa.VARCHAR(length=50), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('bot_session_id', sa.VARCHAR(length=100), nullable=True),
    sa.ForeignKeyConstraint(['contact_id'], ['contacts.id'], ),
    sa.ForeignKeyConstraint(['current_stage_id'], ['sales_stages.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sales_stages',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('order', sa.INTEGER(), nullable=False),
    sa.Column('probability_percent', sa.FLOAT(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('stage_progressions')
    # ### end Alembic commands ###
