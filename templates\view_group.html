{% extends "base.html" %}

{% block title %}{{ group.name }} - Contact Group{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <!-- Group Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <span class="badge me-2" style="background-color: {{ group.color }}; color: white;">
                            <i class="fas fa-layer-group"></i>
                        </span>
                        {{ group.name }}
                    </h2>
                    {% if group.description %}
                    <p class="text-muted">{{ group.description }}</p>
                    {% endif %}
                </div>
                <div>
                    <a href="{{ url_for('add_contacts_to_group', group_id=group.id) }}" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> Add Contacts
                    </a>
                    <a href="{{ url_for('edit_group', group_id=group.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Group
                    </a>
                    <a href="{{ url_for('groups_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Groups
                    </a>
                </div>
            </div>

            <!-- Group Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary">{{ total_contacts }}</h3>
                            <p class="mb-0">Total Contacts</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">{{ group.created_at.strftime('%Y-%m-%d') }}</h3>
                            <p class="mb-0">Created</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success">{{ group.created_by or 'System' }}</h3>
                            <p class="mb-0">Created By</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">Active</h3>
                            <p class="mb-0">Status</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contacts List -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users"></i> Group Contacts ({{ total_contacts }})</h5>
                        <div class="d-flex align-items-center">
                            {% if total_contacts > 0 %}
                            <button class="btn btn-outline-success btn-sm me-2" onclick="exportGroupContacts()">
                                <i class="fas fa-download"></i> Export CSV
                            </button>
                            {% endif %}
                            <label for="per_page" class="form-label me-2 mb-0">Show:</label>
                            <select id="per_page" class="form-select form-select-sm" style="width: auto;" onchange="changePerPage(this.value)">
                                <option value="10" {% if request.args.get('per_page', '20') == '10' %}selected{% endif %}>10</option>
                                <option value="20" {% if request.args.get('per_page', '20') == '20' %}selected{% endif %}>20</option>
                                <option value="50" {% if request.args.get('per_page', '20') == '50' %}selected{% endif %}>50</option>
                                <option value="100" {% if request.args.get('per_page', '20') == '100' %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if contacts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Contact</th>
                                    <th>Company</th>
                                    <th>Status</th>
                                    <th>Added to Group</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for contact in contacts %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ contact.full_name }}</strong><br>
                                            <small class="text-muted">{{ contact.email }}</small>
                                            {% if contact.phone %}
                                            <br><small class="text-muted"><i class="fas fa-phone"></i> {{ contact.phone }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if contact.company %}
                                        <div>
                                            <strong>{{ contact.company }}</strong>
                                            {% if contact.job_title %}
                                            <br><small class="text-muted">{{ contact.job_title }}</small>
                                            {% endif %}
                                        </div>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if contact.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                        {% elif contact.status == 'new' %}
                                        <span class="badge bg-primary">New</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ contact.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for membership in contact.group_memberships %}
                                        {% if membership.group_id == group.id %}
                                        <small class="text-muted">{{ membership.added_at.strftime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('view_contact', contact_id=contact.id) }}" class="btn btn-outline-primary btn-sm" title="View Contact">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-danger btn-sm" onclick="confirmRemoveContact({{ contact.id }}, '{{ contact.full_name }}')" title="Remove from Group">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if contacts_pagination.pages > 1 %}
                    <nav aria-label="Contacts pagination">
                        <ul class="pagination justify-content-center">
                            {% if contacts_pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_group', group_id=group.id, page=1, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_group', group_id=group.id, page=contacts_pagination.prev_num, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in contacts_pagination.iter_pages() %}
                            {% if page_num %}
                            {% if page_num != contacts_pagination.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_group', group_id=group.id, page=page_num, per_page=request.args.get('per_page', 20)) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if contacts_pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_group', group_id=group.id, page=contacts_pagination.next_num, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('view_group', group_id=group.id, page=contacts_pagination.pages, per_page=request.args.get('per_page', 20)) }}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>

                    <div class="text-center text-muted">
                        Showing {{ contacts_pagination.per_page * (contacts_pagination.page - 1) + 1 }} to 
                        {{ contacts_pagination.per_page * (contacts_pagination.page - 1) + contacts|length }} of 
                        {{ total_contacts }} contacts
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Contacts in Group</h5>
                        <p class="text-muted">This group doesn't have any contacts yet.</p>
                        <a href="{{ url_for('add_contacts_to_group', group_id=group.id) }}" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Add Contacts
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Contact Modal -->
<div class="modal fade" id="removeContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Contact from Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to remove "<span id="contactNameToRemove"></span>" from this group?</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> This will only remove the contact from this group. The contact will remain in the system.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="removeContactForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-minus"></i> Remove from Group
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', '1');
    window.location.href = url.toString();
}

function confirmRemoveContact(contactId, contactName) {
    document.getElementById('contactNameToRemove').textContent = contactName;
    document.getElementById('removeContactForm').action = `/groups/{{ group.id }}/remove_contact/${contactId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('removeContactModal'));
    modal.show();
}

function exportGroupContacts() {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tr');
    let csvContent = '';
    
    // Add header row
    const headers = ['Name', 'Email', 'Phone', 'Company', 'Job Title', 'Status', 'Added to Group'];
    csvContent += headers.join(',') + '\n';
    
    // Add data rows (skip header row)
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');
        const rowData = [];
        
        // Extract contact info
        const contactCell = cells[0];
        const contactLines = contactCell.textContent.trim().split('\n').map(line => line.trim()).filter(line => line);
        const name = contactLines[0] || '';
        const email = contactLines[1] || '';
        const phone = contactLines[2] ? contactLines[2].replace('📞', '').trim() : '';
        
        // Extract company info
        const companyCell = cells[1];
        const companyLines = companyCell.textContent.trim().split('\n').map(line => line.trim()).filter(line => line);
        const company = companyLines[0] || '';
        const jobTitle = companyLines[1] || '';
        
        // Extract other data
        const status = cells[2].textContent.trim();
        const addedDate = cells[3].textContent.trim();
        
        rowData.push('"' + name + '"');
        rowData.push('"' + email + '"');
        rowData.push('"' + phone + '"');
        rowData.push('"' + company + '"');
        rowData.push('"' + jobTitle + '"');
        rowData.push('"' + status + '"');
        rowData.push('"' + addedDate + '"');
        
        csvContent += rowData.join(',') + '\n';
    }
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'group_{{ group.id }}_{{ group.name|replace(" ", "_") }}_contacts.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>
{% endblock %}
