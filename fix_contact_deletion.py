#!/usr/bin/env python3
"""
Fix Contact Deletion Issue
==========================
Creates missing chat_messages table and fixes the contact deletion functionality.
"""

import sqlite3
import os

def check_and_create_chat_messages_table():
    """Check if chat_messages table exists and create it if missing"""
    print("🔍 Checking chat_messages table...")
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if chat_messages table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chat_messages'")
        table_exists = cursor.fetchone() is not None
        
        if table_exists:
            print("✅ chat_messages table already exists")
            conn.close()
            return True
        
        print("📝 Creating chat_messages table...")
        
        # Create chat_messages table
        cursor.execute('''
            CREATE TABLE chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(100) NOT NULL,
                message_type VARCHAR(20) NOT NULL,
                content TEXT NOT NULL,
                stage VARCHAR(50),
                task VARCHAR(200),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                message_order INTEGER NOT NULL,
                FOREIGN KEY (session_id) REFERENCES chatbot_sessions (session_id)
            )
        ''')
        
        print("✅ chat_messages table created successfully")
        
        # Create index for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id)")
        print("✅ Index created for chat_messages")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating chat_messages table: {e}")
        return False

def test_contact_deletion():
    """Test the contact deletion functionality"""
    print("\n🧪 Testing Contact Deletion...")
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if we have any contacts to test with
        cursor.execute("SELECT id FROM contacts LIMIT 1")
        test_contact = cursor.fetchone()
        
        if not test_contact:
            print("ℹ️ No contacts found to test deletion")
            conn.close()
            return True
        
        test_contact_id = test_contact[0]
        print(f"🎯 Testing deletion logic for contact ID: {test_contact_id}")
        
        # Test each deletion query individually (without actually deleting)
        deletion_queries = [
            # 1. Chat messages
            f"""SELECT COUNT(*) FROM chat_messages 
                WHERE session_id IN (
                    SELECT session_id FROM chatbot_sessions WHERE contact_id = {test_contact_id}
                )""",
            
            # 2. Chatbot sessions
            f"SELECT COUNT(*) FROM chatbot_sessions WHERE contact_id = {test_contact_id}",
            
            # 3. Activities
            f"SELECT COUNT(*) FROM activities WHERE contact_id = {test_contact_id}",
            
            # 4. Email logs
            f"SELECT COUNT(*) FROM email_logs WHERE contact_id = {test_contact_id}",
            
            # 5. Email failures
            f"SELECT COUNT(*) FROM email_failures WHERE contact_id = {test_contact_id}",
            
            # 6. Group memberships
            f"SELECT COUNT(*) FROM contact_group_memberships WHERE contact_id = {test_contact_id}",
            
            # 7. Opportunities
            f"SELECT COUNT(*) FROM opportunities WHERE contact_id = {test_contact_id}",
            
            # 8. Sales stages
            f"SELECT COUNT(*) FROM sales_stages WHERE contact_id = {test_contact_id}",
            
            # 9. Contact itself
            f"SELECT COUNT(*) FROM contacts WHERE id = {test_contact_id}"
        ]
        
        query_names = [
            "Chat messages",
            "Chatbot sessions", 
            "Activities",
            "Email logs",
            "Email failures",
            "Group memberships",
            "Opportunities",
            "Sales stages",
            "Contact record"
        ]
        
        all_queries_work = True
        
        for i, (query, name) in enumerate(zip(deletion_queries, query_names)):
            try:
                cursor.execute(query)
                count = cursor.fetchone()[0]
                print(f"   ✅ {name}: {count} records")
            except Exception as e:
                print(f"   ❌ {name}: Query failed - {e}")
                all_queries_work = False
        
        conn.close()
        
        if all_queries_work:
            print("✅ All deletion queries work correctly")
            return True
        else:
            print("❌ Some deletion queries failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing contact deletion: {e}")
        return False

def check_all_required_tables():
    """Check if all required tables exist for contact deletion"""
    print("\n📋 Checking all required tables...")
    
    db_path = 'unified_sales.db'
    
    if not os.path.exists(db_path):
        print("❌ Database file not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        # Required tables for contact deletion
        required_tables = [
            'contacts',
            'chat_messages',
            'chatbot_sessions',
            'activities',
            'email_logs',
            'email_failures',
            'contact_group_memberships',
            'opportunities',
            'sales_stages'
        ]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ {table}")
            else:
                print(f"   ❌ {table} (MISSING)")
                missing_tables.append(table)
        
        conn.close()
        
        if missing_tables:
            print(f"\n⚠️ Missing {len(missing_tables)} required tables:")
            for table in missing_tables:
                print(f"   • {table}")
            return False
        else:
            print("\n✅ All required tables exist")
            return True
            
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
        return False

def main():
    """Main function to fix contact deletion"""
    print("🚀 CONTACT DELETION FIX")
    print("=" * 50)
    print("This script will fix the contact deletion issue by:")
    print("1. Creating the missing chat_messages table")
    print("2. Testing all deletion queries")
    print("3. Verifying the fix works")
    print("=" * 50)
    
    # Step 1: Create chat_messages table
    step1_success = check_and_create_chat_messages_table()
    
    # Step 2: Check all required tables
    step2_success = check_all_required_tables()
    
    # Step 3: Test contact deletion logic
    step3_success = test_contact_deletion()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FIX SUMMARY")
    print("=" * 50)
    print(f"Chat messages table:    {'✅ OK' if step1_success else '❌ FAILED'}")
    print(f"Required tables:        {'✅ OK' if step2_success else '❌ FAILED'}")
    print(f"Deletion logic test:    {'✅ OK' if step3_success else '❌ FAILED'}")
    
    if all([step1_success, step2_success, step3_success]):
        print("\n🎉 CONTACT DELETION IS NOW FIXED!")
        print("You can now delete contacts successfully.")
        print("The bulk delete functionality should work without errors.")
    else:
        print("\n❌ SOME ISSUES REMAIN")
        print("Please check the error messages above.")
    
    return all([step1_success, step2_success, step3_success])

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
