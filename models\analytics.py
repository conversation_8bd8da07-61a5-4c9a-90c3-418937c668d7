"""
Analytics Model
==============
Stores aggregated analytics data for the sales department.
"""

from datetime import datetime, date
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, Date, JSON



class SalesAnalytics(db.Model):
    """Sales analytics aggregation model"""
    
    __tablename__ = 'sales_analytics'
    
    id = Column(Integer, primary_key=True)
    
    # Time Period
    date = Column(Date, nullable=False, index=True)
    period_type = Column(String(20), nullable=False, index=True)  # daily, weekly, monthly, quarterly, yearly
    
    # Sales Metrics
    total_opportunities = Column(Integer, default=0)
    new_opportunities = Column(Integer, default=0)
    won_opportunities = Column(Integer, default=0)
    lost_opportunities = Column(Integer, default=0)
    
    # Revenue Metrics
    total_revenue = Column(Float, default=0.0)
    new_revenue = Column(Float, default=0.0)
    pipeline_value = Column(Float, default=0.0)
    weighted_pipeline_value = Column(Float, default=0.0)
    
    # Conversion Metrics
    lead_to_opportunity_rate = Column(Float, default=0.0)
    opportunity_to_customer_rate = Column(Float, default=0.0)
    overall_conversion_rate = Column(Float, default=0.0)
    
    # Activity Metrics
    total_activities = Column(Integer, default=0)
    emails_sent = Column(Integer, default=0)
    calls_made = Column(Integer, default=0)
    meetings_held = Column(Integer, default=0)
    
    # Stage Metrics (JSON for flexibility)
    stage_metrics = Column(JSON, nullable=True)  # {stage_name: {count: X, value: Y}}
    
    # Lead Metrics
    total_leads = Column(Integer, default=0)
    new_leads = Column(Integer, default=0)
    qualified_leads = Column(Integer, default=0)
    
    # Performance Metrics
    average_deal_size = Column(Float, default=0.0)
    average_sales_cycle_days = Column(Float, default=0.0)
    average_time_per_stage = Column(JSON, nullable=True)  # {stage_name: avg_days}
    
    # Email Campaign Metrics
    emails_delivered = Column(Integer, default=0)
    emails_opened = Column(Integer, default=0)
    emails_clicked = Column(Integer, default=0)
    emails_replied = Column(Integer, default=0)
    email_open_rate = Column(Float, default=0.0)
    email_click_rate = Column(Float, default=0.0)
    email_reply_rate = Column(Float, default=0.0)
    
    # AI/Bot Metrics
    ai_interactions = Column(Integer, default=0)
    ai_generated_activities = Column(Integer, default=0)
    ai_success_rate = Column(Float, default=0.0)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    calculated_at = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f'<SalesAnalytics {self.date} - {self.period_type}>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'period_type': self.period_type,
            'total_opportunities': self.total_opportunities,
            'new_opportunities': self.new_opportunities,
            'won_opportunities': self.won_opportunities,
            'lost_opportunities': self.lost_opportunities,
            'total_revenue': self.total_revenue,
            'new_revenue': self.new_revenue,
            'pipeline_value': self.pipeline_value,
            'weighted_pipeline_value': self.weighted_pipeline_value,
            'lead_to_opportunity_rate': self.lead_to_opportunity_rate,
            'opportunity_to_customer_rate': self.opportunity_to_customer_rate,
            'overall_conversion_rate': self.overall_conversion_rate,
            'total_activities': self.total_activities,
            'emails_sent': self.emails_sent,
            'calls_made': self.calls_made,
            'meetings_held': self.meetings_held,
            'stage_metrics': self.stage_metrics,
            'total_leads': self.total_leads,
            'new_leads': self.new_leads,
            'qualified_leads': self.qualified_leads,
            'average_deal_size': self.average_deal_size,
            'average_sales_cycle_days': self.average_sales_cycle_days,
            'average_time_per_stage': self.average_time_per_stage,
            'emails_delivered': self.emails_delivered,
            'emails_opened': self.emails_opened,
            'emails_clicked': self.emails_clicked,
            'emails_replied': self.emails_replied,
            'email_open_rate': self.email_open_rate,
            'email_click_rate': self.email_click_rate,
            'email_reply_rate': self.email_reply_rate,
            'ai_interactions': self.ai_interactions,
            'ai_generated_activities': self.ai_generated_activities,
            'ai_success_rate': self.ai_success_rate,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'calculated_at': self.calculated_at.isoformat() if self.calculated_at else None
        }
    
    @classmethod
    def get_or_create_for_date(cls, target_date, period_type='daily'):
        """Get or create analytics record for a specific date"""
        analytics = cls.query.filter_by(date=target_date, period_type=period_type).first()
        if not analytics:
            analytics = cls(date=target_date, period_type=period_type)
            db.session.add(analytics)
        return analytics
    
    def calculate_rates(self):
        """Calculate conversion rates"""
        if self.total_leads > 0:
            self.lead_to_opportunity_rate = (self.total_opportunities / self.total_leads) * 100
        
        if self.total_opportunities > 0:
            self.opportunity_to_customer_rate = (self.won_opportunities / self.total_opportunities) * 100
        
        if self.total_leads > 0:
            self.overall_conversion_rate = (self.won_opportunities / self.total_leads) * 100
        
        if self.emails_delivered > 0:
            self.email_open_rate = (self.emails_opened / self.emails_delivered) * 100
            self.email_click_rate = (self.emails_clicked / self.emails_delivered) * 100
            self.email_reply_rate = (self.emails_replied / self.emails_delivered) * 100
        
        self.calculated_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
