"""
24Seven Assistants - Unified Sales System
=========================================
Complete integrated system where:
1. Email campaigns send links to the sales chatbot
2. Analytics track the full journey: Email → Chatbot → Sales Stages → Conversion
3. Real-time tracking from opening to closing the sale
"""

import os
import uuid
import threading
import time
import csv
import re
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_mail import Mail, Message
import plotly.graph_objects as go
import requests
from typing import Dict
import plotly
import json

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("⚠️ python-dotenv not installed. Environment variables from .env file will not be loaded.")
    print("   Install with: pip install python-dotenv")

# Import debug system
from debug_system import init_debug_system, performance_monitor
from debug_routes import register_debug_routes

# Import SalesBot for chat generation
from salesbot.bot import SalesBot
from salesbot.config_data import CONFIG_DATA

# Analytics models will be defined after db initialization

# EmailLog model will be defined after db initialization

# Global in-memory store for active SalesBot instances (keyed by session_id)
bot_instances: dict[str, SalesBot] = {}

# Ensure prints are safe on Windows consoles that cannot display emojis
import builtins
import sys

def _safe_print(*args, **kwargs):
    safe_args = []
    for arg in args:
        if isinstance(arg, str):
            try:
                arg.encode(sys.stdout.encoding)
                safe_args.append(arg)
            except UnicodeEncodeError:
                safe_args.append(arg.encode('ascii', 'ignore').decode())
        else:
            safe_args.append(arg)
    builtins.print(*safe_args, **kwargs)

print = _safe_print  # type: ignore

from datetime import datetime  # Needed for ChatbotSession timestamps

# Initialize Flask app
app = Flask(__name__)

# Configuration - Load from environment variables with fallbacks
app.config.update({
    'SECRET_KEY': os.getenv('SECRET_KEY', 'unified-sales-system-secret-key'),
    'SQLALCHEMY_DATABASE_URI': os.getenv('DATABASE_URL', 'sqlite:///unified_sales.db'),
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'MAIL_SERVER': os.getenv('MAIL_SERVER', 'smtp.gmail.com'),
    'MAIL_PORT': int(os.getenv('MAIL_PORT', '587')),
    'MAIL_USE_SSL': os.getenv('MAIL_USE_SSL', 'false').lower() == 'true',
    'MAIL_USE_TLS': os.getenv('MAIL_USE_TLS', 'true').lower() == 'true',
    'MAIL_USERNAME': os.getenv('MAIL_USERNAME', '<EMAIL>'),
    'MAIL_PASSWORD': os.getenv('MAIL_PASSWORD', ''),
    'MAIL_DEFAULT_SENDER': os.getenv('MAIL_DEFAULT_SENDER', os.getenv('MAIL_USERNAME', '<EMAIL>')),
    # Control automatic sample data creation. Set to True only in development/demo.
    'AUTO_CREATE_SAMPLE_DATA': os.getenv('AUTO_CREATE_SAMPLE_DATA', 'false').lower() == 'true'
})

# Initialize extensions
db = SQLAlchemy(app)

# Define models inline to avoid circular import issues
# This ensures all models are available throughout the application

class Contact(db.Model):
    __tablename__ = 'contacts'
    id = db.Column(db.Integer, primary_key=True)

    # Core Fields (Required) - as per documentation
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)

    # Optional Fields - as per documentation
    phone = db.Column(db.String(20))
    company = db.Column(db.String(200))
    job_title = db.Column(db.String(150))
    # website = db.Column(db.String(255))
    # notes = db.Column(db.Text)
    # lead_score = db.Column(db.Float, default=0.0)
    source = db.Column(db.String(100), default='manual_entry')
    status = db.Column(db.String(50), default='new')

    # Essential tracking fields for campaigns
    is_active = db.Column(db.Boolean, default=True)
    do_not_email = db.Column(db.Boolean, default=False)
    chatbot_session_id = db.Column(db.String(100))
    email_campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'))

    # Campaign tracking timestamps
    first_email_sent = db.Column(db.DateTime)
    email_opened = db.Column(db.DateTime)
    chatbot_link_clicked = db.Column(db.DateTime)
    chatbot_conversation_started = db.Column(db.DateTime)
    conversion_completed = db.Column(db.DateTime)
    current_sales_stage = db.Column(db.String(50))

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    email_campaign = db.relationship('EmailCampaign', backref='contacts')

    @property
    def full_name(self):
        first = self.first_name or ""
        last = self.last_name or ""
        return f"{first} {last}".strip()

    def add_stage_progression(self, stage: str):
        """Persist stage progression event for this contact."""
        if not stage:
            return
        progression = StageProgression(contact_id=self.id, stage_name=stage)
        db.session.add(progression)

class ContactGroup(db.Model):
    __tablename__ = 'contact_groups'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#007bff')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.String(100))

    @property
    def contact_count(self):
        """Get number of contacts in this group"""
        try:
            return ContactGroupMembership.query.filter_by(group_id=self.id).count()
        except:
            return 0

class ContactGroupMembership(db.Model):
    __tablename__ = 'contact_group_memberships'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('contact_groups.id'), nullable=False)
    added_at = db.Column(db.DateTime, default=datetime.utcnow)
    added_by = db.Column(db.String(100))

class CampaignGroup(db.Model):
    __tablename__ = 'campaign_groups'
    id = db.Column(db.Integer, primary_key=True)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('contact_groups.id'), nullable=False)
    added_at = db.Column(db.DateTime, default=datetime.utcnow)
    added_by = db.Column(db.String(100))

    # Relationships
    campaign = db.relationship('EmailCampaign', backref='campaign_groups')
    group = db.relationship('ContactGroup', backref='campaign_groups')

    # Unique constraint to prevent duplicate assignments
    __table_args__ = (db.UniqueConstraint('campaign_id', 'group_id', name='unique_campaign_group'),)

class EmailCampaign(db.Model):
    __tablename__ = 'email_campaigns'
    id = db.Column(db.Integer, primary_key=True)

    # Core Fields (Required)
    name = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    template_name = db.Column(db.String(100), nullable=False)

    # Campaign Content
    email_body_html = db.Column(db.Text, nullable=True)
    email_body_text = db.Column(db.Text, nullable=True)

    # Campaign Settings
    sender_name = db.Column(db.String(100), default="24Seven Assistants Sales Team")
    sender_email = db.Column(db.String(255), default="<EMAIL>")
    reply_to_email = db.Column(db.String(255), nullable=True)

    # Targeting
    target_audience = db.Column(db.String(255), nullable=True)
    contact_filters = db.Column(db.JSON, nullable=True)
    recipient_criteria = db.Column(db.Text, nullable=True)

    # Scheduling
    scheduled_at = db.Column(db.DateTime, nullable=True)
    scheduled_start_date = db.Column(db.DateTime, nullable=True)
    scheduled_start_time = db.Column(db.DateTime, nullable=True)
    started_at = db.Column(db.DateTime, nullable=True)
    completed_at = db.Column(db.DateTime, nullable=True)

    # Status and Statistics
    status = db.Column(db.String(50), default='draft', index=True)  # draft, scheduled, running, completed, paused, cancelled
    total_recipients = db.Column(db.Integer, default=0)
    emails_sent = db.Column(db.Integer, default=0)
    emails_delivered = db.Column(db.Integer, default=0)
    emails_opened = db.Column(db.Integer, default=0)
    emails_clicked = db.Column(db.Integer, default=0)
    emails_replied = db.Column(db.Integer, default=0)
    emails_bounced = db.Column(db.Integer, default=0)
    emails_unsubscribed = db.Column(db.Integer, default=0)
    emails_failed = db.Column(db.Integer, default=0)

    # Configuration
    daily_send_limit = db.Column(db.Integer, default=100)
    send_schedule = db.Column(db.String(50), default='immediate')  # immediate, scheduled, etc.
    send_delay_seconds = db.Column(db.Integer, default=2)
    max_emails_per_hour = db.Column(db.Integer, default=100)
    batch_size = db.Column(db.Integer, default=50)
    batch_delay_minutes = db.Column(db.Integer, default=5)
    retry_count = db.Column(db.Integer, default=0)
    max_retries = db.Column(db.Integer, default=3)
    retry_failed_only = db.Column(db.Boolean, default=True)

    # Batch Processing
    batch_status = db.Column(db.String(50), default='not_started')
    next_batch_date = db.Column(db.DateTime, nullable=True)
    total_batches_planned = db.Column(db.Integer, default=1)
    batches_completed = db.Column(db.Integer, default=0)

    # Daily Sending Tracking
    emails_sent_today = db.Column(db.Integer, default=0)
    last_send_date = db.Column(db.DateTime, nullable=True)

    # Additional Campaign Metrics
    chatbot_links_clicked = db.Column(db.Integer, default=0)
    chatbot_conversations_started = db.Column(db.Integer, default=0)
    conversions_achieved = db.Column(db.Integer, default=0)

    # Scheduling Options
    send_days_of_week = db.Column(db.String(20), default='1,2,3,4,5')  # Monday-Friday
    is_recurring = db.Column(db.Boolean, default=False)
    last_retry_at = db.Column(db.DateTime, nullable=True)

    # Tracking Fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.String(100), nullable=True)

    def can_send_today(self):
        """Check if campaign can send emails today based on daily limit"""
        from datetime import date
        today = date.today()

        # If last send date is not today, reset the counter
        if not self.last_send_date or self.last_send_date.date() != today:
            self.emails_sent_today = 0
            return True

        # Check if we've reached the daily limit
        return self.emails_sent_today < self.daily_send_limit

    def calculate_estimated_days(self, total_contacts):
        """Calculate estimated days to complete campaign based on daily limit"""
        if self.daily_send_limit <= 0:
            return 1

        import math
        return math.ceil(total_contacts / self.daily_send_limit)

    def get_remaining_sends_today(self):
        """Get remaining emails that can be sent today"""
        from datetime import date
        today = date.today()

        # If last send date is not today, reset the counter
        if not self.last_send_date or self.last_send_date.date() != today:
            self.emails_sent_today = 0
            return self.daily_send_limit

        # Return remaining sends for today
        return max(0, self.daily_send_limit - self.emails_sent_today)

    def increment_daily_send_count(self, count: int = 1):
        """Increment the daily sent counter and update last_send_date."""
        from datetime import date
        today = date.today()
        if not self.last_send_date or self.last_send_date.date() != today:
            # New day, reset counter first
            self.emails_sent_today = 0
        self.emails_sent_today += count
        self.last_send_date = datetime.utcnow()

class EmailLog(db.Model):
    __tablename__ = 'email_logs'
    id = db.Column(db.Integer, primary_key=True)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'))
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'))
    recipient_email = db.Column(db.String(255), nullable=False)
    recipient_name = db.Column(db.String(255))
    subject = db.Column(db.String(255), nullable=False)
    sent_at = db.Column(db.DateTime)
    opened_at = db.Column(db.DateTime)
    first_clicked_at = db.Column(db.DateTime)
    status = db.Column(db.String(50), default='pending')
    message_id = db.Column(db.String(255))
    open_count = db.Column(db.Integer, default=0)
    click_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Activity(db.Model):
    __tablename__ = 'activities'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'))
    session_id = db.Column(db.String(100))
    activity_type = db.Column(db.String(50), nullable=False)
    subject = db.Column(db.String(255))
    description = db.Column(db.Text)
    extra_data = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class StageProgression(db.Model):
    __tablename__ = 'stage_progressions'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    stage_name = db.Column(db.String(50), nullable=False)
    occurred_at = db.Column(db.DateTime, default=datetime.utcnow)

    contact = db.relationship('Contact', backref='stage_progressions')

    # -- Existing EmailFailure class follows --
class EmailFailure(db.Model):
    __tablename__ = 'email_failures'
    id = db.Column(db.Integer, primary_key=True)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'), nullable=False)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    recipient_email = db.Column(db.String(255), nullable=False)
    error_message = db.Column(db.Text)
    error_type = db.Column(db.String(50))
    retry_count = db.Column(db.Integer, default=0)
    resolved = db.Column(db.Boolean, default=False)
    resolved_at = db.Column(db.DateTime)
    last_retry_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    contact = db.relationship('Contact', backref='email_failures')
    campaign = db.relationship('EmailCampaign', backref='email_failures')


# Analytics Models
class SalesStage(db.Model):
    """Sales stage tracking model"""

    __tablename__ = 'sales_stages'

    id = db.Column(db.Integer, primary_key=True)

    # Stage Information
    name = db.Column(db.String(100), nullable=False, index=True)  # Opening, Trust, Discovery, Demonstration, Close
    description = db.Column(db.Text, nullable=True)
    order = db.Column(db.Integer, nullable=False, index=True)  # Order in the sales process

    # Stage Configuration
    is_active = db.Column(db.Boolean, default=True)
    probability_percent = db.Column(db.Float, default=0.0)  # Win probability at this stage
    expected_duration_days = db.Column(db.Integer, default=7)  # Expected time in this stage

    # Tracking
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<SalesStage {self.name} (Order: {self.order})>'


class Opportunity(db.Model):
    """Sales opportunity tracking model"""

    __tablename__ = 'opportunities'

    id = db.Column(db.Integer, primary_key=True)

    # Basic Information
    name = db.Column(db.String(255), nullable=False)  # Deal name
    description = db.Column(db.Text, nullable=True)

    # Relationships
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False, index=True)
    current_stage_id = db.Column(db.Integer, db.ForeignKey('sales_stages.id'), nullable=True, index=True)

    # Financial Information
    estimated_value = db.Column(db.Float, default=0.0)
    actual_value = db.Column(db.Float, nullable=True)
    currency = db.Column(db.String(3), default='USD')
    probability_percent = db.Column(db.Float, default=0.0)  # Win probability

    # Timeline
    expected_close_date = db.Column(db.DateTime, nullable=True, index=True)
    actual_close_date = db.Column(db.DateTime, nullable=True)

    # Status
    status = db.Column(db.String(50), default='open', index=True)  # open, won, lost, abandoned
    lost_reason = db.Column(db.String(255), nullable=True)

    # Tracking
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity_at = db.Column(db.DateTime, nullable=True, index=True)

    # Additional Information
    source = db.Column(db.String(100), nullable=True)  # Lead source
    notes = db.Column(db.Text, nullable=True)
    tags = db.Column(db.String(500), nullable=True)  # Comma-separated tags

    # AI/Bot Information
    bot_session_id = db.Column(db.String(100), nullable=True)  # Link to chatbot session
    ai_insights = db.Column(db.Text, nullable=True)  # AI-generated insights

    # Relationships
    contact = db.relationship("Contact", backref="opportunities")
    current_stage = db.relationship("SalesStage")

    def __repr__(self):
        return f'<Opportunity {self.name} - {self.status}>'


class SalesStageHistory(db.Model):
    """Track stage progression history for opportunities"""

    __tablename__ = 'sales_stage_history'

    id = db.Column(db.Integer, primary_key=True)

    # Relationships
    opportunity_id = db.Column(db.Integer, db.ForeignKey('opportunities.id'), nullable=False, index=True)
    stage_id = db.Column(db.Integer, db.ForeignKey('sales_stages.id'), nullable=False, index=True)

    # Stage Progression
    entered_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    exited_at = db.Column(db.DateTime, nullable=True)
    duration_hours = db.Column(db.Float, nullable=True)  # Time spent in this stage

    # Context
    notes = db.Column(db.Text, nullable=True)
    changed_by = db.Column(db.String(100), nullable=True)  # User or AI agent
    reason = db.Column(db.String(255), nullable=True)  # Reason for stage change

    # Relationships
    opportunity = db.relationship("Opportunity", backref="stage_history")
    stage = db.relationship("SalesStage")

    def __repr__(self):
        return f'<SalesStageHistory Opp:{self.opportunity_id} Stage:{self.stage_id}>'


class SalesAnalytics(db.Model):
    """Sales analytics aggregation model"""

    __tablename__ = 'sales_analytics'

    id = db.Column(db.Integer, primary_key=True)

    # Time Period
    date = db.Column(db.Date, nullable=False, index=True)
    period_type = db.Column(db.String(20), nullable=False, index=True)  # daily, weekly, monthly, quarterly, yearly

    # Sales Metrics
    total_opportunities = db.Column(db.Integer, default=0)
    new_opportunities = db.Column(db.Integer, default=0)
    won_opportunities = db.Column(db.Integer, default=0)
    lost_opportunities = db.Column(db.Integer, default=0)

    # Revenue Metrics
    total_revenue = db.Column(db.Float, default=0.0)
    new_revenue = db.Column(db.Float, default=0.0)
    pipeline_value = db.Column(db.Float, default=0.0)
    weighted_pipeline_value = db.Column(db.Float, default=0.0)

    # Conversion Metrics
    lead_to_opportunity_rate = db.Column(db.Float, default=0.0)
    opportunity_to_customer_rate = db.Column(db.Float, default=0.0)
    overall_conversion_rate = db.Column(db.Float, default=0.0)

    # Activity Metrics
    total_activities = db.Column(db.Integer, default=0)
    emails_sent = db.Column(db.Integer, default=0)
    calls_made = db.Column(db.Integer, default=0)
    meetings_held = db.Column(db.Integer, default=0)

    # Stage Metrics (JSON for flexibility)
    stage_metrics = db.Column(db.JSON, nullable=True)  # {stage_name: {count: X, value: Y}}

    # Lead Metrics
    total_leads = db.Column(db.Integer, default=0)
    new_leads = db.Column(db.Integer, default=0)
    qualified_leads = db.Column(db.Integer, default=0)

    # Performance Metrics
    average_deal_size = db.Column(db.Float, default=0.0)
    average_sales_cycle_days = db.Column(db.Float, default=0.0)
    average_time_per_stage = db.Column(db.JSON, nullable=True)  # {stage_name: avg_days}

    # Email Campaign Metrics
    emails_delivered = db.Column(db.Integer, default=0)
    emails_opened = db.Column(db.Integer, default=0)
    emails_clicked = db.Column(db.Integer, default=0)
    emails_replied = db.Column(db.Integer, default=0)
    email_open_rate = db.Column(db.Float, default=0.0)
    email_click_rate = db.Column(db.Float, default=0.0)
    email_reply_rate = db.Column(db.Float, default=0.0)

    # AI/Bot Metrics
    ai_interactions = db.Column(db.Integer, default=0)
    ai_generated_activities = db.Column(db.Integer, default=0)
    ai_success_rate = db.Column(db.Float, default=0.0)

    def __repr__(self):
        return f'<SalesAnalytics {self.date} - {self.period_type}>'

# Initialize Flask-Mail
mail = Mail(app)

# Global email system components (initialized later in init_database)
email_system = None


# Import related models first so their tables exist before defining
# ChatbotSession (which has foreign keys to them) to avoid SQLAlchemy
# dependency resolution errors.
# Enhanced ChatbotSession model with complete tracking capabilities.
class ChatbotSession(db.Model):
    __tablename__ = 'chatbot_sessions'

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), unique=True, nullable=False)

    # Relationships
    contact_id = db.Column(db.Integer, index=True)
    email_campaign_id = db.Column(db.Integer, index=True)

    # Timeline
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    ended_at = db.Column(db.DateTime)

    # Current status
    current_stage = db.Column(db.String(50), default='opening')
    current_task = db.Column(db.String(200))
    stage_progression = db.Column(db.Text)  # JSON string of stage progression

    # Message tracking
    total_messages = db.Column(db.Integer, default=0)
    user_messages = db.Column(db.Integer, default=0)
    bot_messages = db.Column(db.Integer, default=0)

    # Engagement and interaction tracking
    objections_handled = db.Column(db.Integer, default=0)
    engagement_level = db.Column(db.String(20), default='medium')  # low, medium, high
    interaction_quality_score = db.Column(db.Float, default=0.0)

    # Outcomes
    completed_successfully = db.Column(db.Boolean, default=False)
    conversion_achieved = db.Column(db.Boolean, default=False)
    final_stage_reached = db.Column(db.String(50))
    abandonment_reason = db.Column(db.String(200))

    # Stage timing fields for detailed analytics
    opening_started_at = db.Column(db.DateTime)
    opening_completed_at = db.Column(db.DateTime)
    trust_started_at = db.Column(db.DateTime)
    trust_completed_at = db.Column(db.DateTime)
    discovery_started_at = db.Column(db.DateTime)
    discovery_completed_at = db.Column(db.DateTime)
    demonstration_started_at = db.Column(db.DateTime)
    demonstration_completed_at = db.Column(db.DateTime)
    close_started_at = db.Column(db.DateTime)
    close_completed_at = db.Column(db.DateTime)

    def __repr__(self):
        return f"<ChatbotSession {self.session_id}>"

    def add_stage_timing(self, stage, timing_type):
        """Add stage timing (started or completed)"""
        try:
            field_name = f"{stage}_{timing_type}_at"
            if hasattr(self, field_name):
                setattr(self, field_name, datetime.utcnow())
                app.logger.info(f"Set {field_name} for session {self.session_id}")
            else:
                app.logger.warning(f"Field {field_name} not found on ChatbotSession model")
        except Exception as e:
            app.logger.error(f"Error setting stage timing: {e}")

    def calculate_stage_duration(self, stage):
        """Calculate duration for a specific stage in minutes"""
        try:
            started_field = f"{stage}_started_at"
            completed_field = f"{stage}_completed_at"

            started_at = getattr(self, started_field, None)
            completed_at = getattr(self, completed_field, None)

            if started_at and completed_at:
                duration = (completed_at - started_at).total_seconds() / 60
                return round(duration, 2)
            return None
        except Exception as e:
            app.logger.error(f"Error calculating stage duration: {e}")
            return None

    def get_stage_durations_dict(self):
        """Get all stage durations as a dictionary"""
        stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']
        durations = {}
        for stage in stages:
            duration = self.calculate_stage_duration(stage)
            if duration is not None:
                durations[stage] = duration
        return durations

    def update_engagement_level(self):
        """Update engagement level based on message count and interaction patterns"""
        try:
            if self.total_messages <= 3:
                self.engagement_level = 'low'
            elif self.total_messages <= 10:
                self.engagement_level = 'medium'
            else:
                self.engagement_level = 'high'

            # Calculate interaction quality score
            if self.total_messages > 0:
                user_ratio = self.user_messages / self.total_messages if self.total_messages > 0 else 0
                self.interaction_quality_score = min(user_ratio * 100, 100)

            app.logger.info(f"Updated engagement level to {self.engagement_level} for session {self.session_id}")
        except Exception as e:
            app.logger.error(f"Error updating engagement level: {e}")

    def add_stage_progression(self, stage):
        """Add stage to progression history"""
        try:
            import json
            if self.stage_progression:
                progression = json.loads(self.stage_progression)
            else:
                progression = []

            progression.append({
                'stage': stage,
                'timestamp': datetime.utcnow().isoformat()
            })

            self.stage_progression = json.dumps(progression)
            app.logger.info(f"Added stage {stage} to progression for session {self.session_id}")
        except Exception as e:
            app.logger.error(f"Error adding stage progression: {e}")

    def get_session_duration_minutes(self):
        """Get total session duration in minutes"""
        if self.ended_at and self.started_at:
            return (self.ended_at - self.started_at).total_seconds() / 60
        elif self.started_at:
            return (datetime.utcnow() - self.started_at).total_seconds() / 60
        return 0

# --- Database migrations ---
from flask_migrate import Migrate
migrate = Migrate(app, db)  # Enables `flask db` CLI commands

# Initialize debug system
init_debug_system(app)
register_debug_routes(app)

# ---------------------------------------------------------------------------
# Follow-up Tracking: EmailEvent & ChatEvent Models
# ---------------------------------------------------------------------------
from itsdangerous import URLSafeSerializer
try:
    from apscheduler.schedulers.background import BackgroundScheduler
except ImportError:
    BackgroundScheduler = None
    print("⚠️ APScheduler not installed – scheduler disabled for this run. Install with 'pip install apscheduler'.")

# Re-use secret key for signing tracking tokens
serializer = URLSafeSerializer(app.config['SECRET_KEY'])

# Define EmailEvent and ChatEvent inline to avoid circular imports
class EmailEvent(db.Model):
    __tablename__ = 'email_events'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'), nullable=True)
    event_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    event_metadata = db.Column(db.JSON)
    event_time = db.Column(db.DateTime, default=datetime.utcnow)

class ChatEvent(db.Model):
    __tablename__ = 'chat_events'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), nullable=False)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)
    stage_name = db.Column(db.String(50))
    event_time = db.Column(db.DateTime, default=datetime.utcnow)

class ChatMessage(db.Model):
    __tablename__ = 'chat_messages'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.String(100), nullable=False, index=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=True)
    message_type = db.Column(db.String(20), nullable=False)  # 'user' or 'bot'
    message_content = db.Column(db.Text, nullable=False)
    stage = db.Column(db.String(50))  # Current sales stage when message was sent
    task = db.Column(db.String(100))  # Current task when message was sent
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)

    # Additional metadata
    message_order = db.Column(db.Integer, default=0)  # Order within the session
    is_system_message = db.Column(db.Boolean, default=False)  # System/automated messages
    message_metadata = db.Column(db.JSON)  # Additional message metadata (renamed from 'metadata')

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'contact_id': self.contact_id,
            'message_type': self.message_type,
            'message_content': self.message_content,
            'stage': self.stage,
            'task': self.task,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'message_order': self.message_order,
            'is_system_message': self.is_system_message,
            'message_metadata': self.message_metadata
        }




@app.route('/track/open/<token>.png')
def track_email_open(token):
    """Tracking-pixel endpoint – records open and returns 1×1 transparent PNG."""
    try:
        data = serializer.loads(token)
        contact_id = data["contact_id"]
        campaign_id = data.get("campaign_id")

        # 1. Log analytics event
        evt = EmailEvent(contact_id=contact_id,
                         campaign_id=campaign_id,
                         event_type="opened",
                         event_metadata={"ua": request.headers.get("User-Agent")})
        db.session.add(evt)

        # 2. Update contact open timestamp / stage
        contact = Contact.query.get(contact_id)
        now = datetime.utcnow()
        if contact and not contact.email_opened:
            contact.email_opened = now
            contact.current_sales_stage = "email_opened"
            try:
                contact.add_stage_progression("email_opened")
            except Exception:
                pass  # method may be stubbed during tests

        # 3. Update EmailLog (by contact & campaign)
        email_log_query = EmailLog.query.filter_by(contact_id=contact_id)
        if campaign_id:
            email_log_query = email_log_query.filter_by(campaign_id=campaign_id)
        email_log = email_log_query.first()
        if email_log and not email_log.opened_at:
            email_log.opened_at = now
            email_log.status = "opened"
            email_log.open_count = (email_log.open_count or 0) + 1

        # 4. Increment campaign counters
        if campaign_id:
            campaign = EmailCampaign.query.get(campaign_id)
            if campaign:
                campaign.emails_opened = (campaign.emails_opened or 0) + 1

        db.session.commit()
    except Exception as exc:
        # If token is not a signed payload, treat it as raw message_id
        from itsdangerous import BadSignature
        if isinstance(exc, BadSignature):
            try:
                email_log = EmailLog.query.filter_by(message_id=token).first()
                contact = None
                campaign = None
                if email_log:
                    contact = email_log.contact
                    campaign = email_log.campaign
                else:
                    # Try session_id path (legacy)
                    contact = Contact.query.filter_by(chatbot_session_id=token).first()
                    if contact:
                        contact_id = contact.id
                        email_log = EmailLog.query.filter_by(contact_id=contact_id).order_by(EmailLog.sent_at.desc()).first()
                        campaign = email_log.campaign if email_log else None
                _record_email_open(contact, email_log, campaign)
                db.session.commit()
            except Exception as inner_exc:
                app.logger.warning(f"Track open fallback error: {inner_exc}")
        else:
            app.logger.warning(f"Track open error: {exc}")

    # 1×1 transparent PNG
    png_bytes = (b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f'  # noqa: E501
                 b'\x15\xc4\x89\x00\x00\x00\x0cIDAT\x08\xd7c```\x00\x00\x00\x04\x00\x01'  # noqa: E501
                 b'\x0d\x0a\x2d\xb4\x00\x00\x00\x00IEND\xaeB`\x82')
    return png_bytes, 200, {"Content-Type": "image/png"}

# ---------------------------------------------------------------------------
# Additional Tracking Routes
# ---------------------------------------------------------------------------

def _record_email_open(contact: Contact | None, email_log: EmailLog | None, campaign: EmailCampaign | None):
    """Shared logic used by fallback pixel paths to mark first open."""
    now = datetime.utcnow()
    if contact and not contact.email_opened:
        contact.email_opened = now
        contact.current_sales_stage = "email_opened"
        try:
            contact.add_stage_progression("email_opened")
        except Exception:
            pass
    if email_log and not email_log.opened_at:
        email_log.opened_at = now
        email_log.status = "opened"
        email_log.open_count = (email_log.open_count or 0) + 1
    if campaign:
        campaign.emails_opened = (campaign.emails_opened or 0) + 1

@app.route('/track/click/<message_id>')
def track_email_click(message_id):
    """Record click-through from email, update metrics, then redirect."""
    redirect_url = request.args.get('url') or '/'
    try:
        email_log = EmailLog.query.filter_by(message_id=message_id).first()
        now = datetime.utcnow()
        if email_log:
            email_log.clicked_at = now
            email_log.click_count = (email_log.click_count or 0) + 1
            contact = email_log.contact
            campaign = email_log.campaign
            if contact and not contact.chatbot_link_clicked:
                contact.chatbot_link_clicked = now
                contact.current_sales_stage = 'clicked'
                try:
                    contact.add_stage_progression('clicked')
                except Exception:
                    pass
            if campaign:
                campaign.emails_clicked = (campaign.emails_clicked or 0) + 1
        # Log regardless
        evt = EmailEvent(contact_id=email_log.contact_id if email_log else None,
                         campaign_id=email_log.campaign_id if email_log else None,
                         event_type='clicked',
                         event_metadata={'ua': request.headers.get('User-Agent')})
        db.session.add(evt)
        db.session.commit()
    except Exception as exc:
        app.logger.warning(f"Track click error: {exc}")
    return redirect(redirect_url)

# Legacy endpoint removed - using enhanced track_session endpoint instead

@app.route('/track/chat_event', methods=['POST'])
def track_chat_event():
    """Receive chat_open or stage_progress events from chatbot JS."""
    data = request.get_json() or {}
    session_id = data.get('session_id')
    contact_id = data.get('contact_id')
    event_type = data.get('event_type')
    stage_name = data.get('stage_name')
    if not all([session_id, contact_id, event_type]):
        return jsonify({"success": False, "message": "Missing fields"}), 400

    evt = ChatEvent(session_id=session_id,
                    contact_id=contact_id,
                    event_type=event_type,
                    stage_name=stage_name)
    db.session.add(evt)

    contact = Contact.query.get(contact_id)
    now = datetime.utcnow()
    if contact:
        # Conversation started marker
        if event_type in {"conversation_started", "chat_open"} and not contact.chatbot_conversation_started:
            contact.chatbot_conversation_started = now
            contact.current_sales_stage = "opening"
            try:
                contact.add_stage_progression("opening")
            except Exception:
                pass

            # Increment campaign counter
            if contact.email_campaign_id:
                campaign = EmailCampaign.query.get(contact.email_campaign_id)
                if campaign:
                    campaign.chatbot_conversations_started = (campaign.chatbot_conversations_started or 0) + 1

        # Stage progression events carry stage_name
        if event_type == "stage_progress" and stage_name:
            contact.current_sales_stage = stage_name
            try:
                contact.add_stage_progression(stage_name)
            except Exception:
                pass

    db.session.commit()
    return jsonify({"success": True})

# ---------------------------------------------------------------------------
# Scheduler – follow-up automation
# ---------------------------------------------------------------------------

def _send_follow_up(contact, followup_number):
    """Send follow-up email via EnhancedSMTPService. For now, just log."""
    """Compose and send the follow-up email with tracking pixel & chat link."""
    from email_system.enhanced_smtp_service import EnhancedSMTPService  # local import to avoid circular

    # Build tracking pixel URL
    token = serializer.dumps({'contact_id': contact.id})
    server_host = request.host_url.rstrip('/')  # e.g., http://localhost:5000
    pixel_url = f"{server_host}/track/open/{token}.png"

    # Ensure session ID
    session_id = contact.chatbot_session_id or str(uuid.uuid4())
    if not contact.chatbot_session_id:
        contact.chatbot_session_id = session_id
        db.session.commit()

    chat_url = f"{server_host}/chat?session_id={session_id}"

    subject = "Just checking in – let’s continue our conversation"
    html_body = f"""
        <p>Hi {contact.first_name},</p>
        <p>I noticed we haven’t progressed to the next step yet. If you have a moment, please jump back into our chat so we can move forward.</p>
        <p><a href=\"{chat_url}\" style=\"background:#667eea;color:#fff;padding:10px 18px;text-decoration:none;border-radius:4px\">Resume Chat</a></p>
        <p>Looking forward to helping you.</p>
        <p>Best,<br/>24Seven Assistants Sales Team</p>
        <img src=\"{pixel_url}\" width=\"1\" height=\"1\" style=\"display:none;\" />
    """
    text_body = (
        f"Hi {contact.first_name},\n\n"
        "I noticed we haven’t progressed to the next step yet. "
        f"Please visit {chat_url} to resume our chat.\n\n"
        "Best,\n24Seven Assistants Sales Team"
    )

    # Use the global email system if available, otherwise create a new instance
    if email_system and 'smtp_service' in email_system:
        smtp_service = email_system['smtp_service']
    else:
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        from email_system.config import get_email_config
        smtp_service = EnhancedSMTPService(get_email_config())
    success, message_id, error_msg = smtp_service.send_email(
        to_email=contact.email,
        subject=subject,
        html_body=html_body,
        text_body=text_body,
    )

    evt_type = f'follow_up_{followup_number}'
    db.session.add(EmailEvent(contact_id=contact.id, event_type=evt_type, event_metadata={'message_id': message_id or '', 'error': error_msg if not success else ''}))
    db.session.commit()

    status = "✅" if success else "❌"
    app.logger.info(f"{status} Follow-up {followup_number} sent to {contact.email}")


def follow_up_job():
    """Scan contacts & chat events and enqueue follow-ups."""
    with app.app_context():
        one_week = datetime.utcnow() - timedelta(days=7)
        # Latest stage progress per session
        subquery = db.session.query(
            ChatEvent.session_id,
            db.func.max(ChatEvent.event_time).label('last_evt')
        ).filter(ChatEvent.event_type == 'stage_progress').group_by(ChatEvent.session_id).subquery()

        rows = db.session.query(ChatEvent.session_id, ChatEvent.contact_id, subquery.c.last_evt).join(
            subquery, ChatEvent.session_id == subquery.c.session_id
        ).distinct()

        for session_id, contact_id, last_evt in rows:
            if last_evt is None or last_evt < one_week:
                # Count existing follow-ups
                fu_count = EmailEvent.query.filter(EmailEvent.contact_id == contact_id,
                                                   EmailEvent.event_type.like('follow_up_%')).count()
                if fu_count < 2:
                    contact = Contact.query.get(contact_id)
                    if contact and contact.is_active and not contact.do_not_email:
                        _send_follow_up(contact, fu_count + 1)

# Start scheduler if APScheduler is available
if BackgroundScheduler:
    scheduler = BackgroundScheduler()
    scheduler.add_job(follow_up_job, 'interval', hours=24, id='follow_up_job')
    scheduler.start()
else:
    scheduler = None


# Centralized Analytics Service
@performance_monitor.monitor_function("get_unified_analytics")
def get_unified_analytics():
    """Get synchronized analytics data for all pages"""
    import sys
    print("🔍 DEBUG: FUNCTION CALLED - get_unified_analytics() started", file=sys.stderr)
    sys.stderr.flush()
    print("🔍 DEBUG: FUNCTION CALLED - get_unified_analytics() started")

    # Ensure we're in Flask app context
    if not app.app_context:
        with app.app_context():
            return get_unified_analytics()

    # Use the models that were imported/defined earlier
    # These are available in the global scope after the import section
    sys.stdout.flush()
    try:
        print("🔍 DEBUG: INSIDE TRY BLOCK", file=sys.stderr)
        sys.stderr.flush()
        print("🔍 DEBUG: INSIDE TRY BLOCK")
        sys.stdout.flush()
        # Basic counts
        total_contacts = Contact.query.count()
        total_campaigns = EmailCampaign.query.count()
        total_sessions = ChatbotSession.query.count()
        active_sessions = ChatbotSession.query.filter_by(completed_successfully=False).count()

        # Email metrics from EmailLog table (most accurate)
        emails_sent_logs = EmailLog.query.filter(EmailLog.sent_at.isnot(None)).count()
        emails_opened_logs = EmailLog.query.filter(EmailLog.opened_at.isnot(None)).count()

        # Email metrics from campaigns (aggregated)
        emails_sent_campaigns = db.session.query(db.func.sum(EmailCampaign.emails_sent)).scalar() or 0
        emails_opened_campaigns = db.session.query(db.func.sum(EmailCampaign.emails_opened)).scalar() or 0
        links_clicked_campaigns = db.session.query(db.func.sum(EmailCampaign.chatbot_links_clicked)).scalar() or 0
        conversations_started_campaigns = db.session.query(db.func.sum(EmailCampaign.chatbot_conversations_started)).scalar() or 0
        conversions_campaigns = db.session.query(db.func.sum(EmailCampaign.conversions_achieved)).scalar() or 0

        # Alternative calculation from contacts (for verification)
        contacts_with_emails_sent = Contact.query.filter(Contact.first_email_sent.isnot(None)).count()
        contacts_with_emails_opened = Contact.query.filter(Contact.email_opened.isnot(None)).count()
        contacts_with_links_clicked = Contact.query.filter(Contact.chatbot_link_clicked.isnot(None)).count()
        contacts_with_conversations = Contact.query.filter(Contact.chatbot_conversation_started.isnot(None)).count()
        contacts_converted = Contact.query.filter(Contact.conversion_completed.isnot(None)).count()

        # Email metrics from activities (for tracking without campaigns)
        email_opened_activities = Activity.query.filter_by(activity_type='email_opened').count()
        link_clicked_activities = Activity.query.filter_by(activity_type='link_clicked').count()

        # Use ChatbotSession data as primary source for conversations and conversions (same as pipeline)
        chatbot_conversations = ChatbotSession.query.count()
        chatbot_conversions = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

        # Use the highest of the calculations for accuracy, but prioritize ChatbotSession data for conversations
        emails_sent = max(emails_sent_logs, emails_sent_campaigns, contacts_with_emails_sent)
        emails_opened = max(emails_opened_logs, emails_opened_campaigns, contacts_with_emails_opened, email_opened_activities)
        links_clicked = max(links_clicked_campaigns, contacts_with_links_clicked, link_clicked_activities)
        conversations_started = max(chatbot_conversations, conversations_started_campaigns, contacts_with_conversations)
        conversions = max(chatbot_conversions, conversions_campaigns, contacts_converted)

        # Debug logging to see what we're getting from each source
        app.logger.info(f"Analytics Debug - Emails sent: logs={emails_sent_logs}, campaigns={emails_sent_campaigns}, contacts={contacts_with_emails_sent}, final={emails_sent}")
        app.logger.info(f"Analytics Debug - Emails opened: logs={emails_opened_logs}, campaigns={emails_opened_campaigns}, contacts={contacts_with_emails_opened}, activities={email_opened_activities}, final={emails_opened}")
        app.logger.info(f"Analytics Debug - Links clicked: campaigns={links_clicked_campaigns}, contacts={contacts_with_links_clicked}, activities={link_clicked_activities}, final={links_clicked}")
        app.logger.info(f"Analytics Debug - Conversations: chatbot={chatbot_conversations}, campaigns={conversations_started_campaigns}, contacts={contacts_with_conversations}, final={conversations_started}")
        app.logger.info(f"Analytics Debug - Conversions: chatbot={chatbot_conversions}, campaigns={conversions_campaigns}, contacts={contacts_converted}, final={conversions}")

        # Skip sample data creation if we're in a test environment or if data already exists
        # This prevents circular dependency during testing
        # Skip sample data creation if we're in test mode OR the feature is disabled via config
        skip_sample_data = (hasattr(app, 'testing') and app.testing) or (not app.config.get('AUTO_CREATE_SAMPLE_DATA', False))

        # If all data is zero and we're not in test mode, create some sample data for demonstration
        if emails_sent == 0 and total_contacts == 0 and not skip_sample_data:
            app.logger.info("No data found - creating sample data for demonstration")
            create_sample_data()
            # Recalculate after creating sample data
            total_contacts = Contact.query.count()
            total_campaigns = EmailCampaign.query.count()

            # Recalculate using same logic as above
            emails_sent_logs_new = EmailLog.query.filter(EmailLog.sent_at.isnot(None)).count()
            emails_sent_campaigns_new = db.session.query(db.func.sum(EmailCampaign.emails_sent)).scalar() or 0
            contacts_with_emails_sent_new = Contact.query.filter(Contact.first_email_sent.isnot(None)).count()

            emails_opened_logs_new = EmailLog.query.filter(EmailLog.opened_at.isnot(None)).count()
            emails_opened_campaigns_new = db.session.query(db.func.sum(EmailCampaign.emails_opened)).scalar() or 0
            contacts_with_emails_opened_new = Contact.query.filter(Contact.email_opened.isnot(None)).count()
            email_opened_activities_new = Activity.query.filter_by(activity_type='email_opened').count()

            links_clicked_campaigns_new = db.session.query(db.func.sum(EmailCampaign.chatbot_links_clicked)).scalar() or 0
            contacts_with_links_clicked_new = Contact.query.filter(Contact.chatbot_link_clicked.isnot(None)).count()
            link_clicked_activities_new = Activity.query.filter_by(activity_type='link_clicked').count()

            conversations_started_campaigns_new = db.session.query(db.func.sum(EmailCampaign.chatbot_conversations_started)).scalar() or 0
            contacts_with_conversations_new = Contact.query.filter(Contact.chatbot_conversation_started.isnot(None)).count()
            chatbot_conversations_new = ChatbotSession.query.count()

            conversions_campaigns_new = db.session.query(db.func.sum(EmailCampaign.conversions_achieved)).scalar() or 0
            contacts_converted_new = Contact.query.filter(Contact.conversion_completed.isnot(None)).count()
            chatbot_conversions_new = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

            emails_sent = max(emails_sent_logs_new, emails_sent_campaigns_new, contacts_with_emails_sent_new)
            emails_opened = max(emails_opened_logs_new, emails_opened_campaigns_new, contacts_with_emails_opened_new, email_opened_activities_new)
            links_clicked = max(links_clicked_campaigns_new, contacts_with_links_clicked_new, link_clicked_activities_new)
            conversations_started = max(chatbot_conversations_new, conversations_started_campaigns_new, contacts_with_conversations_new)
            conversions = max(chatbot_conversions_new, conversions_campaigns_new, contacts_converted_new)

        # Stage distribution – use StageProgression for historical accuracy
        stage_counts = {}
        stage_names = ['opening', 'trust', 'discovery', 'demonstration', 'close', 'converted']

        for stage in stage_names:
            count = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                               .filter(StageProgression.stage_name == stage)
                               .scalar() or 0)
            stage_counts[stage] = count

        # Email-related pseudo-stages are derived from funnel metrics directly
        stage_counts['email_sent'] = emails_sent
        stage_counts['email_opened'] = emails_opened
        stage_counts['link_clicked'] = links_clicked

        # Calculate conversion rates
        email_open_rate = (emails_opened / emails_sent * 100) if emails_sent > 0 else 0
        click_rate = (links_clicked / emails_opened * 100) if emails_opened > 0 else 0
        conversation_rate = (conversations_started / links_clicked * 100) if links_clicked > 0 else 0
        conversion_rate = (conversions / conversations_started * 100) if conversations_started > 0 else 0
        overall_rate = (conversions / emails_sent * 100) if emails_sent > 0 else 0

        # Recent activities
        recent_activities = Activity.query.order_by(Activity.created_at.desc()).limit(10).all()

        return {
            'basic_metrics': {
                'total_contacts': total_contacts,
                'total_campaigns': total_campaigns,
                'total_sessions': total_sessions,
                'active_sessions': active_sessions
            },
            'funnel_metrics': {
                'emails_sent': emails_sent,
                'emails_opened': emails_opened,
                'links_clicked': links_clicked,
                'conversations_started': conversations_started,
                'conversions': conversions
            },
            'conversion_rates': {
                'email_open_rate': round(email_open_rate, 2),
                'click_rate': round(click_rate, 2),
                'conversation_rate': round(conversation_rate, 2),
                'conversion_rate': round(conversion_rate, 2),
                'overall_rate': round(overall_rate, 2)
            },
            'stage_counts': stage_counts,
            'recent_activities': recent_activities
        }

    except Exception as e:
        print(f"🔍 DEBUG: Exception in get_unified_analytics: {str(e)}")
        print(f"🔍 DEBUG: Exception type: {type(e)}")
        import traceback
        print("🔍 DEBUG: Full traceback:")
        traceback.print_exc()
        app.logger.error(f"Analytics calculation error: {str(e)}")
        return {
            'basic_metrics': {
                'total_contacts': 0,
                'total_campaigns': 0,
                'total_sessions': 0,
                'active_sessions': 0
            },
            'funnel_metrics': {
                'emails_sent': 0,
                'emails_opened': 0,
                'links_clicked': 0,
                'conversations_started': 0,
                'conversions': 0
            },
            'conversion_rates': {
                'email_open_rate': 0,
                'click_rate': 0,
                'conversation_rate': 0,
                'conversion_rate': 0,
                'overall_rate': 0
            },
            'stage_counts': {},
            'recent_activities': []
        }

# Email Service Functions
def send_campaign_email(contact, campaign, session_id):
    """Send actual email with chatbot link using centralized template manager"""
    try:
        # Create chatbot link with tracking
        chatbot_link = f"http://localhost:5000/chat/{session_id}"

        # Use centralized template manager instead of hardcoded templates
        if email_system and 'template_manager' in email_system:
            template_manager = email_system['template_manager']
        else:
            # Fallback: create template manager if not available
            from email_system.email_templates import EmailTemplateManager
            template_manager = EmailTemplateManager()

        # Prepare template context
        template_context = {
            'contact_name': contact.first_name or 'there',
            'company_name': contact.company or 'your company',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': chatbot_link,
            'session_id': session_id
        }

        # Determine template name from campaign or use default
        template_name = getattr(campaign, 'template_name', 'customer_support')
        if template_name not in ['customer_support', 'introduction', 'followup']:
            template_name = 'customer_support'  # Default fallback

        # Render email using centralized template
        try:
            rendered_email = template_manager.render_template(template_name, template_context)
            subject = rendered_email['subject']
            html_body = rendered_email['html_body']
            text_body = rendered_email['text_body']

            app.logger.info(f"✅ Using centralized template '{template_name}' for {contact.email}")
        except Exception as template_error:
            app.logger.error(f"❌ Template rendering failed: {template_error}")
            # Fallback to simple email if template fails
            subject = f"Customer Support Solutions for {contact.first_name}"
            html_body = f"<p>Hi {contact.first_name},</p><p>Please visit our chat: <a href='{chatbot_link}'>Start Chat</a></p>"
            text_body = f"Hi {contact.first_name},\n\nPlease visit our chat: {chatbot_link}"



        # Simulate email sending (mail is disabled for testing)
        # In production, uncomment the mail imports and this code:
        # msg = Message(
        #     subject=subject,
        #     sender=app.config['MAIL_DEFAULT_SENDER'],
        #     recipients=[contact.email],
        #     body=text_body,
        #     html=html_body
        # )
        # mail.send(msg)

        # Send actual email using simple enhanced SMTP (no model dependencies)
        try:
            import smtplib
            import imaplib
            import ssl
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart

            # Create email message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"24Seven Assistants Sales Team <{app.config['MAIL_USERNAME']}>"
            msg['To'] = contact.email
            msg['Date'] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S +0000')

            # Add text version
            text_part = MIMEText(text_body, 'plain', 'utf-8')
            msg.attach(text_part)

            # Add HTML version
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)

            # Send email via SMTP
            context = ssl.create_default_context()
            smtp_server = smtplib.SMTP_SSL(app.config['MAIL_SERVER'], app.config['MAIL_PORT'], context=context)
            smtp_server.login(app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])
            smtp_server.send_message(msg)
            message_id = msg['Message-ID']
            smtp_server.quit()

            print(f"📧 Real email sent to {contact.email}")
            print(f"   Subject: {subject}")
            print(f"   Message ID: {message_id}")
            print(f"   Chatbot Link: {chatbot_link}")

            # Save to IMAP sent folder
            try:
                context = ssl.create_default_context()
                imap_server = imaplib.IMAP4_SSL(app.config['MAIL_SERVER'], 993, ssl_context=context)
                imap_server.login(app.config['MAIL_USERNAME'], app.config['MAIL_PASSWORD'])

                # Select sent folder
                status, response = imap_server.select('INBOX.Sent')
                if status == 'OK':
                    # Save message to sent folder
                    message_bytes = msg.as_bytes()
                    imap_server.append('INBOX.Sent', '\\Seen', None, message_bytes)
                    print(f"   ✅ Email saved to IMAP sent folder")
                else:
                    print(f"   ⚠️ Could not access IMAP sent folder")

                imap_server.close()
                imap_server.logout()

            except Exception as e:
                print(f"   ⚠️ IMAP save failed: {str(e)}")
                # Don't fail the whole operation if IMAP save fails

            return True, f"Email sent successfully (Message ID: {message_id})"

        except Exception as e:
            error_msg = f"Failed to send email to {contact.email}: {str(e)}"
            print(f"❌ SMTP error: {error_msg}")
            return False, error_msg

    except Exception as e:
        return False, f"Email sending failed: {str(e)}"

def classify_email_error(error_message):
    """Classify email error for retry strategy"""
    error_lower = error_message.lower()

    if any(keyword in error_lower for keyword in ['invalid', 'not found', 'does not exist', 'unknown user']):
        return 'invalid_email'
    elif any(keyword in error_lower for keyword in ['connection', 'timeout', 'network']):
        return 'connection_error'
    elif any(keyword in error_lower for keyword in ['authentication', 'auth', 'login']):
        return 'auth_error'
    elif any(keyword in error_lower for keyword in ['quota', 'limit', 'rate']):
        return 'rate_limit'
    elif any(keyword in error_lower for keyword in ['blocked', 'spam', 'blacklist']):
        return 'blocked'
    else:
        return 'smtp_error'

def test_smtp_connection():
    """Test SMTP connection"""
    try:
        # with mail.connect() as conn:
        return True, "SMTP connection successful (mail disabled)"
    except Exception as e:
        return False, f"SMTP connection failed: {str(e)}"

def create_sample_data():
    """Create sample data for demonstration purposes"""
    try:
        from datetime import datetime, timedelta
        import uuid

        app.logger.info("Creating sample data for demonstration...")

        # Create sample contact groups
        sample_groups = [
            ContactGroup(name="Demo Prospects", description="Sample prospects for demonstration"),
            ContactGroup(name="Trial Users", description="Users trying our service"),
            ContactGroup(name="Enterprise Leads", description="Enterprise potential customers")
        ]

        for group in sample_groups:
            db.session.add(group)
        db.session.commit()

        # Create sample contacts
        sample_contacts = [
            Contact(
                first_name="John", last_name="Smith", email="<EMAIL>",
                company="TechCorp Inc", phone="******-0101",
                current_sales_stage="email_sent",
                first_email_sent=datetime.utcnow() - timedelta(days=5),
                email_opened=datetime.utcnow() - timedelta(days=4),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=4),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=4)
            ),
            Contact(
                first_name="Sarah", last_name="Johnson", email="<EMAIL>",
                company="Business Corp", phone="******-0102",
                current_sales_stage="trust",
                first_email_sent=datetime.utcnow() - timedelta(days=3),
                email_opened=datetime.utcnow() - timedelta(days=2),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=2),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=2)
            ),
            Contact(
                first_name="Mike", last_name="Davis", email="<EMAIL>",
                company="Startup Solutions", phone="******-0103",
                current_sales_stage="discovery",
                first_email_sent=datetime.utcnow() - timedelta(days=7),
                email_opened=datetime.utcnow() - timedelta(days=6),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=6),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=6)
            ),
            Contact(
                first_name="Lisa", last_name="Wilson", email="<EMAIL>",
                company="Enterprise Solutions", phone="******-0104",
                current_sales_stage="demonstration",
                first_email_sent=datetime.utcnow() - timedelta(days=10),
                email_opened=datetime.utcnow() - timedelta(days=9),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=9),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=9)
            ),
            Contact(
                first_name="David", last_name="Brown", email="<EMAIL>",
                company="Company Networks", phone="******-0105",
                current_sales_stage="converted",
                first_email_sent=datetime.utcnow() - timedelta(days=15),
                email_opened=datetime.utcnow() - timedelta(days=14),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=14),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=14),
                conversion_completed=datetime.utcnow() - timedelta(days=12)
            ),
            # Additional contacts for better funnel visualization
            Contact(
                first_name="Emma", last_name="Taylor", email="<EMAIL>",
                company="Tech Firm", phone="******-0106",
                current_sales_stage="email_opened",
                first_email_sent=datetime.utcnow() - timedelta(days=2),
                email_opened=datetime.utcnow() - timedelta(days=1)
            ),
            Contact(
                first_name="James", last_name="Anderson", email="<EMAIL>",
                company="Solutions Org", phone="******-0107",
                current_sales_stage="link_clicked",
                first_email_sent=datetime.utcnow() - timedelta(days=4),
                email_opened=datetime.utcnow() - timedelta(days=3),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=3)
            ),
            Contact(
                first_name="Anna", last_name="Martinez", email="<EMAIL>",
                company="Consulting Business", phone="******-0108",
                current_sales_stage="opening",
                first_email_sent=datetime.utcnow() - timedelta(days=6),
                email_opened=datetime.utcnow() - timedelta(days=5),
                chatbot_link_clicked=datetime.utcnow() - timedelta(days=5),
                chatbot_conversation_started=datetime.utcnow() - timedelta(days=5)
            )
        ]

        for contact in sample_contacts:
            db.session.add(contact)
        db.session.commit()

        # Create sample email campaign with synchronized data
        sample_campaign = EmailCampaign(
            name="Demo Campaign - AI Sales Assistant Introduction",
            subject="Meet Sarah - Your AI Sales Assistant",
            template_name="ai_assistant_intro",
            status="completed",
            emails_sent=8,  # Match number of contacts with emails sent
            emails_opened=7,  # Match number of contacts with emails opened
            chatbot_links_clicked=7,  # Match number of contacts with links clicked
            chatbot_conversations_started=6,  # Match number of chatbot sessions
            conversions_achieved=1,  # Match number of conversions
            created_at=datetime.utcnow() - timedelta(days=10),
            started_at=datetime.utcnow() - timedelta(days=8)
        )
        db.session.add(sample_campaign)
        db.session.commit()

        # Create sample email logs
        sample_logs = []
        for i, contact in enumerate(sample_contacts):
            if contact.first_email_sent:
                log = EmailLog(
                    contact_id=contact.id,
                    campaign_id=sample_campaign.id,
                    recipient_email=contact.email,
                    recipient_name=f"{contact.first_name} {contact.last_name}",
                    subject=sample_campaign.subject,
                    status='sent',
                    sent_at=contact.first_email_sent,
                    opened_at=contact.email_opened,
                    first_clicked_at=contact.chatbot_link_clicked,
                    message_id=str(uuid.uuid4())
                )
                sample_logs.append(log)

        for log in sample_logs:
            db.session.add(log)
        db.session.commit()

        # Create sample chatbot sessions
        sample_sessions = []
        for contact in sample_contacts:
            if contact.chatbot_conversation_started:
                session = ChatbotSession(
                    contact_id=contact.id,
                    session_id=str(uuid.uuid4()),
                    started_at=contact.chatbot_conversation_started,
                    current_stage='opening' if contact.current_sales_stage in ['opening', 'trust', 'discovery', 'demonstration', 'close', 'converted'] else 'initial',
                    opening_started_at=contact.chatbot_conversation_started if contact.current_sales_stage in ['opening', 'trust', 'discovery', 'demonstration', 'close', 'converted'] else None,
                    trust_started_at=contact.chatbot_conversation_started + timedelta(minutes=5) if contact.current_sales_stage in ['trust', 'discovery', 'demonstration', 'close', 'converted'] else None,
                    discovery_started_at=contact.chatbot_conversation_started + timedelta(minutes=15) if contact.current_sales_stage in ['discovery', 'demonstration', 'close', 'converted'] else None,
                    demonstration_started_at=contact.chatbot_conversation_started + timedelta(minutes=25) if contact.current_sales_stage in ['demonstration', 'close', 'converted'] else None,
                    close_started_at=contact.chatbot_conversation_started + timedelta(minutes=35) if contact.current_sales_stage in ['close', 'converted'] else None,
                    conversion_achieved=True if contact.current_sales_stage == 'converted' else False,
                    ended_at=contact.conversion_completed if contact.conversion_completed else None,
                    completed_successfully=True if contact.current_sales_stage == 'converted' else False,
                    total_messages=25 if contact.current_sales_stage == 'converted' else 15,
                    engagement_level='high' if contact.current_sales_stage in ['demonstration', 'close', 'converted'] else 'medium'
                )
                sample_sessions.append(session)

        for session in sample_sessions:
            db.session.add(session)
        db.session.commit()

        # Create sample activities
        sample_activities = [
            Activity(
                activity_type="email_sent",
                subject="Email Campaign Sent",
                description=f"Email sent to {sample_contacts[0].first_name} {sample_contacts[0].last_name}",
                contact_id=sample_contacts[0].id,
                created_at=datetime.utcnow() - timedelta(hours=2)
            ),
            Activity(
                activity_type="email_opened",
                subject="Email Opened",
                description=f"Email opened by {sample_contacts[1].first_name} {sample_contacts[1].last_name}",
                contact_id=sample_contacts[1].id,
                created_at=datetime.utcnow() - timedelta(hours=1)
            ),
            Activity(
                activity_type="chat_started",
                subject="Chatbot Conversation Started",
                description=f"Chatbot conversation started with {sample_contacts[2].first_name} {sample_contacts[2].last_name}",
                contact_id=sample_contacts[2].id,
                created_at=datetime.utcnow() - timedelta(minutes=30)
            ),
            Activity(
                activity_type="conversion",
                subject="Conversion Completed",
                description=f"Conversion achieved with {sample_contacts[4].first_name} {sample_contacts[4].last_name}",
                contact_id=sample_contacts[4].id,
                created_at=datetime.utcnow() - timedelta(minutes=15)
            )
        ]

        for activity in sample_activities:
            db.session.add(activity)
        db.session.commit()

        app.logger.info(f"Sample data created successfully:")
        app.logger.info(f"- {len(sample_contacts)} contacts")
        app.logger.info(f"- 1 email campaign")
        app.logger.info(f"- {len(sample_logs)} email logs")
        app.logger.info(f"- {len(sample_sessions)} chatbot sessions")
        app.logger.info(f"- {len(sample_activities)} activities")

        return True

    except Exception as e:
        app.logger.error(f"Error creating sample data: {str(e)}")
        db.session.rollback()
        return False

# Flask Routes
@app.route('/')
def index():
    """Unified dashboard showing complete sales funnel - SYNCHRONIZED with all analytics"""
    try:
        # Get synchronized analytics data
        analytics = get_unified_analytics()

        # Debug logging to see what data we're getting
        app.logger.info(f"Dashboard analytics: {analytics}")

        # Ensure we have valid data with safe defaults
        funnel_metrics = analytics.get('funnel_metrics', {})
        basic_metrics = analytics.get('basic_metrics', {})
        conversion_rates = analytics.get('conversion_rates', {})
        stage_counts = analytics.get('stage_counts', {})
        recent_activities = analytics.get('recent_activities', [])

        # Log the specific metrics we're passing
        app.logger.info(f"Emails sent: {funnel_metrics.get('emails_sent', 0)}")
        app.logger.info(f"Emails opened: {funnel_metrics.get('emails_opened', 0)}")
        app.logger.info(f"Links clicked: {funnel_metrics.get('links_clicked', 0)}")
        app.logger.info(f"Conversations: {funnel_metrics.get('conversations_started', 0)}")
        app.logger.info(f"Conversions: {funnel_metrics.get('conversions', 0)}")

        return render_template('unified_dashboard.html',
                             total_contacts=basic_metrics.get('total_contacts', 0),
                             total_campaigns=basic_metrics.get('total_campaigns', 0),
                             total_sessions=basic_metrics.get('total_sessions', 0),
                             active_sessions=basic_metrics.get('active_sessions', 0),
                             emails_sent=funnel_metrics.get('emails_sent', 0),
                             emails_opened=funnel_metrics.get('emails_opened', 0),
                             links_clicked=funnel_metrics.get('links_clicked', 0),
                             conversations_started=funnel_metrics.get('conversations_started', 0),
                             conversions=funnel_metrics.get('conversions', 0),
                             conversion_rates=conversion_rates,
                             stage_counts=stage_counts,
                             recent_activities=recent_activities,
                             analytics=analytics)  # Pass full analytics for debugging

    except Exception as e:
        app.logger.error(f"Dashboard error: {str(e)}")
        import traceback
        app.logger.error(f"Dashboard traceback: {traceback.format_exc()}")

        # Return safe defaults
        return render_template('unified_dashboard.html',
                             total_contacts=0,
                             total_campaigns=0,
                             total_sessions=0,
                             active_sessions=0,
                             emails_sent=0,
                             emails_opened=0,
                             links_clicked=0,
                             conversations_started=0,
                             conversions=0,
                             conversion_rates={},
                             stage_counts={},
                             recent_activities=[],
                             analytics={})

@app.route('/analytics')
def analytics_dashboard():
    """Comprehensive analytics showing email-to-conversion funnel"""
    try:
        # Get synchronized analytics
        analytics = get_unified_analytics()

        # Use SAME data source for both charts - funnel_metrics for consistency
        funnel_metrics = analytics['funnel_metrics']
        stage_counts = analytics['stage_counts']

        # Create conversion funnel chart using funnel_metrics
        funnel_fig = go.Figure(go.Funnel(
            y=['Emails Sent', 'Emails Opened', 'Links Clicked', 'Conversations Started', 'Conversions'],
            x=[funnel_metrics['emails_sent'], funnel_metrics['emails_opened'],
               funnel_metrics['links_clicked'], funnel_metrics['conversations_started'],
               funnel_metrics['conversions']],
            textinfo="value+percent initial"
        ))
        funnel_fig.update_layout(title='Complete Sales Funnel: Email → Chatbot → Conversion')

        # Stage distribution chart using SAME funnel_metrics data for first 5 stages
        # Then current stage counts for conversation stages
        stage_names = ['Email Sent', 'Email Opened', 'Link Clicked', 'Conversations Started', 'Conversions']
        stage_values = [funnel_metrics['emails_sent'], funnel_metrics['emails_opened'],
                       funnel_metrics['links_clicked'], funnel_metrics['conversations_started'],
                       funnel_metrics['conversions']]

        stage_fig = go.Figure(data=[
            go.Bar(x=stage_names, y=stage_values, name='Cumulative Funnel Metrics')
        ])
        stage_fig.update_layout(title='Sales Funnel Metrics (Same as Funnel Chart)')

        charts = {
            'funnel': json.dumps(funnel_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'stages': json.dumps(stage_fig, cls=plotly.utils.PlotlyJSONEncoder)
        }

        # Use centralized summary with SAME data source
        summary = {
            'funnel_metrics': funnel_metrics,
            'conversion_rates': analytics['conversion_rates'],
            'stage_distribution': dict(zip(stage_names, stage_values)),
            'current_stage_counts': stage_counts  # Add current stage counts for reference
        }

        return render_template('unified_analytics.html', charts=charts, summary=summary)

    except Exception as e:
        app.logger.error(f"Analytics error: {str(e)}")
        return render_template('unified_analytics.html', charts={}, summary={})

@app.route('/analytics/comprehensive')
def comprehensive_analytics():
    """Comprehensive analytics dashboard with everything included"""
    try:
        # Get all analytics data with safe defaults
        analytics = get_unified_analytics()
        session_data = get_session_analytics()
        pipeline_data = get_sales_pipeline_analytics()
        cycle_data = get_sales_cycle_analytics()

        # Calculate total revenue safely
        total_revenue = 0
        try:
            converted_sessions = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).all()
            total_revenue = sum([session.conversion_value or 0 for session in converted_sessions])
        except:
            total_revenue = 0

        # Combine all data for comprehensive view with safe defaults
        comprehensive_data = {
            'total_sessions': session_data.get('sessions_started', 0),
            'total_conversions': session_data.get('converted_sessions', 0),
            'conversion_rate': (session_data.get('converted_sessions', 0) / session_data.get('sessions_started', 1) * 100) if session_data.get('sessions_started', 0) > 0 else 0,
            'total_revenue': total_revenue,
            'opening_count': pipeline_data.get('opening_entered', 0),
            'trust_count': pipeline_data.get('trust_entered', 0),
            'discovery_count': pipeline_data.get('discovery_entered', 0),
            'demo_count': pipeline_data.get('demonstration_entered', 0),
            'close_count': pipeline_data.get('close_entered', 0),
            'recent_sessions': session_data.get('recent_sessions', [])
        }

        # Create simple charts with safe data
        charts = {}
        try:
            # Simple funnel chart with safe values
            funnel_values = [
                comprehensive_data['total_sessions'],
                comprehensive_data['opening_count'],
                comprehensive_data['trust_count'],
                comprehensive_data['discovery_count'],
                comprehensive_data['demo_count'],
                comprehensive_data['close_count'],
                comprehensive_data['total_conversions']
            ]

            # Ensure all values are non-negative integers
            funnel_values = [max(0, int(v)) for v in funnel_values]

            funnel_fig = go.Figure(go.Funnel(
                y=['Sessions Started', 'Opening', 'Trust', 'Discovery', 'Demo', 'Close', 'Conversions'],
                x=funnel_values,
                textinfo="value+percent initial"
            ))

            funnel_fig.update_layout(
                title='Complete Sales Funnel',
                height=400
            )

            charts['main_funnel'] = json.dumps(funnel_fig, cls=plotly.utils.PlotlyJSONEncoder)

        except Exception as chart_error:
            # If chart creation fails, provide empty chart
            charts['main_funnel'] = json.dumps({}, cls=plotly.utils.PlotlyJSONEncoder)

        return render_template('comprehensive_analytics.html',
                             analytics_data=comprehensive_data,
                             email_data=analytics.get('funnel_metrics', {}),
                             session_data=session_data,
                             pipeline_data=pipeline_data,
                             cycle_data=cycle_data,
                             charts=charts)

    except Exception as e:
        app.logger.error(f"Comprehensive analytics error: {str(e)}")
        # Return a simple error page instead of redirecting
        return render_template('comprehensive_analytics.html',
                             analytics_data={'total_sessions': 0, 'total_conversions': 0, 'conversion_rate': 0, 'total_revenue': 0, 'opening_count': 0, 'trust_count': 0, 'discovery_count': 0, 'demo_count': 0, 'close_count': 0, 'recent_sessions': []},
                             email_data={},
                             session_data={},
                             pipeline_data={},
                             cycle_data={},
                             charts={'main_funnel': '{}'})

def get_session_analytics():
    """Get detailed session analytics data"""
    try:
        # Basic session counts
        total_sessions = ChatbotSession.query.count()
        active_sessions = ChatbotSession.query.filter(ChatbotSession.ended_at.is_(None)).count()
        completed_sessions = ChatbotSession.query.filter(ChatbotSession.completed_successfully == True).count()
        converted_sessions = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

        # Stage completion counts
        opening_completed = ChatbotSession.query.filter(ChatbotSession.opening_completed_at.isnot(None)).count()
        trust_completed = ChatbotSession.query.filter(ChatbotSession.trust_completed_at.isnot(None)).count()
        discovery_completed = ChatbotSession.query.filter(ChatbotSession.discovery_completed_at.isnot(None)).count()
        demo_completed = ChatbotSession.query.filter(ChatbotSession.demonstration_completed_at.isnot(None)).count()
        close_completed = ChatbotSession.query.filter(ChatbotSession.close_completed_at.isnot(None)).count()

        # Engagement levels
        high_engagement = ChatbotSession.query.filter_by(engagement_level='high').count()
        medium_engagement = ChatbotSession.query.filter_by(engagement_level='medium').count()
        low_engagement = ChatbotSession.query.filter_by(engagement_level='low').count()

        # Calculate average stage durations
        avg_stage_durations = {}
        stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']

        for stage in stages:
            sessions_with_duration = ChatbotSession.query.filter(
                getattr(ChatbotSession, f"{stage}_started_at").isnot(None),
                getattr(ChatbotSession, f"{stage}_completed_at").isnot(None)
            ).all()

            if sessions_with_duration:
                total_duration = 0
                count = 0
                for session in sessions_with_duration:
                    duration = session.calculate_stage_duration(stage)
                    if duration is not None:
                        total_duration += duration
                        count += 1

                if count > 0:
                    avg_stage_durations[stage.title()] = total_duration / count

        # Recent session activities
        recent_sessions = ChatbotSession.query.order_by(ChatbotSession.started_at.desc()).limit(10).all()

        return {
            'sessions_started': total_sessions,
            'active_sessions': active_sessions,
            'completed_sessions': completed_sessions,
            'converted_sessions': converted_sessions,
            'opening_completed': opening_completed,
            'trust_completed': trust_completed,
            'discovery_completed': discovery_completed,
            'demo_completed': demo_completed,
            'close_completed': close_completed,
            'high_engagement': high_engagement,
            'medium_engagement': medium_engagement,
            'low_engagement': low_engagement,
            'avg_stage_durations': avg_stage_durations,
            'recent_sessions': recent_sessions,
            'conversion_rate': (converted_sessions / total_sessions * 100) if total_sessions > 0 else 0,
            'completion_rate': (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
        }

    except Exception as e:
        app.logger.error(f"Session analytics error: {str(e)}")
        return {
            'sessions_started': 0,
            'active_sessions': 0,
            'completed_sessions': 0,
            'converted_sessions': 0,
            'opening_completed': 0,
            'trust_completed': 0,
            'discovery_completed': 0,
            'demo_completed': 0,
            'close_completed': 0,
            'high_engagement': 0,
            'medium_engagement': 0,
            'low_engagement': 0,
            'avg_stage_durations': {},
            'recent_sessions': [],
            'conversion_rate': 0,
            'completion_rate': 0
        }

def get_sales_pipeline_analytics():
    """Get comprehensive sales pipeline analytics data - SYNCHRONIZED with dashboard"""
    try:
        # Get basic pipeline counts directly from ChatbotSession (primary source)
        sessions_started = ChatbotSession.query.count()

        # Stage counts based on StageProgression for historical accuracy
        opening_entered = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                          .filter(StageProgression.stage_name == 'opening')
                                          .scalar() or 0)
        trust_entered = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                        .filter(StageProgression.stage_name == 'trust')
                                        .scalar() or 0)
        discovery_entered = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                            .filter(StageProgression.stage_name == 'discovery')
                                            .scalar() or 0)
        demonstration_entered = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                                .filter(StageProgression.stage_name == 'demonstration')
                                                .scalar() or 0)
        close_entered = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                          .filter(StageProgression.stage_name == 'close')
                                          .scalar() or 0)
        conversions_completed = (db.session.query(db.func.count(db.distinct(StageProgression.contact_id)))
                                                .filter(StageProgression.stage_name == 'converted')
                                                .scalar() or 0)

        # ChatbotSession stage markers for redundancy / validation
        chatbot_sessions_started = ChatbotSession.query.count()
        chatbot_opening_entered = ChatbotSession.query.filter(ChatbotSession.opening_started_at.isnot(None)).count()
        chatbot_trust_entered = ChatbotSession.query.filter(ChatbotSession.trust_started_at.isnot(None)).count()
        chatbot_discovery_entered = ChatbotSession.query.filter(ChatbotSession.discovery_started_at.isnot(None)).count()
        chatbot_demonstration_entered = ChatbotSession.query.filter(ChatbotSession.demonstration_started_at.isnot(None)).count()
        chatbot_close_entered = ChatbotSession.query.filter(ChatbotSession.close_started_at.isnot(None)).count()
        chatbot_conversions_completed = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

        # Use the higher counts
        sessions_started = max(sessions_started, chatbot_sessions_started)
        opening_entered = max(opening_entered, chatbot_opening_entered)
        trust_entered = max(trust_entered, chatbot_trust_entered)
        discovery_entered = max(discovery_entered, chatbot_discovery_entered)
        demonstration_entered = max(demonstration_entered, chatbot_demonstration_entered)
        close_entered = max(close_entered, chatbot_close_entered)
        conversions_completed = max(conversions_completed, chatbot_conversions_completed)

        # Calculate stage conversion rates
        stage_conversion_rates = {}
        if sessions_started > 0:
            stage_conversion_rates['Start → Opening'] = (opening_entered / sessions_started) * 100
        if opening_entered > 0:
            stage_conversion_rates['Opening → Trust'] = (trust_entered / opening_entered) * 100
        if trust_entered > 0:
            stage_conversion_rates['Trust → Discovery'] = (discovery_entered / trust_entered) * 100
        if discovery_entered > 0:
            stage_conversion_rates['Discovery → Demo'] = (demonstration_entered / discovery_entered) * 100
        if demonstration_entered > 0:
            stage_conversion_rates['Demo → Close'] = (close_entered / demonstration_entered) * 100
        if close_entered > 0:
            stage_conversion_rates['Close → Conversion'] = (conversions_completed / close_entered) * 100

        # Calculate sales velocity (average time between stages)
        sales_velocity = {}

        # Get sessions with stage timing data
        sessions_with_timing = ChatbotSession.query.filter(
            ChatbotSession.opening_started_at.isnot(None)
        ).all()

        if sessions_with_timing:
            # Calculate average time between stages
            opening_to_trust_times = []
            trust_to_discovery_times = []
            discovery_to_demo_times = []
            demo_to_close_times = []

            for session in sessions_with_timing:
                if session.opening_started_at and session.trust_started_at:
                    duration = (session.trust_started_at - session.opening_started_at).total_seconds() / 60
                    opening_to_trust_times.append(duration)

                if session.trust_started_at and session.discovery_started_at:
                    duration = (session.discovery_started_at - session.trust_started_at).total_seconds() / 60
                    trust_to_discovery_times.append(duration)

                if session.discovery_started_at and session.demonstration_started_at:
                    duration = (session.demonstration_started_at - session.discovery_started_at).total_seconds() / 60
                    discovery_to_demo_times.append(duration)

                if session.demonstration_started_at and session.close_started_at:
                    duration = (session.close_started_at - session.demonstration_started_at).total_seconds() / 60
                    demo_to_close_times.append(duration)

            sales_velocity['Opening → Trust'] = sum(opening_to_trust_times) / len(opening_to_trust_times) if opening_to_trust_times else 0
            sales_velocity['Trust → Discovery'] = sum(trust_to_discovery_times) / len(trust_to_discovery_times) if trust_to_discovery_times else 0
            sales_velocity['Discovery → Demo'] = sum(discovery_to_demo_times) / len(discovery_to_demo_times) if discovery_to_demo_times else 0
            sales_velocity['Demo → Close'] = sum(demo_to_close_times) / len(demo_to_close_times) if demo_to_close_times else 0

        # Calculate stage performance metrics
        stage_performance = {}
        stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']

        for stage in stages:
            started_sessions = ChatbotSession.query.filter(
                getattr(ChatbotSession, f"{stage}_started_at").isnot(None)
            ).all()

            completed_sessions = ChatbotSession.query.filter(
                getattr(ChatbotSession, f"{stage}_completed_at").isnot(None)
            ).all()

            success_rate = (len(completed_sessions) / len(started_sessions) * 100) if started_sessions else 0

            # Calculate average duration and engagement
            durations = []
            engagement_scores = []

            for session in completed_sessions:
                duration = session.calculate_stage_duration(stage)
                if duration is not None:
                    durations.append(duration)

                # Simple engagement score based on message count and stage
                if session.total_messages:
                    engagement_scores.append(min(session.total_messages * 10, 100))

            avg_duration = sum(durations) / len(durations) if durations else 0
            avg_engagement = sum(engagement_scores) / len(engagement_scores) if engagement_scores else 0

            stage_performance[stage] = {
                'success_rate': success_rate,
                'avg_duration': avg_duration,
                'engagement_score': avg_engagement
            }

        return {
            'sessions_started': sessions_started,
            'opening_entered': opening_entered,
            'trust_entered': trust_entered,
            'discovery_entered': discovery_entered,
            'demonstration_entered': demonstration_entered,
            'close_entered': close_entered,
            'conversions_completed': conversions_completed,
            'stage_conversion_rates': stage_conversion_rates,
            'sales_velocity': sales_velocity,
            'stage_performance': stage_performance,
            'overall_conversion_rate': (conversions_completed / sessions_started * 100) if sessions_started > 0 else 0
        }

    except Exception as e:
        app.logger.error(f"Sales pipeline analytics error: {str(e)}")
        return {
            'sessions_started': 0,
            'opening_entered': 0,
            'trust_entered': 0,
            'discovery_entered': 0,
            'demonstration_entered': 0,
            'close_entered': 0,
            'conversions_completed': 0,
            'stage_conversion_rates': {},
            'sales_velocity': {},
            'stage_performance': {},
            'overall_conversion_rate': 0
        }

def get_sales_cycle_analytics():
    """Get sales cycle timing and pattern analytics"""
    try:
        from datetime import datetime, timedelta

        # Get all completed sessions
        completed_sessions = ChatbotSession.query.filter(
            ChatbotSession.ended_at.isnot(None)
        ).all()

        converted_sessions = ChatbotSession.query.filter(
            ChatbotSession.conversion_achieved == True
        ).all()

        # Calculate average cycle times by stage
        avg_cycle_times = {}
        stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']

        for stage in stages:
            durations = []
            for session in completed_sessions:
                duration = session.calculate_stage_duration(stage)
                if duration is not None:
                    durations.append(duration)

            avg_cycle_times[stage] = sum(durations) / len(durations) if durations else 0

        # Get conversion times (total time from start to conversion)
        conversion_times = []
        for session in converted_sessions:
            if session.started_at and session.ended_at:
                total_time = (session.ended_at - session.started_at).total_seconds() / 60
                conversion_times.append(total_time)

        # Calculate daily trends for the last 30 days
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=30)

        daily_trends = {
            'dates': [],
            'sessions_started': [],
            'conversions': [],
            'avg_cycle_time': []
        }

        current_date = start_date
        while current_date <= end_date:
            daily_trends['dates'].append(current_date.strftime('%Y-%m-%d'))

            # Count sessions started on this date
            daily_sessions = ChatbotSession.query.filter(
                db.func.date(ChatbotSession.started_at) == current_date
            ).count()
            daily_trends['sessions_started'].append(daily_sessions)

            # Count conversions on this date
            daily_conversions = ChatbotSession.query.filter(
                db.func.date(ChatbotSession.ended_at) == current_date,
                ChatbotSession.conversion_achieved == True
            ).count()
            daily_trends['conversions'].append(daily_conversions)

            # Calculate average cycle time for sessions that ended on this date
            daily_completed = ChatbotSession.query.filter(
                db.func.date(ChatbotSession.ended_at) == current_date
            ).all()

            daily_cycle_times = []
            for session in daily_completed:
                if session.started_at and session.ended_at:
                    cycle_time = (session.ended_at - session.started_at).total_seconds() / 60
                    daily_cycle_times.append(cycle_time)

            avg_daily_cycle = sum(daily_cycle_times) / len(daily_cycle_times) if daily_cycle_times else 0
            daily_trends['avg_cycle_time'].append(avg_daily_cycle)

            current_date += timedelta(days=1)

        # Calculate stage drop-offs
        total_sessions = ChatbotSession.query.count()
        opening_dropoffs = total_sessions - ChatbotSession.query.filter(ChatbotSession.trust_started_at.isnot(None)).count()
        trust_dropoffs = ChatbotSession.query.filter(ChatbotSession.trust_started_at.isnot(None)).count() - ChatbotSession.query.filter(ChatbotSession.discovery_started_at.isnot(None)).count()
        discovery_dropoffs = ChatbotSession.query.filter(ChatbotSession.discovery_started_at.isnot(None)).count() - ChatbotSession.query.filter(ChatbotSession.demonstration_started_at.isnot(None)).count()
        demo_dropoffs = ChatbotSession.query.filter(ChatbotSession.demonstration_started_at.isnot(None)).count() - ChatbotSession.query.filter(ChatbotSession.close_started_at.isnot(None)).count()
        close_dropoffs = ChatbotSession.query.filter(ChatbotSession.close_started_at.isnot(None)).count() - ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

        stage_dropoffs = {
            'opening': opening_dropoffs,
            'trust': trust_dropoffs,
            'discovery': discovery_dropoffs,
            'demonstration': demo_dropoffs,
            'close': close_dropoffs
        }

        final_conversions = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()

        return {
            'avg_cycle_times': avg_cycle_times,
            'conversion_times': conversion_times,
            'daily_trends': daily_trends,
            'stage_dropoffs': stage_dropoffs,
            'final_conversions': final_conversions,
            'total_sessions': total_sessions,
            'avg_conversion_time': sum(conversion_times) / len(conversion_times) if conversion_times else 0,
            'fastest_conversion': min(conversion_times) if conversion_times else 0,
            'slowest_conversion': max(conversion_times) if conversion_times else 0
        }

    except Exception as e:
        app.logger.error(f"Sales cycle analytics error: {str(e)}")
        return {
            'avg_cycle_times': {},
            'conversion_times': [],
            'daily_trends': {'dates': [], 'sessions_started': [], 'conversions': [], 'avg_cycle_time': []},
            'stage_dropoffs': {},
            'final_conversions': 0,
            'total_sessions': 0,
            'avg_conversion_time': 0,
            'fastest_conversion': 0,
            'slowest_conversion': 0
        }

@app.route('/analytics/sessions')
def session_analytics():
    """Detailed session analytics showing chatbot performance"""
    try:
        # Get session analytics data
        session_data = get_session_analytics()

        # Create session funnel chart
        session_funnel_fig = go.Figure(go.Funnel(
            y=['Sessions Started', 'Opening Completed', 'Trust Built', 'Discovery Done', 'Demo Given', 'Closed'],
            x=[session_data['sessions_started'], session_data['opening_completed'],
               session_data['trust_completed'], session_data['discovery_completed'],
               session_data['demo_completed'], session_data['close_completed']],
            textinfo="value+percent initial",
            marker=dict(color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'])
        ))

        session_funnel_fig.update_layout(
            title='Chatbot Session Progression Funnel',
            height=500
        )

        # Create engagement level distribution
        engagement_fig = go.Figure(go.Pie(
            labels=['High Engagement', 'Medium Engagement', 'Low Engagement'],
            values=[session_data['high_engagement'], session_data['medium_engagement'], session_data['low_engagement']],
            marker=dict(colors=['#2ECC71', '#F39C12', '#E74C3C'])
        ))

        engagement_fig.update_layout(
            title='Session Engagement Levels',
            height=400
        )

        # Create average stage duration chart
        if session_data['avg_stage_durations']:
            duration_fig = go.Figure(go.Bar(
                x=list(session_data['avg_stage_durations'].keys()),
                y=list(session_data['avg_stage_durations'].values()),
                text=[f"{v:.1f}m" for v in session_data['avg_stage_durations'].values()],
                textposition='auto',
                marker_color='#3498DB'
            ))

            duration_fig.update_layout(
                title='Average Time Spent in Each Stage (Minutes)',
                xaxis_title='Sales Stage',
                yaxis_title='Duration (Minutes)',
                height=400
            )
        else:
            duration_fig = go.Figure()
            duration_fig.add_annotation(
                text="No stage duration data available yet",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            duration_fig.update_layout(title="Average Stage Durations")

        # Convert to JSON for template
        session_charts = {
            'session_funnel': json.dumps(session_funnel_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'engagement': json.dumps(engagement_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'durations': json.dumps(duration_fig, cls=plotly.utils.PlotlyJSONEncoder)
        }

        return render_template('session_analytics.html',
                             session_data=session_data,
                             charts=session_charts)

    except Exception as e:
        app.logger.error(f"Session analytics error: {str(e)}")
        flash('Error loading session analytics.', 'error')
        return redirect(url_for('analytics_dashboard'))

@app.route('/analytics/sales-pipeline')
def sales_pipeline_analytics():
    """Comprehensive sales pipeline analytics for chat sessions"""
    try:
        # Get detailed pipeline data
        pipeline_data = get_sales_pipeline_analytics()

        # Create sales pipeline funnel with conversion rates
        pipeline_funnel_fig = go.Figure(go.Funnel(
            y=['🚀 Sessions Started', '🚪 Opening Stage', '🤝 Trust Building', '🔍 Discovery', '📊 Demonstration', '🎯 Close Attempts', '✅ Conversions'],
            x=[
                pipeline_data['sessions_started'],
                pipeline_data['opening_entered'],
                pipeline_data['trust_entered'],
                pipeline_data['discovery_entered'],
                pipeline_data['demonstration_entered'],
                pipeline_data['close_entered'],
                pipeline_data['conversions_completed']
            ],
            textinfo="value+percent initial+percent previous",
            marker=dict(
                color=['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB', '#9B59B6', '#1ABC9C'],
                line=dict(width=2, color='white')
            )
        ))

        pipeline_funnel_fig.update_layout(
            title='Sales Pipeline: Chat Session Progression',
            height=600,
            font=dict(size=14)
        )

        # Create stage conversion rates chart
        conversion_rates = pipeline_data['stage_conversion_rates']
        conversion_fig = go.Figure(go.Bar(
            x=list(conversion_rates.keys()),
            y=list(conversion_rates.values()),
            text=[f"{v:.1f}%" for v in conversion_rates.values()],
            textposition='auto',
            marker=dict(
                color=['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB', '#9B59B6'],
                line=dict(width=1, color='white')
            )
        ))

        conversion_fig.update_layout(
            title='Stage-to-Stage Conversion Rates',
            xaxis_title='Sales Stage Transition',
            yaxis_title='Conversion Rate (%)',
            height=400
        )

        # Create sales velocity chart (time to progress through stages)
        velocity_data = pipeline_data['sales_velocity']
        velocity_fig = go.Figure(go.Scatter(
            x=list(velocity_data.keys()),
            y=list(velocity_data.values()),
            mode='lines+markers+text',
            text=[f"{v:.1f}m" for v in velocity_data.values()],
            textposition='top center',
            line=dict(width=3, color='#3498DB'),
            marker=dict(size=10, color='#E74C3C')
        ))

        velocity_fig.update_layout(
            title='Sales Velocity: Average Time to Progress Between Stages',
            xaxis_title='Stage Progression',
            yaxis_title='Average Time (Minutes)',
            height=400
        )

        # Create stage performance heatmap
        performance_data = pipeline_data['stage_performance']
        heatmap_fig = go.Figure(go.Heatmap(
            z=[
                [performance_data['opening']['success_rate'], performance_data['opening']['avg_duration'], performance_data['opening']['engagement_score']],
                [performance_data['trust']['success_rate'], performance_data['trust']['avg_duration'], performance_data['trust']['engagement_score']],
                [performance_data['discovery']['success_rate'], performance_data['discovery']['avg_duration'], performance_data['discovery']['engagement_score']],
                [performance_data['demonstration']['success_rate'], performance_data['demonstration']['avg_duration'], performance_data['demonstration']['engagement_score']],
                [performance_data['close']['success_rate'], performance_data['close']['avg_duration'], performance_data['close']['engagement_score']]
            ],
            x=['Success Rate (%)', 'Avg Duration (min)', 'Engagement Score'],
            y=['Opening', 'Trust', 'Discovery', 'Demonstration', 'Close'],
            colorscale='RdYlGn',
            text=[
                [f"{performance_data['opening']['success_rate']:.1f}%", f"{performance_data['opening']['avg_duration']:.1f}m", f"{performance_data['opening']['engagement_score']:.1f}"],
                [f"{performance_data['trust']['success_rate']:.1f}%", f"{performance_data['trust']['avg_duration']:.1f}m", f"{performance_data['trust']['engagement_score']:.1f}"],
                [f"{performance_data['discovery']['success_rate']:.1f}%", f"{performance_data['discovery']['avg_duration']:.1f}m", f"{performance_data['discovery']['engagement_score']:.1f}"],
                [f"{performance_data['demonstration']['success_rate']:.1f}%", f"{performance_data['demonstration']['avg_duration']:.1f}m", f"{performance_data['demonstration']['engagement_score']:.1f}"],
                [f"{performance_data['close']['success_rate']:.1f}%", f"{performance_data['close']['avg_duration']:.1f}m", f"{performance_data['close']['engagement_score']:.1f}"]
            ],
            texttemplate="%{text}",
            textfont={"size": 12}
        ))

        heatmap_fig.update_layout(
            title='Stage Performance Heatmap',
            height=400
        )

        # Convert to JSON for template
        pipeline_charts = {
            'pipeline_funnel': json.dumps(pipeline_funnel_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'conversion_rates': json.dumps(conversion_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'sales_velocity': json.dumps(velocity_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'performance_heatmap': json.dumps(heatmap_fig, cls=plotly.utils.PlotlyJSONEncoder)
        }

        return render_template('sales_pipeline_analytics.html',
                             pipeline_data=pipeline_data,
                             charts=pipeline_charts)

    except Exception as e:
        app.logger.error(f"Sales pipeline analytics error: {str(e)}")
        flash('Error loading sales pipeline analytics.', 'error')
        return redirect(url_for('analytics_dashboard'))

@app.route('/analytics/sales-cycle')
def sales_cycle_analytics():
    """Sales cycle analytics showing timing and patterns"""
    try:
        # Get sales cycle data
        cycle_data = get_sales_cycle_analytics()

        # Create sales cycle timeline chart
        timeline_fig = go.Figure()

        # Add bars for each stage duration
        stages = ['Opening', 'Trust', 'Discovery', 'Demonstration', 'Close']
        colors = ['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB']

        for i, stage in enumerate(stages):
            timeline_fig.add_trace(go.Bar(
                name=stage,
                x=[stage],
                y=[cycle_data['avg_cycle_times'][stage.lower()]],
                marker_color=colors[i],
                text=f"{cycle_data['avg_cycle_times'][stage.lower()]:.1f}m",
                textposition='auto'
            ))

        timeline_fig.update_layout(
            title='Average Sales Cycle Time by Stage',
            xaxis_title='Sales Stage',
            yaxis_title='Average Duration (Minutes)',
            height=400,
            showlegend=False
        )

        # Create cycle completion trends
        trends_fig = go.Figure()

        # Add trend lines for different metrics
        dates = cycle_data['daily_trends']['dates']

        trends_fig.add_trace(go.Scatter(
            x=dates,
            y=cycle_data['daily_trends']['sessions_started'],
            mode='lines+markers',
            name='Sessions Started',
            line=dict(color='#3498DB', width=2)
        ))

        trends_fig.add_trace(go.Scatter(
            x=dates,
            y=cycle_data['daily_trends']['conversions'],
            mode='lines+markers',
            name='Conversions',
            line=dict(color='#2ECC71', width=2)
        ))

        trends_fig.add_trace(go.Scatter(
            x=dates,
            y=cycle_data['daily_trends']['avg_cycle_time'],
            mode='lines+markers',
            name='Avg Cycle Time (min)',
            line=dict(color='#E74C3C', width=2),
            yaxis='y2'
        ))

        trends_fig.update_layout(
            title='Sales Cycle Trends Over Time',
            xaxis_title='Date',
            yaxis_title='Count',
            yaxis2=dict(
                title='Avg Cycle Time (min)',
                overlaying='y',
                side='right'
            ),
            height=400
        )

        # Create conversion time distribution
        distribution_fig = go.Figure(go.Histogram(
            x=cycle_data['conversion_times'],
            nbinsx=20,
            marker_color='#2ECC71',
            opacity=0.7
        ))

        distribution_fig.update_layout(
            title='Distribution of Conversion Times',
            xaxis_title='Time to Conversion (Minutes)',
            yaxis_title='Number of Conversions',
            height=400
        )

        # Create stage drop-off analysis
        dropoff_fig = go.Figure(go.Waterfall(
            name="Stage Drop-off",
            orientation="v",
            measure=["relative", "relative", "relative", "relative", "relative", "total"],
            x=["Opening", "Trust", "Discovery", "Demo", "Close", "Final"],
            textposition="outside",
            text=[f"-{cycle_data['stage_dropoffs']['opening']}",
                  f"-{cycle_data['stage_dropoffs']['trust']}",
                  f"-{cycle_data['stage_dropoffs']['discovery']}",
                  f"-{cycle_data['stage_dropoffs']['demonstration']}",
                  f"-{cycle_data['stage_dropoffs']['close']}",
                  f"{cycle_data['final_conversions']}"],
            y=[cycle_data['stage_dropoffs']['opening'],
               cycle_data['stage_dropoffs']['trust'],
               cycle_data['stage_dropoffs']['discovery'],
               cycle_data['stage_dropoffs']['demonstration'],
               cycle_data['stage_dropoffs']['close'],
               cycle_data['final_conversions']],
            connector={"line": {"color": "rgb(63, 63, 63)"}},
        ))

        dropoff_fig.update_layout(
            title="Sales Cycle Drop-off Analysis",
            height=400
        )

        # Convert to JSON for template
        cycle_charts = {
            'timeline': json.dumps(timeline_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'trends': json.dumps(trends_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'distribution': json.dumps(distribution_fig, cls=plotly.utils.PlotlyJSONEncoder),
            'dropoff': json.dumps(dropoff_fig, cls=plotly.utils.PlotlyJSONEncoder)
        }

        return render_template('sales_cycle_analytics.html',
                             cycle_data=cycle_data,
                             charts=cycle_charts)

    except Exception as e:
        app.logger.error(f"Sales cycle analytics error: {str(e)}")
        flash('Error loading sales cycle analytics.', 'error')
        return redirect(url_for('analytics_dashboard'))

@app.route('/opportunities')
def opportunities_list():
    """List all opportunities with unified tracking"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # For now, return empty since we're focusing on the unified flow
        return render_template('opportunities.html', opportunities=None)

    except Exception as e:
        app.logger.error(f"Opportunities list error: {str(e)}")
        return render_template('opportunities.html', opportunities=None)

@app.route('/contacts')
def contacts_list():
    """Enhanced contacts list with unified tracking and filtering"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        # Limit per_page to reasonable bounds
        per_page = min(max(per_page, 5), 200)

        # Get filter parameters
        search = request.args.get('search', '').strip()
        sales_stage = request.args.get('sales_stage', '').strip()
        source = request.args.get('source', '').strip()

        # Build query with filters
        query = Contact.query

        # Apply search filter
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                db.or_(
                    Contact.first_name.ilike(search_filter),
                    Contact.last_name.ilike(search_filter),
                    Contact.email.ilike(search_filter),
                    Contact.company.ilike(search_filter)
                )
            )

        # Apply sales stage filter
        if sales_stage:
            if sales_stage == 'new':
                # Handle 'new' contacts (those without a sales stage)
                query = query.filter(
                    db.or_(
                        Contact.current_sales_stage.is_(None),
                        Contact.current_sales_stage == ''
                    )
                )
            else:
                query = query.filter(Contact.current_sales_stage == sales_stage)

        # Apply source filter
        if source:
            query = query.filter(Contact.source == source)

        # Get paginated results
        contacts = query.order_by(Contact.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Get synchronized analytics for summary metrics
        analytics = get_unified_analytics()

        return render_template('unified_contacts.html',
                             contacts=contacts,
                             analytics=analytics,
                             current_per_page=per_page)

    except Exception as e:
        app.logger.error(f"Contacts list error: {str(e)}")
        return render_template('unified_contacts.html', contacts=None, analytics={})

@app.route('/contacts/add', methods=['GET', 'POST'])
def add_contact():
    """Add new contact - EXACT IMPLEMENTATION from documentation"""
    if request.method == 'POST':
        # Validate required fields first
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip()

        # Basic validation as per documentation requirements
        if not first_name:
            flash('First name is required.', 'error')
            return render_template('add_contact.html')

        if not last_name:
            flash('Last name is required.', 'error')
            return render_template('add_contact.html')

        if not email:
            flash('Email is required.', 'error')
            return render_template('add_contact.html')

        try:
            # Check for duplicate email (unique constraint)
            existing_contact = Contact.query.filter_by(email=email).first()
            if existing_contact:
                flash('A contact with this email already exists.', 'error')
                return render_template('add_contact.html')

            # Create contact exactly as shown in documentation
            contact = Contact(
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone=request.form.get('phone'),
                company=request.form.get('company'),
                job_title=request.form.get('job_title'),
                source=request.form.get('source', 'manual_entry'),
                status='new'
            )

            db.session.add(contact)
            db.session.commit()
            flash('Contact added successfully!', 'success')
            return redirect(url_for('contacts_list'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Add contact error: {str(e)}")
            app.logger.error(f"Error type: {type(e).__name__}")
            app.logger.error(f"Form data: {dict(request.form)}")

            # More specific error messages
            if "UNIQUE constraint failed" in str(e):
                flash('A contact with this email already exists.', 'error')
            elif "no such column" in str(e).lower():
                flash('Database schema error. Please contact administrator.', 'error')
            elif "NOT NULL constraint failed" in str(e):
                flash('Required field is missing. Please fill all required fields.', 'error')
            else:
                flash(f'Database error: {str(e)}', 'error')

    return render_template('add_contact.html')



@app.route('/contacts/<int:contact_id>')
def view_contact(contact_id):
    """View contact details"""
    try:
        contact = Contact.query.get_or_404(contact_id)

        # Get contact activities
        activities = Activity.query.filter_by(contact_id=contact_id).order_by(Activity.created_at.desc()).limit(20).all()

        # Get chatbot sessions
        chatbot_sessions = ChatbotSession.query.filter_by(contact_id=contact_id).order_by(ChatbotSession.started_at.desc()).all()

        return render_template('view_contact.html',
                             contact=contact,
                             activities=activities,
                             chatbot_sessions=chatbot_sessions)

    except Exception as e:
        app.logger.error(f"View contact error: {str(e)}")
        flash('Error loading contact details.', 'error')
        return redirect(url_for('contacts_list'))

@app.route('/contacts/<int:contact_id>/edit', methods=['GET', 'POST'])
def edit_contact(contact_id):
    """Edit contact"""
    try:
        contact = Contact.query.get_or_404(contact_id)

        if request.method == 'POST':
            contact.first_name = request.form.get('first_name')
            contact.last_name = request.form.get('last_name')
            contact.email = request.form.get('email')
            contact.phone = request.form.get('phone')
            contact.company = request.form.get('company')
            contact.job_title = request.form.get('job_title')
            contact.source = request.form.get('source')
            contact.do_not_email = 'do_not_email' in request.form
            contact.is_active = 'is_active' in request.form

            db.session.commit()
            flash('Contact updated successfully!', 'success')
            return redirect(url_for('view_contact', contact_id=contact.id))

        return render_template('edit_contact.html', contact=contact)

    except Exception as e:
        app.logger.error(f"Edit contact error: {str(e)}")
        flash('Error updating contact.', 'error')
        return redirect(url_for('contacts_list'))

@app.route('/contacts/<int:contact_id>/delete', methods=['POST'])
def delete_contact(contact_id):
    """Delete contact and all related content"""
    try:
        contact = Contact.query.get_or_404(contact_id)

        # Comprehensive cleanup - delete ALL related data
        deleted_items = delete_contact_and_content(contact_id)

        flash(f'Contact "{contact.full_name}" and all related content deleted successfully! Removed: {deleted_items}', 'success')
        return redirect(url_for('contacts_list'))

    except Exception as e:
        app.logger.error(f"Delete contact error: {str(e)}")
        flash('Error deleting contact. Please try again.', 'error')
        return redirect(url_for('contacts_list'))



@app.route('/contacts/bulk-delete', methods=['POST'])
def bulk_delete_contacts():
    """COMPLETELY FIXED: Bulk delete multiple contacts permanently using direct SQL"""
    try:
        # Get form data
        contact_ids = request.form.getlist('contact_ids')
        force_delete = request.form.get('force_delete') == 'true'

        print(f"DEBUG: Received contact_ids: {contact_ids}")
        print(f"DEBUG: Force delete: {force_delete}")

        # Validate input
        if not contact_ids:
            flash('No contacts selected for deletion.', 'warning')
            return redirect(url_for('contacts_list'))

        # Convert to integers
        try:
            contact_ids = [int(id) for id in contact_ids if id and str(id).isdigit()]
        except (ValueError, TypeError):
            flash('Invalid contact IDs provided.', 'error')
            return redirect(url_for('contacts_list'))

        if not contact_ids:
            flash('No valid contacts selected for deletion.', 'warning')
            return redirect(url_for('contacts_list'))

        print(f"DEBUG: Valid contact_ids: {contact_ids}")

        # Get existing contacts to verify they exist
        existing_contacts = Contact.query.filter(Contact.id.in_(contact_ids)).all()
        if not existing_contacts:
            flash('No existing contacts found for deletion.', 'warning')
            return redirect(url_for('contacts_list'))

        existing_contact_ids = [c.id for c in existing_contacts]
        print(f"DEBUG: Found existing contacts: {existing_contact_ids}")

        # Check campaign links
        linked_contacts = [c for c in existing_contacts if c.email_campaign_id is not None]
        if linked_contacts and not force_delete:
            linked_names = [c.full_name for c in linked_contacts[:3]]
            flash(f'Some contacts are linked to campaigns: {", ".join(linked_names)}. Use "Force Delete" to remove all data.', 'warning')
            return redirect(url_for('contacts_list'))

        # Use direct SQL for reliable deletion
        deleted_count = 0
        error_count = 0

        # Use the same database connection as Flask-SQLAlchemy
        # This ensures we're working with the same database
        print(f"DEBUG: Using database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
        conn = db.engine.raw_connection()
        cursor = conn.cursor()

        try:
            # Test if contacts table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='contacts'")
            contacts_table_exists = cursor.fetchone() is not None
            print(f"DEBUG: Contacts table exists: {contacts_table_exists}")

            if not contacts_table_exists:
                print("DEBUG: ERROR - Contacts table does not exist in the database!")
                flash('Database error: Contacts table not found. Please check your database setup.', 'error')
                conn.close()
                return redirect(url_for('contacts_list'))

            # Disable foreign key constraints temporarily
            cursor.execute("PRAGMA foreign_keys = OFF")

            for contact_id in existing_contact_ids:
                try:
                    print(f"DEBUG: Deleting contact {contact_id}")

                    # Delete related data first (in correct order)
                    # 1. Chat messages (if table exists)
                    try:
                        cursor.execute('''
                            DELETE FROM chat_messages
                            WHERE session_id IN (
                                SELECT session_id FROM chatbot_sessions WHERE contact_id = ?
                            )
                        ''', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: chat_messages table doesn't exist, skipping")
                        else:
                            raise e

                    # 2. Chatbot sessions (if table exists)
                    try:
                        cursor.execute('DELETE FROM chatbot_sessions WHERE contact_id = ?', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: chatbot_sessions table doesn't exist, skipping")
                        else:
                            raise e

                    # 3. Activities (if table exists)
                    try:
                        cursor.execute('DELETE FROM activities WHERE contact_id = ?', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: activities table doesn't exist, skipping")
                        else:
                            raise e

                    # 4. Email logs (if table exists)
                    try:
                        cursor.execute('DELETE FROM email_logs WHERE contact_id = ?', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: email_logs table doesn't exist, skipping")
                        else:
                            raise e

                    # 5. Email failures (if table exists)
                    try:
                        cursor.execute('DELETE FROM email_failures WHERE contact_id = ?', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: email_failures table doesn't exist, skipping")
                        else:
                            raise e

                    # 6. Group memberships (if table exists)
                    try:
                        cursor.execute('DELETE FROM contact_group_memberships WHERE contact_id = ?', (contact_id,))
                    except Exception as e:
                        if "no such table" in str(e).lower():
                            print(f"DEBUG: contact_group_memberships table doesn't exist, skipping")
                        else:
                            raise e

                    # 7. Delete the contact itself
                    before_changes = conn.total_changes
                    cursor.execute('DELETE FROM contacts WHERE id = ?', (contact_id,))
                    # SQLite's cursor.rowcount is often -1 (unknown). Instead, compare total_changes,
                    # or assume success when no exception is raised.
                    if conn.total_changes > before_changes:
                        deleted_count += 1
                        print(f"DEBUG: Successfully deleted contact {contact_id}")
                    else:
                        # This should rarely happen since we verified existence earlier, but keep a log.
                        error_count += 1
                        print(f"DEBUG: Contact {contact_id} was not deleted (no changes reported)")

                except Exception as e:
                    error_count += 1
                    print(f"DEBUG: Error deleting contact {contact_id}: {str(e)}")
                    continue

            # Re-enable foreign key constraints
            cursor.execute("PRAGMA foreign_keys = ON")

            # Commit all changes
            conn.commit()
            print(f"DEBUG: Committed changes. Deleted: {deleted_count}, Errors: {error_count}")

        except Exception as e:
            conn.rollback()
            print(f"DEBUG: Database error, rolled back: {str(e)}")
            flash(f'Database error during deletion: {str(e)}', 'error')
            return redirect(url_for('contacts_list'))
        finally:
            conn.close()

        # Also refresh the Flask-SQLAlchemy session to reflect changes
        db.session.expire_all()

        # Provide feedback
        if deleted_count > 0:
            flash(f'✅ Successfully deleted {deleted_count} contacts and all related data!', 'success')
        else:
            flash(f'❌ No contacts were deleted. {error_count} errors occurred.', 'error')

        return redirect(url_for('contacts_list'))

    except Exception as e:
        print(f"DEBUG: Exception in bulk_delete_contacts: {str(e)}")
        flash(f'Error during bulk deletion: {str(e)}', 'error')
        return redirect(url_for('contacts_list'))

def delete_contact_and_content(contact_id):
    """Comprehensive contact deletion with all related content - PERMANENT DELETION"""
    try:
        contact = Contact.query.get(contact_id)
        if not contact:
            return "Contact not found"

        deleted_items = []
        app.logger.info(f"Starting comprehensive deletion for contact {contact_id}: {contact.full_name}")

        # Disable foreign key constraints temporarily for SQLite
        db.session.execute(db.text("PRAGMA foreign_keys = OFF"))

        # Helper function to safely delete from table
        def safe_delete_from_table(table_name, where_clause, params):
            try:
                result = db.session.execute(
                    db.text(f"DELETE FROM {table_name} WHERE {where_clause}"),
                    params
                )
                return result.rowcount
            except Exception as e:
                if "no such table" in str(e).lower():
                    app.logger.info(f"Table {table_name} does not exist, skipping")
                    return 0
                else:
                    app.logger.warning(f"Error deleting from {table_name}: {str(e)}")
                    return 0

        try:
            # 1. Delete all chat messages for sessions related to this contact (if table exists)
            chat_messages_deleted = 0
            try:
                chat_messages_deleted = db.session.execute(
                    db.text("""
                        DELETE FROM chat_messages
                        WHERE session_id IN (
                            SELECT session_id FROM chatbot_sessions WHERE contact_id = :contact_id
                        )
                    """),
                    {"contact_id": contact_id}
                ).rowcount
            except Exception as e:
                if "no such table" not in str(e).lower():
                    app.logger.warning(f"Error deleting chat messages: {str(e)}")

            if chat_messages_deleted > 0:
                deleted_items.append(f"{chat_messages_deleted} chat messages")

            # 2. Delete all chatbot sessions for this contact
            sessions_deleted = safe_delete_from_table(
                "chatbot_sessions", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if sessions_deleted > 0:
                deleted_items.append(f"{sessions_deleted} chatbot sessions")

            # 3. Delete all activities for this contact
            activities_deleted = safe_delete_from_table(
                "activities", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if activities_deleted > 0:
                deleted_items.append(f"{activities_deleted} activities")

            # 4. Delete all email logs for this contact
            email_logs_deleted = safe_delete_from_table(
                "email_logs", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if email_logs_deleted > 0:
                deleted_items.append(f"{email_logs_deleted} email logs")

            # 5. Delete all email failures for this contact
            email_failures_deleted = safe_delete_from_table(
                "email_failures", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if email_failures_deleted > 0:
                deleted_items.append(f"{email_failures_deleted} email failures")

            # 6. Delete all stage progressions for this contact
            stage_progressions_deleted = safe_delete_from_table(
                "stage_progressions", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if stage_progressions_deleted > 0:
                deleted_items.append(f"{stage_progressions_deleted} stage progressions")

            # 7. Delete all group memberships for this contact
            memberships_deleted = safe_delete_from_table(
                "contact_group_memberships", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if memberships_deleted > 0:
                deleted_items.append(f"{memberships_deleted} group memberships")

            # 7. Delete all opportunities for this contact
            opportunities_deleted = safe_delete_from_table(
                "opportunities", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if opportunities_deleted > 0:
                deleted_items.append(f"{opportunities_deleted} opportunities")

            # 8. Delete all email events for this contact
            email_events_deleted = safe_delete_from_table(
                "email_events", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if email_events_deleted > 0:
                deleted_items.append(f"{email_events_deleted} email events")

            # 9. Delete all chat events for this contact
            chat_events_deleted = safe_delete_from_table(
                "chat_events", "contact_id = :contact_id", {"contact_id": contact_id}
            )
            if chat_events_deleted > 0:
                deleted_items.append(f"{chat_events_deleted} chat events")

        except Exception as e:
            app.logger.error(f"Error deleting related data: {str(e)}")
            raise
        finally:
            # Re-enable foreign key constraints
            db.session.execute(db.text("PRAGMA foreign_keys = ON"))

        # 9. Update campaign statistics if contact was part of a campaign
        try:
            if hasattr(contact, 'email_campaign_id') and contact.email_campaign_id:
                campaign = EmailCampaign.query.get(contact.email_campaign_id)
                if campaign:
                    # Decrement campaign counters safely
                    if hasattr(contact, 'first_email_sent') and contact.first_email_sent:
                        campaign.emails_sent = max(0, campaign.emails_sent - 1)
                    if hasattr(contact, 'email_opened') and contact.email_opened:
                        campaign.emails_opened = max(0, campaign.emails_opened - 1)
                    if hasattr(contact, 'chatbot_link_clicked') and contact.chatbot_link_clicked:
                        campaign.chatbot_links_clicked = max(0, campaign.chatbot_links_clicked - 1)
                    if hasattr(contact, 'chatbot_conversation_started') and contact.chatbot_conversation_started:
                        campaign.chatbot_conversations_started = max(0, campaign.chatbot_conversations_started - 1)
                    if hasattr(contact, 'conversion_completed') and contact.conversion_completed:
                        campaign.conversions_achieved = max(0, campaign.conversions_achieved - 1)

                    deleted_items.append("campaign statistics updated")
                    app.logger.info("Updated campaign statistics")
        except Exception as e:
            app.logger.warning(f"Error updating campaign statistics: {str(e)}")

        # 10. Delete the contact itself
        db.session.delete(contact)
        deleted_items.append("1 contact")
        app.logger.info("Marked contact for deletion")

        # 11. Commit all changes
        db.session.commit()
        app.logger.info(f"Successfully deleted contact {contact_id} and all related data")

        return ", ".join(deleted_items)

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error in delete_contact_and_content: {str(e)}")
        app.logger.error(f"Error type: {type(e).__name__}")
        app.logger.error(f"Error details: {repr(e)}")
        import traceback
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        raise e



@app.route('/contacts/upload', methods=['GET', 'POST'])
def upload_contacts():
    """Upload contacts via CSV or JSON file"""
    if request.method == 'POST':
        try:
            # Check if file was uploaded
            if 'contact_file' not in request.files:
                flash('No file selected.', 'error')
                return redirect(request.url)

            file = request.files['contact_file']
            if file.filename == '':
                flash('No file selected.', 'error')
                return redirect(request.url)

            # Get file format
            file_format = request.form.get('file_format', 'csv')

            # Validate file extension
            filename_lower = file.filename.lower()
            if file_format == 'csv' and not filename_lower.endswith('.csv'):
                flash('Please upload a CSV file.', 'error')
                return redirect(request.url)
            elif file_format == 'json' and not filename_lower.endswith('.json'):
                flash('Please upload a JSON file.', 'error')
                return redirect(request.url)

            # Get form data
            group_name = request.form.get('group_name', '').strip()
            create_new_group = request.form.get('create_new_group') == 'on'
            existing_group_id = request.form.get('existing_group_id')

            # Process file based on format
            contacts_data = []

            if file_format == 'csv':
                # Read and process CSV
                import csv
                import io

                # Read file content
                stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
                csv_input = csv.DictReader(stream)
                contacts_data = list(csv_input)

            elif file_format == 'json':
                # Read and process JSON
                import json

                # Read file content
                file_content = file.stream.read().decode("UTF8")
                try:
                    json_data = json.loads(file_content)

                    # Ensure it's a list
                    if isinstance(json_data, dict):
                        contacts_data = [json_data]
                    elif isinstance(json_data, list):
                        contacts_data = json_data
                    else:
                        flash('Invalid JSON format. Expected an object or array of contact objects.', 'error')
                        return redirect(request.url)

                except json.JSONDecodeError as e:
                    flash(f'Invalid JSON file: {str(e)}', 'error')
                    return redirect(request.url)

            contacts_created = 0
            contacts_updated = 0
            contacts_skipped = 0
            errors = []

            # Create or get group if specified
            group = None
            if create_new_group and group_name:
                # Check if group already exists
                existing_group = ContactGroup.query.filter_by(name=group_name).first()
                if existing_group:
                    group = existing_group
                    flash(f'Using existing group: {group_name}', 'info')
                else:
                    group = ContactGroup(
                        name=group_name,
                        description=f'Uploaded from CSV on {datetime.utcnow().strftime("%Y-%m-%d %H:%M")}',
                        created_by='csv_upload'
                    )
                    db.session.add(group)
                    db.session.flush()  # Get the ID
            elif existing_group_id:
                group = ContactGroup.query.get(existing_group_id)

            # Process each contact
            for row_num, contact_data in enumerate(contacts_data, start=1):
                try:
                    # Extract data from contact record
                    email = contact_data.get('email', '').strip().lower()
                    first_name = contact_data.get('first_name', '').strip()
                    last_name = contact_data.get('last_name', '').strip()
                    phone = contact_data.get('phone', '').strip()
                    company = contact_data.get('company', '').strip()
                    job_title = contact_data.get('job_title', '').strip()
                    website = contact_data.get('website', '').strip()
                    notes = contact_data.get('notes', '').strip()

                    if not email:
                        errors.append(f'Row {row_num}: Missing email address')
                        contacts_skipped += 1
                        continue

                    if not first_name or first_name.lower() in ['sir/madam', 'sir', 'madam', 'contact', 'person']:
                        # Try to extract name from email if first_name is generic
                        email_parts = email.split('@')[0].split('.')
                        if len(email_parts) >= 2:
                            first_name = email_parts[0].title()
                            if not last_name:
                                last_name = email_parts[1].title()
                        elif not first_name or first_name.lower() in ['sir/madam', 'sir', 'madam']:
                            first_name = 'Contact'
                            last_name = 'Person'

                    # Clean phone number for Uganda format
                    if phone:
                        phone = re.sub(r'[^\d+]', '', phone)
                        # Handle various phone number formats
                        if len(phone) < 7:  # Too short to be valid
                            phone = ''
                        elif phone.startswith('256') and len(phone) >= 12:
                            phone = '+' + phone
                        elif phone.startswith('0') and len(phone) == 10:
                            phone = '+256' + phone[1:]
                        elif phone.startswith('7') and len(phone) == 9:
                            phone = '+256' + phone
                        elif len(phone) > 15:  # Too long to be valid
                            phone = ''

                    # Check if contact already exists
                    existing_contact = Contact.query.filter_by(email=email).first()

                    if existing_contact:
                        # Update existing contact
                        existing_contact.first_name = first_name
                        existing_contact.last_name = last_name
                        existing_contact.phone = phone or existing_contact.phone
                        existing_contact.company = company or existing_contact.company
                        existing_contact.job_title = job_title or existing_contact.job_title
                        # Skip new fields for now until database is migrated
                        # try:
                        #     if hasattr(existing_contact, 'website'):
                        #         existing_contact.website = website or existing_contact.website
                        #     if hasattr(existing_contact, 'notes'):
                        #         existing_contact.notes = notes or existing_contact.notes
                        #     if hasattr(existing_contact, 'lead_score'):
                        #         existing_contact.lead_score = existing_contact.lead_score or 50.0
                        # except:
                        #     pass  # Skip if columns don't exist yet
                        existing_contact.source = f'{file_format}_upload'
                        existing_contact.updated_at = datetime.utcnow()
                        contacts_updated += 1
                        contact = existing_contact
                    else:
                        # Create new contact with basic fields first
                        contact_data = {
                            'first_name': first_name,
                            'last_name': last_name,
                            'email': email,
                            'phone': phone,
                            'company': company,
                            'job_title': job_title,
                            'source': f'{file_format}_upload',
                            'status': 'new'
                        }

                        # Skip new fields for now until database is migrated
                        # try:
                        #     # Test if the columns exist by checking the model
                        #     if hasattr(Contact, 'website'):
                        #         contact_data['website'] = website
                        #     if hasattr(Contact, 'notes'):
                        #         contact_data['notes'] = notes
                        #     if hasattr(Contact, 'lead_score'):
                        #         contact_data['lead_score'] = 50.0
                        # except:
                        #     pass  # Skip if columns don't exist yet

                        contact = Contact(**contact_data)
                        db.session.add(contact)
                        contacts_created += 1

                    # Add to group if specified
                    if group:
                        db.session.flush()  # Ensure contact has an ID
                        # Check if already in group
                        existing_membership = ContactGroupMembership.query.filter_by(
                            contact_id=contact.id,
                            group_id=group.id
                        ).first()

                        if not existing_membership:
                            membership = ContactGroupMembership(
                                contact_id=contact.id,
                                group_id=group.id,
                                added_by=f'{file_format}_upload'
                            )
                            db.session.add(membership)

                except Exception as e:
                    errors.append(f'Row {row_num}: {str(e)}')
                    contacts_skipped += 1
                    continue

            db.session.commit()

            # Create success message
            message_parts = []
            if contacts_created > 0:
                message_parts.append(f'{contacts_created} contacts created')
            if contacts_updated > 0:
                message_parts.append(f'{contacts_updated} contacts updated')
            if contacts_skipped > 0:
                message_parts.append(f'{contacts_skipped} contacts skipped')

            success_message = ', '.join(message_parts)
            if group:
                success_message += f' and added to group "{group.name}"'

            flash(f'{file_format.upper()} upload completed! {success_message}.', 'success')

            if errors:
                error_message = f'{len(errors)} errors occurred: ' + '; '.join(errors[:5])
                if len(errors) > 5:
                    error_message += f' and {len(errors) - 5} more...'
                flash(error_message, 'warning')

            return redirect(url_for('contacts_list'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"{file_format.upper()} upload error: {str(e)}")
            app.logger.error(f"Full traceback: ", exc_info=True)
            flash(f'Error processing {file_format.upper()} file: {str(e)}', 'error')

    # Get existing groups for the form
    groups = ContactGroup.query.filter_by(is_active=True).order_by(ContactGroup.name).all()
    return render_template('upload_contacts.html', groups=groups)

@app.route('/campaigns/create-with-csv', methods=['POST'])
def create_campaign_with_csv():
    """Create campaign and upload contacts from CSV in one step"""
    try:
        # Validate form data
        name = request.form.get('name', '').strip()
        template = request.form.get('template', '').strip()
        target_audience = request.form.get('target_audience', '').strip()
        group_option = request.form.get('group_option', 'none')
        group_name = request.form.get('group_name', '').strip()
        daily_send_limit = int(request.form.get('daily_send_limit', 100))
        send_option = request.form.get('send_option', 'create_only')
        sender_name = request.form.get('sender_name', '24Seven Assistants Sales Team')

        if not name or not template:
            flash('Campaign name and template are required.', 'error')
            return redirect(url_for('campaigns_list'))

        # Check if file was uploaded
        if 'csv_file' not in request.files:
            flash('No CSV file uploaded.', 'error')
            return redirect(url_for('campaigns_list'))

        file = request.files['csv_file']
        if file.filename == '':
            flash('No file selected.', 'error')
            return redirect(url_for('campaigns_list'))

        if not file.filename.lower().endswith('.csv'):
            flash('Please upload a CSV file.', 'error')
            return redirect(url_for('campaigns_list'))

        # Process CSV file
        csv_content = file.read().decode('utf-8')
        csv_reader = csv.DictReader(csv_content.splitlines())

        # Validate CSV headers
        required_headers = ['email']
        optional_headers = ['first_name', 'last_name', 'company', 'phone', 'website']

        if not all(header in csv_reader.fieldnames for header in required_headers):
            flash(f'CSV file must contain these columns: {", ".join(required_headers)}', 'error')
            return redirect(url_for('campaigns_list'))

        # Create contact group if requested
        group = None
        if group_option == 'create' and group_name:
            # Check if group already exists
            existing_group = ContactGroup.query.filter_by(name=group_name).first()
            if existing_group:
                group = existing_group
                app.logger.info(f"Using existing group: {group_name}")
            else:
                group = ContactGroup(
                    name=group_name,
                    description=f'Contacts uploaded for campaign: {name}',
                    color='#007bff',
                    created_by='csv_campaign_upload'
                )
                db.session.add(group)
                db.session.flush()  # Get the ID
                app.logger.info(f"Created new group: {group_name}")

        # Process contacts from CSV
        contacts_added = 0
        contacts_updated = 0
        contacts_skipped = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):
            try:
                email = row.get('email', '').strip().lower()
                if not email:
                    errors.append(f'Row {row_num}: Missing email')
                    contacts_skipped += 1
                    continue

                first_name = row.get('first_name', '').strip()
                last_name = row.get('last_name', '').strip()
                company = row.get('company', '').strip()
                phone = row.get('phone', '').strip()
                website = row.get('website', '').strip()

                # Use company name as fallback for names
                if not first_name and not last_name and company:
                    words = company.split()
                    if len(words) >= 2:
                        first_name = words[0]
                        last_name = ' '.join(words[1:])
                    else:
                        first_name = company
                        last_name = 'Contact'

                if not first_name:
                    first_name = 'Unknown'
                if not last_name:
                    last_name = 'Contact'

                # Clean phone number
                if phone:
                    phone = re.sub(r'[^\d+]', '', phone)
                    if phone.startswith('256'):
                        phone = '+' + phone
                    elif phone.startswith('0') and len(phone) == 10:
                        phone = '+256' + phone[1:]

                # Check if contact exists
                existing_contact = Contact.query.filter_by(email=email).first()

                if existing_contact:
                    # Update existing contact
                    existing_contact.first_name = first_name
                    existing_contact.last_name = last_name
                    existing_contact.company = company or existing_contact.company
                    existing_contact.phone = phone or existing_contact.phone
                    existing_contact.website = website or existing_contact.website
                    existing_contact.source = 'csv_campaign_upload'
                    existing_contact.updated_at = datetime.utcnow()
                    contact = existing_contact
                    contacts_updated += 1
                else:
                    # Create new contact
                    contact = Contact(
                        first_name=first_name,
                        last_name=last_name,
                        email=email,
                        phone=phone,
                        company=company,
                        website=website,
                        source='csv_campaign_upload',
                        status='new',
                        lead_score=50.0
                    )
                    db.session.add(contact)
                    db.session.flush()
                    contacts_added += 1

                # Add contact to group if group was created
                if group:
                    existing_membership = ContactGroupMembership.query.filter_by(
                        contact_id=contact.id,
                        group_id=group.id
                    ).first()

                    if not existing_membership:
                        membership = ContactGroupMembership(
                            contact_id=contact.id,
                            group_id=group.id,
                            added_by='csv_campaign_upload'
                        )
                        db.session.add(membership)

            except Exception as e:
                error_msg = f'Row {row_num}: Error processing contact - {str(e)}'
                errors.append(error_msg)
                contacts_skipped += 1
                continue

        # Create the email campaign
        campaign = EmailCampaign(
            name=name,
            subject=f"Introduction from {sender_name}",
            template_name=template,
            sender_name=sender_name,
            sender_email=app.config.get('SMTP_USERNAME', '<EMAIL>'),
            reply_to_email=app.config.get('SMTP_USERNAME', '<EMAIL>'),
            target_audience=target_audience or f'Contacts from CSV upload',
            recipient_criteria=json.dumps({
                "type": "groups" if group else "all",
                "group_ids": [group.id] if group else []
            }),
            daily_send_limit=daily_send_limit,
            status='draft',
            created_by='csv_campaign_upload'
        )

        db.session.add(campaign)
        db.session.flush()  # Get campaign ID

        # If group was created, associate it with the campaign
        if group:
            campaign_group = CampaignGroup(
                campaign_id=campaign.id,
                group_id=group.id,
                added_by='csv_campaign_upload'
            )
            db.session.add(campaign_group)

        # Create email logs for all contacts
        total_recipients = 0
        if group:
            # Get contacts from the group
            group_contacts = db.session.query(Contact).join(
                ContactGroupMembership,
                Contact.id == ContactGroupMembership.contact_id
            ).filter(
                ContactGroupMembership.group_id == group.id,
                Contact.is_active == True,
                Contact.do_not_email == False
            ).all()
        else:
            # Get all active contacts
            group_contacts = Contact.query.filter_by(
                is_active=True,
                do_not_email=False
            ).all()

        for contact in group_contacts:
            email_log = EmailLog(
                campaign_id=campaign.id,
                contact_id=contact.id,
                recipient_email=contact.email,
                recipient_name=contact.full_name,
                subject=campaign.subject,
                status='pending'
            )
            db.session.add(email_log)
            total_recipients += 1

        # Update campaign with recipient count
        campaign.total_recipients = total_recipients

        # Commit all changes
        db.session.commit()

        # Prepare success message
        message_parts = []
        if contacts_added > 0:
            message_parts.append(f'{contacts_added} new contacts added')
        if contacts_updated > 0:
            message_parts.append(f'{contacts_updated} contacts updated')
        if group:
            message_parts.append(f'Group "{group.name}" created/updated')
        message_parts.append(f'Campaign "{name}" created with {total_recipients} recipients')

        success_message = f'✅ CSV upload successful! {", ".join(message_parts)}.'

        if errors:
            error_count = len(errors)
            success_message += f' ⚠️ {error_count} rows had errors and were skipped.'

        flash(success_message, 'success')

        # Send immediately if requested
        if send_option == 'send_immediately':
            try:
                # Update campaign status and start sending
                campaign.status = 'sending'
                campaign.started_at = datetime.utcnow()
                db.session.commit()

                flash(f'Campaign "{name}" is now sending emails!', 'info')
                return redirect(url_for('view_campaign', campaign_id=campaign.id))
            except Exception as e:
                app.logger.error(f"Error starting campaign: {str(e)}")
                flash('Campaign created but failed to start sending. You can start it manually.', 'warning')

        return redirect(url_for('view_campaign', campaign_id=campaign.id))

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"CSV campaign creation error: {str(e)}")
        flash(f'Error creating campaign with CSV: {str(e)}', 'error')
        return redirect(url_for('campaigns_list'))

# Contact Groups Management Routes
@app.route('/groups')
def groups_list():
    """List all contact groups"""
    try:
        groups = ContactGroup.query.filter_by(is_active=True).order_by(ContactGroup.name).all()

        # Calculate contact counts for each group
        groups_with_counts = []
        for group in groups:
            contact_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()
            groups_with_counts.append({
                'group': group,
                'contact_count': contact_count
            })

        return render_template('groups_list.html', groups_with_counts=groups_with_counts)
    except Exception as e:
        app.logger.error(f"Groups list error: {str(e)}")
        flash('Error loading groups.', 'error')
        return render_template('groups_list.html', groups_with_counts=[])

@app.route('/groups/create', methods=['GET', 'POST'])
def create_group():
    """Create new contact group"""
    if request.method == 'POST':
        try:
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()
            color = request.form.get('color', '#007bff')

            if not name:
                flash('Group name is required.', 'error')
                return redirect(request.url)

            # Check if group already exists
            existing_group = ContactGroup.query.filter_by(name=name).first()
            if existing_group:
                flash('A group with this name already exists.', 'error')
                return redirect(request.url)

            group = ContactGroup(
                name=name,
                description=description,
                color=color,
                created_by='manual'
            )

            db.session.add(group)
            db.session.commit()

            flash(f'Group "{name}" created successfully!', 'success')
            return redirect(url_for('groups_list'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Create group error: {str(e)}")
            flash('Error creating group.', 'error')

    return render_template('create_group.html')

@app.route('/groups/<int:group_id>')
def view_group(group_id):
    """View group details and contacts"""
    try:
        group = ContactGroup.query.get_or_404(group_id)

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        per_page = min(max(per_page, 5), 100)

        # Get group contacts with pagination
        contact_ids_query = db.session.query(ContactGroupMembership.contact_id).filter_by(group_id=group_id)

        contacts_pagination = Contact.query.filter(
            Contact.id.in_(contact_ids_query)
        ).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        total_contacts = Contact.query.filter(
            Contact.id.in_(contact_ids_query)
        ).count()

        return render_template('view_group.html',
                             group=group,
                             contacts=contacts_pagination.items,
                             contacts_pagination=contacts_pagination,
                             total_contacts=total_contacts)

    except Exception as e:
        app.logger.error(f"View group error: {str(e)}")
        flash('Error loading group details.', 'error')
        return redirect(url_for('groups_list'))

@app.route('/groups/<int:group_id>/edit', methods=['GET', 'POST'])
def edit_group(group_id):
    """Edit group details"""
    try:
        group = ContactGroup.query.get_or_404(group_id)

        if request.method == 'POST':
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()
            color = request.form.get('color', '#007bff')

            if not name:
                flash('Group name is required.', 'error')
                return redirect(request.url)

            # Check if another group has this name
            existing_group = ContactGroup.query.filter(
                ContactGroup.name == name,
                ContactGroup.id != group_id
            ).first()
            if existing_group:
                flash('A group with this name already exists.', 'error')
                return redirect(request.url)

            group.name = name
            group.description = description
            group.color = color
            group.updated_at = datetime.utcnow()

            db.session.commit()

            flash(f'Group "{name}" updated successfully!', 'success')
            return redirect(url_for('view_group', group_id=group_id))

        # Calculate contact count manually to avoid property issues
        contact_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()

        return render_template('edit_group.html', group=group, contact_count=contact_count)

    except Exception as e:
        app.logger.error(f"Edit group error: {str(e)}")
        flash('Error editing group.', 'error')
        return redirect(url_for('groups_list'))

@app.route('/groups/<int:group_id>/delete', methods=['POST'])
def delete_group(group_id):
    """Delete group permanently with all related content"""
    try:
        group = ContactGroup.query.get_or_404(group_id)

        # Comprehensive cleanup - delete ALL related data
        deleted_items = delete_group_and_content(group_id)

        flash(f'Group "{group.name}" and all related content deleted permanently! Removed: {deleted_items}', 'success')
        return redirect(url_for('groups_list'))

    except Exception as e:
        app.logger.error(f"Delete group error: {str(e)}")
        flash('Error deleting group. Please try again.', 'error')
        return redirect(url_for('groups_list'))

def delete_group_and_content(group_id):
    """Comprehensive group deletion with all related content - PERMANENT DELETION"""
    try:
        group = ContactGroup.query.get(group_id)
        if not group:
            return "Group not found"

        deleted_items = []
        app.logger.info(f"Starting comprehensive deletion for group {group_id}: {group.name}")

        # Disable foreign key constraints temporarily for SQLite
        db.session.execute(db.text("PRAGMA foreign_keys = OFF"))

        try:
            # 1. Delete all contact group memberships
            memberships_deleted = db.session.execute(
                db.text("DELETE FROM contact_group_memberships WHERE group_id = :group_id"),
                {"group_id": group_id}
            ).rowcount
            if memberships_deleted > 0:
                deleted_items.append(f"{memberships_deleted} contact memberships")
                app.logger.info(f"Deleted {memberships_deleted} contact memberships")

            # 2. Delete all campaign group associations
            campaign_groups_deleted = db.session.execute(
                db.text("DELETE FROM campaign_groups WHERE group_id = :group_id"),
                {"group_id": group_id}
            ).rowcount
            if campaign_groups_deleted > 0:
                deleted_items.append(f"{campaign_groups_deleted} campaign associations")
                app.logger.info(f"Deleted {campaign_groups_deleted} campaign associations")

        except Exception as e:
            app.logger.error(f"Error deleting group related data: {str(e)}")
            raise
        finally:
            # Re-enable foreign key constraints
            db.session.execute(db.text("PRAGMA foreign_keys = ON"))

        # 3. Delete the group itself
        db.session.delete(group)
        deleted_items.append("1 group")
        app.logger.info("Marked group for deletion")

        # 4. Commit all changes
        db.session.commit()
        app.logger.info(f"Successfully deleted group {group_id} and all related data")

        return ", ".join(deleted_items)

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error in delete_group_and_content: {str(e)}")
        raise

@app.route('/groups/bulk-delete', methods=['POST'])
def bulk_delete_groups():
    """Bulk delete multiple groups permanently"""
    try:
        group_ids = request.form.getlist('group_ids')

        if not group_ids:
            flash('No groups selected for deletion.', 'warning')
            return redirect(url_for('groups_list'))

        # Convert to integers and validate
        try:
            group_ids = [int(id) for id in group_ids if id.isdigit()]
        except ValueError:
            flash('Invalid group IDs provided.', 'error')
            return redirect(url_for('groups_list'))

        if not group_ids:
            flash('No valid groups selected for deletion.', 'warning')
            return redirect(url_for('groups_list'))

        # Delete all groups and their content
        total_deleted_items = []
        groups_deleted = 0

        for group_id in group_ids:
            try:
                deleted_items = delete_group_and_content(group_id)
                total_deleted_items.append(deleted_items)
                groups_deleted += 1
            except Exception as e:
                app.logger.error(f"Error deleting group {group_id}: {str(e)}")
                continue

        if groups_deleted > 0:
            flash(f'Successfully deleted {groups_deleted} groups and all related content! Total removed: {"; ".join(total_deleted_items)}', 'success')
        else:
            flash('No groups were deleted due to errors.', 'error')

        return redirect(url_for('groups_list'))

    except Exception as e:
        app.logger.error(f"Bulk delete groups error: {str(e)}")
        flash('Error during bulk deletion. Please try again.', 'error')
        return redirect(url_for('groups_list'))

@app.route('/groups/<int:group_id>/add_contacts', methods=['GET', 'POST'])
def add_contacts_to_group(group_id):
    """Add contacts to group"""
    try:
        group = ContactGroup.query.get_or_404(group_id)

        if request.method == 'POST':
            contact_ids = request.form.getlist('contact_ids')

            if not contact_ids:
                flash('No contacts selected.', 'warning')
                return redirect(request.url)

            contacts_added = 0
            contacts_skipped = 0

            for contact_id in contact_ids:
                try:
                    contact_id = int(contact_id)

                    # Check if already in group
                    existing_membership = ContactGroupMembership.query.filter_by(
                        contact_id=contact_id,
                        group_id=group_id
                    ).first()

                    if not existing_membership:
                        membership = ContactGroupMembership(
                            contact_id=contact_id,
                            group_id=group_id,
                            added_by='manual'
                        )
                        db.session.add(membership)
                        contacts_added += 1
                    else:
                        contacts_skipped += 1

                except (ValueError, TypeError):
                    continue

            db.session.commit()

            message_parts = []
            if contacts_added > 0:
                message_parts.append(f'{contacts_added} contacts added')
            if contacts_skipped > 0:
                message_parts.append(f'{contacts_skipped} already in group')

            flash(f'Group updated! {", ".join(message_parts)}.', 'success')
            return redirect(url_for('view_group', group_id=group_id))

        # Get contacts not in this group
        existing_contact_ids = db.session.query(ContactGroupMembership.contact_id).filter_by(group_id=group_id)
        available_contacts = Contact.query.filter(
            ~Contact.id.in_(existing_contact_ids),
            Contact.is_active == True
        ).order_by(Contact.first_name, Contact.last_name).all()

        return render_template('add_contacts_to_group.html',
                             group=group,
                             available_contacts=available_contacts)

    except Exception as e:
        app.logger.error(f"Add contacts to group error: {str(e)}")
        flash('Error adding contacts to group.', 'error')
        return redirect(url_for('view_group', group_id=group_id))

@app.route('/groups/<int:group_id>/remove_contact/<int:contact_id>', methods=['POST'])
def remove_contact_from_group(group_id, contact_id):
    """Remove contact from group"""
    try:
        membership = ContactGroupMembership.query.filter_by(
            contact_id=contact_id,
            group_id=group_id
        ).first()

        if membership:
            db.session.delete(membership)
            db.session.commit()
            flash('Contact removed from group successfully!', 'success')
        else:
            flash('Contact not found in group.', 'warning')

        return redirect(url_for('view_group', group_id=group_id))

    except Exception as e:
        app.logger.error(f"Remove contact from group error: {str(e)}")
        flash('Error removing contact from group.', 'error')
        return redirect(url_for('view_group', group_id=group_id))

# Campaign-Group Assignment Routes
@app.route('/campaigns/<int:campaign_id>/assign_groups', methods=['GET', 'POST'])
def assign_groups_to_campaign(campaign_id):
    """Assign contact groups to campaign"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        if request.method == 'POST':
            group_ids = request.form.getlist('group_ids')

            if not group_ids:
                flash('No groups selected.', 'warning')
                return redirect(request.url)

            groups_added = 0
            groups_skipped = 0
            contacts_added = 0

            for group_id in group_ids:
                try:
                    group_id = int(group_id)

                    # Check if already assigned
                    existing_assignment = CampaignGroup.query.filter_by(
                        campaign_id=campaign_id,
                        group_id=group_id
                    ).first()

                    if not existing_assignment:
                        # Assign group to campaign
                        assignment = CampaignGroup(
                            campaign_id=campaign_id,
                            group_id=group_id,
                            added_by='manual'
                        )
                        db.session.add(assignment)
                        groups_added += 1

                        # Create EmailLog records for all contacts in the group
                        group_contacts = db.session.query(Contact).join(
                            ContactGroupMembership,
                            Contact.id == ContactGroupMembership.contact_id
                        ).filter(ContactGroupMembership.group_id == group_id).all()

                        for contact in group_contacts:
                            # Check if EmailLog already exists
                            existing_log = EmailLog.query.filter_by(
                                campaign_id=campaign_id,
                                contact_id=contact.id
                            ).first()

                            if not existing_log:
                                email_log = EmailLog(
                                    campaign_id=campaign_id,
                                    contact_id=contact.id,
                                    recipient_email=contact.email,
                                    recipient_name=contact.full_name,
                                    subject=campaign.subject,
                                    status='pending'
                                )
                                db.session.add(email_log)
                                contacts_added += 1
                    else:
                        groups_skipped += 1

                except (ValueError, TypeError):
                    continue

            db.session.commit()

            message_parts = []
            if groups_added > 0:
                message_parts.append(f'{groups_added} groups assigned')
            if contacts_added > 0:
                message_parts.append(f'{contacts_added} contacts added to campaign')
            if groups_skipped > 0:
                message_parts.append(f'{groups_skipped} groups already assigned')

            flash(f'Campaign updated! {", ".join(message_parts)}.', 'success')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        # Get groups not assigned to this campaign
        assigned_group_ids = db.session.query(CampaignGroup.group_id).filter_by(campaign_id=campaign_id)
        available_groups = ContactGroup.query.filter(
            ~ContactGroup.id.in_(assigned_group_ids),
            ContactGroup.is_active == True
        ).order_by(ContactGroup.name).all()

        return render_template('assign_groups_to_campaign.html',
                             campaign=campaign,
                             available_groups=available_groups)

    except Exception as e:
        app.logger.error(f"Assign groups to campaign error: {str(e)}")
        flash('Error assigning groups to campaign.', 'error')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

@app.route('/campaigns/<int:campaign_id>/remove_group/<int:group_id>', methods=['POST'])
def remove_group_from_campaign(campaign_id, group_id):
    """Remove group from campaign"""
    try:
        assignment = CampaignGroup.query.filter_by(
            campaign_id=campaign_id,
            group_id=group_id
        ).first()

        if assignment:
            # Remove the assignment
            db.session.delete(assignment)

            # Optionally remove EmailLog records for contacts in this group
            # (You might want to keep them for historical tracking)
            group_contact_ids = db.session.query(ContactGroupMembership.contact_id).filter_by(group_id=group_id)
            EmailLog.query.filter(
                EmailLog.campaign_id == campaign_id,
                EmailLog.contact_id.in_(group_contact_ids),
                EmailLog.status == 'pending'  # Only remove pending emails
            ).delete(synchronize_session=False)

            db.session.commit()
            flash('Group removed from campaign successfully!', 'success')
        else:
            flash('Group not found in campaign.', 'warning')

        return redirect(url_for('view_campaign', campaign_id=campaign_id))

    except Exception as e:
        app.logger.error(f"Remove group from campaign error: {str(e)}")
        flash('Error removing group from campaign.', 'error')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

@app.route('/campaigns')
def campaigns_list():
    """Enhanced campaigns list with conversion tracking and filtering"""
    try:
        app.logger.info("🔍 Starting campaigns_list route")

        # Get filter parameters
        search = request.args.get('search', '').strip()
        status = request.args.get('status', '').strip()
        template = request.args.get('template', '').strip()
        performance = request.args.get('performance', '').strip()
        date_range = request.args.get('date_range', '').strip()

        app.logger.info(f"🔍 Filters: search='{search}', status='{status}', template='{template}', performance='{performance}', date_range='{date_range}'")

        # Build query with filters
        query = EmailCampaign.query

        # Apply search filter
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                db.or_(
                    EmailCampaign.name.ilike(search_filter),
                    EmailCampaign.subject.ilike(search_filter),
                    EmailCampaign.template_name.ilike(search_filter)
                )
            )

        # Apply status filter
        if status:
            query = query.filter(EmailCampaign.status == status)

        # Apply template filter
        if template:
            query = query.filter(EmailCampaign.template_name == template)

        # Apply performance filter
        if performance:
            if performance == 'high_open':
                # High open rate (>30%)
                query = query.filter(
                    db.and_(
                        EmailCampaign.emails_sent > 0,
                        (EmailCampaign.emails_opened * 100.0 / EmailCampaign.emails_sent) > 30
                    )
                )
            elif performance == 'high_click':
                # High click rate (>10%)
                query = query.filter(
                    db.and_(
                        EmailCampaign.emails_sent > 0,
                        (EmailCampaign.chatbot_links_clicked * 100.0 / EmailCampaign.emails_sent) > 10
                    )
                )
            elif performance == 'high_conversion':
                # High conversion rate (>5%)
                query = query.filter(
                    db.and_(
                        EmailCampaign.emails_sent > 0,
                        (EmailCampaign.conversions_achieved * 100.0 / EmailCampaign.emails_sent) > 5
                    )
                )
            elif performance == 'low_performance':
                # Low performance (<10% open rate)
                query = query.filter(
                    db.and_(
                        EmailCampaign.emails_sent > 0,
                        (EmailCampaign.emails_opened * 100.0 / EmailCampaign.emails_sent) < 10
                    )
                )

        # Apply date range filter
        if date_range:
            from datetime import datetime, timedelta
            today = datetime.now().date()

            if date_range == 'today':
                query = query.filter(db.func.date(EmailCampaign.created_at) == today)
            elif date_range == 'yesterday':
                yesterday = today - timedelta(days=1)
                query = query.filter(db.func.date(EmailCampaign.created_at) == yesterday)
            elif date_range == 'last-7-days':
                seven_days_ago = today - timedelta(days=7)
                query = query.filter(EmailCampaign.created_at >= seven_days_ago)
            elif date_range == 'last-30-days':
                thirty_days_ago = today - timedelta(days=30)
                query = query.filter(EmailCampaign.created_at >= thirty_days_ago)
            elif date_range == 'this-month':
                first_day_of_month = today.replace(day=1)
                query = query.filter(EmailCampaign.created_at >= first_day_of_month)
            elif date_range == 'last-month':
                # Get first day of last month
                first_day_this_month = today.replace(day=1)
                last_month = first_day_this_month - timedelta(days=1)
                first_day_last_month = last_month.replace(day=1)
                query = query.filter(
                    db.and_(
                        EmailCampaign.created_at >= first_day_last_month,
                        EmailCampaign.created_at < first_day_this_month
                    )
                )

        # Get filtered campaigns
        campaigns = query.order_by(EmailCampaign.created_at.desc()).all()
        app.logger.info(f"🔍 Found {len(campaigns)} campaigns after filtering")

        # Get synchronized analytics for summary metrics
        analytics = get_unified_analytics()
        app.logger.info(f"🔍 Analytics retrieved: {type(analytics)}")

        app.logger.info(f"🔍 About to render unified_campaigns.html with {len(campaigns)} campaigns")
        return render_template('unified_campaigns.html',
                             campaigns=campaigns,
                             analytics=analytics)

    except Exception as e:
        app.logger.error(f"Campaigns list error: {str(e)}")
        import traceback
        app.logger.error(f"Campaigns list traceback: {traceback.format_exc()}")
        return render_template('unified_campaigns.html', campaigns=[], analytics={})

@app.route('/campaigns/create', methods=['GET', 'POST'])
def create_campaign():
    """Create campaign with chatbot integration and recipient selection"""
    if request.method == 'POST':
        try:
            campaign_name = request.form.get('name')
            template_name = request.form.get('template', 'introduction')
            # Determine recipient type based on radio button but also
            # fallback to the data actually provided so that we do not
            # unintentionally target all contacts when the user forgets
            # to switch the radio option.
            recipient_type = request.form.get('recipient_type', 'all')

            # If group/contact selections are submitted but the radio
            # button value wasn't updated accordingly, override the
            # recipient_type so that the campaign only targets the
            # intended subset.
            selected_contacts_raw = request.form.getlist('selected_contacts')
            selected_groups_raw = request.form.getlist('selected_groups')
            # Decide override carefully so that we do **not** accidentally
            # promote the request to "specific" when the user has also
            # selected groups.  Priority order:
            #   1. Explicit radio value wins if it is "specific" or "groups".
            #   2. If radio says "all" or something else, then infer from
            #      the submitted lists.
            if recipient_type not in ('specific', 'groups'):
                if selected_groups_raw:
                    recipient_type = 'groups'
                elif selected_contacts_raw:
                    recipient_type = 'specific'

            # Validate required fields first
            if not campaign_name or len(campaign_name.strip()) == 0:
                flash('Campaign name is required.', 'error')
                return redirect(url_for('create_campaign'))

            if len(campaign_name) > 255:
                flash('Campaign name must be 255 characters or less.', 'error')
                return redirect(url_for('create_campaign'))

            if not template_name:
                flash('Email template is required.', 'error')
                return redirect(url_for('create_campaign'))

            # Validate and convert daily send limit
            try:
                daily_send_limit = int(request.form.get('daily_send_limit', 100))
                if daily_send_limit < 1 or daily_send_limit > 10000:
                    flash('Daily send limit must be between 1 and 10,000.', 'error')
                    return redirect(url_for('create_campaign'))
            except (ValueError, TypeError):
                flash('Daily send limit must be a valid number.', 'error')
                return redirect(url_for('create_campaign'))

            # Validate and convert batch processing options
            try:
                batch_size = int(request.form.get('batch_size', 50))
                if batch_size < 1 or batch_size > 1000:
                    flash('Batch size must be between 1 and 1,000.', 'error')
                    return redirect(url_for('create_campaign'))
            except (ValueError, TypeError):
                flash('Batch size must be a valid number.', 'error')
                return redirect(url_for('create_campaign'))

            try:
                batch_delay_minutes = int(request.form.get('batch_delay_minutes', 5))
                if batch_delay_minutes < 0 or batch_delay_minutes > 1440:  # Max 24 hours
                    flash('Batch delay must be between 0 and 1,440 minutes (24 hours).', 'error')
                    return redirect(url_for('create_campaign'))
            except (ValueError, TypeError):
                flash('Batch delay must be a valid number.', 'error')
                return redirect(url_for('create_campaign'))

            # Get other options after validation
            send_schedule = request.form.get('send_schedule', 'immediate')
            scheduled_start_date = request.form.get('scheduled_start_date')
            scheduled_start_time = request.form.get('scheduled_start_time')

            app.logger.info(f"Creating campaign: name={campaign_name}, template={template_name}, recipient_type={recipient_type}, daily_limit={daily_send_limit}")

            campaign = EmailCampaign(
                name=campaign_name,
                template_name=template_name,
                subject=f"24Seven Assistants - Meet Sarah, Your AI Sales Assistant",
                status='draft',
                emails_failed=0,
                retry_count=0,
                max_retries=3,
                retry_failed_only=True,
                daily_send_limit=daily_send_limit,
                send_schedule=send_schedule,
                batch_size=batch_size,
                batch_delay_minutes=batch_delay_minutes
            )

            # Store recipient selection criteria
            recipient_criteria = {
                'type': recipient_type
            }

            # Build recipient criteria based on the (possibly auto-corrected)
            # recipient_type value determined above.
            # -- Capture recipient selections ----------------------------
            if recipient_type == 'specific':
                selected_contacts = request.form.getlist('selected_contacts')
                if not selected_contacts:
                    flash('Please select at least one contact for the campaign.', 'error')
                    return redirect(url_for('create_campaign'))
                recipient_criteria['contact_ids'] = [int(id) for id in selected_contacts if id.isdigit()]

            elif recipient_type == 'groups':
                # Make sure we fall back to the raw list that was already read before
                selected_groups = request.form.getlist('selected_groups') or selected_groups_raw
                if not selected_groups:
                    flash('Please select at least one contact group for the campaign.', 'error')
                    return redirect(url_for('create_campaign'))
                recipient_criteria['group_ids'] = [int(id) for id in selected_groups if id.isdigit()]

            elif recipient_type == 'filtered':
                # Store filter criteria
                filters = {}
                if request.form.get('status_filter'):
                    filters['status'] = request.form.get('status_filter')
                if request.form.get('source_filter'):
                    filters['source'] = request.form.get('source_filter')
                if request.form.get('industry_filter'):
                    filters['industry'] = request.form.get('industry_filter')
                if request.form.get('company_size_filter'):
                    filters['company_size'] = request.form.get('company_size_filter')
                if request.form.get('min_lead_score'):
                    filters['min_lead_score'] = int(request.form.get('min_lead_score', 0))
                if request.form.get('decision_makers_only'):
                    filters['decision_makers_only'] = True
                if request.form.get('exclude_customers'):
                    filters['exclude_customers'] = True

                recipient_criteria['filters'] = filters

            # Store recipient criteria as JSON
            import json
            campaign.recipient_criteria = json.dumps(recipient_criteria)

            # Debug logging
            app.logger.info(f"Campaign creation - recipient_type: {recipient_type}")
            app.logger.info(f"Campaign creation - recipient_criteria: {recipient_criteria}")
            app.logger.info(f"Campaign creation - recipient_criteria JSON: {campaign.recipient_criteria}")

            # Set scheduled date/time if provided
            if send_schedule == 'scheduled' and scheduled_start_date:
                from datetime import datetime, date, time
                try:
                    campaign.scheduled_start_date = datetime.strptime(scheduled_start_date, '%Y-%m-%d').date()
                    if scheduled_start_time:
                        campaign.scheduled_start_time = datetime.strptime(scheduled_start_time, '%H:%M').time()
                    else:
                        campaign.scheduled_start_time = time(9, 0)  # Default to 9 AM
                except ValueError:
                    flash('Invalid date or time format.', 'error')
                    return redirect(url_for('create_campaign'))

            db.session.add(campaign)
            db.session.flush()  # Get campaign ID before creating group associations

            # Create CampaignGroup records if groups were selected
            if recipient_type == 'groups' and 'group_ids' in recipient_criteria:
                group_ids = recipient_criteria['group_ids']
                app.logger.info(f"Creating CampaignGroup records for groups: {group_ids}")

                for group_id in group_ids:
                    try:
                        # Check if assignment already exists
                        existing_assignment = CampaignGroup.query.filter_by(
                            campaign_id=campaign.id,
                            group_id=group_id
                        ).first()

                        if not existing_assignment:
                            campaign_group = CampaignGroup(
                                campaign_id=campaign.id,
                                group_id=group_id,
                                added_by='campaign_creation'
                            )
                            db.session.add(campaign_group)
                            app.logger.info(f"Created CampaignGroup record for campaign {campaign.id}, group {group_id}")
                    except Exception as e:
                        app.logger.error(f"Error creating CampaignGroup record for group {group_id}: {str(e)}")

            db.session.commit()

            app.logger.info(f"Campaign created successfully with ID: {campaign.id}")
            flash('Campaign created successfully! Emails will include chatbot links.', 'success')
            return redirect(url_for('campaigns_list'))

        except Exception as e:
            app.logger.error(f"Create campaign error: {str(e)}")
            db.session.rollback()
            flash(f'Error creating campaign: {str(e)}', 'error')
            return redirect(url_for('create_campaign'))

    # GET request - show form
    templates = [
        {'name': 'introduction', 'display_name': '24Seven Assistants + Enhanced Chat with Sarah', 'subject': 'Meet Sarah - Our Personal AI Sales Assistant'},
        {'name': 'followup', 'display_name': 'Follow-up with AI Assistant', 'subject': 'Quick Follow-up: Chat with Sarah About Your Needs'}
    ]

    # Get contacts and groups for selection
    contacts = Contact.query.filter_by(is_active=True, do_not_email=False).order_by(Contact.first_name, Contact.last_name).all()
    groups = ContactGroup.query.order_by(ContactGroup.name).all()

    # Note: contact_count is already available as a property on each group

    return render_template('create_campaign.html',
                         templates=templates,
                         contacts=contacts,
                         groups=groups)

@app.route('/campaigns/<int:campaign_id>')
def view_campaign(campaign_id):
    """View campaign details"""
    try:
        app.logger.info(f"🔍 Starting view_campaign for campaign_id: {campaign_id}")
        campaign = EmailCampaign.query.get_or_404(campaign_id)
        app.logger.info(f"✅ Campaign found: {campaign.name}")

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # Limit per_page to reasonable values
        per_page = min(max(per_page, 5), 100)

        # Get campaign contacts through EmailLog relationship
        # First, get all contact IDs that have email logs for this campaign
        contact_ids_query = db.session.query(EmailLog.contact_id).filter_by(campaign_id=campaign_id).distinct()
        contact_ids_list = [row[0] for row in contact_ids_query.all()]

        # If no email logs exist, show all active contacts as potential recipients
        if not contact_ids_list:
            app.logger.info(f"No email logs found for campaign {campaign_id}, showing all active contacts")
            try:
                contacts_pagination = Contact.query.filter_by(
                    is_active=True,
                    do_not_email=False
                ).paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )
                total_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).count()
            except Exception as e:
                app.logger.error(f"Error in pagination (no email logs): {str(e)}")
                # Fallback to simple query without pagination
                contacts = Contact.query.filter_by(is_active=True, do_not_email=False).limit(per_page).all()
                contacts_pagination = type('MockPagination', (), {
                    'items': contacts,
                    'page': page,
                    'per_page': per_page,
                    'total': len(contacts),
                    'pages': 1,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                })()
                total_contacts = len(contacts)
        else:
            # Get contacts with pagination
            try:
                contacts_pagination = Contact.query.filter(
                    Contact.id.in_(contact_ids_list)
                ).paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )
                total_contacts = len(contact_ids_list)
            except Exception as e:
                app.logger.error(f"Error in pagination (with email logs): {str(e)}")
                # Fallback to simple query without pagination
                contacts = Contact.query.filter(Contact.id.in_(contact_ids_list)).limit(per_page).all()
                contacts_pagination = type('MockPagination', (), {
                    'items': contacts,
                    'page': page,
                    'per_page': per_page,
                    'total': len(contacts),
                    'pages': 1,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                })()
                total_contacts = len(contact_ids_list)

        # Get campaign activities
        activities = Activity.query.filter_by(session_id=str(campaign_id)).order_by(Activity.created_at.desc()).limit(20).all()

        app.logger.info(f"🎨 About to render template with {len(contacts_pagination.items)} contacts and {len(activities)} activities")

        # Test if the issue is with template rendering
        try:
            result = render_template('view_campaign.html',
                                 campaign=campaign,
                                 contacts=contacts_pagination.items,
                                 contacts_pagination=contacts_pagination,
                                 total_contacts=total_contacts,
                                 activities=activities)
            app.logger.info(f"✅ Template rendered successfully")
            return result
        except Exception as template_error:
            app.logger.error(f"❌ Template rendering error: {str(template_error)}")
            import traceback
            app.logger.error(f"❌ Template traceback: {traceback.format_exc()}")
            raise template_error

    except Exception as e:
        app.logger.error(f"View campaign error: {str(e)}")
        import traceback
        app.logger.error(f"View campaign traceback: {traceback.format_exc()}")
        flash('Error loading campaign details.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/<int:campaign_id>/edit', methods=['GET', 'POST'])
def edit_campaign(campaign_id):
    """Edit campaign details"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        if request.method == 'POST':
            # Update campaign details
            campaign.name = request.form.get('name', campaign.name)
            campaign.subject = request.form.get('subject', campaign.subject)
            campaign.template_name = request.form.get('template_name', campaign.template_name)

            db.session.commit()
            flash('Campaign updated successfully!', 'success')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        return render_template('edit_campaign.html', campaign=campaign)

    except Exception as e:
        app.logger.error(f"Edit campaign error: {str(e)}")
        flash('Error editing campaign.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/<int:campaign_id>/delete', methods=['POST'])
def delete_campaign(campaign_id):
    """Delete campaign and all related content"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        # Allow deletion of any campaign status with confirmation
        force_delete = request.form.get('force_delete') == 'true'

        if campaign.status not in ['draft', 'failed'] and not force_delete:
            flash('This campaign has been sent and contains tracking data. Use "Force Delete" to permanently remove all data.', 'warning')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        # Comprehensive cleanup - delete ALL related data
        deleted_items = delete_campaign_and_content(campaign_id)

        flash(f'Campaign "{campaign.name}" and all related content deleted successfully! Removed: {deleted_items}', 'success')
        return redirect(url_for('campaigns_list'))

    except Exception as e:
        app.logger.error(f"Delete campaign error: {str(e)}")
        flash('Error deleting campaign. Please try again.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/bulk-delete', methods=['POST'])
def bulk_delete_campaigns():
    """Bulk delete multiple campaigns and all their content"""
    try:
        campaign_ids = request.form.getlist('campaign_ids')
        force_delete = request.form.get('force_delete') == 'true'

        if not campaign_ids:
            flash('No campaigns selected for deletion.', 'warning')
            return redirect(url_for('campaigns_list'))

        # Convert to integers
        campaign_ids = [int(id) for id in campaign_ids if id.isdigit()]

        if not campaign_ids:
            flash('Invalid campaign selection.', 'error')
            return redirect(url_for('campaigns_list'))

        # Check if any campaigns are not draft/failed
        campaigns = EmailCampaign.query.filter(EmailCampaign.id.in_(campaign_ids)).all()
        active_campaigns = [c for c in campaigns if c.status not in ['draft', 'failed']]

        if active_campaigns and not force_delete:
            campaign_names = [c.name for c in active_campaigns]
            flash(f'Some campaigns have been sent and contain tracking data: {", ".join(campaign_names)}. Use "Force Delete" to permanently remove all data.', 'warning')
            return redirect(url_for('campaigns_list'))

        # Delete all campaigns and their content
        total_deleted_items = []
        campaigns_deleted = 0

        for campaign_id in campaign_ids:
            try:
                deleted_items = delete_campaign_and_content(campaign_id)
                total_deleted_items.append(deleted_items)
                campaigns_deleted += 1
            except Exception as e:
                app.logger.error(f"Error deleting campaign {campaign_id}: {str(e)}")
                continue

        if campaigns_deleted > 0:
            flash(f'Successfully deleted {campaigns_deleted} campaigns and all related content! Total removed: {"; ".join(total_deleted_items)}', 'success')
        else:
            flash('No campaigns were deleted due to errors.', 'error')

        return redirect(url_for('campaigns_list'))

    except Exception as e:
        app.logger.error(f"Bulk delete campaigns error: {str(e)}")
        flash('Error during bulk deletion. Please try again.', 'error')
        return redirect(url_for('campaigns_list'))

def get_campaign_contacts(campaign):
    """Get contacts for a campaign based on recipient criteria"""
    try:
        import json

        app.logger.info(f"Getting contacts for campaign {campaign.id}: {campaign.name}")
        app.logger.info(f"Recipient criteria: {campaign.recipient_criteria}")

        # Parse recipient criteria
        if not campaign.recipient_criteria:
            # Default to all active contacts if no criteria specified
            app.logger.info("No recipient criteria found, using all active contacts")
            contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
            app.logger.info(f"Found {len(contacts)} active contacts")
            return contacts

        criteria = json.loads(campaign.recipient_criteria)
        recipient_type = criteria.get('type', 'all')
        app.logger.info(f"Recipient type: {recipient_type}")

        if recipient_type == 'all':
            # All active contacts
            app.logger.info("Getting all active contacts")
            contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
            app.logger.info(f"Found {len(contacts)} active contacts")
            return contacts

        elif recipient_type == 'specific':
            # Specific contact IDs
            contact_ids = criteria.get('contact_ids', [])
            app.logger.info(f"Specific contact IDs: {contact_ids}")
            if not contact_ids:
                app.logger.warning("No specific contact IDs found, falling back to all active contacts")
                contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
                app.logger.info(f"Fallback: Found {len(contacts)} active contacts")
                return contacts
            contacts = Contact.query.filter(
                Contact.id.in_(contact_ids),
                Contact.is_active == True,
                Contact.do_not_email == False
            ).all()
            app.logger.info(f"Found {len(contacts)} specific contacts")
            return contacts

        elif recipient_type == 'groups':
            # Contact groups
            group_ids = criteria.get('group_ids', [])
            app.logger.info(f"Group recipient type - group_ids: {group_ids}")
            if not group_ids:
                app.logger.warning("No group IDs found, returning empty list")
                return []

            # Get contact IDs from group memberships
            contact_ids = db.session.query(ContactGroupMembership.contact_id).filter(
                ContactGroupMembership.group_id.in_(group_ids)
            ).distinct().all()
            contact_ids = [id[0] for id in contact_ids]
            app.logger.info(f"Found contact IDs in groups: {contact_ids}")

            if not contact_ids:
                app.logger.warning("No contacts found in selected groups")
                return []

            contacts = Contact.query.filter(
                Contact.id.in_(contact_ids),
                Contact.is_active == True,
                Contact.do_not_email == False
            ).all()
            app.logger.info(f"Returning {len(contacts)} contacts from groups")
            return contacts

        elif recipient_type == 'filtered':
            # Filtered contacts based on criteria
            filters = criteria.get('filters', {})
            query = Contact.query.filter_by(is_active=True, do_not_email=False)

            # Apply filters
            if filters.get('status'):
                query = query.filter(Contact.status == filters['status'])
            if filters.get('source'):
                query = query.filter(Contact.source == filters['source'])
            if filters.get('industry') and hasattr(Contact, 'industry'):
                query = query.filter(Contact.industry == filters['industry'])
            if filters.get('company_size') and hasattr(Contact, 'company_size'):
                query = query.filter(Contact.company_size == filters['company_size'])
            if filters.get('min_lead_score'):
                query = query.filter(Contact.lead_score >= filters['min_lead_score'])
            if filters.get('decision_makers_only') and hasattr(Contact, 'decision_maker'):
                query = query.filter(Contact.decision_maker == True)
            if filters.get('exclude_customers'):
                query = query.filter(Contact.is_customer == False)

            return query.all()

        else:
            # Unknown type, default to all active contacts
            app.logger.warning(f"Unknown recipient type: {recipient_type}")
            return Contact.query.filter_by(is_active=True, do_not_email=False).all()

    except Exception as e:
        app.logger.error(f"Error getting campaign contacts: {str(e)}")
        # Fallback to all active contacts
        return Contact.query.filter_by(is_active=True, do_not_email=False).all()

def delete_campaign_and_content(campaign_id):
    """Comprehensive campaign deletion with all related content - PERMANENT DELETION"""
    try:
        campaign = EmailCampaign.query.get(campaign_id)
        if not campaign:
            return "Campaign not found"

        deleted_items = []
        app.logger.info(f"Starting comprehensive deletion for campaign {campaign_id}: {campaign.name}")

        # Disable foreign key constraints temporarily for SQLite
        db.session.execute(db.text("PRAGMA foreign_keys = OFF"))

        # Helper function to safely delete from table
        def safe_delete_from_table(table_name, where_clause, params):
            try:
                result = db.session.execute(
                    db.text(f"DELETE FROM {table_name} WHERE {where_clause}"),
                    params
                )
                return result.rowcount
            except Exception as e:
                if "no such table" in str(e).lower():
                    app.logger.info(f"Table {table_name} does not exist, skipping")
                    return 0
                else:
                    app.logger.warning(f"Error deleting from {table_name}: {str(e)}")
                    return 0

        try:
            # 1. Delete all chat messages for sessions related to this campaign (if table exists)
            chat_messages_deleted = 0
            try:
                chat_messages_deleted = db.session.execute(
                    db.text("""
                        DELETE FROM chat_messages
                        WHERE session_id IN (
                            SELECT session_id FROM chatbot_sessions WHERE email_campaign_id = :campaign_id
                        )
                    """),
                    {"campaign_id": campaign_id}
                ).rowcount
            except Exception as e:
                if "no such table" not in str(e).lower():
                    app.logger.warning(f"Error deleting chat messages: {str(e)}")

            if chat_messages_deleted > 0:
                deleted_items.append(f"{chat_messages_deleted} chat messages")

            # 2. Delete all chatbot sessions for this campaign
            sessions_deleted = safe_delete_from_table(
                "chatbot_sessions", "email_campaign_id = :campaign_id", {"campaign_id": campaign_id}
            )
            if sessions_deleted > 0:
                deleted_items.append(f"{sessions_deleted} chatbot sessions")

            # 3. Delete all activities related to this campaign
            activities_deleted = safe_delete_from_table(
                "activities", "session_id = :campaign_id", {"campaign_id": str(campaign_id)}
            )
            if activities_deleted > 0:
                deleted_items.append(f"{activities_deleted} activities")

            # 4. Delete all email logs for this campaign
            email_logs_deleted = safe_delete_from_table(
                "email_logs", "campaign_id = :campaign_id", {"campaign_id": campaign_id}
            )
            if email_logs_deleted > 0:
                deleted_items.append(f"{email_logs_deleted} email logs")

            # 5. Delete all email failures for this campaign
            email_failures_deleted = safe_delete_from_table(
                "email_failures", "campaign_id = :campaign_id", {"campaign_id": campaign_id}
            )
            if email_failures_deleted > 0:
                deleted_items.append(f"{email_failures_deleted} email failures")

            # 6. Delete all email events for this campaign
            email_events_deleted = safe_delete_from_table(
                "email_events", "campaign_id = :campaign_id", {"campaign_id": campaign_id}
            )
            if email_events_deleted > 0:
                deleted_items.append(f"{email_events_deleted} email events")

            # 7. Delete all campaign group associations
            campaign_groups_deleted = safe_delete_from_table(
                "campaign_groups", "campaign_id = :campaign_id", {"campaign_id": campaign_id}
            )
            if campaign_groups_deleted > 0:
                deleted_items.append(f"{campaign_groups_deleted} campaign group associations")

        except Exception as e:
            app.logger.error(f"Error deleting campaign related data: {str(e)}")
            raise
        finally:
            # Re-enable foreign key constraints
            db.session.execute(db.text("PRAGMA foreign_keys = ON"))

        # 7. Reset campaign-related fields in contacts (don't delete contacts, just unlink)
        campaign_contacts = Contact.query.filter_by(email_campaign_id=campaign_id).all()
        contacts_updated = 0
        for contact in campaign_contacts:
            # Reset all campaign-related tracking
            contact.email_campaign_id = None
            contact.first_email_sent = None
            contact.email_opened = None
            contact.chatbot_link_clicked = None
            contact.chatbot_conversation_started = None
            contact.conversion_completed = None
            contact.chatbot_session_id = None
            contact.current_sales_stage = 'new'
            contact.stage_history = []
            contacts_updated += 1

        if contacts_updated > 0:
            deleted_items.append(f"{contacts_updated} contacts unlinked")

        # 4. Delete the campaign itself
        db.session.delete(campaign)
        deleted_items.append("1 campaign")

        # 5. Commit all changes
        db.session.commit()

        return ", ".join(deleted_items)

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error in delete_campaign_and_content: {str(e)}")
        raise e

@app.route('/campaigns/<int:campaign_id>/duplicate', methods=['POST'])
def duplicate_campaign(campaign_id):
    """Duplicate campaign"""
    try:
        original_campaign = EmailCampaign.query.get_or_404(campaign_id)

        # Create new campaign with copied data
        new_campaign = EmailCampaign(
            name=f"{original_campaign.name} (Copy)",
            subject=original_campaign.subject,
            template_name=original_campaign.template_name,
            status='draft'
        )

        db.session.add(new_campaign)
        db.session.commit()

        flash('Campaign duplicated successfully!', 'success')
        return redirect(url_for('edit_campaign', campaign_id=new_campaign.id))

    except Exception as e:
        app.logger.error(f"Duplicate campaign error: {str(e)}")
        flash('Error duplicating campaign.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/<int:campaign_id>/send', methods=['POST'])
def send_campaign(campaign_id):
    """Send campaign with chatbot links and tracking - respects daily limits"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        if campaign.status not in ['draft', 'paused']:
            flash('Campaign can only be sent from draft or paused status.', 'error')
            return redirect(url_for('campaigns_list'))

        # Get contacts to send to based on recipient criteria
        if campaign.status == 'paused':
            # Resume sending - get contacts that haven't been sent to yet
            sent_contact_ids = db.session.query(EmailLog.contact_id).filter_by(
                campaign_id=campaign_id
            ).filter(EmailLog.status.in_(['sent', 'delivered', 'opened', 'clicked'])).distinct()

            # Get eligible contacts based on original criteria, excluding already sent
            eligible_contacts = get_campaign_contacts(campaign)
            contacts = [c for c in eligible_contacts if c.id not in [id[0] for id in sent_contact_ids]]
        else:
            # New campaign - get contacts based on recipient criteria
            contacts = get_campaign_contacts(campaign)

        if not contacts:
            flash('No contacts available for campaign.', 'warning')
            return redirect(url_for('campaigns_list'))

        # Check daily sending limits
        if not campaign.can_send_today():
            flash(f'Daily sending limit of {campaign.daily_send_limit} emails has been reached. Campaign will resume tomorrow.', 'warning')
            campaign.status = 'paused'
            campaign.batch_status = 'paused'
            db.session.commit()
            return redirect(url_for('campaigns_list'))

        # Get batch processing options from form
        batch_mode = request.form.get('batch_mode', 'immediate')
        batch_size = int(request.form.get('batch_size', campaign.batch_size or 50))

        # Update campaign batch settings
        campaign.batch_size = batch_size

        # Calculate how many emails we can send today
        remaining_sends = campaign.get_remaining_sends_today()
        contacts_to_send = contacts[:remaining_sends]

        # Update campaign status
        if campaign.status == 'draft':
            campaign.status = 'sending'
            campaign.started_at = datetime.utcnow()
            campaign.total_recipients = len(contacts)
            campaign.batch_status = 'in_progress'

            # Calculate estimated completion
            estimated_days = campaign.calculate_estimated_days(len(contacts))
            if estimated_days > 1:
                flash(f'Campaign will be sent over {estimated_days} days due to daily limit of {campaign.daily_send_limit} emails.', 'info')
        else:
            campaign.status = 'sending'

        # Send actual emails to contacts (limited by daily quota)
        emails_sent = 0
        emails_failed = 0

        for contact in contacts_to_send:
            # Generate unique session ID for tracking
            session_id = str(uuid.uuid4())

            # Send actual email
            success, message = send_campaign_email(contact, campaign, session_id)

            if success:
                emails_sent += 1

                # Update contact with tracking info
                contact.email_campaign_id = campaign.id
                contact.first_email_sent = datetime.utcnow()
                contact.current_sales_stage = 'email_sent'
                contact.chatbot_session_id = session_id
                contact.add_stage_progression('email_sent')

                # Increment daily counter
                campaign.increment_daily_send_count()

                # Create EmailLog record with message ID for tracking
                email_log = EmailLog(
                    campaign_id=campaign.id,
                    contact_id=contact.id,
                    recipient_email=contact.email,
                    recipient_name=contact.full_name,
                    subject=campaign.subject,
                    status='sent',
                    sent_at=datetime.utcnow(),
                    message_id=session_id  # Use session_id as message_id for tracking
                )
                db.session.add(email_log)

                # Create activity log
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='email_sent',
                    subject=f'Email campaign sent: {campaign.name}',
                    description=f'Email sent successfully with chatbot link: http://localhost:5000/chat/{session_id}',
                    extra_data=json.dumps({
                        'campaign_id': campaign.id,
                        'template': campaign.template_name,
                        'chatbot_link': f'http://localhost:5000/chat/{session_id}',
                        'email_status': 'sent'
                    })
                )
                db.session.add(activity)

            else:
                emails_failed += 1
                app.logger.error(f"Failed to send email to {contact.email}: {message}")

                # Create email failure record for retry management
                email_failure = EmailFailure(
                    campaign_id=campaign.id,
                    contact_id=contact.id,
                    recipient_email=contact.email,
                    error_message=message,
                    error_type=classify_email_error(message)
                )
                db.session.add(email_failure)

                # Log failed email activity
                activity = Activity(
                    contact_id=contact.id,
                    activity_type='email_failed',
                    subject=f'Email sending failed: {campaign.name}',
                    description=f'Failed to send email: {message}',
                    extra_data=json.dumps({
                        'campaign_id': campaign.id,
                        'error': message,
                        'error_type': classify_email_error(message)
                    })
                )
                db.session.add(activity)

        # Update campaign with final results
        campaign.emails_sent += emails_sent
        campaign.emails_delivered += emails_sent  # Assume delivered if sent successfully
        campaign.emails_failed += emails_failed

        # Check if there are more contacts to send to
        remaining_contacts = len(contacts) - len(contacts_to_send)

        # Determine campaign status
        if remaining_contacts > 0:
            # More contacts to send - pause until tomorrow
            campaign.status = 'paused'
            campaign.batch_status = 'paused'
            from datetime import date, timedelta
            campaign.next_batch_date = date.today() + timedelta(days=1)

            flash(f'Daily limit reached! {emails_sent} emails sent today. {remaining_contacts} contacts remaining. Campaign will resume tomorrow.', 'info')
        else:
            # All contacts processed
            if campaign.emails_sent > 0 and campaign.emails_failed == 0:
                campaign.status = 'completed'
                campaign.batch_status = 'completed'
            elif campaign.emails_sent > 0 and campaign.emails_failed > 0:
                campaign.status = 'partial_failure'
                campaign.batch_status = 'completed'
            else:
                campaign.status = 'failed'
                campaign.batch_status = 'completed'

            campaign.completed_at = datetime.utcnow()

            if campaign.emails_sent > 0:
                flash(f'Campaign completed! {campaign.emails_sent} total emails sent with chatbot links. {campaign.emails_failed} failed. Track progress in Analytics.', 'success')
            else:
                flash(f'Campaign failed! No emails were sent. Please check SMTP configuration.', 'error')

        db.session.commit()
        return redirect(url_for('campaigns_list'))

    except Exception as e:
        app.logger.error(f"Send campaign error: {str(e)}")
        flash('Error sending campaign. Please try again.', 'error')
        return redirect(url_for('campaigns_list'))

# Large-scale email management functions
def send_campaign_batch_immediate(campaign, contacts, batch_size):
    """Send campaign emails immediately in batches for large volumes"""
    import time

    sent_count = 0
    failed_count = 0
    batch_number = 0

    try:
        for i in range(0, len(contacts), batch_size):
            batch_number += 1
            batch_contacts = contacts[i:i + batch_size]

            # Check daily limit before each batch
            remaining_sends = campaign.get_remaining_sends_today()
            if remaining_sends <= 0:
                app.logger.info(f"Daily limit reached. Stopping at batch {batch_number}")
                break

            # Limit batch size to remaining daily sends
            actual_batch_size = min(len(batch_contacts), remaining_sends)
            batch_contacts = batch_contacts[:actual_batch_size]

            app.logger.info(f"Sending batch {batch_number}/{campaign.total_batches_planned} ({len(batch_contacts)} emails)")

            # Send batch
            batch_sent, batch_failed = send_email_batch(campaign, batch_contacts)
            sent_count += batch_sent
            failed_count += batch_failed

            # Update campaign progress
            campaign.batches_completed = batch_number
            campaign.emails_sent += batch_sent
            campaign.emails_failed += batch_failed

            db.session.commit()

            # Delay between batches (except for last batch)
            if i + batch_size < len(contacts) and campaign.batch_delay_minutes > 0:
                app.logger.info(f"Waiting {campaign.batch_delay_minutes} minutes before next batch...")
                time.sleep(campaign.batch_delay_minutes * 60)

        # Update final campaign status
        if sent_count == len(contacts):
            campaign.status = 'completed'
            campaign.batch_status = 'completed'
            campaign.completed_at = datetime.utcnow()
        elif sent_count > 0:
            campaign.status = 'partially_sent'
            campaign.batch_status = 'paused'
        else:
            campaign.status = 'failed'
            campaign.batch_status = 'failed'

        db.session.commit()

        return {
            'message': f'Campaign completed! {sent_count} emails sent, {failed_count} failed across {batch_number} batches.',
            'type': 'success' if sent_count > 0 else 'error'
        }

    except Exception as e:
        app.logger.error(f"Batch sending error: {str(e)}")
        return {
            'message': f'Batch sending error: {str(e)}',
            'type': 'error'
        }

def send_email_batch(campaign, contacts):
    """Send a batch of emails with failure tracking"""
    sent_count = 0
    failed_count = 0

    for contact in contacts:
        try:
            # Generate unique session ID for tracking
            session_id = str(uuid.uuid4())

            # Update contact with campaign info
            contact.email_campaign_id = campaign.id
            contact.chatbot_session_id = session_id
            contact.first_email_sent = datetime.utcnow()
            contact.current_sales_stage = 'email_sent'

            # Send email
            success, message = send_campaign_email(contact, campaign, session_id)

            if success:
                sent_count += 1
                campaign.increment_daily_send_count()

                # Create EmailLog record
                email_log = EmailLog(
                    campaign_id=campaign.id,
                    contact_id=contact.id,
                    recipient_email=contact.email,
                    recipient_name=contact.full_name,
                    subject=campaign.subject,
                    status='sent',
                    sent_at=datetime.utcnow(),
                    message_id=session_id
                )
                db.session.add(email_log)

                # Create activity log
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='email_sent',
                    subject=f'Campaign email sent: {campaign.name}',
                    description=f'Email sent to {contact.email} for campaign "{campaign.name}"',
                    extra_data=json.dumps({
                        'campaign_id': campaign.id,
                        'template': campaign.template_name,
                        'session_id': session_id,
                        'batch_number': campaign.batches_completed + 1
                    })
                )
                db.session.add(activity)

            else:
                failed_count += 1
                create_email_failure(campaign, contact, message or "Failed to send email", "smtp_error")

        except Exception as e:
            failed_count += 1
            app.logger.error(f"Error sending email to {contact.email}: {str(e)}")
            create_email_failure(campaign, contact, str(e), "processing_error")

    return sent_count, failed_count

def send_campaign_email(contact, campaign, session_id):
    """Send individual campaign email with chatbot link using centralized template manager"""
    try:
        app.logger.info(f"Attempting to send email to {contact.email} for campaign {campaign.name}")
        app.logger.info(f"Session ID: {session_id}")
        app.logger.info(f"Global email_system available: {email_system is not None}")

        # Generate email content
        chatbot_link = f"http://localhost:5000/chat/{session_id}"

        # Use centralized template manager instead of hardcoded templates
        if email_system and 'template_manager' in email_system:
            template_manager = email_system['template_manager']
        else:
            # Fallback: create template manager if not available
            from email_system.email_templates import EmailTemplateManager
            template_manager = EmailTemplateManager()

        # Prepare template context
        template_context = {
            'contact_name': contact.first_name or 'there',
            'company_name': contact.company or 'your company',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': chatbot_link,
            'session_id': session_id
        }

        # Determine template name from campaign or use default
        template_name = getattr(campaign, 'template_name', 'customer_support')
        if template_name not in ['customer_support', 'introduction', 'followup']:
            template_name = 'customer_support'  # Default fallback

        # Render email using centralized template
        try:
            rendered_email = template_manager.render_template(template_name, template_context)
            subject = rendered_email['subject']
            html_body = rendered_email['html_body']
            text_body = rendered_email['text_body']

            app.logger.info(f"✅ Using centralized template '{template_name}' for {contact.email}")
        except Exception as template_error:
            app.logger.error(f"❌ Template rendering failed: {template_error}")
            # Fallback to simple email if template fails
            subject = campaign.subject or f"Customer Support Solutions for {contact.first_name}"
            html_body = f"<p>Hi {contact.first_name},</p><p>Please visit our chat: <a href='{chatbot_link}'>Start Chat</a></p>"
            text_body = f"Hi {contact.first_name},\n\nPlease visit our chat: {chatbot_link}"


        # Send email using Enhanced SMTP Service
        try:
            # Use the global email system if available, otherwise create a new instance
            if email_system and 'smtp_service' in email_system:
                smtp_service = email_system['smtp_service']
            else:
                from email_system.enhanced_smtp_service import EnhancedSMTPService
                from email_system.config import get_email_config
                smtp_service = EnhancedSMTPService(get_email_config())

            # Send email using enhanced SMTP service
            success, message_id, error_msg = smtp_service.send_email(
                to_email=contact.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                from_name="24Seven Assistants Sales Team",
                from_email=None  # Will use default from config
            )

            if success:
                app.logger.info(f"Email sent successfully to {contact.email} for campaign {campaign.name}. Message ID: {message_id}")
                return True, f"Email sent successfully (Message ID: {message_id})"
            else:
                app.logger.error(f"Failed to send email to {contact.email}: {error_msg}")
                return False, error_msg

        except Exception as smtp_error:
            app.logger.error(f"SMTP service error: {str(smtp_error)}")
            return False, f"SMTP service error: {str(smtp_error)}"

    except Exception as e:
        error_msg = f"Failed to send email to {contact.email}: {str(e)}"
        app.logger.error(error_msg)
        return False, error_msg

def classify_email_error(error_message):
    """Classify email error type for better handling"""
    if not error_message:
        return "unknown"

    error_lower = error_message.lower()

    if "smtp" in error_lower or "connection" in error_lower:
        return "smtp_error"
    elif "authentication" in error_lower or "login" in error_lower:
        return "auth_error"
    elif "invalid" in error_lower and "email" in error_lower:
        return "invalid_email"
    elif "timeout" in error_lower:
        return "timeout_error"
    elif "refused" in error_lower or "rejected" in error_lower:
        return "rejected"
    else:
        return "unknown"

def test_smtp_connection():
    """Test SMTP connection and configuration using Enhanced SMTP Service"""
    try:
        # Use the global email system if available, otherwise create a new instance
        if email_system and 'smtp_service' in email_system:
            smtp_service = email_system['smtp_service']
        else:
            from email_system.enhanced_smtp_service import EnhancedSMTPService
            from email_system.config import get_email_config
            smtp_service = EnhancedSMTPService(get_email_config())

        # Test connection first
        success, message = smtp_service.test_connection()
        if not success:
            return False, f"SMTP connection test failed: {message}"

        # Send test email
        success, message_id, error_msg = smtp_service.send_email(
            to_email=smtp_service.config['MAIL_DEFAULT_SENDER'],  # Send to self
            subject="SMTP Test - 24Seven Assistants Enhanced System",
            html_body="<h1>SMTP Test</h1><p>This is a test email to verify the enhanced SMTP configuration is working correctly.</p>",
            text_body="SMTP Test\n\nThis is a test email to verify the enhanced SMTP configuration is working correctly.",
            from_name="24Seven Assistants Test System"
        )

        if success:
            return True, f"Enhanced SMTP test successful! Email sent with Message ID: {message_id}"
        else:
            return False, f"Enhanced SMTP test failed: {error_msg}"

    except Exception as e:
        return False, f"Enhanced SMTP test failed: {str(e)}"

def create_email_failure(campaign, contact, error_message, error_type):
    """Create an email failure record for easy management"""
    failure = EmailFailure(
        campaign_id=campaign.id,
        contact_id=contact.id,
        recipient_email=contact.email,
        error_message=error_message,
        error_type=error_type
    )
    db.session.add(failure)

def schedule_campaign_batches(campaign, contacts, batch_size):
    """Schedule campaign batches for future sending"""
    from datetime import date, timedelta

    # This would integrate with a task scheduler like Celery
    # For now, we'll set up the campaign for manual batch processing

    campaign.batch_status = 'scheduled'
    campaign.next_batch_date = date.today()

    db.session.commit()

    return {
        'message': f'Campaign scheduled for batch sending. {len(contacts)} emails will be sent in batches of {batch_size}.',
        'type': 'info'
    }

@app.route('/campaigns/<int:campaign_id>/resume', methods=['POST'])
def resume_campaign(campaign_id):
    """Resume a paused campaign"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        if campaign.status != 'paused':
            flash('Only paused campaigns can be resumed.', 'error')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        # Check if we can send today
        if not campaign.can_send_today():
            flash(f'Daily sending limit of {campaign.daily_send_limit} emails has been reached. Campaign will resume tomorrow.', 'warning')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        # Resume the campaign by calling send_campaign
        return send_campaign(campaign_id)

    except Exception as e:
        app.logger.error(f"Resume campaign error: {str(e)}")
        flash('Error resuming campaign.', 'error')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

@app.route('/campaigns/<int:campaign_id>/pause', methods=['POST'])
def pause_campaign(campaign_id):
    """Pause a running campaign"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        if campaign.status != 'sending':
            flash('Only running campaigns can be paused.', 'error')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        campaign.status = 'paused'
        campaign.batch_status = 'paused'
        db.session.commit()

        flash('Campaign paused successfully.', 'success')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

    except Exception as e:
        app.logger.error(f"Pause campaign error: {str(e)}")
        flash('Error pausing campaign.', 'error')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

@app.route('/campaigns/<int:campaign_id>/retry', methods=['POST'])
def retry_campaign(campaign_id):
    """Retry failed emails in a campaign"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)
        retry_type = request.form.get('retry_type', 'failed_only')  # 'failed_only' or 'all'

        # Check if campaign can be retried
        if campaign.retry_count >= campaign.max_retries:
            flash(f'Campaign has reached maximum retry limit ({campaign.max_retries}).', 'error')
            return redirect(url_for('campaigns_list'))

        # Get contacts to retry
        if retry_type == 'failed_only':
            # Get contacts with failed emails
            failed_failures = EmailFailure.query.filter_by(
                campaign_id=campaign_id,
                resolved=False
            ).all()

            if not failed_failures:
                flash('No failed emails to retry.', 'warning')
                return redirect(url_for('campaigns_list'))

            contacts_to_retry = [failure.contact for failure in failed_failures]
        else:
            # Retry all contacts
            contacts_to_retry = Contact.query.filter_by(is_active=True, do_not_email=False).all()

        # Update campaign retry info
        campaign.retry_count += 1
        campaign.last_retry_at = datetime.utcnow()
        campaign.status = 'sending'

        # Send emails to retry contacts
        emails_sent = 0
        emails_failed = 0

        for contact in contacts_to_retry:
            # Generate new session ID for tracking
            session_id = str(uuid.uuid4())

            # Send email
            success, message = send_campaign_email(contact, campaign, session_id)

            if success:
                emails_sent += 1

                # Update contact tracking
                contact.email_campaign_id = campaign.id
                contact.first_email_sent = datetime.utcnow()
                contact.current_sales_stage = 'email_sent'
                contact.chatbot_session_id = session_id
                contact.add_stage_progression('email_sent')

                # Mark previous failures as resolved if this was a retry
                if retry_type == 'failed_only':
                    previous_failures = EmailFailure.query.filter_by(
                        campaign_id=campaign_id,
                        contact_id=contact.id,
                        resolved=False
                    ).all()
                    for failure in previous_failures:
                        failure.resolved = True
                        failure.resolved_at = datetime.utcnow()

                # Create success activity
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='email_sent',
                    subject=f'Email retry successful: {campaign.name}',
                    description=f'Email retry sent successfully (attempt {campaign.retry_count})',
                    extra_data=json.dumps({
                        'campaign_id': campaign.id,
                        'retry_attempt': campaign.retry_count,
                        'chatbot_link': f'http://localhost:5000/chat/{session_id}'
                    })
                )
                db.session.add(activity)

            else:
                emails_failed += 1

                # Update existing failure or create new one
                existing_failure = EmailFailure.query.filter_by(
                    campaign_id=campaign_id,
                    contact_id=contact.id,
                    resolved=False
                ).first()

                if existing_failure:
                    existing_failure.retry_count += 1
                    existing_failure.last_retry_at = datetime.utcnow()
                    existing_failure.error_message = message
                    existing_failure.error_type = classify_email_error(message)
                else:
                    # Create new failure record
                    email_failure = EmailFailure(
                        campaign_id=campaign.id,
                        contact_id=contact.id,
                        recipient_email=contact.email,
                        error_message=message,
                        error_type=classify_email_error(message),
                        retry_count=1
                    )
                    db.session.add(email_failure)

                # Create failure activity
                activity = Activity(
                    contact_id=contact.id,
                    activity_type='email_failed',
                    subject=f'Email retry failed: {campaign.name}',
                    description=f'Email retry failed (attempt {campaign.retry_count}): {message}',
                    extra_data=json.dumps({
                        'campaign_id': campaign.id,
                        'retry_attempt': campaign.retry_count,
                        'error': message,
                        'error_type': classify_email_error(message)
                    })
                )
                db.session.add(activity)

        # Update campaign statistics
        campaign.emails_sent += emails_sent
        campaign.emails_delivered += emails_sent
        campaign.emails_failed += emails_failed

        # Update campaign status
        if emails_sent > 0 and emails_failed == 0:
            campaign.status = 'completed'
        elif emails_sent > 0 and emails_failed > 0:
            campaign.status = 'partial_failure'
        else:
            campaign.status = 'failed'

        campaign.completed_at = datetime.utcnow()

        db.session.commit()

        if emails_sent > 0:
            flash(f'Campaign retry successful! {emails_sent} emails sent, {emails_failed} failed. (Attempt {campaign.retry_count}/{campaign.max_retries})', 'success')
        else:
            flash(f'Campaign retry failed! No emails were sent. (Attempt {campaign.retry_count}/{campaign.max_retries})', 'error')

        return redirect(url_for('campaigns_list'))

    except Exception as e:
        app.logger.error(f"Retry campaign error: {str(e)}")
        flash('Error retrying campaign. Please try again.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/<int:campaign_id>/batch-send', methods=['POST'])
def batch_send_campaign(campaign_id):
    """Send campaign in batches for large volumes"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        # Get batch parameters
        batch_size = int(request.form.get('batch_size', 100))
        batch_delay = int(request.form.get('batch_delay', 5))
        send_mode = request.form.get('send_mode', 'immediate')  # immediate or scheduled

        # Update campaign settings
        campaign.batch_size = batch_size
        campaign.batch_delay_minutes = batch_delay

        # Get contacts for campaign
        contacts = get_campaign_contacts(campaign)

        if not contacts:
            flash('No contacts found for this campaign.', 'error')
            return redirect(url_for('view_campaign', campaign_id=campaign_id))

        # Calculate batches
        total_batches = (len(contacts) + batch_size - 1) // batch_size
        campaign.total_batches_planned = total_batches
        campaign.total_recipients = len(contacts)

        if send_mode == 'immediate':
            # Send immediately in batches
            result = send_campaign_batch_immediate(campaign, contacts, batch_size)
            flash(result['message'], result['type'])
        else:
            # Schedule for later
            result = schedule_campaign_batches(campaign, contacts, batch_size)
            flash(result['message'], result['type'])

        return redirect(url_for('view_campaign', campaign_id=campaign_id))

    except Exception as e:
        app.logger.error(f"Batch send error: {str(e)}")
        flash('Error starting batch send. Please try again.', 'error')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

@app.route('/campaigns/<int:campaign_id>/failures')
def view_campaign_failures(campaign_id):
    """View detailed failure information for a campaign"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        # Get all failures for this campaign
        failures = EmailFailure.query.filter_by(campaign_id=campaign_id).order_by(
            EmailFailure.created_at.desc()
        ).all()

        # Group failures by error type
        failure_stats = {}
        for failure in failures:
            error_type = failure.error_type or 'unknown'
            if error_type not in failure_stats:
                failure_stats[error_type] = {
                    'count': 0,
                    'resolved': 0,
                    'unresolved': 0
                }
            failure_stats[error_type]['count'] += 1
            if failure.resolved:
                failure_stats[error_type]['resolved'] += 1
            else:
                failure_stats[error_type]['unresolved'] += 1

        return render_template('campaign_failures.html',
                             campaign=campaign,
                             failures=failures,
                             failure_stats=failure_stats)

    except Exception as e:
        app.logger.error(f"View campaign failures error: {str(e)}")
        flash('Error loading campaign failures.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/campaigns/<int:campaign_id>/skip-failed', methods=['POST'])
def skip_failed_emails(campaign_id):
    """Mark failed emails as resolved without retrying"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)

        # Get unresolved failures
        unresolved_failures = EmailFailure.query.filter_by(
            campaign_id=campaign_id,
            resolved=False
        ).all()

        if not unresolved_failures:
            flash('No unresolved failures to skip.', 'warning')
            return redirect(url_for('campaigns_list'))

        # Mark all as resolved
        for failure in unresolved_failures:
            failure.resolved = True
            failure.resolved_at = datetime.utcnow()

        # Update campaign status if it was failed
        if campaign.status in ['failed', 'partial_failure']:
            if campaign.emails_sent > 0:
                campaign.status = 'completed'

        db.session.commit()

        flash(f'Skipped {len(unresolved_failures)} failed emails. Campaign marked as completed.', 'success')
        return redirect(url_for('campaigns_list'))

    except Exception as e:
        app.logger.error(f"Skip failed emails error: {str(e)}")
        flash('Error skipping failed emails.', 'error')
        return redirect(url_for('campaigns_list'))

@app.route('/test-smtp', methods=['GET', 'POST'])
def test_smtp_route():
    """Test SMTP configuration via web interface"""
    if request.method == 'POST':
        try:
            success, message = test_smtp_connection()
            if success:
                flash(f'✅ {message}', 'success')
            else:
                flash(f'❌ {message}', 'error')
        except Exception as e:
            flash(f'❌ SMTP test error: {str(e)}', 'error')

        return redirect(url_for('test_smtp_route'))

    # GET request - show test page
    from flask import render_template_string
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>SMTP Test - 24Seven Assistants</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .btn { background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            .btn:hover { background: #5a6fd8; }
            .alert { padding: 15px; margin: 20px 0; border-radius: 5px; }
            .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        </style>
    </head>
    <body>
        <h1>🧪 SMTP Configuration Test</h1>
        <p>Test the enhanced email system configuration and connectivity.</p>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <button type="submit" class="btn">🔍 Test SMTP Connection</button>
        </form>

        <h3>📧 Current Configuration</h3>
        <p><strong>Server:</strong> smtp.gmail.com:587</p>
        <p><strong>Username:</strong> <EMAIL></p>
        <p><strong>TLS:</strong> Enabled</p>
        <p><strong>IMAP Saving:</strong> Disabled (for stability)</p>

        <p><a href="{{ url_for('campaigns_list') }}">← Back to Campaigns</a></p>
    </body>
    </html>
    ''')

@app.route('/campaigns/<int:campaign_id>/bulk-manage-failures', methods=['POST'])
def bulk_manage_failures(campaign_id):
    """Bulk manage email failures - retry, skip, or delete"""
    try:
        campaign = EmailCampaign.query.get_or_404(campaign_id)
        action = request.form.get('action')  # retry, skip, delete
        failure_ids = request.form.getlist('failure_ids')

        if not failure_ids:
            flash('No failures selected.', 'warning')
            return redirect(url_for('view_campaign_failures', campaign_id=campaign_id))

        # Convert to integers
        failure_ids = [int(id) for id in failure_ids if id.isdigit()]

        if action == 'retry':
            # Retry selected failures
            failures = EmailFailure.query.filter(
                EmailFailure.id.in_(failure_ids),
                EmailFailure.campaign_id == campaign_id,
                EmailFailure.resolved == False
            ).all()

            retried_count = 0
            for failure in failures:
                try:
                    # Generate new session ID
                    session_id = str(uuid.uuid4())

                    # Retry sending email
                    success, message = send_campaign_email(failure.contact, campaign, session_id)

                    if success:
                        # Mark failure as resolved
                        failure.resolved = True
                        failure.resolved_at = datetime.utcnow()
                        retried_count += 1

                        # Create new EmailLog
                        email_log = EmailLog(
                            campaign_id=campaign.id,
                            contact_id=failure.contact_id,
                            recipient_email=failure.recipient_email,
                            recipient_name=failure.contact.full_name,
                            subject=campaign.subject,
                            status='sent',
                            sent_at=datetime.utcnow(),
                            message_id=session_id
                        )
                        db.session.add(email_log)
                    else:
                        # Update failure with new error
                        failure.retry_count += 1
                        failure.last_retry_at = datetime.utcnow()
                        failure.error_message = message

                except Exception as e:
                    app.logger.error(f"Error retrying failure {failure.id}: {str(e)}")
                    continue

            db.session.commit()
            flash(f'Retried {retried_count} failed emails successfully.', 'success')

        elif action == 'skip':
            # Mark selected failures as resolved
            failures = EmailFailure.query.filter(
                EmailFailure.id.in_(failure_ids),
                EmailFailure.campaign_id == campaign_id
            ).all()

            for failure in failures:
                failure.resolved = True
                failure.resolved_at = datetime.utcnow()

            db.session.commit()
            flash(f'Skipped {len(failures)} failed emails.', 'success')

        elif action == 'delete':
            # Delete selected failures
            deleted_count = EmailFailure.query.filter(
                EmailFailure.id.in_(failure_ids),
                EmailFailure.campaign_id == campaign_id
            ).delete(synchronize_session=False)

            db.session.commit()
            flash(f'Deleted {deleted_count} failure records.', 'success')

        return redirect(url_for('view_campaign_failures', campaign_id=campaign_id))

    except Exception as e:
        app.logger.error(f"Bulk manage failures error: {str(e)}")
        flash('Error managing failures. Please try again.', 'error')
        return redirect(url_for('view_campaign_failures', campaign_id=campaign_id))

@app.route('/track/open/<session_id>', endpoint='track_email_open_legacy')
def track_email_open_legacy(session_id):
    """Track email open via tracking pixel"""
    try:
        # Track the email open
        contact = Contact.query.filter_by(chatbot_session_id=session_id).first()
        if contact:
            # Only track first open
            if not contact.email_opened:
                contact.email_opened = datetime.utcnow()
                contact.current_sales_stage = 'email_opened'
                contact.add_stage_progression('email_opened')

                # Update EmailLog record
                email_log = EmailLog.query.filter_by(
                    message_id=session_id,
                    contact_id=contact.id
                ).first()
                if email_log and not email_log.opened_at:
                    email_log.opened_at = datetime.utcnow()
                    email_log.status = 'opened'
                    email_log.open_count = 1

                # Update campaign metrics
                if contact.email_campaign:
                    contact.email_campaign.emails_opened += 1

                # Create activity log
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='email_opened',
                    subject='Email opened',
                    description=f'Email opened by {contact.email}',
                    extra_data=json.dumps({
                        'campaign_id': contact.email_campaign_id,
                        'opened_at': datetime.utcnow().isoformat()
                    })
                )
                db.session.add(activity)

                db.session.commit()
                app.logger.info(f"Email open tracked for contact {contact.email} (session: {session_id})")

        # Return 1x1 transparent pixel
        from flask import Response
        import base64

        # 1x1 transparent PNG pixel
        pixel_data = base64.b64decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')

        response = Response(pixel_data, mimetype='image/png')
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response

    except Exception as e:
        app.logger.error(f"Email open tracking error: {str(e)}")
        # Still return pixel even if tracking fails
        from flask import Response
        import base64
        pixel_data = base64.b64decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')
        return Response(pixel_data, mimetype='image/png')

@app.route('/chat/<session_id>')
def chat_page(session_id):
    """Serve the Flask chat UI page"""
    return render_template('chat_public.html', session_id=session_id)

# Backwards-compat entry route from email campaigns (kept same endpoint)
@app.route('/chatbot/<session_id>')
@app.route('/chat/<session_id>/legacy')
def chatbot_entry(session_id):
    """Entry point from email campaigns; now redirects to Flask chat UI"""
    try:
        # Track the click
        contact = Contact.query.filter_by(chatbot_session_id=session_id).first()
        if contact:
            contact.chatbot_link_clicked = datetime.utcnow()
            contact.current_sales_stage = 'link_clicked'
            contact.add_stage_progression('link_clicked')

            # Update campaign metrics
            if contact.email_campaign:
                contact.email_campaign.chatbot_links_clicked += 1

            db.session.commit()

        # Redirect to new Flask chat UI
        return redirect(url_for('chat_page', session_id=session_id))

    except Exception as e:
        app.logger.error(f"Chatbot entry error: {str(e)}")
        return redirect(url_for('chat_page', session_id=session_id))

@app.route('/api/chatbot/session', methods=['POST'])
def track_chatbot_session():
    """API to track chatbot session progression"""
    try:
        data = request.json
        session_id = data.get('session_id')
        stage = data.get('stage')
        task = data.get('task')
        contact_name = data.get('contact_name')
        contact_email = data.get('contact_email')

        # Find or create contact
        contact = None

        # First, try to find by session_id (from email campaigns)
        if session_id:
            contact = Contact.query.filter_by(chatbot_session_id=session_id).first()

        # If not found by session_id, try by email
        if not contact and contact_email:
            contact = Contact.query.filter_by(email=contact_email).first()
            # If found by email, update their session_id
            if contact:
                contact.chatbot_session_id = session_id

        # If still no contact and we have name/email, create new one
        if not contact and contact_name and contact_email:
            contact = Contact(
                first_name=contact_name.split()[0],
                last_name=' '.join(contact_name.split()[1:]) if len(contact_name.split()) > 1 else '',
                email=contact_email,
                source='chatbot_direct',
                status='chatbot_active',
                chatbot_session_id=session_id,
                chatbot_conversation_started=datetime.utcnow(),
                current_sales_stage=stage
            )
            db.session.add(contact)

        # If we have a contact but no session_id, and this is a new session, update it
        elif contact and not contact.chatbot_session_id and session_id:
            contact.chatbot_session_id = session_id

        if contact:
            # Update contact stage progression
            if stage != contact.current_sales_stage:
                contact.add_stage_progression(stage)
                contact.current_sales_stage = stage

            # Find or create chatbot session
            chatbot_session = ChatbotSession.query.filter_by(session_id=session_id).first()
            if not chatbot_session:
                chatbot_session = ChatbotSession(
                    session_id=session_id,
                    contact_id=contact.id,
                    email_campaign_id=contact.email_campaign_id,
                    current_stage=stage,
                    current_task=task
                )
                db.session.add(chatbot_session)

                # Mark conversation as started
                if not contact.chatbot_conversation_started:
                    contact.chatbot_conversation_started = datetime.utcnow()

                # Update campaign metrics
                if contact.email_campaign:
                    contact.email_campaign.chatbot_conversations_started += 1
            else:
                chatbot_session.current_stage = stage
                chatbot_session.current_task = task
                chatbot_session.total_messages += 1

            # Log activity
            activity = Activity(
                contact_id=contact.id,
                session_id=session_id,
                activity_type='stage_progression',
                subject=f'Moved to {stage} stage',
                description=f'Task: {task}',
                extra_data=json.dumps(data)
            )
            db.session.add(activity)

            # Check for conversion
            if stage == 'close' and data.get('conversion_completed'):
                contact.conversion_completed = datetime.utcnow()
                contact.current_sales_stage = 'converted'
                contact.add_stage_progression('converted')
                chatbot_session.conversion_achieved = True
                chatbot_session.completed_successfully = True

                if contact.email_campaign:
                    contact.email_campaign.conversions_achieved += 1

        db.session.commit()
        return jsonify({'success': True})

    except Exception as e:
        app.logger.error(f"Chatbot session tracking error: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/test-smtp', methods=['POST'])
def test_smtp():
    """Test SMTP configuration"""
    try:
        success, message = test_smtp_connection()
        return jsonify({'success': success, 'message': message})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/contacts/by-groups', methods=['POST'])
def api_contacts_by_groups():
    """Get contacts by group IDs"""
    try:
        data = request.get_json() or {}
        group_ids = data.get('group_ids', [])

        if not group_ids:
            return jsonify({'contacts': []})

        # Get contact IDs from group memberships
        contact_ids_query = db.session.query(ContactGroupMembership.contact_id).filter(
            ContactGroupMembership.group_id.in_(group_ids)
        ).distinct()
        contact_ids = [row[0] for row in contact_ids_query.all()]

        # Get contacts
        contacts = Contact.query.filter(
            Contact.id.in_(contact_ids),
            Contact.is_active == True,
            Contact.do_not_email == False
        ).order_by(Contact.first_name, Contact.last_name).all()

        # Convert to JSON-serializable format
        contacts_data = []
        for contact in contacts:
            contacts_data.append({
                'id': contact.id,
                'full_name': contact.full_name,
                'email': contact.email,
                'company': contact.company or 'No company'
            })

        return jsonify({'contacts': contacts_data})

    except Exception as e:
        app.logger.error(f"API contacts by groups error: {str(e)}")
        return jsonify({'error': 'Failed to get contacts by groups'}), 500

@app.route('/api/contacts/count', methods=['GET', 'POST'])
def api_contacts_count():
    """Count contacts based on criteria"""
    try:
        if request.method == 'GET':
            active = request.args.get('active', 'true').lower() == 'true'
            do_not_email = request.args.get('do_not_email', 'false').lower() == 'true'

            query = Contact.query
            if active:
                query = query.filter_by(is_active=True)
            if not do_not_email:
                query = query.filter_by(do_not_email=False)

            count = query.count()
            return jsonify({'count': count})
        else:
            # POST with filter criteria
            filters = request.get_json() or {}
            query = Contact.query.filter_by(is_active=True, do_not_email=False)

            # Apply additional filters
            if filters.get('status'):
                query = query.filter_by(status=filters['status'])
            if filters.get('source'):
                query = query.filter_by(source=filters['source'])
            if filters.get('min_lead_score'):
                query = query.filter(Contact.lead_score >= filters['min_lead_score'])

            count = query.count()
            return jsonify({'count': count})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500



@app.route('/api/sync-statistics', methods=['POST'])
def sync_statistics():
    """Manually sync all campaign statistics"""
    try:
        success = sync_campaign_statistics()
        if success:
            return jsonify({'success': True, 'message': 'Statistics synchronized successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to synchronize statistics'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/session-from-email/<session_id>')
def track_email_to_session(session_id):
    """Track when user clicks from email to chatbot"""
    try:
        # Find contact by session ID
        contact = Contact.query.filter_by(chatbot_session_id=session_id).first()

        if contact:
            # Update link clicked tracking
            if not contact.link_clicked:
                contact.link_clicked = datetime.utcnow()

                # Update campaign metrics
                if contact.email_campaign:
                    contact.email_campaign.links_clicked += 1

                # Create activity log
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='link_clicked',
                    subject='Email link clicked',
                    description=f'Contact clicked chatbot link from email campaign: {contact.email_campaign.name if contact.email_campaign else "Unknown"}',
                    extra_data=json.dumps({
                        'campaign_id': contact.email_campaign_id,
                        'session_id': session_id,
                        'timestamp': datetime.utcnow().isoformat()
                    })
                )
                db.session.add(activity)

                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': 'Email-to-session tracking updated',
                    'contact_id': contact.id,
                    'campaign_id': contact.email_campaign_id
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'Link already tracked for this contact',
                    'contact_id': contact.id
                })
        else:
            return jsonify({
                'success': False,
                'message': 'Contact not found for session ID'
            }), 404

    except Exception as e:
        app.logger.error(f"Email-to-session tracking error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# Debug endpoints removed - track_session is working correctly

def update_stage_analytics(contact, stage, previous_stage, session_id):
    """Update analytics tracking for stage progression"""
    from datetime import date
    import json

    try:
        # Find or create opportunity for this contact
        opportunity = Opportunity.query.filter_by(contact_id=contact.id).first()
        if not opportunity:
            # Create opportunity for this contact
            opportunity = Opportunity(
                contact_id=contact.id,
                name=f"Opportunity for {contact.full_name}",
                description=f"Generated from chatbot session {session_id}",
                status='open',
                source='chatbot',
                created_at=datetime.utcnow()
            )
            db.session.add(opportunity)
            db.session.flush()  # Get the ID

        # Find the sales stage by name
        sales_stage = SalesStage.query.filter_by(name=stage.title()).first()
        if not sales_stage:
            # Create the stage if it doesn't exist
            stage_order_map = {
                'opening': 4, 'trust': 5, 'discovery': 6,
                'demonstration': 7, 'close': 8, 'converted': 9
            }
            sales_stage = SalesStage(
                name=stage.title(),
                order=stage_order_map.get(stage.lower(), 10),
                probability_percent=stage_order_map.get(stage.lower(), 10) * 10
            )
            db.session.add(sales_stage)
            db.session.flush()

        # Update opportunity stage
        if opportunity.current_stage_id != sales_stage.id:
            # Close previous stage history if exists
            if opportunity.current_stage_id:
                previous_history = SalesStageHistory.query.filter_by(
                    opportunity_id=opportunity.id,
                    stage_id=opportunity.current_stage_id,
                    exited_at=None
                ).first()

                if previous_history:
                    previous_history.exited_at = datetime.utcnow()
                    duration_hours = (previous_history.exited_at - previous_history.entered_at).total_seconds() / 3600
                    previous_history.duration_hours = duration_hours

            # Create new stage history entry
            stage_history = SalesStageHistory(
                opportunity_id=opportunity.id,
                stage_id=sales_stage.id,
                entered_at=datetime.utcnow(),
                changed_by='AI_Chatbot',
                reason=f'Progressed from {previous_stage or "initial"} to {stage}'
            )
            db.session.add(stage_history)

            # Update opportunity current stage
            opportunity.current_stage_id = sales_stage.id
            opportunity.updated_at = datetime.utcnow()

        # Update daily analytics
        today = date.today()
        analytics = SalesAnalytics.query.filter_by(
            date=today,
            period_type='daily'
        ).first()

        if not analytics:
            analytics = SalesAnalytics(
                date=today,
                period_type='daily',
                stage_metrics={}
            )
            db.session.add(analytics)

        # Update stage metrics
        if analytics.stage_metrics is None:
            analytics.stage_metrics = {}

        stage_name = stage.title()
        if stage_name not in analytics.stage_metrics:
            analytics.stage_metrics[stage_name] = {'count': 0, 'value': 0.0}

        analytics.stage_metrics[stage_name]['count'] += 1

        # Update other relevant metrics
        if stage.lower() == 'opening':
            if analytics.new_opportunities is None:
                analytics.new_opportunities = 0
            analytics.new_opportunities += 1
        elif stage.lower() == 'converted':
            if analytics.won_opportunities is None:
                analytics.won_opportunities = 0
            analytics.won_opportunities += 1

        # Mark the analytics object as modified for JSON field
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(analytics, 'stage_metrics')

        app.logger.info(f"Updated analytics for stage progression: {previous_stage} -> {stage}")

    except Exception as e:
        app.logger.error(f"Error updating stage analytics: {e}")
        raise

@app.route('/api/track-session', methods=['POST'])
def track_session():
    """Enhanced chatbot session and stage progression tracking"""
    try:
        data = request.get_json()

        session_id = data.get('session_id')
        stage = data.get('stage')
        task = data.get('task')
        contact_name = data.get('contact_name')
        contact_email = data.get('contact_email')
        action = data.get('action')
        message_count = data.get('message_count', 0)
        user_message = data.get('user_message', '')
        bot_response = data.get('bot_response', '')

        if not session_id:
            return jsonify({'success': False, 'message': 'Session ID required'}), 400

        # Find or create contact
        contact = None

        # First, try to find by session_id (from email campaigns)
        if session_id:
            contact = Contact.query.filter_by(chatbot_session_id=session_id).first()

        # If not found by session_id, try by email
        if not contact and contact_email:
            contact = Contact.query.filter_by(email=contact_email).first()
            # If found by email, update their session_id
            if contact:
                contact.chatbot_session_id = session_id

        # If still no contact and we have name/email, create new one
        if not contact and contact_name and contact_email:
            contact = Contact(
                first_name=contact_name.split()[0],
                last_name=' '.join(contact_name.split()[1:]) if len(contact_name.split()) > 1 else '',
                email=contact_email,
                source='chatbot_direct',
                status='chatbot_active',
                chatbot_session_id=session_id,
                chatbot_conversation_started=datetime.utcnow(),
                current_sales_stage=stage
            )
            db.session.add(contact)

        # If we have a contact but no session_id, and this is a new session, update it
        elif contact and not contact.chatbot_session_id and session_id:
            contact.chatbot_session_id = session_id

        if contact:
            # Find or create chatbot session
            chatbot_session = ChatbotSession.query.filter_by(session_id=session_id).first()
            previous_stage = None

            if not chatbot_session:
                chatbot_session = ChatbotSession(
                    session_id=session_id,
                    contact_id=contact.id,
                    email_campaign_id=contact.email_campaign_id,
                    current_stage=stage,
                    current_task=task
                )
                db.session.add(chatbot_session)

                # Mark conversation as started
                if not contact.chatbot_conversation_started:
                    contact.chatbot_conversation_started = datetime.utcnow()

                # Update campaign metrics
                if contact.email_campaign:
                    if contact.email_campaign.chatbot_conversations_started is None:
                        contact.email_campaign.chatbot_conversations_started = 0
                    contact.email_campaign.chatbot_conversations_started += 1

                # Set initial stage timing
                if stage:
                    chatbot_session.add_stage_timing(stage, 'started')
            else:
                previous_stage = chatbot_session.current_stage

                # Update existing session
                if stage and stage != previous_stage:
                    # Complete previous stage
                    if previous_stage:
                        chatbot_session.add_stage_timing(previous_stage, 'completed')

                    # Start new stage
                    chatbot_session.current_stage = stage
                    chatbot_session.add_stage_timing(stage, 'started')

                if task:
                    chatbot_session.current_task = task

            # Update message counts
            if message_count > 0:
                chatbot_session.total_messages = message_count
                if user_message:
                    if chatbot_session.user_messages is None:
                        chatbot_session.user_messages = 0
                    chatbot_session.user_messages += 1
                if bot_response:
                    if chatbot_session.bot_messages is None:
                        chatbot_session.bot_messages = 0
                    chatbot_session.bot_messages += 1

                # Update engagement level
                chatbot_session.update_engagement_level()

            # Update contact stage progression if stage changed
            if stage and stage != contact.current_sales_stage:
                contact.current_sales_stage = stage
                contact.add_stage_progression(stage)

            # Handle specific actions
            if action == 'conversation_started':
                if not contact.chatbot_conversation_started:
                    contact.chatbot_conversation_started = datetime.utcnow()
                    if contact.email_campaign:
                        if contact.email_campaign.chatbot_conversations_started is None:
                            contact.email_campaign.chatbot_conversations_started = 0
                        contact.email_campaign.chatbot_conversations_started += 1

            elif action == 'stage_progression':
                # Log detailed activity for stage progression
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='stage_progression',
                    subject=f'Progressed to {stage} stage',
                    description=f'Task: {task}. Previous stage: {previous_stage}',
                    extra_data=json.dumps({
                        'previous_stage': previous_stage,
                        'new_stage': stage,
                        'task': task,
                        'message_count': message_count,
                        'engagement_level': chatbot_session.engagement_level
                    })
                )
                db.session.add(activity)

                # Update analytics tracking for stage progression
                try:
                    update_stage_analytics(contact, stage, previous_stage, session_id)
                except Exception as analytics_error:
                    app.logger.error(f"Analytics update error: {analytics_error}")

            elif action == 'objection_handled':
                if chatbot_session.objections_handled is None:
                    chatbot_session.objections_handled = 0
                chatbot_session.objections_handled += 1
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='objection_handled',
                    subject=f'Objection handled in {stage} stage',
                    description=f'User message: {user_message[:200]}...',
                    extra_data=json.dumps({
                        'stage': stage,
                        'user_message': user_message,
                        'bot_response': bot_response
                    })
                )
                db.session.add(activity)

            # Check for conversion
            if stage == 'close' and data.get('conversion_completed'):
                contact.conversion_completed = datetime.utcnow()
                contact.current_sales_stage = 'converted'
                contact.add_stage_progression('converted')
                chatbot_session.conversion_achieved = True
                chatbot_session.completed_successfully = True
                chatbot_session.final_stage_reached = 'converted'
                chatbot_session.ended_at = datetime.utcnow()

                # Complete the close stage
                chatbot_session.add_stage_timing('close', 'completed')

                if contact.email_campaign:
                    if contact.email_campaign.conversions_achieved is None:
                        contact.email_campaign.conversions_achieved = 0
                    contact.email_campaign.conversions_achieved += 1

                # Log conversion activity
                activity = Activity(
                    contact_id=contact.id,
                    session_id=session_id,
                    activity_type='conversion_completed',
                    subject='Conversion completed successfully',
                    description=f'Contact converted through chatbot session',
                    extra_data=json.dumps({
                        'conversion_value': data.get('conversion_value'),
                        'total_messages': chatbot_session.total_messages,
                        'session_duration_minutes': (datetime.utcnow() - chatbot_session.started_at).total_seconds() / 60,
                        'stage_durations': chatbot_session.get_stage_durations_dict()
                    })
                )
                db.session.add(activity)

            # Handle session abandonment
            elif action == 'session_abandoned':
                chatbot_session.ended_at = datetime.utcnow()
                chatbot_session.final_stage_reached = stage
                chatbot_session.abandonment_reason = data.get('abandonment_reason', 'User left conversation')

                # Complete current stage
                if stage:
                    chatbot_session.add_stage_timing(stage, 'completed')

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Session tracked successfully',
                'contact_id': contact.id,
                'session_id': session_id,
                'stage': stage,
                'previous_stage': previous_stage,
                'engagement_level': chatbot_session.engagement_level,
                'total_messages': chatbot_session.total_messages
            })
        else:
            return jsonify({'success': False, 'message': 'Could not find or create contact'}), 400

    except Exception as e:
        app.logger.error(f"Enhanced session tracking error: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/preview-email/<session_id>')
def preview_email(session_id):
    """Preview email content for debugging using centralized template manager"""
    try:
        # Find contact by session ID
        contact = Contact.query.filter_by(chatbot_session_id=session_id).first()
        if not contact:
            return jsonify({'error': 'Contact not found'}), 404

        # Generate email content using centralized template manager
        chatbot_link = f"http://localhost:5000/chat/{session_id}"

        # Use centralized template manager instead of hardcoded templates
        if email_system and 'template_manager' in email_system:
            template_manager = email_system['template_manager']
        else:
            # Fallback: create template manager if not available
            from email_system.email_templates import EmailTemplateManager
            template_manager = EmailTemplateManager()

        # Prepare template context
        template_context = {
            'contact_name': contact.first_name or 'there',
            'company_name': contact.company or 'your company',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': chatbot_link,
            'session_id': session_id
        }

        # Render email using centralized template
        try:
            rendered_email = template_manager.render_template('customer_support', template_context)
            subject = rendered_email['subject']
            html_body = rendered_email['html_body']

            app.logger.info(f"✅ Preview using centralized template for {contact.email}")
        except Exception as template_error:
            app.logger.error(f"❌ Template rendering failed in preview: {template_error}")
            # Fallback to simple email if template fails
            subject = f"Customer Support Solutions for {contact.first_name}"
            html_body = f"<p>Hi {contact.first_name},</p><p>Please visit our chat: <a href='{chatbot_link}'>Start Chat</a></p>"

        return jsonify({
            'contact': {
                'name': contact.full_name,
                'email': contact.email,
                'session_id': session_id
            },
            'subject': subject,
            'chatbot_link': chatbot_link,
            'tracking_pixel_url': f"http://localhost:5000/track/open/{session_id}",
            'html_body': html_body
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/save-chat-message', methods=['POST'])
def save_chat_message():
    """Save individual chat message to database"""
    try:
        data = request.get_json()
        app.logger.info(f"Received save message request: {data}")

        session_id = data.get('session_id')
        message_type = data.get('message_type')  # 'user' or 'assistant'
        content = data.get('content')
        stage = data.get('stage')
        task = data.get('task')
        message_order = data.get('message_order', 0)

        if not all([session_id, message_type, content]):
            return jsonify({'success': False, 'message': 'Missing required fields'}), 400

        # Create new chat message
        chat_message = ChatMessage(
            session_id=session_id,
            message_type=message_type,
            message_content=content,
            stage=stage,
            task=task,
            message_order=message_order
        )

        db.session.add(chat_message)
        db.session.commit()

        app.logger.info(f"Successfully saved message with ID: {chat_message.id}")

        return jsonify({
            'success': True,
            'message': 'Chat message saved successfully',
            'message_id': chat_message.id
        })

    except Exception as e:
        app.logger.error(f"Save chat message error: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Internal server error: {str(e)}'}), 500


@app.route('/api/chat/start', methods=['POST'])

def api_chat_start():
    """Initiate a chat session and return opening assistant message."""
    try:
        data = request.get_json() or {}
        session_id = data.get('session_id') or str(uuid.uuid4())
        prospect_name = data.get('prospect_name', 'there')

        # Retrieve or create SalesBot instance
        bot = bot_instances.get(session_id)
        if not bot:
            bot = SalesBot(CONFIG_DATA)
            bot_instances[session_id] = bot

            # Call start_conversation to get opening
            opening_msg = bot.start_conversation(prospect_name)

            # Persist opening message
            save_chat_to_db(session_id, 'assistant', opening_msg, bot)
        else:
            # If bot already exists, just return last assistant message or generate opening if no history
            if bot.chat_history:
                opening_msg = bot.chat_history[-1]['content']
            else:
                opening_msg = bot.start_conversation(prospect_name)
                save_chat_to_db(session_id, 'assistant', opening_msg, bot)

        return jsonify({
            'success': True,
            'reply': opening_msg,
            'session_id': session_id,
            'stage': bot.current_stage
        })

    except Exception as e:
        app.logger.error(f"/api/chat/start error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/chat/send', methods=['POST'])
def api_chat_send():
    """Receive a user message, get bot reply, persist messages, return reply JSON"""
    try:
        data = request.get_json()
        user_msg = data.get('message', '').strip()
        session_id = data.get('session_id')
        prospect_name = data.get('prospect_name', 'there')

        if not user_msg:
            return jsonify({'success': False, 'error': 'Empty message'}), 400

        # Create new session_id if missing
        if not session_id:
            session_id = str(uuid.uuid4())

        # Retrieve or create SalesBot instance
        bot = bot_instances.get(session_id)
        if bot is None:
            bot = SalesBot(CONFIG_DATA)
            bot_instances[session_id] = bot
            # Start conversation automatically with first assistant message
            opening_msg = bot.start_conversation(prospect_name)
            # Persist opening assistant message
            save_chat_to_db(session_id, 'assistant', opening_msg, bot)

        # Append user message and get reply
        bot_reply = bot.chat(user_msg)

        # Persist messages
        save_chat_to_db(session_id, 'user', user_msg, bot)
        save_chat_to_db(session_id, 'assistant', bot_reply, bot)

        # Track session progression
        try:
            track_data = {
                'session_id': session_id,
                'stage': bot.current_stage,
                'task': bot.current_task,
                'contact_name': prospect_name,
                'action': 'stage_progression',
                'message_count': len(bot.chat_history)
            }
            requests.post(url_for('track_session', _external=True), json=track_data, timeout=2)
        except Exception:
            pass

        return jsonify({'success': True, 'reply': bot_reply, 'session_id': session_id, 'stage': bot.current_stage})

    except Exception as e:
        app.logger.error(f"/api/chat/send error: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


def save_chat_to_db(session_id: str, msg_type: str, content: str, bot: SalesBot):
    """Utility to persist chat message using existing model."""
    try:
        message_order = len(bot.chat_history)
        chat_msg = ChatMessage(
            session_id=session_id,
            message_type=msg_type,
            message_content=content,
            stage=bot.current_stage,
            task=bot.current_task,
            message_order=message_order
        )
        db.session.add(chat_msg)
        db.session.commit()
    except Exception as db_err:
        app.logger.error(f"DB save error: {db_err}")


# Existing endpoint remains
@app.route('/api/get-chat-history/<session_id>')
def get_chat_history(session_id):
    """Retrieve chat history for a session"""
    try:
        # Get all messages for this session, ordered by message_order
        messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.message_order).all()

        # Convert to list of dictionaries
        history = []
        for message in messages:
            history.append({
                'role': message.message_type,
                'content': message.message_content,
                'stage': message.stage,
                'task': message.task,
                'timestamp': message.timestamp.isoformat() if message.timestamp else None,
                'message_order': message.message_order
            })

        return jsonify({
            'success': True,
            'history': history,
            'message_count': len(history)
        })

    except Exception as e:
        app.logger.error(f"Get chat history error: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'}), 500


@app.route('/api/reset-chat-session', methods=['POST'])
def reset_chat_session():
    """Reset chat session by deleting all messages and resetting session data"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'success': False, 'message': 'Session ID required'}), 400

        # Delete all chat messages for this session
        ChatMessage.query.filter_by(session_id=session_id).delete()

        # Reset chatbot session data
        chatbot_session = ChatbotSession.query.filter_by(session_id=session_id).first()
        if chatbot_session:
            # Reset session to initial state
            chatbot_session.current_stage = 'opening'
            chatbot_session.current_task = None
            chatbot_session.total_messages = 0
            chatbot_session.user_messages = 0
            chatbot_session.bot_messages = 0
            chatbot_session.objections_handled = 0
            chatbot_session.completed_successfully = False
            chatbot_session.conversion_achieved = False
            chatbot_session.ended_at = None
            chatbot_session.final_stage_reached = None

            # Reset stage timings
            chatbot_session.opening_started_at = None
            chatbot_session.opening_completed_at = None
            chatbot_session.trust_started_at = None
            chatbot_session.trust_completed_at = None
            chatbot_session.discovery_started_at = None
            chatbot_session.discovery_completed_at = None
            chatbot_session.demonstration_started_at = None
            chatbot_session.demonstration_completed_at = None
            chatbot_session.close_started_at = None
            chatbot_session.close_completed_at = None

        # Also clear any active in-memory SalesBot instance so stages fully reset
        if session_id in bot_instances:
            del bot_instances[session_id]

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Chat session reset successfully'
        })

    except Exception as e:
        app.logger.error(f"Reset chat session error: {str(e)}")
        return jsonify({'success': False, 'message': 'Internal server error'}), 500


@app.route('/api/analytics/stage-progression')
def get_stage_progression_analytics():
    """Get stage progression analytics data"""
    try:
        from datetime import date, timedelta

        # Get recent stage progressions (last 30 days)
        end_date = date.today()
        start_date = end_date - timedelta(days=30)

        # Get daily analytics
        daily_analytics = SalesAnalytics.query.filter(
            SalesAnalytics.date >= start_date,
            SalesAnalytics.date <= end_date,
            SalesAnalytics.period_type == 'daily'
        ).order_by(SalesAnalytics.date).all()

        # Get stage history data
        stage_histories = db.session.query(SalesStageHistory).join(SalesStage).filter(
            SalesStageHistory.entered_at >= datetime.combine(start_date, datetime.min.time())
        ).order_by(SalesStageHistory.entered_at.desc()).limit(100).all()

        # Get current stage distribution
        current_stages = db.session.query(
            SalesStage.name,
            db.func.count(Opportunity.id).label('count')
        ).join(
            Opportunity, SalesStage.id == Opportunity.current_stage_id
        ).filter(
            Opportunity.status == 'open'
        ).group_by(SalesStage.name).all()

        # Format the data
        analytics_data = []
        for analytics in daily_analytics:
            data = {
                'date': analytics.date.isoformat(),
                'total_opportunities': analytics.total_opportunities or 0,
                'new_opportunities': analytics.new_opportunities or 0,
                'won_opportunities': analytics.won_opportunities or 0,
                'stage_metrics': analytics.stage_metrics or {}
            }
            analytics_data.append(data)

        stage_history_data = []
        for history in stage_histories:
            data = {
                'id': history.id,
                'opportunity_id': history.opportunity_id,
                'stage_name': history.stage.name if history.stage else 'Unknown',
                'entered_at': history.entered_at.isoformat() if history.entered_at else None,
                'exited_at': history.exited_at.isoformat() if history.exited_at else None,
                'duration_hours': history.duration_hours,
                'changed_by': history.changed_by,
                'reason': history.reason
            }
            stage_history_data.append(data)

        current_stage_distribution = [
            {'stage_name': stage_name, 'count': count}
            for stage_name, count in current_stages
        ]

        return jsonify({
            'success': True,
            'data': {
                'daily_analytics': analytics_data,
                'recent_stage_progressions': stage_history_data,
                'current_stage_distribution': current_stage_distribution,
                'summary': {
                    'total_progressions': len(stage_history_data),
                    'active_opportunities': sum(item['count'] for item in current_stage_distribution),
                    'date_range': {
                        'start': start_date.isoformat(),
                        'end': end_date.isoformat()
                    }
                }
            }
        })

    except Exception as e:
        app.logger.error(f"Analytics error: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500


def init_database():
    """Initialize database with default data and handle schema migrations"""
    try:
        # First, try to create all tables
        db.create_all()

        # Check if we need to add new columns to existing tables

        # Email campaigns columns
        email_campaign_columns = [
            ("emails_failed", "INTEGER DEFAULT 0"),
            ("retry_count", "INTEGER DEFAULT 0"),
            ("max_retries", "INTEGER DEFAULT 3"),
            ("retry_failed_only", "BOOLEAN DEFAULT 1"),
            ("last_retry_at", "DATETIME"),
            ("daily_send_limit", "INTEGER DEFAULT 100"),
            ("emails_sent_today", "INTEGER DEFAULT 0"),
            ("last_send_date", "DATE"),
            ("send_schedule", "VARCHAR(50) DEFAULT 'immediate'"),
            ("scheduled_start_date", "DATE"),
            ("scheduled_start_time", "TIME"),
            ("send_days_of_week", "VARCHAR(20) DEFAULT '1,2,3,4,5'"),
            ("is_recurring", "BOOLEAN DEFAULT 0"),
            ("batch_status", "VARCHAR(50) DEFAULT 'not_started'"),
            ("next_batch_date", "DATETIME"),
            ("total_batches_planned", "INTEGER DEFAULT 1"),
            ("batches_completed", "INTEGER DEFAULT 0"),
            ("recipient_criteria", "TEXT")
        ]

        for column_name, column_def in email_campaign_columns:
            try:
                # Test if the column exists by trying to query it
                db.session.execute(db.text(f"SELECT {column_name} FROM email_campaigns LIMIT 1"))
            except Exception as e:
                if f"no column named {column_name}" in str(e) or "no such column" in str(e):
                    print(f"🔄 Adding column {column_name} to email_campaigns table...")
                    try:
                        db.session.execute(db.text(f"ALTER TABLE email_campaigns ADD COLUMN {column_name} {column_def}"))
                        db.session.commit()
                        print(f"✅ Column {column_name} added successfully!")
                    except Exception as migration_error:
                        print(f"⚠️ Failed to add column {column_name}: {migration_error}")
                        db.session.rollback()

        # Contact table columns
        contact_columns = [
            ("website", "VARCHAR(255)"),
            ("notes", "TEXT"),
            ("lead_score", "FLOAT DEFAULT 0.0")
        ]

        for column_name, column_def in contact_columns:
            try:
                # Test if the column exists by trying to query it
                db.session.execute(db.text(f"SELECT {column_name} FROM contacts LIMIT 1"))
            except Exception as e:
                if f"no column named {column_name}" in str(e) or "no such column" in str(e):
                    print(f"🔄 Adding column {column_name} to contacts table...")
                    try:
                        db.session.execute(db.text(f"ALTER TABLE contacts ADD COLUMN {column_name} {column_def}"))
                        db.session.commit()
                        print(f"✅ Column {column_name} added successfully!")
                    except Exception as migration_error:
                        print(f"⚠️ Failed to add column {column_name}: {migration_error}")
                        db.session.rollback()

        # Create default sales stages
        if not SalesStage.query.first():
            stages = [
                {'name': 'Email Sent', 'order': 1, 'probability_percent': 5.0},
                {'name': 'Email Opened', 'order': 2, 'probability_percent': 10.0},
                {'name': 'Link Clicked', 'order': 3, 'probability_percent': 15.0},
                {'name': 'Opening', 'order': 4, 'probability_percent': 20.0},
                {'name': 'Trust', 'order': 5, 'probability_percent': 35.0},
                {'name': 'Discovery', 'order': 6, 'probability_percent': 55.0},
                {'name': 'Demonstration', 'order': 7, 'probability_percent': 75.0},
                {'name': 'Close', 'order': 8, 'probability_percent': 90.0},
                {'name': 'Converted', 'order': 9, 'probability_percent': 100.0}
            ]

            for stage_data in stages:
                stage = SalesStage(**stage_data)
                db.session.add(stage)

            db.session.commit()
            print("✅ Sales stages created for unified tracking.")

        # Create sample data
        if not Contact.query.first():
            sample_contact = Contact(
                first_name='John',
                last_name='Doe',
                email='<EMAIL>',
                company='Example Corp',
                job_title='CEO',
                source='demo_data',
                status='new',
                current_sales_stage='email_sent'
            )
            db.session.add(sample_contact)
            db.session.commit()
            print("✅ Sample contact created.")

    except Exception as e:
        print(f"❌ Database initialization error: {str(e)}")
        db.session.rollback()

def sync_campaign_statistics():
    """Synchronize all campaign statistics to ensure consistency across all analytics"""
    try:
        campaigns = EmailCampaign.query.all()

        for campaign in campaigns:
            # Get contacts for this campaign
            campaign_contacts = Contact.query.filter_by(email_campaign_id=campaign.id).all()

            # Recalculate statistics from actual contact data
            emails_sent = len([c for c in campaign_contacts if c.first_email_sent])
            emails_opened = len([c for c in campaign_contacts if c.email_opened])
            chatbot_links_clicked = len([c for c in campaign_contacts if c.chatbot_link_clicked])
            conversations_started = len([c for c in campaign_contacts if c.chatbot_conversation_started])
            conversions_achieved = len([c for c in campaign_contacts if c.current_sales_stage == 'converted'])

            # Update campaign statistics
            campaign.emails_sent = emails_sent
            campaign.emails_delivered = emails_sent  # Assume delivered if sent
            campaign.emails_opened = emails_opened
            campaign.total_recipients = len(campaign_contacts)
            campaign.chatbot_links_clicked = chatbot_links_clicked
            campaign.chatbot_conversations_started = conversations_started
            campaign.conversions_achieved = conversions_achieved

        db.session.commit()
        app.logger.info("Campaign statistics synchronized successfully")
        return True

    except Exception as e:
        app.logger.error(f"Error synchronizing campaign statistics: {str(e)}")
        db.session.rollback()
        return False



@app.route('/api/test-smtp', methods=['POST'])
def api_test_smtp():
    """API endpoint to test SMTP configuration"""
    try:
        success, message = test_smtp_connection()

        return jsonify({
            'success': success,
            'message': message
        })

    except Exception as e:
        app.logger.error(f"SMTP test API error: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'SMTP test error: {str(e)}'
        }), 500



@app.route('/admin/delete-all-data', methods=['POST'])
def delete_all_data():
    """Nuclear option: Delete ALL data from the system - COMPLETE PERMANENT DELETION"""
    try:
        confirmation = request.form.get('confirmation')

        if confirmation != 'DELETE ALL DATA':
            flash('Incorrect confirmation text. Data deletion cancelled.', 'error')
            return redirect(url_for('dashboard'))

        app.logger.warning("STARTING COMPLETE SYSTEM DATA DELETION - ALL DATA WILL BE PERMANENTLY LOST")

        # Disable foreign key constraints temporarily for SQLite
        db.session.execute(db.text("PRAGMA foreign_keys = OFF"))

        deleted_summary = []

        try:
            # 1. Delete all chat messages
            chat_messages_count = db.session.execute(db.text("SELECT COUNT(*) FROM chat_messages")).scalar()
            if chat_messages_count > 0:
                db.session.execute(db.text("DELETE FROM chat_messages"))
                deleted_summary.append(f"{chat_messages_count} chat messages")
                app.logger.info(f"Deleted {chat_messages_count} chat messages")

            # 2. Delete all chatbot sessions
            sessions_count = db.session.execute(db.text("SELECT COUNT(*) FROM chatbot_sessions")).scalar()
            if sessions_count > 0:
                db.session.execute(db.text("DELETE FROM chatbot_sessions"))
                deleted_summary.append(f"{sessions_count} chatbot sessions")
                app.logger.info(f"Deleted {sessions_count} chatbot sessions")

            # 3. Delete all activities
            activities_count = db.session.execute(db.text("SELECT COUNT(*) FROM activities")).scalar()
            if activities_count > 0:
                db.session.execute(db.text("DELETE FROM activities"))
                deleted_summary.append(f"{activities_count} activities")
                app.logger.info(f"Deleted {activities_count} activities")

            # 4. Delete all email logs
            email_logs_count = db.session.execute(db.text("SELECT COUNT(*) FROM email_logs")).scalar()
            if email_logs_count > 0:
                db.session.execute(db.text("DELETE FROM email_logs"))
                deleted_summary.append(f"{email_logs_count} email logs")
                app.logger.info(f"Deleted {email_logs_count} email logs")

            # 5. Delete all email failures
            email_failures_count = db.session.execute(db.text("SELECT COUNT(*) FROM email_failures")).scalar()
            if email_failures_count > 0:
                db.session.execute(db.text("DELETE FROM email_failures"))
                deleted_summary.append(f"{email_failures_count} email failures")
                app.logger.info(f"Deleted {email_failures_count} email failures")

            # 6. Delete all contact group memberships
            memberships_count = db.session.execute(db.text("SELECT COUNT(*) FROM contact_group_memberships")).scalar()
            if memberships_count > 0:
                db.session.execute(db.text("DELETE FROM contact_group_memberships"))
                deleted_summary.append(f"{memberships_count} group memberships")
                app.logger.info(f"Deleted {memberships_count} group memberships")

            # 7. Delete all campaign group associations
            campaign_groups_count = db.session.execute(db.text("SELECT COUNT(*) FROM campaign_groups")).scalar()
            if campaign_groups_count > 0:
                db.session.execute(db.text("DELETE FROM campaign_groups"))
                deleted_summary.append(f"{campaign_groups_count} campaign group associations")
                app.logger.info(f"Deleted {campaign_groups_count} campaign group associations")

            # 8. Delete all opportunities
            opportunities_count = db.session.execute(db.text("SELECT COUNT(*) FROM opportunities")).scalar() if db.session.execute(db.text("SELECT name FROM sqlite_master WHERE type='table' AND name='opportunities'")).scalar() else 0
            if opportunities_count > 0:
                db.session.execute(db.text("DELETE FROM opportunities"))
                deleted_summary.append(f"{opportunities_count} opportunities")
                app.logger.info(f"Deleted {opportunities_count} opportunities")

            # 9. Delete all sales stages
            sales_stages_count = db.session.execute(db.text("SELECT COUNT(*) FROM sales_stages")).scalar() if db.session.execute(db.text("SELECT name FROM sqlite_master WHERE type='table' AND name='sales_stages'")).scalar() else 0
            if sales_stages_count > 0:
                db.session.execute(db.text("DELETE FROM sales_stages"))
                deleted_summary.append(f"{sales_stages_count} sales stages")
                app.logger.info(f"Deleted {sales_stages_count} sales stages")

            # 10. Delete all contacts
            contacts_count = db.session.execute(db.text("SELECT COUNT(*) FROM contacts")).scalar()
            if contacts_count > 0:
                db.session.execute(db.text("DELETE FROM contacts"))
                deleted_summary.append(f"{contacts_count} contacts")
                app.logger.info(f"Deleted {contacts_count} contacts")

            # 11. Delete all contact groups
            groups_count = db.session.execute(db.text("SELECT COUNT(*) FROM contact_groups")).scalar()
            if groups_count > 0:
                db.session.execute(db.text("DELETE FROM contact_groups"))
                deleted_summary.append(f"{groups_count} contact groups")
                app.logger.info(f"Deleted {groups_count} contact groups")

            # 12. Delete all email campaigns
            campaigns_count = db.session.execute(db.text("SELECT COUNT(*) FROM email_campaigns")).scalar()
            if campaigns_count > 0:
                db.session.execute(db.text("DELETE FROM email_campaigns"))
                deleted_summary.append(f"{campaigns_count} email campaigns")
                app.logger.info(f"Deleted {campaigns_count} email campaigns")

            # 13. Reset any auto-increment sequences
            db.session.execute(db.text("DELETE FROM sqlite_sequence"))
            deleted_summary.append("auto-increment sequences reset")
            app.logger.info("Reset auto-increment sequences")

        except Exception as e:
            app.logger.error(f"Error during data deletion: {str(e)}")
            raise
        finally:
            # Re-enable foreign key constraints
            db.session.execute(db.text("PRAGMA foreign_keys = ON"))

        # Commit all deletions
        db.session.commit()

        if deleted_summary:
            flash(f'🗑️ COMPLETE SYSTEM RESET: {", ".join(deleted_summary)}. ALL DATA HAS BEEN PERMANENTLY DELETED.', 'success')
            app.logger.warning(f"COMPLETE SYSTEM RESET COMPLETED: {', '.join(deleted_summary)}")
        else:
            flash('System was already empty. No data to delete.', 'info')

        return redirect(url_for('dashboard'))

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Delete all data error: {str(e)}")
        flash('Error during complete data deletion. Please try again.', 'error')
        return redirect(url_for('dashboard'))

@app.route('/admin/create-test-contacts')
def create_test_contacts():
    """Create test contacts for testing deletion functionality"""
    try:
        # Create 5 test contacts
        test_contacts = [
            {
                'first_name': 'Test',
                'last_name': 'Contact1',
                'email': '<EMAIL>',
                'company': 'Test Company 1',
                'job_title': 'CEO',
                'source': 'test_data'
            },
            {
                'first_name': 'Test',
                'last_name': 'Contact2',
                'email': '<EMAIL>',
                'company': 'Test Company 2',
                'job_title': 'CTO',
                'source': 'test_data'
            },
            {
                'first_name': 'Test',
                'last_name': 'Contact3',
                'email': '<EMAIL>',
                'company': 'Test Company 3',
                'job_title': 'Manager',
                'source': 'test_data'
            },
            {
                'first_name': 'Test',
                'last_name': 'Contact4',
                'email': '<EMAIL>',
                'company': 'Test Company 4',
                'job_title': 'Director',
                'source': 'test_data'
            },
            {
                'first_name': 'Test',
                'last_name': 'Contact5',
                'email': '<EMAIL>',
                'company': 'Test Company 5',
                'job_title': 'VP',
                'source': 'test_data'
            }
        ]

        contacts_created = 0
        for contact_data in test_contacts:
            # Check if contact already exists
            existing = Contact.query.filter_by(email=contact_data['email']).first()
            if not existing:
                contact = Contact(**contact_data)
                db.session.add(contact)
                contacts_created += 1

        db.session.commit()
        flash(f'Created {contacts_created} test contacts for deletion testing!', 'success')
        return redirect(url_for('contacts_list'))

    except Exception as e:
        app.logger.error(f"Error creating test contacts: {str(e)}")
        flash('Error creating test contacts.', 'error')
        return redirect(url_for('contacts_list'))

@app.route('/create_test_data')
def create_test_data():
    """Create test data for demonstration"""
    try:
        # Create test contacts if they don't exist
        test_contacts = [
            {
                'first_name': 'John',
                'last_name': 'Smith',
                'email': '<EMAIL>',
                'company': 'Tech Solutions Inc',
                'job_title': 'CEO',
                'phone': '******-0101'
            },
            {
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'email': '<EMAIL>',
                'company': 'Marketing Pro',
                'job_title': 'Marketing Director',
                'phone': '******-0102'
            },
            {
                'first_name': 'Mike',
                'last_name': 'Davis',
                'email': '<EMAIL>',
                'company': 'Sales Corp',
                'job_title': 'Sales Manager',
                'phone': '******-0103'
            },
            {
                'first_name': 'Lisa',
                'last_name': 'Wilson',
                'email': '<EMAIL>',
                'company': 'Business Solutions',
                'job_title': 'Operations Manager',
                'phone': '******-0104'
            },
            {
                'first_name': 'David',
                'last_name': 'Brown',
                'email': '<EMAIL>',
                'company': 'Growth Ventures',
                'job_title': 'Founder',
                'phone': '******-0105'
            }
        ]

        contacts_created = 0
        for contact_data in test_contacts:
            existing = Contact.query.filter_by(email=contact_data['email']).first()
            if not existing:
                contact = Contact(**contact_data)
                db.session.add(contact)
                contacts_created += 1

        # Get the first campaign (should exist)
        campaign = EmailCampaign.query.first()
        if not campaign:
            # Create a test campaign
            campaign = EmailCampaign(
                name='24Seven Assistant Introduction',
                subject='Meet Sarah - Our Personal AI Sales Assistant',
                template_name='introduction',
                status='completed',
                total_recipients=5,
                emails_sent=5,
                emails_delivered=5,
                emails_opened=3,
                chatbot_links_clicked=2,
                chatbot_conversations_started=2,
                conversions_achieved=1
            )
            db.session.add(campaign)
            db.session.flush()  # Get the ID

        # Create EmailLog records to link contacts to campaign
        contacts = Contact.query.limit(5).all()
        email_logs_created = 0

        for i, contact in enumerate(contacts):
            # Check if EmailLog already exists
            existing_log = EmailLog.query.filter_by(
                campaign_id=campaign.id,
                contact_id=contact.id
            ).first()

            if not existing_log:
                # Create different statuses for variety
                if i == 0:
                    status = 'clicked'
                    sent_at = datetime.utcnow()
                    opened_at = datetime.utcnow()
                    first_clicked_at = datetime.utcnow()
                elif i == 1:
                    status = 'opened'
                    sent_at = datetime.utcnow()
                    opened_at = datetime.utcnow()
                    first_clicked_at = None
                elif i == 2:
                    status = 'sent'
                    sent_at = datetime.utcnow()
                    opened_at = None
                    first_clicked_at = None
                else:
                    status = 'delivered'
                    sent_at = datetime.utcnow()
                    opened_at = None
                    first_clicked_at = None

                email_log = EmailLog(
                    campaign_id=campaign.id,
                    contact_id=contact.id,
                    recipient_email=contact.email,
                    recipient_name=contact.full_name,
                    subject=campaign.subject,
                    status=status,
                    sent_at=sent_at,
                    opened_at=opened_at,
                    first_clicked_at=first_clicked_at
                )
                db.session.add(email_log)
                email_logs_created += 1

                # Update contact with campaign info
                contact.email_campaign_id = campaign.id
                contact.current_sales_stage = 'email_sent' if status == 'sent' else 'email_opened' if opened_at else 'link_clicked' if first_clicked_at else 'email_sent'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Test data created successfully! Contacts: {contacts_created}, Email logs: {email_logs_created}',
            'campaign_id': campaign.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error creating test data: {str(e)}'
        })

@app.route('/create_more_test_data')
def create_more_test_data():
    """Create additional test data for pagination testing"""
    try:
        # Create more test contacts
        additional_contacts = []
        companies = ['TechCorp', 'InnovateLab', 'DataSystems', 'CloudWorks', 'AI Solutions', 'WebDev Pro', 'Mobile First', 'StartupHub', 'Enterprise Plus', 'Digital Agency']
        titles = ['CEO', 'CTO', 'Marketing Director', 'Sales Manager', 'Operations Manager', 'Product Manager', 'VP Sales', 'Business Owner', 'Founder', 'Director']

        for i in range(6, 26):  # Create contacts 6-25
            additional_contacts.append({
                'first_name': f'Contact{i}',
                'last_name': f'User{i}',
                'email': f'contact{i}@example.com',
                'company': companies[i % len(companies)],
                'job_title': titles[i % len(titles)],
                'phone': f'******-{1000 + i:04d}'
            })

        contacts_created = 0
        for contact_data in additional_contacts:
            existing = Contact.query.filter_by(email=contact_data['email']).first()
            if not existing:
                contact = Contact(**contact_data)
                db.session.add(contact)
                contacts_created += 1

        # Get the first campaign
        campaign = EmailCampaign.query.first()
        if campaign:
            # Create EmailLog records for the new contacts
            new_contacts = Contact.query.filter(Contact.email.like('<EMAIL>')).all()
            email_logs_created = 0

            for i, contact in enumerate(new_contacts):
                # Check if EmailLog already exists
                existing_log = EmailLog.query.filter_by(
                    campaign_id=campaign.id,
                    contact_id=contact.id
                ).first()

                if not existing_log:
                    # Create different statuses for variety
                    statuses = ['sent', 'delivered', 'opened', 'clicked']
                    status = statuses[i % len(statuses)]

                    sent_at = datetime.utcnow()
                    opened_at = datetime.utcnow() if status in ['opened', 'clicked'] else None
                    first_clicked_at = datetime.utcnow() if status == 'clicked' else None

                    email_log = EmailLog(
                        campaign_id=campaign.id,
                        contact_id=contact.id,
                        recipient_email=contact.email,
                        recipient_name=contact.full_name,
                        subject=campaign.subject,
                        status=status,
                        sent_at=sent_at,
                        opened_at=opened_at,
                        first_clicked_at=first_clicked_at
                    )
                    db.session.add(email_log)
                    email_logs_created += 1

                    # Update contact with campaign info
                    contact.email_campaign_id = campaign.id
                    contact.current_sales_stage = 'email_sent' if status == 'sent' else 'email_opened' if opened_at else 'link_clicked' if first_clicked_at else 'email_sent'

            # Update campaign stats
            campaign.total_recipients = EmailLog.query.filter_by(campaign_id=campaign.id).count()
            campaign.emails_sent = EmailLog.query.filter_by(campaign_id=campaign.id).count()
            campaign.emails_delivered = EmailLog.query.filter(EmailLog.campaign_id == campaign.id, EmailLog.status.in_(['delivered', 'opened', 'clicked'])).count()
            campaign.emails_opened = EmailLog.query.filter(EmailLog.campaign_id == campaign.id, EmailLog.opened_at.isnot(None)).count()
            campaign.chatbot_links_clicked = EmailLog.query.filter(EmailLog.campaign_id == campaign.id, EmailLog.first_clicked_at.isnot(None)).count()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Additional test data created! Contacts: {contacts_created}, Email logs: {email_logs_created}',
            'total_contacts': Contact.query.count(),
            'campaign_contacts': EmailLog.query.filter_by(campaign_id=campaign.id).count() if campaign else 0
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error creating additional test data: {str(e)}'
        })

# ---------------------------------------------------------------------------
# IMAP Email Retrieval Routes
# ---------------------------------------------------------------------------

@app.route('/emails/sent')
def view_sent_emails():
    """View sent emails from IMAP server"""
    try:
        # Import IMAP service directly to avoid model conflicts
        import imaplib
        import email
        import ssl
        from datetime import datetime, timedelta
        from email.header import decode_header
        import re

        # IMAP configuration
        imap_server = app.config['MAIL_SERVER']
        imap_port = 993
        username = app.config['MAIL_USERNAME']
        password = app.config['MAIL_PASSWORD']
        sent_folder = 'INBOX.Sent'

        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        days_back = request.args.get('days_back', 30, type=int)
        search_term = request.args.get('search', '')

        # Helper function to decode headers
        def decode_header_safe(header):
            if not header:
                return ""
            try:
                decoded_parts = decode_header(header)
                decoded_string = ""
                for part, encoding in decoded_parts:
                    if isinstance(part, bytes):
                        if encoding:
                            decoded_string += part.decode(encoding)
                        else:
                            decoded_string += part.decode('utf-8', errors='ignore')
                    else:
                        decoded_string += part
                return decoded_string
            except:
                return str(header)

        # Helper function to extract email body
        def extract_body(email_message):
            body_text = ""
            body_html = ""
            try:
                if email_message.is_multipart():
                    for part in email_message.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))
                        if "attachment" in content_disposition:
                            continue
                        if content_type == "text/plain":
                            body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        elif content_type == "text/html":
                            body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                else:
                    content_type = email_message.get_content_type()
                    if content_type == "text/plain":
                        body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        body_html = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            except Exception as e:
                app.logger.error(f"Error extracting body: {e}")
            return body_text, body_html

        # Connect to IMAP server
        context = ssl.create_default_context()
        server = imaplib.IMAP4_SSL(imap_server, imap_port, ssl_context=context)
        server.login(username, password)

        # Get folder list
        status, folder_list = server.list()
        folders = []
        if status == 'OK':
            for folder in folder_list:
                folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else folder
                match = re.search(r'"([^"]*)"$', folder_str)
                if match:
                    folders.append(match.group(1))

        # Select sent folder
        status, count = server.select(sent_folder)
        sent_emails = []

        if status == 'OK':
            # Search for emails
            since_date = (datetime.now() - timedelta(days=days_back)).strftime("%d-%b-%Y")
            search_criteria = f'(SINCE "{since_date}")'

            if search_term:
                search_criteria = f'({search_criteria} BODY "{search_term}")'

            status, messages = server.search(None, search_criteria)
            if status != 'OK':
                status, messages = server.search(None, 'ALL')

            if status == 'OK':
                message_ids = messages[0].split()

                # Limit messages
                if len(message_ids) > limit:
                    message_ids = message_ids[-limit:]

                # Fetch emails
                for msg_id in reversed(message_ids):
                    try:
                        status, msg_data = server.fetch(msg_id, '(RFC822)')
                        if status == 'OK':
                            email_body = msg_data[0][1]
                            email_message = email.message_from_bytes(email_body)

                            # Extract headers
                            subject = decode_header_safe(email_message['Subject'])
                            from_addr = decode_header_safe(email_message['From'])
                            to_addr = decode_header_safe(email_message['To'])
                            date_str = email_message['Date']
                            message_id = email_message['Message-ID']

                            # Parse date
                            try:
                                email_date = email.utils.parsedate_to_datetime(date_str)
                            except:
                                email_date = datetime.now()

                            # Extract body
                            body_text, body_html = extract_body(email_message)

                            sent_emails.append({
                                'message_id': message_id,
                                'subject': subject,
                                'from': from_addr,
                                'to': to_addr,
                                'date': email_date,
                                'body_text': body_text,
                                'body_html': body_html,
                                'raw_size': len(email_body)
                            })
                    except Exception as e:
                        app.logger.error(f"Error fetching email: {e}")
                        continue

        server.close()
        server.logout()

        return render_template('emails/sent_emails.html',
                             sent_emails=sent_emails,
                             folders=folders,
                             search_term=search_term,
                             limit=limit,
                             days_back=days_back)

    except Exception as e:
        app.logger.error(f"Error retrieving sent emails: {str(e)}")
        return render_template('emails/sent_emails.html',
                             sent_emails=[],
                             folders=[],
                             error=str(e))

@app.route('/emails/test-imap')
def test_imap_connection():
    """Test IMAP connection"""
    try:
        import imaplib
        import ssl
        import re

        # IMAP configuration
        imap_server = app.config['MAIL_SERVER']
        imap_port = 993
        username = app.config['MAIL_USERNAME']
        password = app.config['MAIL_PASSWORD']

        # Test connection
        context = ssl.create_default_context()
        server = imaplib.IMAP4_SSL(imap_server, imap_port, ssl_context=context)
        server.login(username, password)

        # Get folder list
        status, folder_list = server.list()
        folders = []
        if status == 'OK':
            for folder in folder_list:
                folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else folder
                match = re.search(r'"([^"]*)"$', folder_str)
                if match:
                    folders.append(match.group(1))

        # Test sent folder access
        status, count = server.select('INBOX.Sent')
        sent_folder_accessible = status == 'OK'
        message_count = int(count[0]) if sent_folder_accessible else 0

        server.close()
        server.logout()

        return jsonify({
            'success': True,
            'message': f'IMAP connection successful. Found {len(folders)} folders. Sent folder has {message_count} messages.',
            'folders': folders,
            'config': {
                'server': imap_server,
                'port': imap_port,
                'ssl': True,
                'username': username
            },
            'sent_folder_accessible': sent_folder_accessible,
            'sent_folder_message_count': message_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"IMAP test failed: {str(e)}"
        })

@app.route('/emails/sync-sent')
def sync_sent_emails():
    """Sync sent emails with campaign data"""
    try:
        import imaplib
        import ssl
        import email
        from datetime import datetime, timedelta
        from email.header import decode_header
        import re

        # IMAP configuration
        imap_server = app.config['MAIL_SERVER']
        imap_port = 993
        username = app.config['MAIL_USERNAME']
        password = app.config['MAIL_PASSWORD']
        sent_folder = 'INBOX.Sent'

        # Connect to IMAP
        context = ssl.create_default_context()
        server = imaplib.IMAP4_SSL(imap_server, imap_port, ssl_context=context)
        server.login(username, password)

        # Get recent sent emails
        status, count = server.select(sent_folder)
        sent_emails = []

        if status == 'OK':
            # Search for recent emails
            since_date = (datetime.now() - timedelta(days=7)).strftime("%d-%b-%Y")
            status, messages = server.search(None, f'(SINCE "{since_date}")')

            if status == 'OK':
                message_ids = messages[0].split()

                # Limit to 100 messages
                if len(message_ids) > 100:
                    message_ids = message_ids[-100:]

                for msg_id in message_ids:
                    try:
                        status, msg_data = server.fetch(msg_id, '(RFC822)')
                        if status == 'OK':
                            email_body = msg_data[0][1]
                            email_message = email.message_from_bytes(email_body)

                            # Extract basic info
                            to_addr = email_message['To']
                            message_id = email_message['Message-ID']
                            date_str = email_message['Date']

                            try:
                                email_date = email.utils.parsedate_to_datetime(date_str)
                            except:
                                email_date = datetime.now()

                            sent_emails.append({
                                'to': to_addr,
                                'message_id': message_id,
                                'date': email_date
                            })
                    except:
                        continue

        server.close()
        server.logout()

        synced_count = 0
        matched_count = 0

        for email_data in sent_emails:
            # Try to match with existing email logs
            if email_data.get('message_id'):
                email_log = EmailLog.query.filter_by(
                    message_id=email_data['message_id']
                ).first()

                if email_log:
                    matched_count += 1
                    # Update email log with sent confirmation
                    if not email_log.sent_at:
                        email_log.sent_at = email_data['date']
                        email_log.status = 'sent'
                        synced_count += 1

            # Try to match by recipient email and approximate time
            if email_data.get('to'):
                # Extract email from "Name <email>" format
                import re
                email_match = re.search(r'<([^>]+)>', email_data['to'])
                recipient_email = email_match.group(1) if email_match else email_data['to']

                # Look for email logs sent around the same time
                time_window = timedelta(minutes=5)
                start_time = email_data['date'] - time_window
                end_time = email_data['date'] + time_window

                email_logs = EmailLog.query.filter(
                    EmailLog.recipient_email == recipient_email,
                    EmailLog.created_at >= start_time,
                    EmailLog.created_at <= end_time
                ).all()

                for log in email_logs:
                    if not log.sent_at:
                        log.sent_at = email_data['date']
                        log.status = 'sent'
                        if not log.message_id:
                            log.message_id = email_data.get('message_id')
                        synced_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Sync completed. Found {len(sent_emails)} sent emails, matched {matched_count}, synced {synced_count}',
            'total_sent_emails': len(sent_emails),
            'matched_emails': matched_count,
            'synced_emails': synced_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f"Sync failed: {str(e)}"
        })

def init_database(quiet=False):
    """Initialize database with proper schema"""
    global email_system
    try:
        # Only create tables if they don't exist (preserve existing data)
        db.create_all()
        if not quiet:
            print("✅ Database tables verified/created")

        # Initialize enhanced email system
        try:
            # Import locally to avoid circular imports
            from email_system import create_email_system, test_email_system
            from email_system.config import get_flask_email_config

            # Update Flask config with email system configuration
            app.config.update(get_flask_email_config())

            email_system = create_email_system(db_session=db.session)
            if not quiet:
                print("✅ Enhanced email system initialized")

            # Test email system connectivity
            success, message = test_email_system()
            if success:
                if not quiet:
                    print(f"✅ Email system connectivity test passed: {message}")
            else:
                if not quiet:
                    print(f"⚠️ Email system connectivity test failed: {message}")
                    print("   Email campaigns may not work properly")
        except Exception as e:
            if not quiet:
                print(f"⚠️ Email system initialization failed: {str(e)}")
                print("   Email campaigns will not be available")
            email_system = None

        # Test analytics function
        analytics = get_unified_analytics()
        if not quiet:
            print(f"✅ Analytics initialized ({len(analytics)} sections)")

        return True
    except Exception as e:
        print(f"❌ Database initialization error: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 24Seven Assistants Sales System")
    print("=" * 50)

    try:
        with app.app_context():
            if init_database(quiet=True):
                print("✅ System initialized successfully!")
            else:
                print("❌ System initialization failed!")
                exit(1)
    except Exception as e:
        print(f"❌ Initialization error: {e}")
        exit(1)

    print("🌐 Server running at: http://localhost:5000")
    print("⚡ Press Ctrl+C to stop")
    print("=" * 50)

    # Set logging level to reduce verbosity
    import logging
    logging.getLogger('werkzeug').setLevel(logging.ERROR)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
