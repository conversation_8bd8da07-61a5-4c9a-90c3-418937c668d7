#!/usr/bin/env python3
"""
Send Test Email
===============
Send a test <NAME_EMAIL> using the enhanced email system
"""

import os

def load_env_variables():
    """Load environment variables from .env file"""
    try:
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        
        print(f"✅ Loaded {len(env_vars)} environment variables from .env")
        return True
        
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")
        return False

def send_test_email():
    """Send a test email using the enhanced email system"""
    try:
        print("📧 Sending Test Email")
        print("=" * 50)
        
        # Load environment variables
        if not load_env_variables():
            print("❌ Failed to load environment variables")
            return False
        
        # Import email system
        from email_system.config import get_email_config
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        
        # Get configuration
        config = get_email_config()
        print(f"📧 Using email server: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
        print(f"📧 From: {config['MAIL_USERNAME']}")
        print(f"📧 To: <EMAIL>")
        
        # Create SMTP service
        smtp_service = EnhancedSMTPService(config)
        
        # Test connection first
        print("\n🔗 Testing SMTP connection...")
        success, message = smtp_service.test_connection()
        if not success:
            print(f"❌ SMTP connection failed: {message}")
            return False
        
        print(f"✅ SMTP connection successful: {message}")
        
        # Prepare email content
        subject = "🧪 Test Email - 24Seven Assistants Campaign System"
        
        html_body = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background: #667eea; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎉 Email System Test Successful!</h1>
            </div>
            
            <div class="content">
                <div class="success">
                    <strong>✅ Congratulations!</strong> The 24Seven Assistants email campaign system is now working correctly.
                </div>
                
                <h2>📧 Test Results</h2>
                <ul>
                    <li><strong>SMTP Server:</strong> Gmail (smtp.gmail.com:587)</li>
                    <li><strong>Authentication:</strong> Successful</li>
                    <li><strong>Email Delivery:</strong> Working</li>
                    <li><strong>Configuration:</strong> Loaded from .env file</li>
                </ul>
                
                <h2>🚀 What's Working Now</h2>
                <ul>
                    <li>✅ Email campaigns can send successfully</li>
                    <li>✅ SMTP authentication with Gmail</li>
                    <li>✅ Enhanced error handling and fallback options</li>
                    <li>✅ Proper environment variable loading</li>
                    <li>✅ IMAP integration for sent folder (optional)</li>
                </ul>
                
                <h2>📋 Next Steps</h2>
                <ol>
                    <li>Start the application with: <code>python start_with_gmail.py</code></li>
                    <li>Go to <a href="http://localhost:5000/campaigns">http://localhost:5000/campaigns</a></li>
                    <li>Click "Send" on your email campaign</li>
                    <li>Monitor the results in the dashboard</li>
                </ol>
                
                <p><strong>The email campaign system is now ready for production use!</strong></p>
            </div>
            
            <div class="footer">
                <p>This test email was sent by the 24Seven Assistants Sales System<br>
                Email Campaign Management & Analytics Platform</p>
            </div>
        </body>
        </html>
        """
        
        text_body = """
        🎉 EMAIL SYSTEM TEST SUCCESSFUL!
        
        Congratulations! The 24Seven Assistants email campaign system is now working correctly.
        
        📧 TEST RESULTS:
        - SMTP Server: Gmail (smtp.gmail.com:587)
        - Authentication: Successful
        - Email Delivery: Working
        - Configuration: Loaded from .env file
        
        🚀 WHAT'S WORKING NOW:
        ✅ Email campaigns can send successfully
        ✅ SMTP authentication with Gmail
        ✅ Enhanced error handling and fallback options
        ✅ Proper environment variable loading
        ✅ IMAP integration for sent folder (optional)
        
        📋 NEXT STEPS:
        1. Start the application with: python start_with_gmail.py
        2. Go to http://localhost:5000/campaigns
        3. Click "Send" on your email campaign
        4. Monitor the results in the dashboard
        
        The email campaign system is now ready for production use!
        
        ---
        This test email was sent by the 24Seven Assistants Sales System
        Email Campaign Management & Analytics Platform
        """
        
        # Send the email
        print("\n📤 Sending test email...")
        success, message_id, error_msg = smtp_service.send_email(
            to_email="<EMAIL>",
            subject=subject,
            html_body=html_body,
            text_body=text_body,
            from_name="24Seven Assistants Sales Team"
        )
        
        if success:
            print("✅ Test email sent successfully!")
            print(f"📧 Message ID: {message_id}")
            print("📬 Check your <NAME_EMAIL>")
            return True
        else:
            print(f"❌ Failed to send test email: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ Test email failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🚀 24Seven Assistants - Test Email Sender")
    print("=" * 60)
    
    success = send_test_email()
    
    print("\n" + "=" * 60)
    print("📊 TEST EMAIL SUMMARY")
    print("=" * 60)
    
    if success:
        print("✅ TEST EMAIL SENT SUCCESSFULLY!")
        print("\n📬 Check your email <NAME_EMAIL>")
        print("📧 Subject: 🧪 Test Email - 24Seven Assistants Campaign System")
        print("\n🎉 The email campaign system is working correctly!")
        print("\nNext steps:")
        print("1. Check your email inbox")
        print("2. Start the application: python start_with_gmail.py")
        print("3. Send your actual email campaigns")
    else:
        print("❌ TEST EMAIL FAILED!")
        print("\nPlease check:")
        print("1. Gmail credentials in .env file")
        print("2. Internet connectivity")
        print("3. Gmail app password validity")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
