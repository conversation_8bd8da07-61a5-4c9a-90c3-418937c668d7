#!/usr/bin/env python3
"""
Quick SMTP Fix
==============
Temporarily disable IMAP and test SMTP only to isolate the issue
"""

def quick_fix_smtp_issue():
    """Apply quick fix to isolate SMTP vs IMAP issues"""
    try:
        print("🔧 Applying Quick SMTP Fix")
        print("=" * 50)
        
        from unified_sales_system import app, db, Contact, EmailCampaign
        from email_system.config import get_email_config
        
        with app.app_context():
            # Step 1: Temporarily disable IMAP saving
            print("📧 Step 1: Testing SMTP without IMAP")
            print("-" * 30)
            
            config = get_email_config()
            config['SAVE_TO_SENT_FOLDER'] = False  # Disable IMAP temporarily
            
            print("✅ IMAP saving disabled for testing")
            
            # Step 2: Test SMTP connection only
            print("\n🔗 Step 2: Testing SMTP Connection Only")
            print("-" * 30)
            
            from email_system.enhanced_smtp_service import EnhancedSMTPService
            
            try:
                smtp_service = EnhancedSMTPService(config)
                print("✅ SMTP service created")
                
                # Test connection
                with smtp_service.create_smtp_connection() as server:
                    print("✅ SMTP connection successful")
                
                print("🎉 SMTP works without IMAP!")
                
            except Exception as e:
                print(f"❌ SMTP still failing: {e}")
                return False
            
            # Step 3: Test sending a simple email
            print("\n📧 Step 3: Testing Simple Email Send")
            print("-" * 30)
            
            try:
                # Get first contact
                contact = Contact.query.filter_by(is_active=True, do_not_email=False).first()
                if not contact:
                    print("❌ No email-enabled contacts found")
                    return False
                
                print(f"Testing email to: {contact.email}")
                
                # Send test email
                success, message_id, error_msg = smtp_service.send_email(
                    to_email=contact.email,
                    subject="SMTP Test - 24Seven Assistants",
                    html_body="""
                    <html>
                    <body>
                        <h2>SMTP Test Successful!</h2>
                        <p>This email confirms that the SMTP configuration is working correctly.</p>
                        <p>The email campaign system should now work properly.</p>
                        <br>
                        <p>Best regards,<br>24Seven Assistants Team</p>
                    </body>
                    </html>
                    """,
                    text_body="SMTP Test Successful! The email campaign system is working."
                )
                
                if success:
                    print(f"✅ Test email sent successfully!")
                    print(f"   Message ID: {message_id}")
                    return True
                else:
                    print(f"❌ Test email failed: {error_msg}")
                    return False
                    
            except Exception as e:
                print(f"❌ Email sending test failed: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Quick fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def update_campaign_config():
    """Update campaign configuration to disable IMAP temporarily"""
    try:
        print("\n⚙️ Updating Campaign Configuration")
        print("-" * 40)
        
        # Update the email system configuration
        config_file = "email_system/config.py"
        
        print("📝 Temporarily disabling IMAP in configuration...")
        
        # Read current config
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Replace SAVE_TO_SENT_FOLDER default
        updated_content = content.replace(
            "'SAVE_TO_SENT_FOLDER': os.getenv('SAVE_TO_SENT_FOLDER', 'true').lower() == 'true'",
            "'SAVE_TO_SENT_FOLDER': os.getenv('SAVE_TO_SENT_FOLDER', 'false').lower() == 'true'"
        )
        
        # Write updated config
        with open(config_file, 'w') as f:
            f.write(updated_content)
        
        print("✅ Configuration updated - IMAP saving disabled by default")
        print("   This will help isolate SMTP vs IMAP issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to update configuration: {e}")
        return False

def test_campaign_with_fixed_config():
    """Test campaign sending with the fixed configuration"""
    try:
        print("\n🧪 Testing Campaign with Fixed Configuration")
        print("-" * 50)
        
        from unified_sales_system import app, db, Contact, EmailCampaign, get_campaign_contacts, send_campaign_email
        import uuid
        
        with app.app_context():
            # Get campaign and contacts
            campaign = EmailCampaign.query.first()
            if not campaign:
                print("❌ No campaigns found")
                return False
            
            contacts = get_campaign_contacts(campaign)
            if not contacts:
                print("❌ No contacts found for campaign")
                return False
            
            print(f"📧 Campaign: {campaign.name}")
            print(f"👥 Contacts: {len(contacts)}")
            
            # Test sending to first contact
            first_contact = contacts[0]
            session_id = str(uuid.uuid4())
            
            print(f"🧪 Testing email to: {first_contact.email}")
            
            success, message = send_campaign_email(first_contact, campaign, session_id)
            
            if success:
                print("✅ Campaign email sent successfully!")
                print("🎉 The email campaign system is now working!")
                return True
            else:
                print(f"❌ Campaign email failed: {message}")
                return False
                
    except Exception as e:
        print(f"❌ Campaign test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main fix function"""
    print("🚀 Quick SMTP Fix Tool")
    print("Isolating SMTP vs IMAP issues")
    print("=" * 60)
    
    # Step 1: Update configuration
    config_updated = update_campaign_config()
    
    # Step 2: Test SMTP only
    if config_updated:
        smtp_success = quick_fix_smtp_issue()
    else:
        smtp_success = False
    
    # Step 3: Test campaign with fixed config
    if smtp_success:
        campaign_success = test_campaign_with_fixed_config()
    else:
        campaign_success = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 QUICK FIX SUMMARY")
    print("=" * 60)
    print(f"Configuration Update: {'✅ SUCCESS' if config_updated else '❌ FAILED'}")
    print(f"SMTP Test: {'✅ SUCCESS' if smtp_success else '❌ FAILED'}")
    print(f"Campaign Test: {'✅ SUCCESS' if campaign_success else '❌ FAILED'}")
    
    if campaign_success:
        print("\n🎉 QUICK FIX SUCCESSFUL!")
        print("The email campaign system should now work.")
        print("\nNext steps:")
        print("1. Try sending your campaign again")
        print("2. IMAP saving is temporarily disabled")
        print("3. Once SMTP works, we can re-enable IMAP")
    elif smtp_success:
        print("\n⚠️ SMTP works but campaign system needs attention")
        print("Check the campaign configuration and contact selection")
    else:
        print("\n❌ SMTP connection still failing")
        print("The issue may be with credentials or network connectivity")
    
    return campaign_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
