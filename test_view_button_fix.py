#!/usr/bin/env python3
"""
Test View Button Fix
"""

import requests
import time
import re

def test_view_button_fix():
    """Test that the view button fix is working"""
    print("🔧 Testing View Button Fix")
    print("=" * 30)
    
    # Step 1: Create a new campaign
    print("📝 Creating new test campaign...")
    
    campaign_data = {
        'name': 'View Button Test Campaign',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate'
    }
    
    create_response = requests.post(
        'http://localhost:5000/campaigns/create',
        data=campaign_data
    )
    
    if create_response.status_code in [200, 302]:
        print("✅ Campaign created successfully")
    else:
        print(f"❌ Failed to create campaign: {create_response.status_code}")
        return False
    
    # Step 2: Get the campaign ID from campaigns list
    print("🔍 Finding new campaign ID...")
    
    campaigns_response = requests.get('http://localhost:5000/campaigns')
    if campaigns_response.status_code != 200:
        print(f"❌ Failed to get campaigns list: {campaigns_response.status_code}")
        return False
    
    # Extract campaign IDs
    campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', campaigns_response.text)
    
    if not campaign_ids:
        print("❌ No campaigns found")
        return False
    
    # Use the first (newest) campaign ID
    test_campaign_id = campaign_ids[0]
    print(f"🎯 Testing with campaign ID: {test_campaign_id}")
    
    # Step 3: Test view campaign functionality
    print("👁️ Testing view campaign...")
    
    view_response = requests.get(f'http://localhost:5000/campaigns/{test_campaign_id}')
    
    print(f"   Status Code: {view_response.status_code}")
    
    if view_response.status_code == 200:
        print("✅ View campaign returned 200 OK")
        
        # Check if it's the campaign details page
        if "Campaign Details" in view_response.text:
            print("✅ Campaign details page loaded correctly")
            return True
        elif "Campaign Information" in view_response.text:
            print("✅ Campaign information page loaded correctly")
            return True
        elif "Error" in view_response.text:
            print("❌ Error page displayed instead of campaign details")
            return False
        else:
            print("⚠️ Unknown page content - checking for key elements...")
            
            # Check for key elements that should be on campaign details page
            key_elements = [
                "campaign.name",
                "Quick Stats",
                "Performance Metrics",
                "Campaign Information"
            ]
            
            found_elements = 0
            for element in key_elements:
                if element in view_response.text:
                    found_elements += 1
                    print(f"   ✅ Found: {element}")
                else:
                    print(f"   ❌ Missing: {element}")
            
            if found_elements >= 2:
                print("✅ Sufficient campaign details elements found")
                return True
            else:
                print("❌ Campaign details page not properly loaded")
                return False
                
    elif view_response.status_code == 302:
        print("❌ Redirected (likely an error occurred)")
        
        # Check for error flash messages
        if 'session=' in view_response.headers.get('Set-Cookie', ''):
            print("   📝 Error flash message likely present")
        
        return False
    elif view_response.status_code == 404:
        print("❌ Campaign not found")
        return False
    else:
        print(f"❌ Unexpected status code: {view_response.status_code}")
        return False

def test_all_action_buttons():
    """Test all action buttons are working"""
    print("\n🎮 Testing All Action Buttons")
    print("=" * 35)
    
    # Get campaigns list
    campaigns_response = requests.get('http://localhost:5000/campaigns')
    if campaigns_response.status_code != 200:
        print("❌ Failed to get campaigns list")
        return False
    
    # Extract campaign IDs
    campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', campaigns_response.text)
    
    if not campaign_ids:
        print("❌ No campaigns found to test")
        return False
    
    test_campaign_id = campaign_ids[0]
    print(f"🎯 Testing action buttons with campaign ID: {test_campaign_id}")
    
    # Test each action button
    actions = {
        'View': f'/campaigns/{test_campaign_id}',
        'Edit': f'/campaigns/{test_campaign_id}/edit',
    }
    
    results = {}
    
    for action_name, url in actions.items():
        print(f"   Testing {action_name}...")
        
        try:
            response = requests.get(f'http://localhost:5000{url}')
            if response.status_code == 200:
                print(f"   ✅ {action_name}: Working")
                results[action_name] = True
            else:
                print(f"   ❌ {action_name}: Status {response.status_code}")
                results[action_name] = False
        except Exception as e:
            print(f"   ❌ {action_name}: Error - {str(e)}")
            results[action_name] = False
    
    # Test POST actions (duplicate)
    print("   Testing Duplicate (POST)...")
    try:
        duplicate_response = requests.post(f'http://localhost:5000/campaigns/{test_campaign_id}/duplicate')
        if duplicate_response.status_code in [200, 302]:
            print("   ✅ Duplicate: Working")
            results['Duplicate'] = True
        else:
            print(f"   ❌ Duplicate: Status {duplicate_response.status_code}")
            results['Duplicate'] = False
    except Exception as e:
        print(f"   ❌ Duplicate: Error - {str(e)}")
        results['Duplicate'] = False
    
    working_actions = sum(results.values())
    total_actions = len(results)
    
    print(f"\n📊 Action Buttons Summary: {working_actions}/{total_actions} working")
    
    return working_actions >= 2  # At least view and edit should work

if __name__ == "__main__":
    print("🚀 View Button Fix Test")
    print("=" * 40)
    
    # Test 1: View button functionality
    view_test = test_view_button_fix()
    
    # Test 2: All action buttons
    buttons_test = test_all_action_buttons()
    
    print("\n" + "=" * 40)
    print("📋 TEST RESULTS")
    print("=" * 40)
    print(f"View Button Fix:        {'✅ PASSED' if view_test else '❌ FAILED'}")
    print(f"Action Buttons:         {'✅ PASSED' if buttons_test else '❌ FAILED'}")
    
    overall_result = view_test and buttons_test
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if overall_result:
        print("\n🎉 View button fix is working correctly!")
        print("📝 You can now:")
        print("   • Click the eye icon to view campaign details")
        print("   • Access campaign performance metrics")
        print("   • View campaign contacts and activities")
        print("   • Use all other action buttons")
    else:
        print("\n⚠️ Some issues remain:")
        if not view_test:
            print("   • View campaign functionality needs attention")
        if not buttons_test:
            print("   • Some action buttons may not be working")
        print("\n🔧 Troubleshooting:")
        print("   • Check Flask application logs")
        print("   • Verify database connectivity")
        print("   • Restart Flask application if needed")
