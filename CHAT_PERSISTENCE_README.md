# Chat Persistence Feature

## Overview

The chat persistence feature allows users to continue their conversations with the sales bot even after refreshing the page or closing the browser. All chat messages are automatically saved to the database and restored when the user returns to the same session.

## Features

### ✅ **Automatic Message Saving**
- Every user message and bot response is automatically saved to the database
- Messages are stored with timestamps, stage information, and proper ordering
- No user action required - everything happens seamlessly in the background

### ✅ **Session Restoration**
- When a user returns to a chat session, their complete conversation history is loaded
- Bot state is restored to the correct stage and task
- Conversation continues exactly where it left off

### ✅ **Reset Functionality**
- Users can click the "Reset Chat" button to start fresh
- This clears all messages from the database for that session
- Resets the bot to the initial state
- Provides a clean slate for new conversations

### ✅ **Session Tracking Integration**
- Works seamlessly with existing email campaign tracking
- Maintains session IDs from email links
- Preserves analytics and conversion tracking

## Database Schema

### New Table: `chat_messages`

```sql
CREATE TABLE chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(100) NOT NULL,
    message_type VARCHAR(20) NOT NULL,  -- 'user' or 'assistant'
    content TEXT NOT NULL,
    stage VARCHAR(50),                  -- Current sales stage
    task VARCHAR(200),                  -- Current task within stage
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    message_order INTEGER NOT NULL,     -- Order of message in conversation
    FOREIGN KEY (session_id) REFERENCES chatbot_sessions (session_id)
);
```

### Indexes for Performance
- `idx_chat_messages_session_id` - Fast lookup by session
- `idx_chat_messages_order` - Fast ordering of messages

## API Endpoints

### Save Chat Message
```
POST /api/save-chat-message
Content-Type: application/json

{
    "session_id": "uuid-string",
    "message_type": "user|assistant", 
    "content": "message content",
    "stage": "opening|trust|discovery|demonstration|close",
    "task": "specific task name",
    "message_order": 0
}
```

### Get Chat History
```
GET /api/get-chat-history/{session_id}

Response:
{
    "success": true,
    "history": [
        {
            "role": "assistant",
            "content": "Hello! I'm Sarah...",
            "stage": "opening",
            "task": "greeting",
            "timestamp": "2024-01-01T12:00:00",
            "message_order": 0
        }
    ],
    "message_count": 1
}
```

### Reset Chat Session
```
POST /api/reset-chat-session
Content-Type: application/json

{
    "session_id": "uuid-string"
}
```

## Installation & Migration

### For New Installations
The chat persistence feature is automatically included when you create a new database using:
- `create_database.py`
- `fix_database_connection.py`

### For Existing Installations
Run the migration script to add the chat_messages table:

```bash
python migrate_chat_persistence.py
```

This script will:
1. Find all database files in your project
2. Check if the chat_messages table exists
3. Create the table and indexes if needed
4. Provide a summary of the migration

## Testing

### Automated Testing
Run the test suite to verify chat persistence is working:

```bash
python test_chat_persistence.py
```

This will test:
- Saving messages to database
- Retrieving chat history
- Message ordering
- Session reset functionality

### Manual Testing
1. Start a conversation with the sales bot
2. Have a few exchanges with the bot
3. Refresh the page or close/reopen the browser
4. Return to the same session URL
5. Verify the conversation continues where you left off
6. Click "Reset Chat" and verify everything is cleared

## How It Works

### Message Flow
1. **User sends message** → Saved to database with order number
2. **Bot responds** → Response saved to database with next order number
3. **Session tracking** → Stage and task information included with each message
4. **Page refresh** → History loaded from database and bot state restored

### Session Management
- Each chat session has a unique UUID
- Session IDs are preserved across page refreshes
- Email campaign sessions maintain their original session ID
- Bot state is reconstructed from the last message's stage/task information

### Performance Considerations
- Database indexes ensure fast message retrieval
- Messages are loaded only once per session start
- Minimal overhead during conversation (just saving messages)

## Troubleshooting

### Common Issues

**Messages not saving:**
- Check that the unified sales system is running
- Verify database connection is working
- Check browser console for API errors

**History not loading:**
- Ensure session ID is preserved in URL
- Check that chat_messages table exists
- Verify API endpoints are accessible

**Reset not working:**
- Check that reset API endpoint is responding
- Verify session ID is being passed correctly
- Check database permissions

### Debug Information
The chat interface shows debug information including:
- Current session ID (first 8 characters)
- Whether session was restored from database
- Current stage and task information
- Message counts and completion status

## Future Enhancements

Potential improvements for the chat persistence feature:
- Message search functionality
- Export conversation history
- Multiple conversation threads per user
- Message encryption for sensitive data
- Conversation analytics and insights
- Automatic session cleanup for old conversations
