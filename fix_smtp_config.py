#!/usr/bin/env python3
"""
Fix SMTP Configuration for Campaign Sending
===========================================
This script diagnoses and fixes SMTP configuration issues that are preventing
email campaigns from being sent.
"""

import os
import sys
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_smtp_config():
    """Test the current SMTP configuration"""
    print("🔍 Testing SMTP Configuration...")
    print("=" * 50)
    
    # Configuration from email_system/config.py
    config = {
        'MAIL_SERVER': 'mail.24seven.site',
        'MAIL_PORT': 465,
        'MAIL_USE_SSL': True,
        'MAIL_USE_TLS': False,
        'MAIL_USERNAME': '<EMAIL>',
        'MAIL_PASSWORD': 'M@kerere1',
        'MAIL_DEFAULT_SENDER': '<EMAIL>',
    }
    
    print(f"Server: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
    print(f"Username: {config['MAIL_USERNAME']}")
    print(f"SSL: {config['MAIL_USE_SSL']}, TLS: {config['MAIL_USE_TLS']}")
    
    try:
        # Create SSL context
        context = ssl.create_default_context()
        
        if config['MAIL_USE_SSL']:
            # SSL connection
            server = smtplib.SMTP_SSL(config['MAIL_SERVER'], config['MAIL_PORT'], context=context)
        else:
            # Regular connection with TLS
            server = smtplib.SMTP(config['MAIL_SERVER'], config['MAIL_PORT'])
            if config['MAIL_USE_TLS']:
                server.starttls(context=context)
        
        print("✅ Connection established")
        
        # Test authentication
        server.login(config['MAIL_USERNAME'], config['MAIL_PASSWORD'])
        print("✅ Authentication successful")
        
        # Send test email
        msg = MIMEMultipart()
        msg['From'] = config['MAIL_DEFAULT_SENDER']
        msg['To'] = config['MAIL_DEFAULT_SENDER']  # Send to ourselves
        msg['Subject'] = "SMTP Test - Campaign System"
        
        body = """
        <h2>SMTP Test Successful!</h2>
        <p>This email confirms that the SMTP configuration is working correctly.</p>
        <p>Email campaigns should now work properly.</p>
        <p><strong>Timestamp:</strong> {timestamp}</p>
        """.format(timestamp=str(os.popen('date').read().strip()))
        
        msg.attach(MIMEText(body, 'html'))
        
        server.send_message(msg)
        print("✅ Test email sent successfully")
        
        server.quit()
        print("\n🎉 SMTP configuration is working correctly!")
        print("Email campaigns should now work properly.")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Check if the password is correct")
        print("2. Check if the email account is active")
        print("3. Check if 2FA is enabled (may need app password)")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Check if the server address is correct")
        print("2. Check if the port is correct")
        print("3. Check firewall/network connectivity")
        return False
        
    except Exception as e:
        print(f"❌ SMTP test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_unified_system_smtp():
    """Fix SMTP configuration in the unified system"""
    try:
        print("\n🔧 Fixing Unified System SMTP Configuration...")
        print("=" * 50)
        
        # Import the unified system
        from unified_sales_system import app, test_smtp_connection
        
        with app.app_context():
            # Test current configuration
            success, message = test_smtp_connection()
            
            if success:
                print(f"✅ Unified system SMTP test passed: {message}")
                return True
            else:
                print(f"❌ Unified system SMTP test failed: {message}")
                
                # Try to fix by updating the email system
                try:
                    from email_system.config import get_email_config
                    config = get_email_config()
                    
                    print("\n📧 Current email system configuration:")
                    print(f"   Server: {config.get('MAIL_SERVER', 'Not set')}")
                    print(f"   Port: {config.get('MAIL_PORT', 'Not set')}")
                    print(f"   Username: {config.get('MAIL_USERNAME', 'Not set')}")
                    print(f"   Password: {'Set' if config.get('MAIL_PASSWORD') else 'Not set'}")
                    
                    # The configuration should be correct, so the issue might be elsewhere
                    print("\n⚠️ Configuration looks correct but test failed.")
                    print("This might be a network or server issue.")
                    
                except Exception as e:
                    print(f"❌ Error checking email system config: {e}")
                
                return False
                
    except Exception as e:
        print(f"❌ Error fixing unified system SMTP: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to test and fix SMTP configuration"""
    print("🚀 SMTP Configuration Fix Tool")
    print("=" * 50)
    
    # Test basic SMTP configuration
    smtp_works = test_smtp_config()
    
    if smtp_works:
        print("\n✅ Basic SMTP test passed!")
        
        # Test unified system integration
        unified_works = fix_unified_system_smtp()
        
        if unified_works:
            print("\n🎉 All tests passed! Email campaigns should work now.")
            print("\nNext steps:")
            print("1. Try creating a new campaign")
            print("2. Add some test contacts")
            print("3. Send the campaign")
        else:
            print("\n⚠️ Basic SMTP works but unified system integration has issues.")
            print("Check the unified system email configuration.")
    else:
        print("\n❌ Basic SMTP test failed!")
        print("Fix the SMTP configuration before testing campaigns.")
    
    return smtp_works

if __name__ == "__main__":
    main()
