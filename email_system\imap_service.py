"""
IMAP Service for 24Seven Assistants
===================================
Service to retrieve sent emails from IMAP server and sync with campaign data.
"""

import imaplib
import email
import ssl
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from email.header import decode_header
import re

logger = logging.getLogger(__name__)

class IMAPService:
    """IMAP service for retrieving sent emails"""
    
    def __init__(self, config: Dict):
        """Initialize IMAP service with configuration"""
        # IMAP server configuration (usually same domain as SMTP)
        self.imap_server = config.get('IMAP_SERVER') or config.get('MAIL_SERVER', 'mail.24seven.site')
        self.imap_port = config.get('IMAP_PORT', 993)  # Default IMAP SSL port
        self.use_ssl = config.get('IMAP_USE_SSL', True)
        self.username = config.get('MAIL_USERNAME')
        self.password = config.get('MAIL_PASSWORD')
        
        # Folder names (may vary by server)
        self.sent_folder = config.get('IMAP_SENT_FOLDER', 'Sent')
        self.inbox_folder = config.get('IMAP_INBOX_FOLDER', 'INBOX')
        
        # Validate configuration
        if not all([self.username, self.password]):
            raise ValueError("IMAP configuration incomplete. Need username and password.")
    
    def create_connection(self) -> imaplib.IMAP4:
        """Create and authenticate IMAP connection"""
        try:
            # Create IMAP connection
            if self.use_ssl:
                # Create SSL context
                context = ssl.create_default_context()
                server = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
            else:
                server = imaplib.IMAP4(self.imap_server, self.imap_port)
                server.starttls()
            
            # Authenticate
            server.login(self.username, self.password)
            
            logger.info(f"IMAP connection established to {self.imap_server}:{self.imap_port}")
            return server
            
        except Exception as e:
            logger.error(f"Failed to create IMAP connection: {str(e)}")
            raise
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test IMAP connection and authentication"""
        try:
            with self.create_connection() as server:
                # List folders to verify connection
                status, folders = server.list()
                if status == 'OK':
                    logger.info("IMAP connection test successful")
                    return True, f"Connection successful. Found {len(folders)} folders."
                else:
                    return False, "Failed to list folders"
                    
        except Exception as e:
            error_msg = f"IMAP connection test failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_folder_list(self) -> List[str]:
        """Get list of available folders"""
        try:
            with self.create_connection() as server:
                status, folders = server.list()
                if status == 'OK':
                    folder_names = []
                    for folder in folders:
                        # Parse folder name from IMAP response
                        folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else folder
                        # Extract folder name (usually the last quoted part)
                        match = re.search(r'"([^"]*)"$', folder_str)
                        if match:
                            folder_names.append(match.group(1))
                    return folder_names
                return []
                
        except Exception as e:
            logger.error(f"Failed to get folder list: {str(e)}")
            return []
    
    def get_sent_emails(self, limit: int = 50, days_back: int = 30) -> List[Dict]:
        """
        Retrieve sent emails from the Sent folder
        
        Args:
            limit: Maximum number of emails to retrieve
            days_back: How many days back to search
            
        Returns:
            List of email dictionaries
        """
        try:
            with self.create_connection() as server:
                # Try different common sent folder names
                sent_folders = [self.sent_folder, 'Sent', 'INBOX.Sent', 'Sent Items', 'Sent Messages']
                selected_folder = None
                
                for folder in sent_folders:
                    try:
                        status, count = server.select(folder)
                        if status == 'OK':
                            selected_folder = folder
                            logger.info(f"Selected sent folder: {folder}")
                            break
                    except:
                        continue
                
                if not selected_folder:
                    logger.warning("Could not find sent folder")
                    return []
                
                # Search for emails from the last N days
                since_date = (datetime.now() - timedelta(days=days_back)).strftime("%d-%b-%Y")
                search_criteria = f'(SINCE "{since_date}")'
                
                status, messages = server.search(None, search_criteria)
                if status != 'OK':
                    logger.warning("Search failed, trying to get all messages")
                    status, messages = server.search(None, 'ALL')
                
                if status != 'OK':
                    return []
                
                message_ids = messages[0].split()
                
                # Limit the number of messages
                if len(message_ids) > limit:
                    message_ids = message_ids[-limit:]  # Get most recent
                
                emails = []
                for msg_id in reversed(message_ids):  # Most recent first
                    try:
                        email_data = self._fetch_email(server, msg_id)
                        if email_data:
                            emails.append(email_data)
                    except Exception as e:
                        logger.error(f"Failed to fetch email {msg_id}: {str(e)}")
                        continue
                
                logger.info(f"Retrieved {len(emails)} sent emails")
                return emails
                
        except Exception as e:
            logger.error(f"Failed to get sent emails: {str(e)}")
            return []
    
    def _fetch_email(self, server: imaplib.IMAP4, msg_id: bytes) -> Optional[Dict]:
        """Fetch and parse a single email"""
        try:
            # Fetch email
            status, msg_data = server.fetch(msg_id, '(RFC822)')
            if status != 'OK':
                return None
            
            # Parse email
            email_body = msg_data[0][1]
            email_message = email.message_from_bytes(email_body)
            
            # Extract headers
            subject = self._decode_header(email_message['Subject'])
            from_addr = self._decode_header(email_message['From'])
            to_addr = self._decode_header(email_message['To'])
            date_str = email_message['Date']
            message_id = email_message['Message-ID']
            
            # Parse date
            try:
                email_date = email.utils.parsedate_to_datetime(date_str)
            except:
                email_date = datetime.now()
            
            # Extract body content
            body_text, body_html = self._extract_body(email_message)
            
            return {
                'message_id': message_id,
                'subject': subject,
                'from': from_addr,
                'to': to_addr,
                'date': email_date,
                'body_text': body_text,
                'body_html': body_html,
                'raw_size': len(email_body)
            }
            
        except Exception as e:
            logger.error(f"Failed to parse email: {str(e)}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """Decode email header"""
        if not header:
            return ""
        
        try:
            decoded_parts = decode_header(header)
            decoded_string = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding)
                    else:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += part
            
            return decoded_string
        except:
            return str(header)
    
    def _extract_body(self, email_message) -> Tuple[str, str]:
        """Extract text and HTML body from email"""
        body_text = ""
        body_html = ""
        
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    if content_type == "text/plain":
                        body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                content_type = email_message.get_content_type()
                if content_type == "text/plain":
                    body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    body_html = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        except Exception as e:
            logger.error(f"Failed to extract email body: {str(e)}")
        
        return body_text, body_html
    
    def search_sent_emails(self, 
                          search_term: str = None,
                          recipient: str = None,
                          subject_contains: str = None,
                          days_back: int = 30) -> List[Dict]:
        """
        Search sent emails with specific criteria
        
        Args:
            search_term: General search term
            recipient: Specific recipient email
            subject_contains: Text that should be in subject
            days_back: How many days back to search
            
        Returns:
            List of matching email dictionaries
        """
        try:
            with self.create_connection() as server:
                # Select sent folder
                status, count = server.select(self.sent_folder)
                if status != 'OK':
                    return []
                
                # Build search criteria
                criteria = []
                
                # Date range
                since_date = (datetime.now() - timedelta(days=days_back)).strftime("%d-%b-%Y")
                criteria.append(f'SINCE "{since_date}"')
                
                # Recipient filter
                if recipient:
                    criteria.append(f'TO "{recipient}"')
                
                # Subject filter
                if subject_contains:
                    criteria.append(f'SUBJECT "{subject_contains}"')
                
                # General search (in body)
                if search_term:
                    criteria.append(f'BODY "{search_term}"')
                
                search_string = ' '.join(criteria) if criteria else 'ALL'
                
                status, messages = server.search(None, search_string)
                if status != 'OK':
                    return []
                
                message_ids = messages[0].split()
                
                emails = []
                for msg_id in reversed(message_ids):  # Most recent first
                    try:
                        email_data = self._fetch_email(server, msg_id)
                        if email_data:
                            emails.append(email_data)
                    except Exception as e:
                        logger.error(f"Failed to fetch email {msg_id}: {str(e)}")
                        continue
                
                logger.info(f"Found {len(emails)} emails matching search criteria")
                return emails
                
        except Exception as e:
            logger.error(f"Failed to search sent emails: {str(e)}")
            return []
