#!/usr/bin/env python3
"""
24Seven Assistants Sales Department System Startup Script
=========================================================
Easy startup script for the complete sales department system.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🚀 24Seven Assistants                          ║
    ║            Complete Sales Department System                  ║
    ║                                                              ║
    ║  📧 SMTP Email Campaigns  📊 Sales Analytics                ║
    ║  🎯 Stage Tracking        💼 CRM Integration                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check if all requirements are installed"""
    print("🔍 Checking requirements...")
    
    try:
        import flask
        import sqlalchemy
        import plotly
        import jinja2
        print("✅ Core dependencies found")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("📦 Installing requirements...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed")

def check_environment():
    """Check environment configuration"""
    print("🔧 Checking environment configuration...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found, creating from example...")
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ .env file created from example")
            print("📝 Please edit .env file with your SMTP settings")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file found")
    
    return True

def initialize_database():
    """Initialize database with default data"""
    print("🗄️  Initializing database...")
    
    try:
        # Import here to avoid circular imports
        from sales_department_app import app, init_database
        
        with app.app_context():
            init_database()
        
        print("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def start_application():
    """Start the Flask application"""
    print("🚀 Starting 24Seven Assistants Sales Department System...")
    print("📍 Application will be available at: http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 60)
    
    try:
        from sales_department_app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Sales Department System stopped")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")

def show_system_info():
    """Show system information and features"""
    info = """
    📋 SYSTEM FEATURES:
    
    🎯 Sales Process Management:
       • 5-Stage Sales Pipeline (Opening → Trust → Discovery → Demonstration → Close)
       • Opportunity tracking with stage progression
       • Contact management with lead scoring
       • Activity logging (emails, calls, meetings, notes)
    
    📧 Email Campaign System:
       • Professional 24Seven Assistants templates
       • SMTP bulk email sending with tracking
       • Open/click/reply analytics
       • Campaign scheduling and management
    
    📊 Analytics Dashboard:
       • Interactive charts with Plotly.js
       • Sales funnel analysis
       • Stage duration and bottleneck detection
       • Email performance metrics
       • Real-time KPI tracking
    
    🔗 Integration Ready:
       • SambaNova AI API integration
       • Compatible with existing salesbot
       • RESTful API endpoints
       • Extensible architecture
    
    🛠️  QUICK START:
       1. Configure SMTP settings in .env file
       2. Add your first contacts
       3. Create email campaigns
       4. Monitor analytics dashboard
       5. Track opportunities through sales stages
    
    📚 DOCUMENTATION:
       • README.md - Complete setup guide
       • .env.example - Configuration template
       • Built-in help and tooltips
    """
    print(info)

def main():
    """Main startup function"""
    print_banner()
    
    # Check if we're in the right directory
    if not Path("sales_department_app.py").exists():
        print("❌ sales_department_app.py not found!")
        print("📁 Please run this script from the project directory")
        sys.exit(1)
    
    # Run checks
    check_requirements()
    
    if not check_environment():
        print("❌ Environment setup failed")
        sys.exit(1)
    
    if not initialize_database():
        print("❌ Database setup failed")
        sys.exit(1)
    
    # Show system information
    show_system_info()
    
    # Ask user if they want to start
    try:
        response = input("\n🚀 Start the Sales Department System? (y/n): ").lower().strip()
        if response in ['y', 'yes', '']:
            start_application()
        else:
            print("👋 Setup complete. Run 'python sales_department_app.py' to start later.")
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
