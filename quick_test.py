#!/usr/bin/env python3
"""Quick test of email templates"""

try:
    from email_system.email_templates import EmailTemplateManager
    print("✅ Import successful!")
    
    manager = EmailTemplateManager()
    print("✅ Manager created!")
    
    templates = manager.get_template_list()
    print(f"✅ Found {len(templates)} templates")
    
    print("🎉 ALL TESTS PASSED!")
    
except Exception as e:
    print(f"❌ Error: {e}")
