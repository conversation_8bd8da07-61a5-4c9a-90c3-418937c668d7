<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sent Emails - 24Seven Assistants</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .email-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .email-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
        }
        .email-body {
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
        }
        .email-meta {
            font-size: 0.9em;
            color: #6c757d;
        }
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .folder-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .btn-sync {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-sync:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-envelope me-2"></i>24Seven Assistants - Sent Emails
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Dashboard</a>
                <a class="nav-link" href="/campaigns"><i class="fas fa-paper-plane me-1"></i>Campaigns</a>
                <a class="nav-link" href="/analytics"><i class="fas fa-chart-bar me-1"></i>Analytics</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-paper-plane text-primary me-2"></i>
                    Sent Emails from IMAP Server
                </h1>
            </div>
        </div>

        <!-- Error Display -->
        {% if error %}
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Error:</strong> {{ error }}
            <hr>
            <p class="mb-0">
                <strong>Troubleshooting:</strong>
                <br>• Check IMAP server settings
                <br>• Verify email credentials
                <br>• Ensure IMAP is enabled on your email account
                <br>• Try the <a href="/emails/test-imap" class="alert-link">IMAP connection test</a>
            </p>
        </div>
        {% endif %}

        <!-- Controls and Stats -->
        <div class="row">
            <div class="col-md-8">
                <!-- Search and Filters -->
                <div class="search-box">
                    <form method="GET" action="/emails/sent">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search Emails</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ search_term }}" placeholder="Search subject, recipient, or content...">
                            </div>
                            <div class="col-md-3">
                                <label for="limit" class="form-label">Limit</label>
                                <select class="form-select" id="limit" name="limit">
                                    <option value="25" {% if limit == 25 %}selected{% endif %}>25 emails</option>
                                    <option value="50" {% if limit == 50 %}selected{% endif %}>50 emails</option>
                                    <option value="100" {% if limit == 100 %}selected{% endif %}>100 emails</option>
                                    <option value="200" {% if limit == 200 %}selected{% endif %}>200 emails</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="days_back" class="form-label">Days Back</label>
                                <select class="form-select" id="days_back" name="days_back">
                                    <option value="7" {% if days_back == 7 %}selected{% endif %}>7 days</option>
                                    <option value="30" {% if days_back == 30 %}selected{% endif %}>30 days</option>
                                    <option value="90" {% if days_back == 90 %}selected{% endif %}>90 days</option>
                                    <option value="365" {% if days_back == 365 %}selected{% endif %}>1 year</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>Search
                                </button>
                                <a href="/emails/sent" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                                <button type="button" class="btn btn-sync me-2" onclick="syncEmails()">
                                    <i class="fas fa-sync me-1"></i>Sync with Campaigns
                                </button>
                                <a href="/emails/test-imap" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-plug me-1"></i>Test IMAP
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-md-4">
                <!-- Stats -->
                <div class="stats-card">
                    <h5><i class="fas fa-chart-bar me-2"></i>Email Statistics</h5>
                    <div class="row text-center">
                        <div class="col-6">
                            <h3>{{ sent_emails|length }}</h3>
                            <small>Emails Found</small>
                        </div>
                        <div class="col-6">
                            <h3>{{ days_back }}</h3>
                            <small>Days Searched</small>
                        </div>
                    </div>
                </div>

                <!-- Available Folders -->
                {% if folders %}
                <div class="folder-list">
                    <h6><i class="fas fa-folder me-2"></i>Available Folders</h6>
                    <div class="small">
                        {% for folder in folders %}
                        <span class="badge bg-secondary me-1">{{ folder }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Sent Emails List -->
        <div class="row">
            <div class="col-12">
                {% if sent_emails %}
                <h4 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    Sent Emails ({{ sent_emails|length }} found)
                </h4>

                {% for email in sent_emails %}
                <div class="email-item">
                    <div class="email-header">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <i class="fas fa-envelope me-2"></i>
                                    {{ email.subject or 'No Subject' }}
                                </h6>
                                <div class="email-meta">
                                    <i class="fas fa-user me-1"></i>To: {{ email.to }}
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="email-meta">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ email.date.strftime('%Y-%m-%d %H:%M') if email.date else 'Unknown' }}
                                </div>
                                {% if email.message_id %}
                                <div class="email-meta">
                                    <i class="fas fa-fingerprint me-1"></i>
                                    <small>{{ email.message_id[:30] }}...</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="email-body">
                        {% if email.body_html %}
                        <div class="mb-2">
                            <strong>HTML Content:</strong>
                            <div class="border p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                                {{ email.body_html|safe }}
                            </div>
                        </div>
                        {% endif %}
                        {% if email.body_text %}
                        <div>
                            <strong>Text Content:</strong>
                            <pre class="border p-2 bg-light" style="max-height: 100px; overflow-y: auto; white-space: pre-wrap;">{{ email.body_text }}</pre>
                        </div>
                        {% endif %}
                        {% if not email.body_html and not email.body_text %}
                        <em class="text-muted">No content available</em>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Sent Emails Found</h4>
                    <p class="text-muted">
                        {% if search_term %}
                        No emails found matching your search criteria.
                        {% else %}
                        No sent emails found in the specified time range.
                        {% endif %}
                    </p>
                    <div class="mt-3">
                        <a href="/emails/test-imap" class="btn btn-outline-primary me-2">
                            <i class="fas fa-plug me-1"></i>Test IMAP Connection
                        </a>
                        <button type="button" class="btn btn-outline-success" onclick="syncEmails()">
                            <i class="fas fa-sync me-1"></i>Sync Emails
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sync Modal -->
    <div class="modal fade" id="syncModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-sync me-2"></i>Syncing Emails
                    </h5>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Syncing sent emails with campaign data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function syncEmails() {
            const modal = new bootstrap.Modal(document.getElementById('syncModal'));
            modal.show();
            
            fetch('/emails/sync-sent')
                .then(response => response.json())
                .then(data => {
                    modal.hide();
                    if (data.success) {
                        alert('Sync completed!\n\n' + data.message);
                        location.reload();
                    } else {
                        alert('Sync failed: ' + data.message);
                    }
                })
                .catch(error => {
                    modal.hide();
                    alert('Sync error: ' + error.message);
                });
        }
    </script>
</body>
</html>
