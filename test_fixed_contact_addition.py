#!/usr/bin/env python3
"""
Test Fixed Contact Addition
===========================
Test the fixed contact addition route based on documentation
"""

import os
import requests
from datetime import datetime

def setup_environment():
    """Set up environment variables"""
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'

def test_direct_contact_creation():
    """Test contact creation directly in Flask context"""
    try:
        print("🧪 Testing Direct Contact Creation")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            print("✅ Application context created")
            
            # Test the exact same way as the documentation shows
            contact = Contact(
                first_name='<PERSON>',
                last_name='Scof',
                email='<EMAIL>',
                phone='(*************',
                company='Test Company',
                job_title='CEO',
                source='manual_entry',
                status='new'
            )
            
            # Set defaults as per documentation
            contact.is_active = True
            contact.do_not_email = False
            
            # Check if contact already exists
            existing = Contact.query.filter_by(email='<EMAIL>').first()
            if existing:
                print(f"📝 Contact already exists: {existing.full_name}")
                print("   Updating existing contact...")
                existing.first_name = 'Alex'
                existing.last_name = 'Scof'
                existing.company = 'Test Company'
                existing.job_title = 'CEO'
                existing.phone = '(*************'
                existing.is_active = True
                existing.do_not_email = False
                db.session.commit()
                print("✅ Contact updated successfully!")
                return True
            else:
                print("📝 Creating new contact...")
                db.session.add(contact)
                db.session.commit()
                print("✅ Contact created successfully!")
                print(f"   ID: {contact.id}")
                print(f"   Name: {contact.full_name}")
                print(f"   Email: {contact.email}")
                return True
                
    except Exception as e:
        print(f"❌ Direct creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_form_submission():
    """Test the web form with minimal data"""
    try:
        print("\n🌐 Testing Web Form Submission")
        print("-" * 40)
        
        # Minimal form data as per documentation
        form_data = {
            'first_name': 'Alex',
            'last_name': 'Scof',
            'email': '<EMAIL>',
            'phone': '(*************',
            'company': 'Test Company',
            'job_title': 'CEO',
            'source': 'manual_entry'
        }
        
        print("📝 Submitting minimal contact form...")
        print(f"   Data: {form_data}")
        
        response = requests.post(
            'http://localhost:5000/contacts/add',
            data=form_data,
            timeout=30,
            allow_redirects=False
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 302:
            # Success - redirected
            redirect_location = response.headers.get('Location', '')
            print(f"✅ Form submitted successfully!")
            print(f"   Redirected to: {redirect_location}")
            return True
        elif response.status_code == 200:
            # Form returned - check for errors
            if 'error' in response.text.lower():
                print("❌ Form returned with errors")
                return False
            else:
                print("⚠️ Form returned but no clear error")
                return False
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to application")
        print("   Make sure the application is running")
        return False
    except Exception as e:
        print(f"❌ Web form test failed: {e}")
        return False

def verify_contact_exists():
    """Verify the contact was created"""
    try:
        print("\n🔍 Verifying Contact Exists")
        print("-" * 40)
        
        setup_environment()
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            contact = Contact.query.filter_by(email='<EMAIL>').first()
            
            if contact:
                print("✅ Contact found in database!")
                print(f"   ID: {contact.id}")
                print(f"   Name: {contact.full_name}")
                print(f"   Email: {contact.email}")
                print(f"   Company: {contact.company}")
                print(f"   Job Title: {contact.job_title}")
                print(f"   Phone: {contact.phone}")
                print(f"   Status: {contact.status}")
                print(f"   Active: {contact.is_active}")
                print(f"   Do Not Email: {contact.do_not_email}")
                return True
            else:
                print("❌ Contact not found in database")
                return False
                
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 TESTING FIXED CONTACT ADDITION")
    print("Based on CONTACTS_AND_CAMPAIGNS_SYSTEM_DOCUMENTATION.md")
    print("=" * 60)
    
    # Test 1: Direct creation
    direct_success = test_direct_contact_creation()
    
    # Test 2: Web form submission
    if direct_success:
        web_success = test_web_form_submission()
    else:
        web_success = False
    
    # Test 3: Verification
    verification_success = verify_contact_exists()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIXED CONTACT ADDITION TEST SUMMARY")
    print("=" * 60)
    print(f"Direct Creation: {'✅ SUCCESS' if direct_success else '❌ FAILED'}")
    print(f"Web Form Test: {'✅ SUCCESS' if web_success else '❌ FAILED'}")
    print(f"Contact Verification: {'✅ FOUND' if verification_success else '❌ NOT FOUND'}")
    
    if verification_success:
        print("\n🎉 CONTACT ADDITION IS NOW WORKING!")
        print("\n📋 Contact Details:")
        print("   Name: Alex Scof")
        print("   Email: <EMAIL>")
        print("   Company: Test Company")
        print("   Job Title: CEO")
        print("   Status: new")
        print("   Active: True")
        print("   Do Not Email: False")
        
        print("\n🚀 READY FOR EMAIL CAMPAIGNS!")
        print("=" * 40)
        print("Next steps:")
        print("1. Go to: http://localhost:5000/campaigns")
        print("2. Create a new campaign")
        print("3. Select 'All contacts' as recipients")
        print("4. Send the campaign")
        print("5. Check <EMAIL> for the email")
        print("\n✅ The email system is already working (SMTP confirmed)")
        print("✅ Contact system is now fixed")
        print("✅ Ready to test complete email campaign flow!")
    else:
        print("\n❌ CONTACT ADDITION STILL HAS ISSUES")
        print("Check the error messages above for details")
    
    return verification_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
