#!/usr/bin/env python3
"""
Email Campaign Workflow Demo
============================
Demonstrates the complete email campaign workflow based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md
"""

import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

def demo_complete_workflow():
    """Demonstrate the complete email campaign workflow"""
    print("🚀 Email Campaign Workflow Demo")
    print("Based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    print("=" * 60)
    
    try:
        # Import Flask app and models
        from unified_sales_system import app, db, EmailCampaign, EmailLog, Contact
        from email_system import create_email_system, get_email_config
        from email_system.config import print_email_config
        
        with app.app_context():
            print("✅ Flask app context created")
            
            # Step 1: Show configuration
            print("\n📧 Step 1: Email System Configuration")
            print("-" * 40)
            config = get_email_config()
            print_email_config(config)
            
            # Step 2: Initialize services
            print("\n🔧 Step 2: Initialize Email System Services")
            print("-" * 40)
            email_system = create_email_system(db_session=db.session)
            
            smtp_service = email_system['smtp_service']
            template_manager = email_system['template_manager']
            campaign_manager = email_system['campaign_manager']
            email_tracker = email_system['email_tracker']
            
            print("✅ Enhanced SMTP Service initialized")
            print("✅ Email Template Manager initialized")
            print("✅ Campaign Manager initialized")
            print("✅ Email Tracker initialized")
            
            # Step 3: Test connectivity
            print("\n🔌 Step 3: Test Email System Connectivity")
            print("-" * 40)
            success, message = smtp_service.test_connection()
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                return False
            
            # Step 4: Create test contacts
            print("\n👥 Step 4: Create Test Contacts")
            print("-" * 40)
            
            test_contacts = [
                {
                    'first_name': 'John',
                    'last_name': 'Smith',
                    'email': '<EMAIL>',  # Send to ourselves for testing
                    'company': 'Tech Solutions Inc',
                    'job_title': 'CTO',
                    'industry': 'technology',
                    'source': 'demo_workflow'
                },
                {
                    'first_name': 'Sarah',
                    'last_name': 'Johnson',
                    'email': '<EMAIL>',  # Send to ourselves for testing
                    'company': 'Marketing Pro LLC',
                    'job_title': 'Marketing Director',
                    'industry': 'marketing',
                    'source': 'demo_workflow'
                }
            ]
            
            created_contacts = []
            for contact_data in test_contacts:
                # Check if contact already exists
                existing_contact = Contact.query.filter_by(
                    email=contact_data['email'],
                    company=contact_data['company']
                ).first()
                
                if not existing_contact:
                    contact = Contact(**contact_data)
                    db.session.add(contact)
                    created_contacts.append(contact)
                else:
                    created_contacts.append(existing_contact)
            
            db.session.commit()
            print(f"✅ {len(created_contacts)} test contacts ready")
            
            # Step 5: Show available templates
            print("\n📝 Step 5: Available Email Templates")
            print("-" * 40)
            templates = template_manager.get_template_list()
            for template in templates:
                print(f"   • {template['name']}: {template['display_name']}")
                print(f"     Subject: {template['subject']}")
            
            # Step 6: Create campaign
            print("\n📧 Step 6: Create Email Campaign")
            print("-" * 40)
            
            campaign = campaign_manager.create_campaign(
                name="Demo Technology Sector Outreach",
                template_name="introduction",
                target_filters={'industry': 'technology', 'source': 'demo_workflow'},
                scheduled_at=datetime.utcnow() + timedelta(minutes=1),
                created_by="demo_system"
            )
            
            print(f"✅ Campaign created: '{campaign.name}' (ID: {campaign.id})")
            print(f"   Template: {campaign.template_name}")
            print(f"   Subject: {campaign.subject}")
            print(f"   Status: {campaign.status}")
            
            # Step 7: Prepare campaign
            print("\n🎯 Step 7: Prepare Campaign (Target Contacts)")
            print("-" * 40)
            
            success, message = campaign_manager.prepare_campaign(campaign.id)
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                return False
            
            # Show campaign status
            status = campaign_manager.get_campaign_status(campaign.id)
            if status:
                print(f"   Total recipients: {status['total_logs']}")
                print(f"   Campaign status: {status['campaign']['status']}")
            
            # Step 8: Send campaign
            print("\n📤 Step 8: Send Email Campaign")
            print("-" * 40)
            
            success, message = campaign_manager.send_campaign(
                campaign_id=campaign.id,
                batch_size=2  # Small batch for demo
            )
            
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                return False
            
            # Step 9: Check final status
            print("\n📊 Step 9: Campaign Results")
            print("-" * 40)
            
            final_status = campaign_manager.get_campaign_status(campaign.id)
            if final_status:
                campaign_data = final_status['campaign']
                print(f"   Campaign Status: {campaign_data['status']}")
                print(f"   Total Recipients: {campaign_data['total_recipients']}")
                print(f"   Emails Sent: {campaign_data['emails_sent']}")
                print(f"   Progress: {final_status['progress_percent']:.1f}%")
                
                if final_status['pending_logs'] == 0:
                    print("✅ Campaign completed successfully!")
                else:
                    print(f"   Pending: {final_status['pending_logs']} emails")
            
            # Step 10: Show email logs
            print("\n📋 Step 10: Email Logs")
            print("-" * 40)
            
            email_logs = EmailLog.query.filter_by(campaign_id=campaign.id).all()
            for log in email_logs:
                print(f"   • {log.recipient_email}: {log.status}")
                if log.sent_at:
                    print(f"     Sent at: {log.sent_at}")
                if log.message_id:
                    print(f"     Message ID: {log.message_id}")
            
            print("\n🎉 Email Campaign Workflow Demo Completed Successfully!")
            print("=" * 60)
            print("The enhanced email system is working according to the documentation.")
            print("Key improvements implemented:")
            print("• Enhanced SMTP service with IMAP integration")
            print("• Automatic saving to sent folder")
            print("• Proper error handling and logging")
            print("• Bulk email sending with rate limiting")
            print("• Campaign management with status tracking")
            print("• Template system with personalization")
            
            return True
            
    except Exception as e:
        print(f"❌ Demo workflow failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main demo function"""
    success = demo_complete_workflow()
    
    if success:
        print("\n✅ Demo completed successfully!")
        print("The email campaign system is now working according to EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    else:
        print("\n❌ Demo failed. Please check the configuration and fix any issues.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
