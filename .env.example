# 24Seven Assistants Sales Department Configuration
# Copy this file to .env and update with your actual values

# Flask Configuration
FLASK_CONFIG=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///sales_department.db
# For PostgreSQL: postgresql://username:password@localhost/sales_department

# Email/SMTP Configuration for 24Seven Assistants
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_DEFAULT_SENDER=<EMAIL>

# SambaNova AI Configuration (from your existing setup)
SAMBA_API_KEY=76273dc7-7c75-417f-8cfe-ef88ad56db78
SAMBA_BASE_URL=https://api.sambanova.ai/v1
SAMBA_MODEL=Meta-Llama-3.3-70B-Instruct
SAMBA_TEMPERATURE=0.3

# Redis Configuration (for Celery background tasks)
REDIS_URL=redis://localhost:6379/0

# Application URLs
BASE_URL=http://localhost:5000

# Email Campaign Settings
EMAIL_BATCH_SIZE=50
EMAIL_DELAY_SECONDS=2

# Analytics Settings
ANALYTICS_RETENTION_DAYS=365
DASHBOARD_REFRESH_INTERVAL=300

# File Upload Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=sales_department.log
