#!/usr/bin/env python3
"""
Debug Analytics Data
===================
This script checks the analytics data for <NAME_EMAIL>
and verifies that session tracking is working properly.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def check_contact_data():
    """Check contact <NAME_EMAIL>"""
    print("🔍 Checking Contact Data")
    print("=" * 40)
    
    # Check if contact exists
    try:
        response = requests.get(f"{BASE_URL}/contacts")
        if "<EMAIL>" in response.text:
            print("✅ Contact <EMAIL> found in contacts page")
        else:
            print("❌ Contact <EMAIL> NOT found in contacts page")
    except Exception as e:
        print(f"❌ Error checking contacts: {e}")

def check_session_data():
    """Check session data"""
    print("\n🔍 Checking Session Data")
    print("=" * 40)
    
    # Test session tracking
    session_data = {
        "session_id": "4f3b1f41-38bc-4fef-bf04-9b802c77af6",
        "stage": "trust",
        "task": "building_rapport",
        "contact_name": "allan scof",
        "contact_email": "<EMAIL>",
        "action": "stage_progression",
        "message_count": 10,
        "user_message": "tell me more about your services",
        "bot_response": "Let me share our LocalCafe success story..."
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/track-session", json=session_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Session tracking successful")
            print(f"   📝 Contact ID: {result.get('contact_id')}")
            print(f"   🎯 Stage: {result.get('stage')}")
            print(f"   💬 Total Messages: {result.get('total_messages')}")
            print(f"   📊 Engagement: {result.get('engagement_level')}")
        else:
            print(f"❌ Session tracking failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error tracking session: {e}")

def check_analytics_data():
    """Check analytics data"""
    print("\n🔍 Checking Analytics Data")
    print("=" * 40)
    
    # Check session analytics
    try:
        response = requests.get(f"{BASE_URL}/analytics/sessions")
        if response.status_code == 200:
            print("✅ Session analytics page accessible")
            if "alscof" in response.text or "trust" in response.text:
                print("✅ Session data appears in analytics")
            else:
                print("⚠️ Session data may not be visible in analytics")
        else:
            print(f"❌ Session analytics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking session analytics: {e}")
    
    # Check comprehensive analytics
    try:
        response = requests.get(f"{BASE_URL}/analytics/comprehensive")
        if response.status_code == 200:
            print("✅ Comprehensive analytics page accessible")
            if "trust" in response.text.lower():
                print("✅ Trust stage data appears in comprehensive analytics")
            else:
                print("⚠️ Trust stage data may not be visible in comprehensive analytics")
        else:
            print(f"❌ Comprehensive analytics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking comprehensive analytics: {e}")

def check_database_direct():
    """Check database data directly via API"""
    print("\n🔍 Checking Database Data")
    print("=" * 40)
    
    # Try to get contact details
    try:
        response = requests.get(f"{BASE_URL}/contacts/1")
        if response.status_code == 200:
            print("✅ Contact ID 1 accessible")
            if "<EMAIL>" in response.text:
                print("✅ Contact email matches")
            if "trust" in response.text.lower():
                print("✅ Trust stage mentioned in contact details")
            else:
                print("⚠️ Trust stage not visible in contact details")
        else:
            print(f"❌ Contact details failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking contact details: {e}")

def test_new_session():
    """Test creating a new session for the contact"""
    print("\n🔍 Testing New Session Creation")
    print("=" * 40)
    
    # Create a new session with more data
    session_data = {
        "session_id": "debug-session-" + str(int(time.time())),
        "stage": "discovery",
        "task": "understanding_needs",
        "contact_name": "allan scof",
        "contact_email": "<EMAIL>",
        "action": "stage_progression",
        "message_count": 15,
        "user_message": "I need help with customer service automation",
        "bot_response": "Great! Let me understand your specific needs...",
        "conversion_completed": False
    }
    
    try:
        import time
        response = requests.post(f"{BASE_URL}/api/track-session", json=session_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ New session created successfully")
            print(f"   📝 Session ID: {session_data['session_id']}")
            print(f"   👤 Contact ID: {result.get('contact_id')}")
            print(f"   🎯 Stage: {result.get('stage')}")
            print(f"   💬 Messages: {result.get('total_messages')}")
            return session_data['session_id']
        else:
            print(f"❌ New session creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error creating new session: {e}")
        return None

def main():
    """Main debug function"""
    print("🐛 ANALYTICS DEBUG TOOL")
    print("=" * 50)
    print("Checking analytics data for contact: <EMAIL>")
    print("Session ID: 4f3b1f41-38bc-4fef-bf04-9b802c77af6")
    print("=" * 50)
    
    # Run all checks
    check_contact_data()
    check_session_data()
    check_analytics_data()
    check_database_direct()
    
    # Test new session
    new_session_id = test_new_session()
    
    print("\n🎯 SUMMARY")
    print("=" * 40)
    print("1. Check the analytics dashboards:")
    print(f"   📊 Comprehensive: {BASE_URL}/analytics/comprehensive")
    print(f"   💬 Sessions: {BASE_URL}/analytics/sessions")
    print(f"   👤 Contact Details: {BASE_URL}/contacts/1")
    
    if new_session_id:
        print(f"\n2. New test session created: {new_session_id}")
        print("   This should appear in analytics within a few seconds")
    
    print("\n3. If data still doesn't appear, check:")
    print("   - Database connection")
    print("   - Analytics data refresh")
    print("   - Template rendering")

if __name__ == "__main__":
    import time
    main()
