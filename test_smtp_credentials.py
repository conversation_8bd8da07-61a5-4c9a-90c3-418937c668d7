#!/usr/bin/env python3
"""
Test SMTP Credentials
=====================
Test SMTP credentials directly to diagnose authentication issues
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_smtp_credentials():
    """Test SMTP credentials directly"""
    print("🔐 Testing SMTP Credentials")
    print("=" * 50)
    
    # Configuration
    smtp_server = "mail.24seven.site"
    smtp_port = 465
    username = "<EMAIL>"
    password = "M@kerere1"
    
    print(f"Server: {smtp_server}:{smtp_port}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    try:
        # Test 1: Basic connection
        print("🔗 Step 1: Testing basic connection...")
        context = ssl.create_default_context()
        
        with smtplib.SMTP_SSL(smtp_server, smtp_port, context=context) as server:
            print("✅ SSL connection established")
            
            # Test 2: Authentication
            print("🔐 Step 2: Testing authentication...")
            server.login(username, password)
            print("✅ Authentication successful")
            
            # Test 3: Send test email
            print("📧 Step 3: Sending test email...")
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = username
            msg['To'] = username  # Send to self
            msg['Subject'] = "SMTP Test - 24Seven Assistants"
            
            body = """
            This is a test email to verify SMTP configuration.
            
            If you receive this email, the SMTP settings are working correctly.
            
            Best regards,
            24Seven Assistants Email System
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server.send_message(msg)
            print("✅ Test email sent successfully")
            
        print("\n🎉 ALL SMTP TESTS PASSED!")
        print("The SMTP configuration is working correctly.")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Check if the password is correct")
        print("2. Check if the email account exists")
        print("3. Check if SMTP is enabled for this account")
        print("4. Check if two-factor authentication is enabled")
        print("5. Try using an app-specific password if 2FA is enabled")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Check if the server address is correct")
        print("2. Check if the port is correct (465 for SSL)")
        print("3. Check firewall/network connectivity")
        print("4. Try using port 587 with STARTTLS instead")
        return False
        
    except smtplib.SMTPException as e:
        print(f"❌ SMTP error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_configurations():
    """Test alternative SMTP configurations"""
    print("\n🔄 Testing Alternative Configurations")
    print("=" * 50)
    
    configurations = [
        {
            'name': 'SMTP with STARTTLS (Port 587)',
            'server': 'mail.24seven.site',
            'port': 587,
            'use_ssl': False,
            'use_tls': True
        },
        {
            'name': 'SMTP without SSL/TLS (Port 25)',
            'server': 'mail.24seven.site',
            'port': 25,
            'use_ssl': False,
            'use_tls': False
        }
    ]
    
    username = "<EMAIL>"
    password = "M@kerere1"
    
    for config in configurations:
        print(f"\n📧 Testing: {config['name']}")
        print(f"   Server: {config['server']}:{config['port']}")
        print(f"   SSL: {config['use_ssl']}, TLS: {config['use_tls']}")
        
        try:
            if config['use_ssl']:
                # SSL connection
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(config['server'], config['port'], context=context)
            else:
                # Regular connection
                server = smtplib.SMTP(config['server'], config['port'])
                
                if config['use_tls']:
                    server.starttls()
            
            print("   ✅ Connection established")
            
            # Test authentication
            server.login(username, password)
            print("   ✅ Authentication successful")
            
            server.quit()
            print(f"   🎉 {config['name']} works!")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
            continue
    
    print("\n❌ All alternative configurations failed")
    return False

def main():
    """Main test function"""
    print("🧪 SMTP Credentials Test")
    print("Based on EMAIL_CAMPAIGN_SYSTEM_DOCUMENTATION.md")
    print("=" * 60)
    
    # Test primary configuration
    primary_success = test_smtp_credentials()
    
    # If primary fails, test alternatives
    if not primary_success:
        alternative_success = test_alternative_configurations()
    else:
        alternative_success = True
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SMTP TEST SUMMARY")
    print("=" * 60)
    
    if primary_success:
        print("✅ Primary SMTP configuration (SSL port 465) works!")
        print("\nThe email campaign system should work correctly.")
        print("If campaigns are still failing, check other components.")
    elif alternative_success:
        print("⚠️ Primary configuration failed but alternative works!")
        print("You may need to update the email configuration.")
    else:
        print("❌ All SMTP configurations failed!")
        print("\nPossible issues:")
        print("1. Incorrect credentials")
        print("2. Email server configuration changed")
        print("3. Network/firewall issues")
        print("4. Email account suspended or disabled")
        
    return primary_success or alternative_success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
