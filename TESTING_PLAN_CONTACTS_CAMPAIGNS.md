# Testing Plan: Contacts and Campaigns Creation & Deletion

## 🎯 Testing Objectives
Verify that the fixed contacts and campaigns system properly:
1. **Creates contacts** with all documented fields and validation
2. **Creates campaigns** with proper configuration and targeting
3. **Deletes contacts** with complete cleanup of all related data
4. **Deletes campaigns** with complete cleanup of all related data
5. **Handles bulk operations** safely and efficiently
6. **Maintains data integrity** throughout all operations

## 🧪 Test Environment
- **Server**: http://localhost:5000 (Flask development server)
- **Database**: SQLite (unified_sales.db)
- **Browser**: Any modern browser for web interface testing

## 📋 Test Cases

### 1. Contact Creation Tests

#### Test 1.1: Basic Contact Creation
**Objective**: Verify basic contact creation with required fields
**Steps**:
1. Navigate to http://localhost:5000/contacts/add
2. Fill in required fields:
   - First Name: "John"
   - Last Name: "Doe" 
   - Email: "<EMAIL>"
3. Submit form
4. Verify contact appears in contacts list
5. Check that contact has proper default values

**Expected Result**: Contact created successfully with proper validation

#### Test 1.2: Extended Contact Creation
**Objective**: Test all documented fields from the enhanced Contact model
**Steps**:
1. Navigate to http://localhost:5000/contacts/add
2. Fill in all available fields:
   - Basic: First Name, Last Name, Email, Phone, Company, Job Title
   - Extended: Website, LinkedIn URL, Industry, Company Size
   - Preferences: Preferred Contact Method, Timezone, Best Contact Time
   - Sales: Lead Score, Estimated Budget, Decision Maker status
   - Notes: Tags, Pain Points, Current Solution
3. Submit form
4. View contact details to verify all fields saved correctly

**Expected Result**: All fields saved and displayed correctly

#### Test 1.3: Email Uniqueness Validation
**Objective**: Verify email uniqueness constraint
**Steps**:
1. Create a contact with email "<EMAIL>"
2. Try to create another contact with same email
3. Verify error message appears
4. Confirm first contact still exists

**Expected Result**: Duplicate email rejected with clear error message

### 2. Campaign Creation Tests

#### Test 2.1: Basic Campaign Creation
**Objective**: Verify basic campaign creation
**Steps**:
1. Navigate to http://localhost:5000/campaigns/create
2. Fill in required fields:
   - Name: "Test Campaign"
   - Subject: "Test Email Subject"
   - Template: Select available template
3. Configure basic settings
4. Submit form
5. Verify campaign appears in campaigns list

**Expected Result**: Campaign created successfully

#### Test 2.2: Campaign with Contact Groups
**Objective**: Test campaign targeting with contact groups
**Steps**:
1. Create a contact group with test contacts
2. Create a new campaign
3. Set recipient criteria to target the created group
4. Verify campaign shows correct recipient count
5. Check that campaign statistics are initialized properly

**Expected Result**: Campaign correctly targets group members

### 3. Contact Deletion Tests

#### Test 3.1: Single Contact Deletion
**Objective**: Verify complete contact deletion with all related data
**Steps**:
1. Create a test contact
2. Add some activities/interactions for the contact
3. Delete the contact using the delete button
4. Verify contact is removed from contacts list
5. Check that all related data is cleaned up

**Expected Result**: Contact and ALL related data deleted completely

#### Test 3.2: Contact Deletion with Campaign Links
**Objective**: Test deletion of contacts linked to campaigns
**Steps**:
1. Create a contact and link to a campaign
2. Try to delete contact (should show warning)
3. Use "Force Delete" option
4. Verify contact deleted and campaign statistics updated

**Expected Result**: Contact deleted with proper campaign statistics adjustment

#### Test 3.3: Bulk Contact Deletion
**Objective**: Test bulk deletion functionality
**Steps**:
1. Create multiple test contacts
2. Select multiple contacts in the contacts list
3. Use bulk delete function
4. Verify all selected contacts are deleted
5. Check that related data is cleaned up for all contacts

**Expected Result**: All selected contacts and related data deleted

### 4. Campaign Deletion Tests

#### Test 4.1: Single Campaign Deletion
**Objective**: Verify complete campaign deletion
**Steps**:
1. Create a test campaign with some contacts
2. Delete the campaign
3. Verify campaign is removed from campaigns list
4. Check that all related data (logs, failures, etc.) is cleaned up
5. Verify contacts are not deleted (only campaign references removed)

**Expected Result**: Campaign and related data deleted, contacts preserved

#### Test 4.2: Campaign Deletion with Active Sessions
**Objective**: Test deletion of campaigns with active chatbot sessions
**Steps**:
1. Create campaign with contacts that have active sessions
2. Delete the campaign
3. Verify sessions are properly handled
4. Check that session data is preserved but campaign references removed

**Expected Result**: Campaign deleted, sessions preserved with null campaign references

### 5. Data Integrity Tests

#### Test 5.1: Database Consistency Check
**Objective**: Verify database remains consistent after operations
**Steps**:
1. Perform various create/delete operations
2. Check for orphaned records in related tables
3. Verify foreign key constraints are maintained
4. Check that statistics are accurate

**Expected Result**: No orphaned records, all constraints maintained

#### Test 5.2: Error Handling
**Objective**: Test system behavior under error conditions
**Steps**:
1. Try to delete non-existent contacts/campaigns
2. Submit forms with invalid data
3. Test with missing required fields
4. Verify proper error messages and system stability

**Expected Result**: Graceful error handling with informative messages

### 6. API Endpoint Tests

#### Test 6.1: Contact Count API
**Objective**: Test the /api/contacts/count endpoint
**Steps**:
1. Create several test contacts with different statuses
2. Call API with various filter parameters
3. Verify counts are accurate
4. Test both GET and POST methods

**Expected Result**: API returns accurate contact counts

#### Test 6.2: Chatbot Session Tracking
**Objective**: Test the /api/chatbot/session endpoint
**Steps**:
1. Send test session data to the API
2. Verify session tracking works correctly
3. Check that activities are created properly

**Expected Result**: Session tracking works and creates proper records

## 🔍 Manual Testing Checklist

### Pre-Testing Setup
- [ ] Server is running at http://localhost:5000
- [ ] Database is accessible and tables are created
- [ ] Browser is open and ready

### Contact Management
- [ ] Create contact with required fields only
- [ ] Create contact with all extended fields
- [ ] Test email uniqueness validation
- [ ] Edit existing contact
- [ ] View contact details page
- [ ] Delete single contact
- [ ] Test bulk contact deletion
- [ ] Verify contact search/filtering

### Campaign Management  
- [ ] Create basic campaign
- [ ] Create campaign with group targeting
- [ ] Edit campaign settings
- [ ] View campaign details and statistics
- [ ] Delete single campaign
- [ ] Test bulk campaign deletion

### Group Management
- [ ] Create contact group
- [ ] Add contacts to group
- [ ] Remove contacts from group
- [ ] Delete group
- [ ] Use group for campaign targeting

### Error Scenarios
- [ ] Try duplicate email addresses
- [ ] Submit forms with missing required fields
- [ ] Delete non-existent records
- [ ] Test with invalid data types

### Data Verification
- [ ] Check that deleted contacts don't appear in lists
- [ ] Verify related data is cleaned up after deletions
- [ ] Confirm statistics are updated correctly
- [ ] Check that foreign key relationships are maintained

## 📊 Success Criteria

### ✅ All tests should demonstrate:
1. **Complete Functionality**: All CRUD operations work as expected
2. **Data Integrity**: No orphaned records or broken relationships
3. **Proper Validation**: Invalid data is rejected with clear messages
4. **Complete Cleanup**: Deletions remove ALL related data
5. **User Experience**: Clear feedback and intuitive interface
6. **Performance**: Operations complete in reasonable time
7. **Error Handling**: Graceful handling of edge cases

## 🚨 Critical Issues to Watch For
- Orphaned records in related tables after deletions
- Incomplete validation allowing invalid data
- Missing error messages or unclear feedback
- Performance issues with bulk operations
- Database constraint violations
- Inconsistent statistics or counts

## 📝 Test Results Documentation
Document any issues found during testing:
- Issue description
- Steps to reproduce
- Expected vs actual behavior
- Severity level
- Screenshots if applicable

This comprehensive testing plan ensures the contacts and campaigns system works reliably and safely handles all creation and deletion operations.
