{% extends "base.html" %}

{% block title %}Email Campaigns - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-envelope text-primary"></i> Email Campaigns</h1>
    <div class="btn-group" role="group">
        <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Campaign
        </a>
        <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
            <span class="visually-hidden">Toggle Dropdown</span>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="{{ url_for('create_campaign') }}">
                <i class="fas fa-plus"></i> Standard Campaign
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="showCSVUploadModal()">
                <i class="fas fa-file-csv"></i> Campaign with CSV Upload
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="{{ url_for('upload_contacts') }}">
                <i class="fas fa-users"></i> Upload Contacts Only
            </a></li>
        </ul>
    </div>
</div>

<!-- Campaign Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ campaigns|length }}</div>
                <div class="metric-label">Total Campaigns</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">{{ campaigns|selectattr('status', 'equalto', 'sending')|list|length }}</div>
                <div class="metric-label">Sending</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">{{ campaigns|selectattr('status', 'equalto', 'completed')|list|length }}</div>
                <div class="metric-label">Completed</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ campaigns|selectattr('status', 'equalto', 'paused')|list|length }}</div>
                <div class="metric-label">Paused</div>
            </div>
        </div>
    </div>
</div>

<!-- Campaigns List -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Campaign List</h5>
            </div>
            <div class="card-body">
                {% if campaigns %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Campaign</th>
                                    <th>Status</th>
                                    <th>Recipients</th>
                                    <th>Sent</th>
                                    <th>Daily Limit</th>
                                    <th>Click Rate</th>
                                    <th>Conversions</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for campaign in campaigns %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ campaign.name }}</strong>
                                            <small class="text-muted d-block">{{ campaign.subject }}</small>
                                            <span class="badge bg-light text-dark">{{ campaign.template_name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if campaign.status == 'draft' %}bg-secondary
                                            {% elif campaign.status == 'sending' %}bg-warning
                                            {% elif campaign.status == 'paused' %}bg-info
                                            {% elif campaign.status == 'completed' %}bg-success
                                            {% elif campaign.status == 'failed' %}bg-danger
                                            {% else %}bg-secondary{% endif %}">
                                            {{ campaign.status.title() }}
                                        </span>
                                        {% if campaign.status in ['sending', 'paused'] and campaign.total_recipients %}
                                            <div class="progress mt-1" style="height: 4px;">
                                                <div class="progress-bar
                                                    {% if campaign.status == 'paused' %}bg-info
                                                    {% else %}bg-warning{% endif %}"
                                                    role="progressbar"
                                                    style="width: {{ (campaign.emails_sent / campaign.total_recipients * 100) if campaign.total_recipients > 0 else 0 }}%">
                                                </div>
                                            </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ campaign.total_recipients or 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ campaign.emails_sent or 0 }}</span>
                                        {% if campaign.emails_failed and campaign.emails_failed > 0 %}
                                            <span class="badge bg-danger ms-1">{{ campaign.emails_failed }} failed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ campaign.daily_send_limit or 100 }}/day</span>
                                        {% if campaign.status in ['sending', 'paused'] %}
                                            <small class="text-muted d-block">Today: {{ campaign.emails_sent_today or 0 }}</small>
                                            {% if campaign.status == 'paused' and campaign.next_batch_date %}
                                            <small class="text-info d-block">Next: {{ campaign.next_batch_date.strftime('%m/%d') }}</small>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                                            <span class="badge bg-info">{{ "%.1f"|format(campaign.email_to_click_rate) }}%</span>
                                            <small class="text-muted d-block">{{ campaign.chatbot_links_clicked or 0 }} clicks</small>
                                        {% else %}
                                            <span class="badge bg-secondary">0%</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                                            <span class="badge bg-success">{{ campaign.conversions_achieved or 0 }}</span>
                                            <small class="text-muted d-block">{{ "%.1f"|format(campaign.overall_conversion_rate) }}% rate</small>
                                        {% else %}
                                            <span class="badge bg-secondary">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ campaign.created_at.strftime('%m/%d/%Y') if campaign.created_at else 'N/A' }}
                                        </small>
                                        {% if campaign.started_at %}
                                            <small class="text-muted d-block">
                                                Started: {{ campaign.started_at.strftime('%m/%d %H:%M') }}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="viewCampaign({{ campaign.id }})" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            {% if campaign.status == 'draft' %}
                                                <form method="POST" action="{{ url_for('send_campaign', campaign_id=campaign.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-success" title="Send Campaign"
                                                            onclick="return confirm('Are you sure you want to send this campaign?')">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </button>
                                                </form>
                                            {% elif campaign.status == 'sending' %}
                                                <form method="POST" action="{{ url_for('pause_campaign', campaign_id=campaign.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-warning" title="Pause Campaign"
                                                            onclick="return confirm('Are you sure you want to pause this campaign?')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            {% elif campaign.status == 'paused' %}
                                                <form method="POST" action="{{ url_for('resume_campaign', campaign_id=campaign.id) }}" style="display: inline;">
                                                    <button type="submit" class="btn btn-outline-success" title="Resume Campaign">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            {% endif %}

                                            {% if campaign.status in ['completed', 'draft'] %}
                                                <button class="btn btn-outline-info" onclick="duplicateCampaign({{ campaign.id }})" title="Duplicate Campaign">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Email Campaigns</h4>
                        <p class="text-muted">Create your first email campaign to start reaching out to prospects.</p>
                        <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Your First Campaign
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Campaign Templates Info -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> Available Email Templates</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-handshake text-primary"></i> Enhanced Introduction Template</h6>
                            <p class="small text-muted mb-2">Professional introduction with enhanced chat interface featuring Sarah</p>
                            <ul class="small text-muted mb-0">
                                <li>Interactive chat preview with Sarah's message</li>
                                <li>Smaller, flexible pricing cards (28% more space efficient)</li>
                                <li>Email client compatible design</li>
                                <li>Direct chat engagement (no call pressure)</li>
                                <li>Mobile responsive layout</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-reply text-success"></i> Follow-up Template</h6>
                            <p class="small text-muted mb-2">Gentle follow-up for non-responders</p>
                            <ul class="small text-muted mb-0">
                                <li>Brief and respectful</li>
                                <li>Value reinforcement</li>
                                <li>Easy response options</li>
                                <li>Professional tone</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSV Upload Modal -->
<div class="modal fade" id="csvUploadModal" tabindex="-1" aria-labelledby="csvUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="csvUploadModalLabel">
                    <i class="fas fa-file-csv text-primary"></i> Create Campaign with CSV Upload
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="csvCampaignForm" method="POST" action="{{ url_for('create_campaign_with_csv') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Campaign Details -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Campaign Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="csv_campaign_name" class="form-label">Campaign Name *</label>
                                        <input type="text" class="form-control" id="csv_campaign_name" name="name" required
                                               placeholder="e.g., PPDA Suppliers Campaign">
                                    </div>

                                    <div class="mb-3">
                                        <label for="csv_template" class="form-label">Email Template *</label>
                                        <select class="form-select" id="csv_template" name="template" required>
                                            <option value="">Select Template</option>
                                            <option value="introduction">Enhanced Introduction Template</option>
                                            <option value="follow_up">Follow-up Template</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="csv_target_audience" class="form-label">Target Audience</label>
                                        <input type="text" class="form-control" id="csv_target_audience" name="target_audience"
                                               placeholder="e.g., PPDA Suppliers, New Prospects">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- CSV Upload -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-upload"></i> Upload Contacts</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="csv_file" class="form-label">CSV File *</label>
                                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                        <div class="form-text">Upload a CSV file with contact information</div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Group Options</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="group_option" id="csv_create_group" value="create" checked>
                                            <label class="form-check-label" for="csv_create_group">
                                                Create new group
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="group_option" id="csv_no_group" value="none">
                                            <label class="form-check-label" for="csv_no_group">
                                                No group (add to general contacts)
                                            </label>
                                        </div>
                                    </div>

                                    <div id="csv_group_name_section">
                                        <div class="mb-3">
                                            <label for="csv_group_name" class="form-label">Group Name</label>
                                            <input type="text" class="form-control" id="csv_group_name" name="group_name"
                                                   placeholder="e.g., 100PPDA, Q1 Prospects">
                                            <div class="form-text">Name for the new contact group</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Campaign Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cogs"></i> Campaign Settings</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="csv_daily_limit" class="form-label">Daily Send Limit</label>
                                        <select class="form-select" id="csv_daily_limit" name="daily_send_limit">
                                            <option value="50">50 emails/day</option>
                                            <option value="100" selected>100 emails/day</option>
                                            <option value="500">500 emails/day</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="csv_send_option" class="form-label">Send Option</label>
                                        <select class="form-select" id="csv_send_option" name="send_option">
                                            <option value="create_only">Create campaign only</option>
                                            <option value="send_immediately">Send immediately</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="csv_sender_name" class="form-label">Sender Name</label>
                                        <input type="text" class="form-control" id="csv_sender_name" name="sender_name"
                                               value="24Seven Assistants Sales Team">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CSV Format Help -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> CSV Format Requirements:</h6>
                        <p class="mb-2">Your CSV file should include these columns:</p>
                        <ul class="mb-2">
                            <li><strong>email</strong> (required) - Contact email address</li>
                            <li><strong>first_name</strong> - Contact first name</li>
                            <li><strong>last_name</strong> - Contact last name</li>
                            <li><strong>company</strong> - Company name</li>
                            <li><strong>phone</strong> - Phone number</li>
                            <li><strong>website</strong> - Company website</li>
                        </ul>
                        <small class="text-muted">
                            Example: <code>email,first_name,last_name,company,phone</code><br>
                            <code><EMAIL>,John,Smith,Tech Corp,******-0101</code>
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload & Create Campaign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showCSVUploadModal() {
        const modal = new bootstrap.Modal(document.getElementById('csvUploadModal'));
        modal.show();
    }

    function viewCampaign(campaignId) {
        window.location.href = `/campaigns/${campaignId}`;
    }

    function pauseCampaign(campaignId) {
        if (confirm('Are you sure you want to pause this campaign?')) {
            // Create a form and submit it to pause the campaign
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/campaigns/${campaignId}/pause`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    function resumeCampaign(campaignId) {
        if (confirm('Are you sure you want to resume this campaign?')) {
            // Create a form and submit it to resume the campaign
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/campaigns/${campaignId}/resume`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    function duplicateCampaign(campaignId) {
        if (confirm('Create a copy of this campaign?')) {
            // Create a form and submit it to duplicate the campaign
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/campaigns/${campaignId}/duplicate`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    function sendCampaign(campaignId) {
        if (confirm('Are you sure you want to send this campaign? This action cannot be undone.')) {
            // Show loading indicator
            const button = event.target.closest('button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            button.disabled = true;

            // Create a form and submit it to send the campaign
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/campaigns/${campaignId}/send`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    function deleteCampaign(campaignId, campaignName) {
        if (confirm(`Are you sure you want to delete the campaign "${campaignName}"? This action cannot be undone.`)) {
            // Create a form and submit it to delete the campaign
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/campaigns/${campaignId}/delete`;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Enhanced campaign status management
    function updateCampaignStatus(campaignId, action) {
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        fetch(`/campaigns/${campaignId}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                // Reload the page to show updated status
                location.reload();
            } else {
                throw new Error('Network response was not ok');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Restore button state
            button.innerHTML = originalContent;
            button.disabled = false;
            alert('Error updating campaign status. Please try again.');
        });
    }

    // Auto-refresh for active campaigns
    const activeCampaigns = document.querySelectorAll('.badge').length;
    const sendingCampaigns = Array.from(document.querySelectorAll('.badge')).filter(badge =>
        badge.textContent.includes('Sending') || badge.textContent.includes('Paused')
    ).length;

    if (sendingCampaigns > 0) {
        setTimeout(function() {
            location.reload();
        }, 30000); // Refresh every 30 seconds if there are active campaigns
    }

    // Handle CSV group options
    function toggleCSVGroupOptions() {
        const createGroup = document.getElementById('csv_create_group').checked;
        const groupNameSection = document.getElementById('csv_group_name_section');

        if (createGroup) {
            groupNameSection.style.display = 'block';
        } else {
            groupNameSection.style.display = 'none';
        }
    }

    // Add click handlers for action buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Handle view campaign buttons
        document.querySelectorAll('[onclick^="viewCampaign"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const campaignId = this.getAttribute('onclick').match(/\d+/)[0];
                viewCampaign(campaignId);
            });
        });

        // Handle duplicate campaign buttons
        document.querySelectorAll('[onclick^="duplicateCampaign"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const campaignId = this.getAttribute('onclick').match(/\d+/)[0];
                duplicateCampaign(campaignId);
            });
        });

        // Handle CSV group option changes
        document.querySelectorAll('input[name="group_option"]').forEach(radio => {
            radio.addEventListener('change', toggleCSVGroupOptions);
        });

        // CSV file validation
        document.getElementById('csv_file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.size > 10 * 1024 * 1024) { // 10MB
                    alert('File size must be less than 10MB');
                    e.target.value = '';
                    return;
                }

                if (!file.name.toLowerCase().endsWith('.csv')) {
                    alert('Please select a CSV file');
                    e.target.value = '';
                    return;
                }

                // Auto-generate group name from filename if creating group
                if (document.getElementById('csv_create_group').checked) {
                    const groupNameInput = document.getElementById('csv_group_name');
                    if (!groupNameInput.value) {
                        const fileName = file.name.replace('.csv', '').replace(/[^a-zA-Z0-9]/g, '_');
                        groupNameInput.value = fileName.substring(0, 50); // Limit length
                    }
                }
            }
        });
    });
</script>
{% endblock %}
