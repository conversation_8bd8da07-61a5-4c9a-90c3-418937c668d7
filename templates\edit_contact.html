{% extends "base.html" %}

{% block title %}Edit {{ contact.full_name }} - Contact Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-edit"></i> Edit Contact</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('contacts_list') }}">Contacts</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('view_contact', contact_id=contact.id) }}">{{ contact.full_name }}</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('view_contact', contact_id=contact.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Contact
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-edit"></i> Contact Information</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('edit_contact', contact_id=contact.id) }}" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name"
                                           value="{{ contact.first_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name"
                                           value="{{ contact.last_name }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="{{ contact.email }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="{{ contact.phone or '' }}">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company" class="form-label">Company</label>
                                    <input type="text" class="form-control" id="company" name="company"
                                           value="{{ contact.company or '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="job_title" class="form-label">Job Title</label>
                                    <input type="text" class="form-control" id="job_title" name="job_title"
                                           value="{{ contact.job_title or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="source" class="form-label">Source</label>
                            <select class="form-select" id="source" name="source">
                                <option value="manual_entry" {{ 'selected' if contact.source == 'manual_entry' else '' }}>Manual Entry</option>
                                <option value="website" {{ 'selected' if contact.source == 'website' else '' }}>Website</option>
                                <option value="referral" {{ 'selected' if contact.source == 'referral' else '' }}>Referral</option>
                                <option value="social_media" {{ 'selected' if contact.source == 'social_media' else '' }}>Social Media</option>
                                <option value="email_campaign" {{ 'selected' if contact.source == 'email_campaign' else '' }}>Email Campaign</option>
                                <option value="chatbot" {{ 'selected' if contact.source == 'chatbot' else '' }}>Chatbot</option>
                                <option value="other" {{ 'selected' if contact.source == 'other' else '' }}>Other</option>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                           {{ 'checked' if contact.is_active else '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active Contact
                                    </label>
                                    <div class="form-text">Inactive contacts won't receive email campaigns</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="do_not_email" name="do_not_email"
                                           {{ 'checked' if contact.do_not_email else '' }}>
                                    <label class="form-check-label" for="do_not_email">
                                        Do Not Email
                                    </label>
                                    <div class="form-text">Contact has opted out of email communications</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <a href="{{ url_for('view_contact', contact_id=contact.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Contact Summary -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Contact Summary</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Status:</strong><br>
                        {% if contact.is_customer %}
                            <span class="badge bg-success">Customer</span>
                        {% elif contact.do_not_email %}
                            <span class="badge bg-danger">Do Not Email</span>
                        {% elif contact.is_active %}
                            <span class="badge bg-primary">Active</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <strong>Sales Stage:</strong><br>
                        {% set stage_colors = {
                            'email_sent': 'primary',
                            'email_opened': 'info',
                            'link_clicked': 'warning',
                            'opening': 'secondary',
                            'trust': 'info',
                            'discovery': 'primary',
                            'demonstration': 'warning',
                            'close': 'danger',
                            'converted': 'success'
                        } %}
                        <span class="badge bg-{{ stage_colors.get(contact.current_sales_stage, 'secondary') }}">
                            {{ (contact.current_sales_stage or 'new').replace('_', ' ').title() }}
                        </span>
                    </div>

                    <div class="mb-3">
                        <strong>Created:</strong><br>
                        <small>{{ contact.created_at.strftime('%Y-%m-%d %H:%M') if contact.created_at else '-' }}</small>
                    </div>

                    {% if contact.first_email_sent %}
                    <div class="mb-3">
                        <strong>First Email Sent:</strong><br>
                        <small>{{ contact.first_email_sent.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}

                    {% if contact.email_opened %}
                    <div class="mb-3">
                        <strong>Email Opened:</strong><br>
                        <small>{{ contact.email_opened.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}

                    {% if contact.chatbot_link_clicked %}
                    <div class="mb-3">
                        <strong>Chatbot Link Clicked:</strong><br>
                        <small>{{ contact.chatbot_link_clicked.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    {% endif %}

                    {% if contact.chatbot_session_id %}
                    <div class="mb-3">
                        <strong>Chatbot Session:</strong><br>
                        <code>{{ contact.chatbot_session_id[:8] }}...</code>
                        <br>
                        <a href="{{ url_for('chat_page', session_id=contact.chatbot_session_id) }}"
                           target="_blank" class="btn btn-sm btn-outline-success mt-1">
                            <i class="fas fa-external-link-alt"></i> Open Session
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ contact.email }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope"></i> Send Email
                        </a>
                        {% if contact.phone %}
                        <a href="tel:{{ contact.phone }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-phone"></i> Call Contact
                        </a>
                        {% endif %}
                        {% if contact.chatbot_session_id %}
                        <a href="{{ url_for('chat_page', session_id=contact.chatbot_session_id) }}"
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-robot"></i> View Chatbot
                        </a>
                        {% endif %}
                        <button class="btn btn-outline-danger btn-sm"
                                onclick="deleteContact({{ contact.id }}, '{{ contact.full_name }}')">
                            <i class="fas fa-trash"></i> Delete Contact
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteContactName"></strong>?</p>
                <p class="text-muted">This action cannot be undone. All associated activities and chatbot sessions will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteContactForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Contact</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteContact(contactId, contactName) {
    document.getElementById('deleteContactName').textContent = contactName;
    document.getElementById('deleteContactForm').action = '/contacts/' + contactId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteContactModal')).show();
}
</script>
{% endblock %}
