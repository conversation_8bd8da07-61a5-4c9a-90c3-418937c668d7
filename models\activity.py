"""
Activity Model
=============
Tracks all sales activities and interactions.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship



class Activity(db.Model):
    """Activity tracking model for sales interactions"""
    
    __tablename__ = 'activities'
    
    id = Column(Integer, primary_key=True)
    
    # Relationships
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=True, index=True)
    opportunity_id = Column(Integer, ForeignKey('opportunities.id'), nullable=True, index=True)
    
    # Activity Information
    activity_type = Column(String(50), nullable=False, index=True)  # email, call, meeting, note, task, etc.
    subject = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    
    # Activity Details
    direction = Column(String(20), nullable=True)  # inbound, outbound
    channel = Column(String(50), nullable=True)  # email, phone, linkedin, website, etc.
    
    # Timing
    activity_date = Column(DateTime, default=datetime.utcnow, index=True)
    duration_minutes = Column(Integer, nullable=True)
    
    # Status
    status = Column(String(50), default='completed', index=True)  # completed, scheduled, cancelled, failed
    priority = Column(String(20), default='medium')  # low, medium, high, urgent
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(100), nullable=True)  # User or AI agent
    
    # Additional Data
    activity_metadata = Column(JSON, nullable=True)  # Additional structured data
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    
    # Email-specific fields
    email_message_id = Column(String(255), nullable=True)
    email_thread_id = Column(String(255), nullable=True)
    email_subject = Column(String(255), nullable=True)
    email_from = Column(String(255), nullable=True)
    email_to = Column(String(255), nullable=True)
    
    # Call-specific fields
    call_duration_seconds = Column(Integer, nullable=True)
    call_outcome = Column(String(100), nullable=True)  # connected, voicemail, busy, no_answer
    call_recording_url = Column(String(500), nullable=True)
    
    # Meeting-specific fields
    meeting_location = Column(String(255), nullable=True)
    meeting_attendees = Column(Text, nullable=True)
    meeting_agenda = Column(Text, nullable=True)
    meeting_notes = Column(Text, nullable=True)
    
    # AI/Bot fields
    ai_generated = Column(Boolean, default=False)
    ai_confidence_score = Column(Float, nullable=True)
    ai_insights = Column(Text, nullable=True)
    
    # Relationships
    contact = relationship("Contact")
    opportunity = relationship("Opportunity", back_populates="activities")
    
    def __repr__(self):
        return f'<Activity {self.activity_type} - {self.subject}>'
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'contact_id': self.contact_id,
            'contact_name': self.contact.full_name if self.contact else None,
            'contact_email': self.contact.email if self.contact else None,
            'opportunity_id': self.opportunity_id,
            'opportunity_name': self.opportunity.name if self.opportunity else None,
            'activity_type': self.activity_type,
            'subject': self.subject,
            'description': self.description,
            'direction': self.direction,
            'channel': self.channel,
            'activity_date': self.activity_date.isoformat() if self.activity_date else None,
            'duration_minutes': self.duration_minutes,
            'status': self.status,
            'priority': self.priority,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'activity_metadata': self.activity_metadata,
            'tags': self.tags,
            'email_message_id': self.email_message_id,
            'email_thread_id': self.email_thread_id,
            'email_subject': self.email_subject,
            'email_from': self.email_from,
            'email_to': self.email_to,
            'call_duration_seconds': self.call_duration_seconds,
            'call_outcome': self.call_outcome,
            'call_recording_url': self.call_recording_url,
            'meeting_location': self.meeting_location,
            'meeting_attendees': self.meeting_attendees,
            'meeting_agenda': self.meeting_agenda,
            'meeting_notes': self.meeting_notes,
            'ai_generated': self.ai_generated,
            'ai_confidence_score': self.ai_confidence_score,
            'ai_insights': self.ai_insights
        }
    
    @classmethod
    def create_email_activity(cls, contact_id=None, opportunity_id=None, subject=None, 
                             direction='outbound', email_from=None, email_to=None, 
                             message_id=None, thread_id=None, description=None, 
                             created_by=None, ai_generated=False):
        """Create an email activity"""
        return cls(
            contact_id=contact_id,
            opportunity_id=opportunity_id,
            activity_type='email',
            subject=subject,
            description=description,
            direction=direction,
            channel='email',
            email_subject=subject,
            email_from=email_from,
            email_to=email_to,
            email_message_id=message_id,
            email_thread_id=thread_id,
            created_by=created_by,
            ai_generated=ai_generated
        )
    
    @classmethod
    def create_call_activity(cls, contact_id=None, opportunity_id=None, subject=None,
                            duration_seconds=None, outcome=None, description=None,
                            created_by=None, ai_generated=False):
        """Create a call activity"""
        return cls(
            contact_id=contact_id,
            opportunity_id=opportunity_id,
            activity_type='call',
            subject=subject,
            description=description,
            direction='outbound',
            channel='phone',
            call_duration_seconds=duration_seconds,
            call_outcome=outcome,
            duration_minutes=duration_seconds // 60 if duration_seconds else None,
            created_by=created_by,
            ai_generated=ai_generated
        )
    
    @classmethod
    def create_meeting_activity(cls, contact_id=None, opportunity_id=None, subject=None,
                               location=None, attendees=None, agenda=None, notes=None,
                               duration_minutes=None, created_by=None):
        """Create a meeting activity"""
        return cls(
            contact_id=contact_id,
            opportunity_id=opportunity_id,
            activity_type='meeting',
            subject=subject,
            description=notes,
            channel='in_person',
            meeting_location=location,
            meeting_attendees=attendees,
            meeting_agenda=agenda,
            meeting_notes=notes,
            duration_minutes=duration_minutes,
            created_by=created_by
        )
    
    @classmethod
    def create_note_activity(cls, contact_id=None, opportunity_id=None, subject=None,
                            description=None, created_by=None, ai_generated=False,
                            ai_insights=None):
        """Create a note activity"""
        return cls(
            contact_id=contact_id,
            opportunity_id=opportunity_id,
            activity_type='note',
            subject=subject,
            description=description,
            channel='internal',
            created_by=created_by,
            ai_generated=ai_generated,
            ai_insights=ai_insights
        )
