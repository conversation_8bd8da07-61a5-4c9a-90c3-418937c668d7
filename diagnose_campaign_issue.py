#!/usr/bin/env python3
"""
Campaign Issue Diagnosis
========================
Diagnose why email campaigns are failing to send
"""

def diagnose_campaign_issue():
    """Diagnose the campaign sending issue"""
    try:
        print("🔍 Diagnosing Campaign Issue")
        print("=" * 50)
        
        from unified_sales_system import app, db, Contact, EmailCampaign
        
        with app.app_context():
            # Step 1: Check contacts
            print("\n📊 Step 1: Checking Contacts")
            print("-" * 30)
            
            total_contacts = Contact.query.count()
            active_contacts = Contact.query.filter_by(is_active=True).count()
            email_enabled_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).count()
            
            print(f"Total contacts: {total_contacts}")
            print(f"Active contacts: {active_contacts}")
            print(f"Email-enabled contacts: {email_enabled_contacts}")
            
            if email_enabled_contacts == 0:
                print("❌ ISSUE FOUND: No email-enabled contacts!")
                print("   Solution: Add contacts or enable email for existing contacts")
                return False
            
            # Show sample contacts
            sample_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).limit(3).all()
            print(f"\n📋 Sample email-enabled contacts:")
            for contact in sample_contacts:
                print(f"   • {contact.first_name} {contact.last_name} <{contact.email}>")
            
            # Step 2: Check campaigns
            print("\n📧 Step 2: Checking Campaigns")
            print("-" * 30)
            
            total_campaigns = EmailCampaign.query.count()
            draft_campaigns = EmailCampaign.query.filter_by(status='draft').count()
            
            print(f"Total campaigns: {total_campaigns}")
            print(f"Draft campaigns: {draft_campaigns}")
            
            if total_campaigns == 0:
                print("❌ ISSUE FOUND: No campaigns exist!")
                return False
            
            # Get the first campaign for testing
            campaign = EmailCampaign.query.first()
            print(f"\n📋 Testing campaign: {campaign.name}")
            print(f"   Status: {campaign.status}")
            print(f"   Template: {campaign.template_name}")
            print(f"   Daily limit: {campaign.daily_send_limit}")
            print(f"   Recipient criteria: {campaign.recipient_criteria}")
            
            # Step 3: Test get_campaign_contacts function
            print("\n👥 Step 3: Testing Contact Selection")
            print("-" * 30)
            
            from unified_sales_system import get_campaign_contacts
            try:
                contacts = get_campaign_contacts(campaign)
                print(f"✅ get_campaign_contacts returned {len(contacts)} contacts")
                
                if len(contacts) == 0:
                    print("❌ ISSUE FOUND: get_campaign_contacts returned no contacts!")
                    print("   This is likely the main issue")
                    return False
                
                # Show sample contacts from campaign
                print(f"📋 Sample campaign contacts:")
                for contact in contacts[:3]:
                    print(f"   • {contact.first_name} {contact.last_name} <{contact.email}>")
                    
            except Exception as e:
                print(f"❌ ISSUE FOUND: get_campaign_contacts failed: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
            
            # Step 4: Test SMTP service
            print("\n📤 Step 4: Testing SMTP Service")
            print("-" * 30)
            
            try:
                from unified_sales_system import test_smtp_connection
                success, message = test_smtp_connection()
                
                if success:
                    print(f"✅ SMTP test passed: {message}")
                else:
                    print(f"❌ ISSUE FOUND: SMTP test failed: {message}")
                    return False
                    
            except Exception as e:
                print(f"❌ ISSUE FOUND: SMTP test error: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
            
            # Step 5: Test email sending function
            print("\n📧 Step 5: Testing Email Sending Function")
            print("-" * 30)
            
            try:
                from unified_sales_system import send_campaign_email
                import uuid
                
                # Get first contact for testing
                test_contact = contacts[0]
                test_session_id = str(uuid.uuid4())
                
                print(f"Testing email to: {test_contact.email}")
                success, message = send_campaign_email(test_contact, campaign, test_session_id)
                
                if success:
                    print(f"✅ Test email sent successfully: {message}")
                else:
                    print(f"❌ ISSUE FOUND: Test email failed: {message}")
                    return False
                    
            except Exception as e:
                print(f"❌ ISSUE FOUND: Email sending function error: {str(e)}")
                import traceback
                traceback.print_exc()
                return False
            
            # Step 6: Check daily limits
            print("\n📊 Step 6: Checking Daily Limits")
            print("-" * 30)
            
            can_send = campaign.can_send_today()
            remaining = campaign.get_remaining_sends_today()
            
            print(f"Can send today: {can_send}")
            print(f"Remaining sends: {remaining}")
            print(f"Emails sent today: {campaign.emails_sent_today}")
            print(f"Last send date: {campaign.last_send_date}")
            
            if not can_send:
                print("❌ ISSUE FOUND: Daily send limit reached!")
                return False
            
            print("\n🎉 All diagnostic tests passed!")
            print("The email system should be working correctly.")
            print("\nIf campaigns are still failing, check the application logs for specific errors.")
            
            return True
            
    except Exception as e:
        print(f"❌ Diagnosis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function"""
    print("🔍 Email Campaign Issue Diagnosis")
    print("=" * 50)
    
    success = diagnose_campaign_issue()
    
    if success:
        print("\n✅ Diagnosis completed successfully!")
        print("The email system appears to be working correctly.")
    else:
        print("\n❌ Issues found that need to be fixed.")
        print("Please address the issues above and try again.")
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
