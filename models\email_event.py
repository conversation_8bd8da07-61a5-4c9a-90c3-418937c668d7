"""
Email Event Model
=================
Tracks email open, click, and follow-up events for analytics and automated
follow-up logic.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship

# NOTE: A local SQLAlchemy instance is declared here.  The main Flask application
# creates a global `db` object and then monkey-patches the individual model
# modules so that all models share the same metadata.  This file follows the
# same pattern as existing model modules (e.g. `activity.py`).



class EmailEvent(db.Model):
    """Log a single email-related event (sent, opened, follow-up sent, etc.)."""

    __tablename__ = 'email_events'

    id = Column(Integer, primary_key=True)

    # Relations
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=False, index=True)
    campaign_id = Column(Integer, ForeignKey('email_campaigns.id'), nullable=True, index=True)

    # Event Information
    event_type = Column(String(50), nullable=False, index=True)  # sent, opened, follow_up_1, follow_up_2
    description = Column(Text, nullable=True)

    # Metadata can hold arbitrary structured data (e.g. user-agent, IP address)
    event_metadata = Column(JSON, nullable=True)

    # Timing
    event_time = Column(DateTime, default=datetime.utcnow, index=True)

    # Tracking flags
    processed = Column(Boolean, default=False)  # Has automation logic consumed this event?

    def __repr__(self):
        return f'<EmailEvent {self.event_type} Contact:{self.contact_id}>'  # noqa: E501

    def to_dict(self):
        return {
            'id': self.id,
            'contact_id': self.contact_id,
            'campaign_id': self.campaign_id,
            'event_type': self.event_type,
            'description': self.description,
            'event_metadata': self.event_metadata,
            'event_time': self.event_time.isoformat() if self.event_time else None,
            'processed': self.processed,
        }
