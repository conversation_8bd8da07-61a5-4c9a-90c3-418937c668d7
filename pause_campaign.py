#!/usr/bin/env python3
"""
Pause Campaign
==============
Quickly pause any currently sending campaigns.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def pause_campaigns():
    """Pause all currently sending campaigns"""
    try:
        from unified_sales_system import app, db, EmailCampaign
        
        with app.app_context():
            # Find campaigns that are currently sending
            sending_campaigns = EmailCampaign.query.filter_by(status='sending').all()
            
            print("⏸️ PAUSING CAMPAIGNS")
            print("=" * 30)
            
            if not sending_campaigns:
                print("✅ No campaigns are currently sending")
                return True
            
            for campaign in sending_campaigns:
                print(f"📋 Pausing: {campaign.name}")
                print(f"   Status: {campaign.status} → paused")
                
                # Pause the campaign
                campaign.status = 'paused'
                db.session.commit()
                
                print(f"   ✅ Campaign paused successfully")
            
            print(f"\n🎉 Paused {len(sending_campaigns)} campaigns")
            print("💡 You can now fix recipient criteria and resume sending")
            return True
            
    except Exception as e:
        print(f"❌ Error pausing campaigns: {e}")
        return False

if __name__ == "__main__":
    pause_campaigns()
