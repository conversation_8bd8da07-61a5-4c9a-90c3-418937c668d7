{% extends "base.html" %}

{% block title %}Complete Sales Analytics Dashboard - 24Seven Assistants{% endblock %}

{% block extra_css %}
<style>
    .analytics-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .metric-card {
        background: #2a2a2a;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        border: none;
        height: 100%;
        color: white;
    }

    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.8);
        border-color: var(--primary-color, #203A8F);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        /* Fallback for browsers that don't support background-clip */
        color: var(--primary-color, #667eea);
    }

    .metric-label {
        font-size: 0.9rem;
        color: var(--text-secondary, #9ca3af);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .metric-change {
        font-size: 0.8rem;
        font-weight: 600;
        margin-top: 0.5rem;
        color: var(--text-primary, #e6e6e6);
    }

    .chart-container {
        background: #2a2a2a;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
        border: none;
        color: white;
    }

    .chart-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary, #e6e6e6);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 3px solid var(--glass-border, rgba(255, 255, 255, 0.15));
        position: relative;
    }

    .chart-title::after {
        content: '';
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    .nav-pills .nav-link {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }

    .nav-pills .nav-link.active {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
    }

    .nav-pills .nav-link:not(.active) {
        background: var(--glass-bg, rgba(255, 255, 255, 0.05));
        color: var(--text-secondary, #9ca3af);
        border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.15));
    }

    .nav-pills .nav-link:not(.active):hover {
        background: var(--glass-border, rgba(255, 255, 255, 0.15));
        color: var(--text-primary, #e6e6e6);
    }

    .section-divider {
        height: 3px;
        background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
        border: none;
        border-radius: 2px;
        margin: 3rem 0;
    }

    .insight-card {
        background: var(--glass-bg, rgba(255, 255, 255, 0.05));
        border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.15));
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #667eea;
        color: var(--text-primary, #e6e6e6);
        backdrop-filter: blur(20px);
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-excellent { background: #28a745; }
    .status-good { background: #17a2b8; }
    .status-warning { background: #ffc107; }
    .status-danger { background: #dc3545; }

    .quick-action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 0.25rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .analytics-tabs {
        background: #2a2a2a;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
        border: none;
    }

    .table-modern {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-modern thead {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }

    .table-modern tbody tr:hover {
        background-color: var(--glass-border, rgba(255, 255, 255, 0.15));
    }

    .performance-gauge {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }

    .gauge-bg {
        fill: none;
        stroke: var(--glass-border, rgba(255, 255, 255, 0.15));
        stroke-width: 8;
    }

    .gauge-fill {
        fill: none;
        stroke: url(#gaugeGradient);
        stroke-width: 8;
        stroke-linecap: round;
        transition: stroke-dasharray 1s ease;
    }

    .stage-progress {
        background: #2a2a2a;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: none;
        color: white;
    }

    .stage-progress-bar {
        height: 8px;
        background: var(--glass-border, rgba(255, 255, 255, 0.15));
        border-radius: 4px;
        overflow: hidden;
        margin-top: 0.5rem;
    }

    .stage-progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 4px;
        transition: width 0.5s ease;
    }
</style>
{% endblock %}

{% block content %}
<!-- Analytics Header -->
<div class="analytics-header text-center">
    <div class="container">
        <h1 class="display-4 mb-3">
            <i class="fas fa-chart-line me-3"></i>
            Complete Sales Analytics Dashboard
        </h1>
        <p class="lead mb-4">
            Comprehensive insights into your sales pipeline, cycle performance, and session analytics
        </p>
        <div class="d-flex justify-content-center flex-wrap">
            <a href="#pipeline-section" class="quick-action-btn">
                <i class="fas fa-funnel-dollar me-2"></i>Pipeline Analysis
            </a>
            <a href="#cycle-section" class="quick-action-btn">
                <i class="fas fa-clock me-2"></i>Cycle Metrics
            </a>
            <a href="#sessions-section" class="quick-action-btn">
                <i class="fas fa-comments me-2"></i>Session Insights
            </a>
            <a href="#performance-section" class="quick-action-btn">
                <i class="fas fa-trophy me-2"></i>Performance
            </a>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card text-center">
                <div class="metric-value">{{ analytics_data.total_sessions or 0 }}</div>
                <div class="metric-label">Total Sessions</div>
                <div class="metric-change text-success">
                    <i class="fas fa-arrow-up"></i> +12% this week
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card text-center">
                <div class="metric-value">{{ analytics_data.total_conversions or 0 }}</div>
                <div class="metric-label">Conversions</div>
                <div class="metric-change text-success">
                    <i class="fas fa-arrow-up"></i> +8% this week
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card text-center">
                <div class="metric-value">{{ "%.1f"|format(analytics_data.conversion_rate or 0) }}%</div>
                <div class="metric-label">Conversion Rate</div>
                <div class="metric-change text-warning">
                    <i class="fas fa-arrow-right"></i> Stable
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card text-center">
                <div class="metric-value">${{ "%.0f"|format(analytics_data.total_revenue or 0) }}</div>
                <div class="metric-label">Total Revenue</div>
                <div class="metric-change text-success">
                    <i class="fas fa-arrow-up"></i> +15% this week
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Navigation Tabs -->
    <div class="analytics-tabs">
        <ul class="nav nav-pills justify-content-center" id="analyticsTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>Overview
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pipeline-tab" data-bs-toggle="pill" data-bs-target="#pipeline" type="button" role="tab">
                    <i class="fas fa-funnel-dollar me-2"></i>Sales Pipeline
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cycle-tab" data-bs-toggle="pill" data-bs-target="#cycle" type="button" role="tab">
                    <i class="fas fa-clock me-2"></i>Sales Cycle
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sessions-tab" data-bs-toggle="pill" data-bs-target="#sessions" type="button" role="tab">
                    <i class="fas fa-comments me-2"></i>Sessions
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="performance-tab" data-bs-toggle="pill" data-bs-target="#performance" type="button" role="tab">
                    <i class="fas fa-trophy me-2"></i>Performance
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="analyticsTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <!-- Sales Funnel Overview -->
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-funnel-dollar text-primary me-2"></i>
                            Complete Sales Funnel
                        </h5>
                        <div id="overviewFunnelChart"></div>
                    </div>
                </div>

                <!-- Performance Summary -->
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-gauge text-primary me-2"></i>
                            Performance Score
                        </h5>
                        <div class="text-center">
                            <div class="performance-gauge">
                                <svg width="150" height="150">
                                    <defs>
                                        <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#667eea"/>
                                            <stop offset="100%" style="stop-color:#764ba2"/>
                                        </linearGradient>
                                    </defs>
                                    <circle cx="75" cy="75" r="65" class="gauge-bg"/>
                                    <circle cx="75" cy="75" r="65" class="gauge-fill"
                                            stroke-dasharray="0 408"
                                            transform="rotate(-90 75 75)"/>
                                </svg>
                                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                    <div class="metric-value" style="font-size: 2rem;">85%</div>
                                    <div class="metric-label">Overall</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stage Progress Overview -->
            <div class="row">
                <div class="col-md-12">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-bar text-primary me-2"></i>
                            Sales Stage Progress
                        </h5>
                        <div class="row">
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Opening</strong></span>
                                        <span>{{ analytics_data.opening_count or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 85%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Trust</strong></span>
                                        <span>{{ analytics_data.trust_count or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 72%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Discovery</strong></span>
                                        <span>{{ analytics_data.discovery_count or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 58%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Demo</strong></span>
                                        <span>{{ analytics_data.demo_count or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 45%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Close</strong></span>
                                        <span>{{ analytics_data.close_count or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 35%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="stage-progress">
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Won</strong></span>
                                        <span>{{ analytics_data.total_conversions or 0 }}</span>
                                    </div>
                                    <div class="stage-progress-bar">
                                        <div class="stage-progress-fill" style="width: 25%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Pipeline Tab -->
        <div class="tab-pane fade" id="pipeline" role="tabpanel">
            <div class="row">
                <!-- Pipeline Funnel -->
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-funnel-dollar text-success me-2"></i>
                            Sales Pipeline Progression
                        </h5>
                        <div id="pipelineFunnelChart"></div>
                    </div>
                </div>

                <!-- Conversion Rates -->
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-percentage text-success me-2"></i>
                            Stage Conversion Rates
                        </h5>
                        <div id="conversionRatesChart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Sales Velocity -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-tachometer-alt text-success me-2"></i>
                            Sales Velocity
                        </h5>
                        <div id="salesVelocityChart"></div>
                    </div>
                </div>

                <!-- Performance Heatmap -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-fire text-success me-2"></i>
                            Performance Heatmap
                        </h5>
                        <div id="performanceHeatmapChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Cycle Tab -->
        <div class="tab-pane fade" id="cycle" role="tabpanel">
            <div class="row">
                <!-- Cycle Timeline -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-clock text-info me-2"></i>
                            Sales Cycle Timeline
                        </h5>
                        <div id="cycleTimelineChart"></div>
                    </div>
                </div>

                <!-- Trends -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-line text-info me-2"></i>
                            Cycle Trends
                        </h5>
                        <div id="cycleTrendsChart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Drop-off Analysis -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-waterfall text-info me-2"></i>
                            Drop-off Analysis
                        </h5>
                        <div id="dropoffChart"></div>
                    </div>
                </div>

                <!-- Conversion Distribution -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-chart-area text-info me-2"></i>
                            Conversion Time Distribution
                        </h5>
                        <div id="conversionDistributionChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions Tab -->
        <div class="tab-pane fade" id="sessions" role="tabpanel">
            <div class="row">
                <!-- Session Funnel -->
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-comments text-primary me-2"></i>
                            Session Progression Funnel
                        </h5>
                        <div id="sessionFunnelChart"></div>
                    </div>
                </div>

                <!-- Engagement Levels -->
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-heart text-primary me-2"></i>
                            Engagement Levels
                        </h5>
                        <div id="engagementChart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Stage Durations -->
                <div class="col-md-12">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-stopwatch text-primary me-2"></i>
                            Average Stage Durations
                        </h5>
                        <div id="stageDurationsChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Tab -->
        <div class="tab-pane fade" id="performance" role="tabpanel">
            <div class="row">
                <!-- Performance Insights -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-trophy text-warning me-2"></i>
                            Performance Insights
                        </h5>
                        <div class="insight-card">
                            <h6><i class="fas fa-star text-success me-2"></i>Top Performers</h6>
                            <ul class="list-unstyled">
                                <li><span class="status-indicator status-excellent"></span>Trust Stage: 85% success rate</li>
                                <li><span class="status-indicator status-excellent"></span>Opening Stage: 78% success rate</li>
                                <li><span class="status-indicator status-good"></span>Discovery Stage: 65% success rate</li>
                            </ul>
                        </div>

                        <div class="insight-card">
                            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>Areas for Improvement</h6>
                            <ul class="list-unstyled">
                                <li><span class="status-indicator status-warning"></span>Demo Stage: 45% success rate</li>
                                <li><span class="status-indicator status-danger"></span>Close Stage: 35% success rate</li>
                                <li><span class="status-indicator status-warning"></span>Average cycle time: 28 minutes</li>
                            </ul>
                        </div>

                        <div class="insight-card">
                            <h6><i class="fas fa-lightbulb text-info me-2"></i>Recommendations</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Focus on demo stage optimization</li>
                                <li><i class="fas fa-check text-success me-2"></i>Implement better closing techniques</li>
                                <li><i class="fas fa-check text-success me-2"></i>Reduce time in discovery phase</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Recent Sessions -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5 class="chart-title">
                            <i class="fas fa-history text-warning me-2"></i>
                            Recent Sessions
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-modern">
                                <thead>
                                    <tr>
                                        <th>Session ID</th>
                                        <th>Stage</th>
                                        <th>Status</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in analytics_data.recent_sessions[:10] %}
                                    <tr>
                                        <td><code>{{ session.session_id[:8] }}...</code></td>
                                        <td>
                                            <span class="badge bg-primary">{{ session.current_stage|title }}</span>
                                        </td>
                                        <td>
                                            {% if session.conversion_achieved %}
                                                <span class="badge bg-success">Converted</span>
                                            {% elif session.completed_successfully %}
                                                <span class="badge bg-info">Completed</span>
                                            {% else %}
                                                <span class="badge bg-warning">In Progress</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ session.duration or 'N/A' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="section-divider"></div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="chart-container text-center">
                <h5 class="chart-title">
                    <i class="fas fa-rocket text-primary me-2"></i>
                    Quick Actions
                </h5>
                <div class="d-flex justify-content-center flex-wrap">
                    <a href="{{ url_for('sales_pipeline_analytics') }}" class="quick-action-btn">
                        <i class="fas fa-funnel-dollar me-2"></i>Detailed Pipeline
                    </a>
                    <a href="{{ url_for('sales_cycle_analytics') }}" class="quick-action-btn">
                        <i class="fas fa-clock me-2"></i>Detailed Cycle Analysis
                    </a>
                    <a href="{{ url_for('session_analytics') }}" class="quick-action-btn">
                        <i class="fas fa-comments me-2"></i>Session Details
                    </a>
                    <a href="{{ url_for('contacts_list') }}" class="quick-action-btn">
                        <i class="fas fa-users me-2"></i>Manage Contacts
                    </a>
                    <a href="{{ url_for('campaigns_list') }}" class="quick-action-btn">
                        <i class="fas fa-envelope me-2"></i>Email Campaigns
                    </a>
                    <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="quick-action-btn">
                        <i class="fas fa-robot me-2"></i>Test Chatbot
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize performance gauge
        const gaugeCircle = document.querySelector('.gauge-fill');
        const performanceScore = 85; // This would come from your analytics data
        const circumference = 2 * Math.PI * 65;
        const offset = circumference - (performanceScore / 100) * circumference;

        setTimeout(() => {
            gaugeCircle.style.strokeDasharray = `${circumference - offset} ${circumference}`;
        }, 500);

        // Use real analytics data from backend
        {% if charts.main_funnel %}
        var funnelData = {{ charts.main_funnel|safe }};
        Plotly.newPlot('overviewFunnelChart', funnelData.data, funnelData.layout, {responsive: true});
        {% else %}
        // Fallback to real analytics data if chart not available
        const realFunnelData = {
            data: [{
                type: 'funnel',
                y: ['Sessions Started', 'Opening', 'Trust', 'Discovery', 'Demo', 'Close', 'Conversions'],
                x: [
                    {{ analytics_data.total_sessions or 0 }},
                    {{ analytics_data.opening_count or 0 }},
                    {{ analytics_data.trust_count or 0 }},
                    {{ analytics_data.discovery_count or 0 }},
                    {{ analytics_data.demo_count or 0 }},
                    {{ analytics_data.close_count or 0 }},
                    {{ analytics_data.total_conversions or 0 }}
                ],
                textinfo: "value+percent initial",
                marker: {
                    color: ['#E74C3C', '#F39C12', '#F1C40F', '#2ECC71', '#3498DB', '#9B59B6', '#1ABC9C']
                }
            }],
            layout: {
                title: 'Complete Sales Funnel',
                height: 400,
                margin: { t: 50, b: 50, l: 50, r: 50 }
            }
        };

        // Render overview funnel with real data
        Plotly.newPlot('overviewFunnelChart', realFunnelData.data, realFunnelData.layout, {responsive: true});
        {% endif %}

        // Auto-refresh every 2 minutes
        setInterval(function() {
            // You can add AJAX calls here to refresh data
            console.log('Refreshing analytics data...');
        }, 120000);

        // Tab switching analytics
        const tabButtons = document.querySelectorAll('[data-bs-toggle="pill"]');
        tabButtons.forEach(button => {
            button.addEventListener('shown.bs.tab', function(e) {
                const targetTab = e.target.getAttribute('data-bs-target');
                console.log('Switched to tab:', targetTab);

                // Trigger chart redraws when tabs are shown
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'));
                }, 100);
            });
        });
    });
</script>
{% endblock %}