#!/usr/bin/env python3
"""
Restart Flask App and Test View Campaign
"""

import subprocess
import time
import requests
import os
import signal

def find_flask_process():
    """Find the Flask process"""
    try:
        # Try to find Python processes running the Flask app
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            python_processes = [line for line in lines if 'python.exe' in line]
            print(f"Found {len(python_processes)} Python processes")
            for proc in python_processes:
                print(f"  {proc}")
        
        # Also try to check if port 5000 is in use
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True, shell=True)
        if ':5000' in result.stdout:
            print("✅ Port 5000 is in use (Flask app likely running)")
            return True
        else:
            print("❌ Port 5000 not in use (Flask app not running)")
            return False
            
    except Exception as e:
        print(f"Error checking processes: {e}")
        return False

def test_flask_app():
    """Test if Flask app is responding"""
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        if response.status_code == 200:
            print("✅ Flask app is responding")
            return True
        else:
            print(f"❌ Flask app returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Flask app is not responding (connection refused)")
        return False
    except Exception as e:
        print(f"❌ Error testing Flask app: {e}")
        return False

def start_flask_app():
    """Start the Flask application"""
    print("🚀 Starting Flask application...")
    
    try:
        # Start the Flask app in the background
        process = subprocess.Popen(
            ['python', 'unified_sales_system.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        print(f"Started Flask app with PID: {process.pid}")
        
        # Wait a bit for the app to start
        print("⏳ Waiting for Flask app to start...")
        time.sleep(5)
        
        # Test if it's responding
        for i in range(10):
            if test_flask_app():
                print("✅ Flask app started successfully!")
                return process
            print(f"   Attempt {i+1}/10 - waiting...")
            time.sleep(2)
        
        print("❌ Flask app failed to start properly")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        return None

def test_view_campaign():
    """Test the view campaign functionality"""
    print("\n🔍 Testing View Campaign Functionality")
    print("=" * 40)
    
    # First, create a new campaign to test with
    print("📝 Creating test campaign...")
    
    campaign_data = {
        'name': 'Restart Test Campaign',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate'
    }
    
    try:
        create_response = requests.post(
            'http://localhost:5000/campaigns/create',
            data=campaign_data,
            timeout=10
        )
        
        if create_response.status_code in [200, 302]:
            print("✅ Test campaign created")
            
            # Get the campaign ID
            campaigns_response = requests.get('http://localhost:5000/campaigns')
            if campaigns_response.status_code == 200:
                import re
                campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', campaigns_response.text)
                
                if campaign_ids:
                    test_id = campaign_ids[0]  # Get the newest campaign
                    print(f"🎯 Testing view campaign with ID: {test_id}")
                    
                    # Test the view campaign
                    view_response = requests.get(f'http://localhost:5000/campaigns/{test_id}')
                    
                    print(f"   Status Code: {view_response.status_code}")
                    
                    if view_response.status_code == 200:
                        if "Campaign Details" in view_response.text or "Restart Test Campaign" in view_response.text:
                            print("✅ SUCCESS! View campaign is working!")
                            return True
                        else:
                            print("❌ Page loaded but content is not campaign details")
                            return False
                    elif view_response.status_code == 302:
                        print("❌ Redirected (error occurred)")
                        return False
                    else:
                        print(f"❌ Unexpected status: {view_response.status_code}")
                        return False
                else:
                    print("❌ No campaigns found")
                    return False
            else:
                print("❌ Failed to get campaigns list")
                return False
        else:
            print(f"❌ Failed to create campaign: {create_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing view campaign: {e}")
        return False

def main():
    print("🔄 Flask App Restart and Test Tool")
    print("=" * 50)
    
    # Check current state
    print("1. Checking current Flask app state...")
    flask_running = find_flask_process()
    flask_responding = test_flask_app()
    
    print(f"\n📊 Current State:")
    print(f"   Flask Process: {'✅ Running' if flask_running else '❌ Not Found'}")
    print(f"   Flask Response: {'✅ OK' if flask_responding else '❌ Failed'}")
    
    if flask_responding:
        print("\n2. Testing view campaign with current app...")
        if test_view_campaign():
            print("\n🎉 View campaign is already working!")
            print("💡 Try clicking the view buttons in your browser now.")
            return
        else:
            print("\n⚠️ View campaign not working with current app.")
            print("🔄 The Flask app needs to be restarted to pick up code changes.")
    
    print("\n3. Instructions to restart Flask app manually:")
    print("=" * 45)
    print("   1. Go to the terminal where Flask is running")
    print("   2. Press Ctrl+C to stop the Flask app")
    print("   3. Run: python unified_sales_system.py")
    print("   4. Wait for the app to start")
    print("   5. Test the view buttons again")
    
    print("\n💡 After restarting, the view campaign buttons should work!")
    print("🔧 The code fix has been applied and just needs a restart to take effect.")

if __name__ == "__main__":
    main()
