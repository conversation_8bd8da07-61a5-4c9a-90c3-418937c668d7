<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat with <PERSON> - 24Seven Assistants</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            /* Dark professional palette */
            --primary-gradient: linear-gradient(135deg, #203A8F 0%, #1C254A 100%);
            --secondary-gradient: linear-gradient(135deg, #FF0080 0%, #7928CA 100%);
            --success-gradient: linear-gradient(135deg, #16a085 0%, #2ecc71 100%);
            --chat-bg: linear-gradient(135deg, #0d1117 0%, #1c1f26 100%);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.15);
            --text-primary: #e6e6e6;
            --text-secondary: #9ca3af;
            --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.6);
            --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.7);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
            
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--chat-bg);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Animated background particles */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Main chat container */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
            height: 100vh;
        }

        /* Chat header */
        .chat-header {
            position: relative;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 20px 24px;
            text-align: center;
            box-shadow: var(--shadow-soft);
        }

        .chat-title {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .avatar-img-header {
width: 60px;
height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }

        

        .status-indicator {
            
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-left: 8px;
            animation: pulse 2s infinite;
        }

        .icon-button {
            position: absolute;
            top: 16px;
            background: transparent;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 34px;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: var(--transition);
        }

        .icon-button:hover {
            background: rgba(255,255,255,0.1);
            color: #ff6b81;
            border-color: rgba(255,255,255,0.35);
        }

        .icon-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .reset-button {
            right: 20px;
            position: absolute;
            top: 16px;
            right: 20px;
            background: transparent;
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 34px;
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            transition: var(--transition);
        }

        .reset-button:hover {
            background: rgba(255,255,255,0.1);
            color: #ff6b81;
            border-color: rgba(255,255,255,0.35);
        }

        .reset-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .share-button {
            right: 80px;
            position: absolute;
            top: 16px;
            background: transparent;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 6px 16px;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
            transition: var(--transition);
        }
        .share-button:hover {
            background: rgba(255,255,255,0.1);
            color: #ff6b81;
            border-color: rgba(255,255,255,0.5);
        }

        

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Chat messages area */
        .chat-messages {
            flex: 1;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-left: 1px solid var(--glass-border);
            border-right: 1px solid var(--glass-border);
            padding: 24px;
            overflow-y: auto;
            scroll-behavior: smooth;
            min-height: 0;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Message bubbles */
        .message-wrapper {
            margin-bottom: 16px;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-bubble {
            max-width: 75%;
            padding: 14px 18px;
            border-radius: 20px;
            font-size: 0.95rem;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
            box-shadow: var(--shadow-soft);
            transition: var(--transition);
        }

        .message-bubble:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .assistant-message {
            display: flex;
            align-items: flex-start;
            text-align: left;
        }

        .avatar-img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .assistant-bubble {
            background: rgba(255, 255, 255, 0.08);
            color: var(--text-primary);
            border-bottom-left-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.12);
        }

        .user-message {
            text-align: right;
        }

        .user-bubble {
            background: var(--primary-gradient);
            color: white;
            border-bottom-right-radius: 6px;
            margin-left: auto;
        }

        .message-time {
            font-size: 0.75rem;
            opacity: 0.7;
            margin-top: 4px;
            color: var(--text-secondary);
        }

        .user-message .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Typing indicator */
        .typing-indicator {
            display: none;
            padding: 16px 20px;
            margin-bottom: 16px;
        }

        .typing-dots {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            background: rgba(255, 255, 255, 0.08);
            padding: 12px 16px;
            border-radius: 20px;
            border-bottom-left-radius: 6px;
            box-shadow: var(--shadow-soft);
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: var(--text-secondary);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        /* Chat input area */
        .chat-input {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            padding: 20px 24px;
            box-shadow: var(--shadow-soft);
        }

        .input-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.08);
            border: 2px solid transparent;
            border-radius: 25px;
            padding: 14px 20px;
            font-size: 0.95rem;
            resize: none;
            outline: none;
            transition: var(--transition);
            font-family: inherit;
            max-height: 120px;
            color: var(--text-primary);
            caret-color: #ffffff;
            min-height: 50px;
        }


        /* Placeholder text */
        .message-input::placeholder {
            color: var(--text-secondary);
            opacity: 0.8;
        }

        .message-input:focus {
            border-color: rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
        }



        .send-button {
            width: 50px;
            height: 50px;
            background: var(--secondary-gradient);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-soft);
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: var(--shadow-medium);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Status messages */
        .status-message {
            text-align: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 12px;
            font-size: 0.85rem;
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: #fca5a5;
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-container {
                padding: 10px;
                max-width: 100%;
            }

            .chat-header {
                padding: 16px 20px;
            }

            .chat-title {
                font-size: 1.25rem;
            }

            .chat-messages {
                padding: 16px;
            }

            .message-bubble {
                max-width: 85%;
                font-size: 0.9rem;
            }

            .chat-input {
                padding: 16px 20px;
            }
        }

        @media (max-width: 480px) {
            .chat-container {
                padding: 5px;
            }

            .message-bubble {
                max-width: 90%;
                padding: 12px 16px;
            }

            .chat-header, .chat-input {
                padding: 12px 16px;
            }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus styles for accessibility */
        .send-button:focus-visible,
        .message-input:focus-visible {
            outline: 2px solid rgba(255, 255, 255, 0.8);
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Animated background -->
    <div class="bg-animation" id="bgAnimation"></div>

    <!-- Main chat container -->
    <div class="chat-container">
        <!-- Chat header -->
        <div class="chat-header">
            <h1 class="chat-title">
                <img src="{{ url_for('static', filename='avator.png') }}" alt="AI Avatar" class="avatar-img-header">
                    
                Chat with Sarah
                <div class="status-indicator" title="Online"></div>
            </h1>
            <button id="shareChatBtn" class="share-button" title="Share conversation">Share</button>
            <button id="resetChatBtn" class="reset-button" title="Reset conversation"><i class="fas fa-rotate-left"></i></button>
            <p style="color: rgba(255, 255, 255, 0.8); margin: 8px 0 0 0; font-size: 0.9rem;">
                Your AI Sales Assistant • Always here to help
            </p>
        </div>

        <!-- Chat messages area -->
        <div class="chat-messages" id="chatBox">
            <!-- Messages will be added here dynamically -->
        </div>

        <!-- Typing indicator -->
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <span style="margin-left: 8px; color: var(--text-secondary); font-size: 0.85rem;">Sarah is typing...</span>
            </div>
        </div>

        <!-- Chat input area -->
        <div class="chat-input">
            <form id="chatForm" class="input-group">
                <textarea
                    class="message-input"
                    id="messageInput"
                    placeholder="Type your message here..."
                    rows="1"
                    autocomplete="off"
                    required
                    aria-label="Message input"
                ></textarea>
                <button
                    class="send-button"
                    type="submit"
                    id="sendButton"
                    aria-label="Send message"
                    title="Send message (Enter)"
                >
                    <i class="fas fa-paper-plane"></i>
                </button>
            </form>
        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Enhanced Chat Application
class EnhancedChatApp {
    constructor() {
        this.sessionId = "{{ session_id }}";
        this.prospectName = localStorage.getItem('prospect_name') || 'there';
        this.isTyping = false;
        this.messageQueue = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createBackgroundAnimation();
        this.loadChatHistory();
        this.setupTextareaAutoResize();
    }

    setupEventListeners() {
        const form = document.getElementById('chatForm');
        const input = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // Form submission
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSendMessage();
        });

        // Enter key handling for textarea
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            const hasText = input.value.trim().length > 0;
            sendButton.disabled = !hasText || this.isTyping;
            sendButton.style.opacity = hasText && !this.isTyping ? '1' : '0.6';
        });

        // Focus management
        input.addEventListener('focus', () => {
            input.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', () => {
            input.parentElement.style.transform = 'scale(1)';
        });
    }

    setupTextareaAutoResize() {
        const textarea = document.getElementById('messageInput');
        textarea.addEventListener('input', () => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        });
    }

    createBackgroundAnimation() {
        const bgAnimation = document.getElementById('bgAnimation');
        const particleCount = 15;

        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            // Random size and position
            const size = Math.random() * 4 + 2;
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (Math.random() * 3 + 4) + 's';

            bgAnimation.appendChild(particle);
        }
    }

    formatTime(date = new Date()) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    addMessage(role, text, showTime = true) {
        const chatBox = document.getElementById('chatBox');

        // Create message wrapper
        const wrapper = document.createElement('div');
        wrapper.className = `message-wrapper ${role}-message`;

        // Create avatar for assistant
    let avatarEl = null;
    if(role === 'assistant'){
        avatarEl = document.createElement('img');
        avatarEl.src = "{{ url_for('static', filename='avator.png') }}";
        avatarEl.alt = 'AI Avatar';
        avatarEl.className = 'avatar-img';
    }

    // Create message bubble
        const bubble = document.createElement('div');
        bubble.className = `message-bubble ${role}-bubble`;

        // Handle text formatting (basic markdown-like support)
        const formattedText = this.formatMessageText(text);
        bubble.innerHTML = formattedText;

        // Add timestamp if requested
        if(avatarEl){ wrapper.appendChild(avatarEl);} 

    if (showTime) {
            const timeElement = document.createElement('div');
            timeElement.className = 'message-time';
            timeElement.textContent = this.formatTime();
            wrapper.appendChild(bubble);
            wrapper.appendChild(timeElement);
        } else {
            wrapper.appendChild(bubble);
        }

        chatBox.appendChild(wrapper);
        this.scrollToBottom();

        // Add entrance animation
        setTimeout(() => {
            wrapper.style.opacity = '1';
            wrapper.style.transform = 'translateY(0)';
        }, 50);
    }

    formatMessageText(text) {
        // Basic formatting support
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code style="background: rgba(0,0,0,0.1); padding: 2px 4px; border-radius: 4px;">$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'block';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    scrollToBottom() {
        const chatBox = document.getElementById('chatBox');
        chatBox.scrollTo({
            top: chatBox.scrollHeight,
            behavior: 'smooth'
        });
    }

    showStatus(message, isError = false) {
        const chatBox = document.getElementById('chatBox');
        const statusDiv = document.createElement('div');
        statusDiv.className = `status-message ${isError ? 'error-message' : ''}`;
        statusDiv.textContent = message;

        chatBox.appendChild(statusDiv);
        this.scrollToBottom();

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.style.opacity = '0';
                setTimeout(() => statusDiv.remove(), 300);
            }
        }, 3000);
    }

    async handleSendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();

        if (!message || this.isTyping) return;

        // Disable input during processing
        this.isTyping = true;
        input.disabled = true;
        document.getElementById('sendButton').disabled = true;

        // Add user message
        this.addMessage('user', message);

        // Clear input and reset height
        input.value = '';
        input.style.height = 'auto';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            const response = await fetch('/api/chat/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    prospect_name: this.prospectName
                })
            });

            const data = await response.json();

            // Hide typing indicator
            this.hideTypingIndicator();

            if (data.success && data.reply) {
                // Simulate realistic typing delay
                await this.simulateTypingDelay(data.reply);
                this.addMessage('assistant', data.reply);
            } else {
                this.addMessage('assistant', '⚠️ I apologize, but I encountered an error. Please try again.', false);
                this.showStatus(data.error || 'Unknown error occurred', true);
            }

        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('assistant', '⚠️ I\'m having trouble connecting. Please check your internet connection and try again.', false);
            this.showStatus('Network connection error', true);
            console.error('Chat error:', error);
        } finally {
            // Re-enable input
            this.isTyping = false;
            input.disabled = false;
            input.focus();
            document.getElementById('sendButton').disabled = false;
        }
    }

    async simulateTypingDelay(text) {
        // Simulate realistic typing speed (adjust based on message length)
        const baseDelay = 500;
        const charDelay = Math.min(text.length * 20, 2000);
        const totalDelay = baseDelay + charDelay;

        return new Promise(resolve => setTimeout(resolve, totalDelay));
    }

    async loadChatHistory() {
        try {
            this.showStatus('Loading conversation...');

            const response = await fetch(`/api/get-chat-history/${this.sessionId}`);
            const data = await response.json();

            if (data.success) {
                // Load existing messages
                if (data.history && data.history.length > 0) {
                    data.history.forEach(item => {
                        this.addMessage(
                            item.role === 'user' ? 'user' : 'assistant',
                            item.content,
                            false // Don't show timestamps for historical messages
                        );
                    });
                }

                // Start conversation if no messages exist
                if (data.message_count === 0) {
                    await this.startConversation();
                }
            } else {
                this.showStatus('Failed to load conversation history', true);
            }
        } catch (error) {
            console.error('History loading error:', error);
            this.showStatus('Error loading conversation', true);
            // Still try to start conversation
            await this.startConversation();
        }
    }

    async startConversation() {
        try {
            this.showTypingIndicator();

            const response = await fetch('/api/chat/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    prospect_name: this.prospectName
                })
            });

            const data = await response.json();
            this.hideTypingIndicator();

            if (data.success && data.reply) {
                await this.simulateTypingDelay(data.reply);
                this.addMessage('assistant', data.reply);
            } else {
                this.showStatus('Failed to start conversation', true);
            }
        } catch (error) {
            this.hideTypingIndicator();
            console.error('Conversation start error:', error);
            this.showStatus('Error starting conversation', true);
        }
    }
}

// Initialize the enhanced chat application
document.addEventListener('DOMContentLoaded', () => {
    const chat = new EnhancedChatApp();
    const shareBtn = document.getElementById('shareChatBtn');
    if(shareBtn){
        shareBtn.addEventListener('click', async ()=>{
            const url = window.location.href;
            try{
                if(navigator.share){
                    await navigator.share({title:'Chat Session', url});
                }else{
                    await navigator.clipboard.writeText(url);
                    alert('Link copied to clipboard');
                }
            }catch(err){ console.error(err); alert('Share failed'); }
        });
    }
    const resetBtn = document.getElementById('resetChatBtn');
    if(resetBtn){
        resetBtn.addEventListener('click', async ()=>{
            if(!confirm('Clear this conversation?')) return;
            resetBtn.disabled=true;
            resetBtn.innerHTML='<i class="fas fa-spinner fa-spin"></i>';
            try{
                const res = await fetch('/api/reset-chat-session',{method:'POST',headers:{'Content-Type':'application/json'},body: JSON.stringify({session_id: chat.sessionId})});
                const data = await res.json();
                if(data.success){ location.reload(); }
                else { alert(data.message||'Reset failed'); }
            }catch(err){ console.error(err); alert('Reset failed'); }
            resetBtn.disabled=false;
            resetBtn.innerHTML='<i class="fas fa-rotate-left"></i>';
        });
    }
});

// Add some global utility functions for better UX
window.addEventListener('beforeunload', (e) => {
    // Warn user if they're in the middle of a conversation
    const chatBox = document.getElementById('chatBox');
    if (chatBox && chatBox.children.length > 1) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Handle visibility changes (tab switching)
document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
        // User returned to tab, focus input
        const input = document.getElementById('messageInput');
        if (input && !input.disabled) {
            input.focus();
        }
    }
});
</script>
</body>
</html>
