#!/usr/bin/env python3
"""
Test Email Sending and Deletion
===============================
Test the fixed email sending functionality and deletion features.
"""

import requests
import json
from datetime import datetime
import time

# Base URL for the application
BASE_URL = "http://127.0.0.1:5000"

def test_smtp_configuration():
    """Test SMTP configuration"""
    print("📮 Testing SMTP Configuration...")
    
    try:
        response = requests.post(f"{BASE_URL}/api/test-smtp")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("  ✅ SMTP configuration is working")
                print(f"  📧 Message: {result.get('message')}")
                return True
            else:
                print(f"  ❌ SMTP test failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"  ❌ SMTP test endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing SMTP: {str(e)}")
        return False

def create_test_campaign():
    """Create a test campaign for sending"""
    print("📧 Creating Test Campaign...")
    
    campaign_data = {
        'name': f'Email Send Test Campaign {datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '10',  # Small limit for testing
        'send_schedule': 'immediate',
        'batch_size': '5',
        'batch_delay_minutes': '1'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)
        
        if response.status_code in [200, 302]:
            print("  ✅ Test campaign created successfully")
            return True
        else:
            print(f"  ❌ Failed to create test campaign: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error creating test campaign: {str(e)}")
        return False

def get_latest_campaign_id():
    """Get the ID of the latest campaign"""
    try:
        response = requests.get(f"{BASE_URL}/campaigns")
        
        if response.status_code == 200:
            # Parse HTML to find campaign IDs (simplified approach)
            content = response.text
            # Look for campaign send buttons or links that contain campaign IDs
            import re
            campaign_ids = re.findall(r'/campaigns/(\d+)/send', content)
            if campaign_ids:
                return int(campaign_ids[0])  # Return the first (latest) campaign ID
        
        return None
        
    except Exception as e:
        print(f"  ❌ Error getting campaign ID: {str(e)}")
        return None

def test_campaign_sending():
    """Test sending a campaign"""
    print("🚀 Testing Campaign Sending...")
    
    # First create a test campaign
    if not create_test_campaign():
        return False
    
    # Get the campaign ID
    campaign_id = get_latest_campaign_id()
    if not campaign_id:
        print("  ❌ Could not find campaign ID")
        return False
    
    print(f"  📋 Found campaign ID: {campaign_id}")
    
    # Try to send the campaign
    try:
        send_data = {
            'batch_mode': 'immediate',
            'batch_size': '5'
        }
        
        response = requests.post(f"{BASE_URL}/campaigns/{campaign_id}/send", data=send_data)
        
        if response.status_code in [200, 302]:
            print("  ✅ Campaign send request processed")
            
            # Check if we got redirected to campaigns list (success)
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                if 'campaigns' in redirect_url:
                    print("  ✅ Successfully redirected to campaigns list")
                    return True
                else:
                    print(f"  ⚠️ Unexpected redirect: {redirect_url}")
                    return False
            else:
                print("  ✅ Campaign send form processed")
                return True
        else:
            print(f"  ❌ Campaign send failed: {response.status_code}")
            print(f"  Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error sending campaign: {str(e)}")
        return False

def test_campaign_deletion():
    """Test campaign deletion functionality"""
    print("🗑️ Testing Campaign Deletion...")
    
    # Get the campaign ID
    campaign_id = get_latest_campaign_id()
    if not campaign_id:
        print("  ❌ Could not find campaign ID for deletion")
        return False
    
    print(f"  📋 Testing deletion of campaign ID: {campaign_id}")
    
    try:
        delete_data = {
            'force_delete': 'true'  # Force delete to remove all data
        }
        
        response = requests.post(f"{BASE_URL}/campaigns/{campaign_id}/delete", data=delete_data)
        
        if response.status_code in [200, 302]:
            print("  ✅ Campaign deletion request processed")
            
            # Check if we got redirected to campaigns list (success)
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', '')
                if 'campaigns' in redirect_url:
                    print("  ✅ Successfully redirected to campaigns list after deletion")
                    return True
                else:
                    print(f"  ⚠️ Unexpected redirect after deletion: {redirect_url}")
                    return False
            else:
                print("  ✅ Campaign deletion form processed")
                return True
        else:
            print(f"  ❌ Campaign deletion failed: {response.status_code}")
            print(f"  Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error deleting campaign: {str(e)}")
        return False

def test_contact_creation():
    """Create test contacts for email testing"""
    print("👤 Creating Test Contacts...")
    
    test_contacts = [
        {
            'first_name': 'Test',
            'last_name': 'User1',
            'email': '<EMAIL>',
            'company': 'Test Company 1',
            'job_title': 'CEO',
            'phone': '+1234567890',
            'source': 'email_test'
        },
        {
            'first_name': 'Test',
            'last_name': 'User2',
            'email': '<EMAIL>',
            'company': 'Test Company 2',
            'job_title': 'CTO',
            'phone': '+1234567891',
            'source': 'email_test'
        }
    ]
    
    contacts_created = 0
    
    for contact_data in test_contacts:
        try:
            response = requests.post(f"{BASE_URL}/contacts/add", data=contact_data)
            
            if response.status_code in [200, 302]:
                contacts_created += 1
            else:
                print(f"  ⚠️ Failed to create contact {contact_data['email']}: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error creating contact {contact_data['email']}: {str(e)}")
    
    print(f"  ✅ Created {contacts_created}/{len(test_contacts)} test contacts")
    return contacts_created > 0

def test_system_status():
    """Test overall system status"""
    print("🔍 Testing System Status...")
    
    endpoints = [
        ('Dashboard', '/'),
        ('Campaigns', '/campaigns'),
        ('Contacts', '/contacts'),
        ('Analytics', '/analytics'),
    ]
    
    results = []
    
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"  ✅ {name}: Accessible")
                results.append(True)
            else:
                print(f"  ❌ {name}: Error {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {name}: Exception - {str(e)}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"  📊 System Status: {sum(results)}/{len(results)} endpoints accessible ({success_rate:.1f}%)")
    
    return success_rate >= 75

def main():
    """Run email sending and deletion tests"""
    print("🚀 Starting Email Sending and Deletion Tests")
    print("=" * 60)
    
    tests = [
        ("System Status", test_system_status),
        ("SMTP Configuration", test_smtp_configuration),
        ("Contact Creation", test_contact_creation),
        ("Campaign Sending", test_campaign_sending),
        ("Campaign Deletion", test_campaign_deletion),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"Result: {'✅ PASS' if result else '❌ FAIL'}")
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Email System Test Results")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / total * 100
    print(f"\nOverall: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Email sending and deletion are working perfectly.")
    elif passed >= total * 0.8:
        print("✅ Most tests passed! Email system is working well with minor issues.")
    else:
        print("⚠️ Several tests failed. Email system needs attention.")
    
    print("\n🎯 Email System Status:")
    if passed >= total * 0.8:
        print("  • ✅ Campaign creation working")
        print("  • ✅ Email sending functionality fixed")
        print("  • ✅ SMTP configuration working")
        print("  • ✅ Campaign deletion working")
        print("  • ✅ System ready for production use")
    else:
        print("  • ❌ Email system needs debugging")
        print("  • ❌ Check SMTP configuration")
        print("  • ❌ Review error logs")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
