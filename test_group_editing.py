#!/usr/bin/env python3
"""
Test Group Editing Functionality
===============================
Test that group editing is working properly.
"""

import requests
import json

def test_group_editing():
    """Test the group editing functionality"""
    print("🧪 Testing Group Editing Functionality")
    print("=" * 50)
    
    base_url = 'http://localhost:5000'
    
    # Test 1: Check if the server is running
    print("\n1. Checking if server is running...")
    try:
        response = requests.get(f'{base_url}/groups', timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and groups page is accessible")
        else:
            print(f"❌ Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Please make sure the Flask application is running on localhost:5000")
        return False
    
    # Test 2: Check if there are any groups to edit
    print("\n2. Checking for existing groups...")
    try:
        groups_html = response.text
        
        # Look for groups in the page content
        if 'No Contact Groups' in groups_html or 'Create Your First Group' in groups_html:
            print("ℹ️ No groups found. Creating a test group first...")
            
            # Create a test group
            test_group_data = {
                'name': 'Test Group for Editing',
                'description': 'This is a test group created to test editing functionality',
                'color': '#ff6b6b'
            }
            
            create_response = requests.post(
                f'{base_url}/groups/create',
                data=test_group_data,
                allow_redirects=False,
                timeout=10
            )
            
            if create_response.status_code in [200, 302]:
                print("✅ Test group created successfully")
            else:
                print(f"❌ Failed to create test group: {create_response.status_code}")
                return False
        else:
            print("✅ Found existing groups")
        
    except Exception as e:
        print(f"❌ Error checking for groups: {e}")
        return False
    
    # Test 3: Try to access an edit group page
    print("\n3. Testing group edit page access...")
    try:
        # Get the groups page again to find a group ID
        response = requests.get(f'{base_url}/groups', timeout=5)
        groups_html = response.text
        
        # Look for edit links in the HTML
        import re
        edit_links = re.findall(r'/groups/(\d+)/edit', groups_html)
        
        if edit_links:
            group_id = edit_links[0]
            print(f"📝 Found group ID {group_id} to test editing")
            
            # Try to access the edit page
            edit_response = requests.get(f'{base_url}/groups/{group_id}/edit', timeout=10)
            
            if edit_response.status_code == 200:
                print("✅ Edit group page is accessible")
                
                # Check if the page contains the expected form elements
                edit_html = edit_response.text
                if all(field in edit_html for field in ['name="name"', 'name="description"', 'name="color"']):
                    print("✅ Edit form contains all required fields")
                else:
                    print("⚠️ Edit form may be missing some fields")
                
                return True
            else:
                print(f"❌ Edit group page returned status {edit_response.status_code}")
                return False
        else:
            print("❌ No edit links found in groups page")
            return False
        
    except Exception as e:
        print(f"❌ Error testing edit page access: {e}")
        return False

def test_group_update():
    """Test actually updating a group"""
    print("\n4. Testing group update functionality...")
    
    base_url = 'http://localhost:5000'
    
    try:
        # Get the groups page to find a group ID
        response = requests.get(f'{base_url}/groups', timeout=5)
        groups_html = response.text
        
        # Look for edit links in the HTML
        import re
        edit_links = re.findall(r'/groups/(\d+)/edit', groups_html)
        
        if edit_links:
            group_id = edit_links[0]
            print(f"📝 Testing update for group ID {group_id}")
            
            # Prepare update data
            update_data = {
                'name': f'Updated Test Group {group_id}',
                'description': 'This group has been updated by the test script',
                'color': '#28a745'
            }
            
            # Submit the update
            update_response = requests.post(
                f'{base_url}/groups/{group_id}/edit',
                data=update_data,
                allow_redirects=False,
                timeout=10
            )
            
            if update_response.status_code in [200, 302]:
                print("✅ Group update submitted successfully")
                
                # Check if we were redirected to the view page
                if update_response.status_code == 302:
                    location = update_response.headers.get('Location', '')
                    if f'/groups/{group_id}' in location:
                        print("✅ Redirected to group view page as expected")
                    else:
                        print(f"⚠️ Unexpected redirect location: {location}")
                
                return True
            else:
                print(f"❌ Group update failed with status {update_response.status_code}")
                return False
        else:
            print("❌ No groups found to update")
            return False
        
    except Exception as e:
        print(f"❌ Error testing group update: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 GROUP EDITING FUNCTIONALITY TEST")
    print("=" * 60)
    print("This script tests that group editing is working properly.")
    print("=" * 60)
    
    # Test basic functionality
    basic_test_passed = test_group_editing()
    
    # Test update functionality if basic test passed
    update_test_passed = False
    if basic_test_passed:
        update_test_passed = test_group_update()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if basic_test_passed and update_test_passed:
        print("✅ GROUP EDITING IS WORKING!")
        print("\nAll tests passed:")
        print("• Server is accessible")
        print("• Groups page loads correctly")
        print("• Edit group page is accessible")
        print("• Group update functionality works")
        print("\n🎉 You can now edit groups successfully!")
    elif basic_test_passed:
        print("⚠️ PARTIAL SUCCESS")
        print("\nBasic functionality works but update test failed:")
        print("• Groups page loads correctly")
        print("• Edit group page is accessible")
        print("• Group update may have issues")
        print("\nTry editing a group manually to see if it works.")
    else:
        print("❌ GROUP EDITING HAS ISSUES!")
        print("\nPlease check:")
        print("• Is the Flask application running?")
        print("• Are there any server errors in the logs?")
        print("• Try accessing /groups manually in your browser")
    
    return basic_test_passed and update_test_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
