#!/usr/bin/env python3
"""
Input Button Test
================
Test the updated email template with input field converted to a clickable button.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_system.email_templates import EmailTemplateManager

def test_input_button():
    """Test the input field converted to a clickable button"""
    print("🖱️ Input Button Test")
    print("=" * 50)
    
    try:
        # Initialize template manager
        template_manager = EmailTemplateManager()
        
        # Test context
        test_context = {
            'contact_name': 'Test User',
            'company_name': 'Test Company Ltd',
            'agent_name': '<PERSON>',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-input-button',
            'session_id': 'test-input-button'
        }
        
        # Test introduction template
        print("📧 Testing Input Button in Introduction Template...")
        intro_result = template_manager.render_template('introduction', test_context)
        
        # Check for input button features
        html_content = intro_result['html_body']
        
        button_checks = [
            ('Input converted to button', 'Click here to start typing your message...' in html_content),
            ('Text cursor styling', 'cursor: text' in html_content),
            ('Button is clickable link', 'href="{{ chat_url }}"' in html_content),
            ('Input-like appearance', 'border: 2px solid #007bff' in html_content),
            ('Rounded corners', 'border-radius: 25px' in html_content),
            ('Proper padding', 'padding: 12px 18px' in html_content),
            ('Centered text', 'text-align: center' in html_content),
            ('Transition effects', 'transition: all 0.3s ease' in html_content),
            ('Opens in new window', 'target="_blank"' in html_content),
            ('No actual input element', '<input' not in html_content or html_content.count('<input') <= 1)
        ]
        
        print("   Input Button Features:")
        all_passed = True
        for check_name, passed in button_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Test customer support template
        print("\n📧 Testing Input Button in Customer Support Template...")
        support_result = template_manager.render_template('customer_support', test_context)
        support_html = support_result['html_body']
        
        support_checks = [
            ('Input converted to button', 'Click here to start typing your message...' in support_html),
            ('Text cursor styling', 'cursor: text' in support_html),
            ('Flex layout maintained', 'flex: 1' in support_html),
            ('Send button still present', 'chat-send-btn' in support_html)
        ]
        
        print("   Customer Support Template Features:")
        for check_name, passed in support_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Create demo HTML
        demo_html = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Input Button Demo</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        
        .demo-container {{
            background: white;
            max-width: 900px;
            margin: 0 auto;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .demo-header {{
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }}
        
        .demo-content {{
            padding: 20px;
        }}
        
        .success-banner {{
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }}
        
        .email-preview {{
            border: 2px solid #007bff;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            max-height: 600px;
            overflow-y: auto;
        }}
        
        .email-header {{
            background: #007bff;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }}
        
        .features-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        
        .feature-card {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }}
        
        .feature-card h5 {{
            color: #007bff;
            margin-top: 0;
        }}
        
        .improvement-status {{
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h2>🖱️ Input Field Converted to Button!</h2>
            <p>Entire input area is now a clickable button that opens the chatbot</p>
        </div>
        
        <div class="demo-content">
            <div class="success-banner">
                <h4>✅ Input Button Successfully Implemented!</h4>
                <p><strong>The input field now acts as a single clickable button while maintaining the visual appearance of an input field.</strong></p>
            </div>
            
            <div class="email-preview">
                <div class="email-header">
                    📧 Email Template with Input Button
                </div>
                <div style="padding: 15px;">
                    {intro_result['html_body']}
                </div>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h5>🖱️ Button-Like Input</h5>
                    <ul>
                        <li>Entire input area is clickable</li>
                        <li>Text cursor appearance maintained</li>
                        <li>Opens chatbot when clicked anywhere</li>
                        <li>No actual input element needed</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>🎨 Visual Design</h5>
                    <ul>
                        <li>Looks exactly like an input field</li>
                        <li>Same styling and appearance</li>
                        <li>Rounded corners and borders</li>
                        <li>Smooth transition effects</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>🎯 User Experience</h5>
                    <ul>
                        <li>Clear call-to-action text</li>
                        <li>Intuitive clicking behavior</li>
                        <li>Opens chat in new window</li>
                        <li>Consistent with send button</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>📧 Email Compatibility</h5>
                    <ul>
                        <li>Works in all email clients</li>
                        <li>No JavaScript required</li>
                        <li>Simple HTML link element</li>
                        <li>Reliable across platforms</li>
                    </ul>
                </div>
            </div>
            
            <div class="improvement-status">
                <h4 style="color: #1976d2; margin-top: 0;">🚀 Key Improvements:</h4>
                <div style="color: #1976d2;">
                    <strong>✅ Simplified Design:</strong> Single clickable element instead of input + button<br>
                    <strong>✅ Better UX:</strong> Entire area is clickable for easier interaction<br>
                    <strong>✅ Text Cursor:</strong> Maintains input field appearance with text cursor<br>
                    <strong>✅ Clear Action:</strong> "Click here to start typing your message..."<br>
                    <strong>✅ Email Safe:</strong> No complex elements, just a styled link
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <h3 style="color: #007bff;">🎉 Perfect Input Button Implementation!</h3>
                <p style="color: #666;">The input field now acts as a single button while maintaining the visual appearance users expect from an input field.</p>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <strong style="color: #856404;">User Experience:</strong>
                    <ul style="color: #856404; text-align: left; display: inline-block;">
                        <li>Looks like an input field with text cursor</li>
                        <li>Entire area is clickable to open chat</li>
                        <li>Clear instruction text guides users</li>
                        <li>Opens chatbot when clicked anywhere</li>
                        <li>Maintains professional appearance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        '''
        
        # Save the demo
        demo_filename = 'input_button_demo.html'
        with open(demo_filename, 'w', encoding='utf-8') as f:
            f.write(demo_html)
        
        print(f"\n📁 Input button demo saved to: {demo_filename}")
        
        if all_passed:
            print("\n🎉 Input Button Successfully Implemented!")
            print("\n🖱️ Key Features:")
            print("   • Entire input area is now a clickable button")
            print("   • Maintains visual appearance of input field")
            print("   • Text cursor styling for familiar UX")
            print("   • Clear call-to-action text")
            print("   • Opens chatbot when clicked anywhere")
            
            print("\n🎨 Design Benefits:")
            print("   • Looks exactly like an input field")
            print("   • Same styling and visual appearance")
            print("   • Smooth transition effects")
            print("   • Consistent with overall design")
            
            return True
        else:
            print("\n❌ Some input button features failed to implement")
            return False
        
    except Exception as e:
        print(f"❌ Error in input button test: {e}")
        return False

if __name__ == "__main__":
    success = test_input_button()
    sys.exit(0 if success else 1)
