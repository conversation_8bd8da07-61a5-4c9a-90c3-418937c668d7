#!/usr/bin/env python3
"""
Test Enhanced SMTP with Sent Folder Integration
==============================================
Test script that sends emails and saves them to the IMAP sent folder.
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_smtp():
    """Test the enhanced SMTP service with IMAP sent folder integration"""
    print("🧪 Testing Enhanced SMTP with Sent Folder Integration")
    print("=" * 60)
    
    try:
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        
        # Configuration with both SMTP and IMAP settings
        config = {
            # SMTP settings (for sending)
            'MAIL_SERVER': 'mail.24seven.site',
            'MAIL_PORT': 465,
            'MAIL_USE_SSL': True,
            'MAIL_USE_TLS': False,
            'MAIL_USERNAME': '<EMAIL>',
            'MAIL_PASSWORD': 'M@kerere1',
            'MAIL_DEFAULT_SENDER': '<EMAIL>',
            
            # IMAP settings (for saving to sent folder)
            'IMAP_SERVER': 'mail.24seven.site',
            'IMAP_PORT': 993,
            'IMAP_USE_SSL': True,
            'IMAP_SENT_FOLDER': 'INBOX.Sent',
            
            # Integration settings
            'SAVE_TO_SENT_FOLDER': True,
            'EMAIL_DELAY_SECONDS': 2
        }
        
        print("📧 Initializing Enhanced SMTP Service...")
        smtp_service = EnhancedSMTPService(config)
        
        # Test connections
        print("\n🔌 Testing Connections...")
        success, message = smtp_service.test_connection()
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return False
        
        # Send test email
        print("\n📤 Sending Test Email with Sent Folder Integration...")
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Email content
        subject = f"Test Email with Sent Folder Integration - {timestamp}"
        
        html_body = f"""
        <html>
        <body>
            <h2>🧪 Enhanced SMTP Test Email</h2>
            <p>This email was sent using the enhanced SMTP service that automatically saves emails to the IMAP sent folder.</p>
            
            <h3>Test Details:</h3>
            <ul>
                <li><strong>Sent at:</strong> {timestamp}</li>
                <li><strong>SMTP Server:</strong> mail.24seven.site:465 (SSL)</li>
                <li><strong>IMAP Server:</strong> mail.24seven.site:993 (SSL)</li>
                <li><strong>Sent Folder:</strong> INBOX.Sent</li>
            </ul>
            
            <h3>Features Tested:</h3>
            <ul>
                <li>✅ SMTP email sending</li>
                <li>✅ IMAP sent folder integration</li>
                <li>✅ Automatic email archiving</li>
                <li>✅ HTML and text content</li>
            </ul>
            
            <p>If you can see this email in your IMAP sent folder, the integration is working perfectly!</p>
            
            <hr>
            <p><em>24Seven Assistants Sales System<br>
            Enhanced Email Integration Test</em></p>
        </body>
        </html>
        """
        
        text_body = f"""
Enhanced SMTP Test Email

This email was sent using the enhanced SMTP service that automatically saves emails to the IMAP sent folder.

Test Details:
- Sent at: {timestamp}
- SMTP Server: mail.24seven.site:465 (SSL)
- IMAP Server: mail.24seven.site:993 (SSL)
- Sent Folder: INBOX.Sent

Features Tested:
✅ SMTP email sending
✅ IMAP sent folder integration
✅ Automatic email archiving
✅ HTML and text content

If you can see this email in your IMAP sent folder, the integration is working perfectly!

---
24Seven Assistants Sales System
Enhanced Email Integration Test
        """
        
        # Send the email
        success, message_id, error_msg = smtp_service.send_email(
            to_email='<EMAIL>',  # Send to self for testing
            subject=subject,
            html_body=html_body,
            text_body=text_body,
            from_name='24Seven Assistants Enhanced SMTP'
        )
        
        if success:
            print(f"✅ Email sent successfully!")
            print(f"   Message ID: {message_id}")
            print(f"   Recipient: <EMAIL>")
            print(f"   Subject: {subject}")
            print(f"   ✅ Email should now appear in INBOX.Sent folder")
            
            # Wait a moment for the email to be processed
            print("\n⏳ Waiting 5 seconds for email processing...")
            import time
            time.sleep(5)
            
            # Test IMAP retrieval
            print("\n📬 Testing IMAP Retrieval...")
            test_imap_retrieval()
            
            return True
        else:
            print(f"❌ Email sending failed: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced SMTP test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_imap_retrieval():
    """Test retrieving the sent email from IMAP"""
    try:
        from email_system.imap_service import IMAPService
        
        imap_config = {
            'IMAP_SERVER': 'mail.24seven.site',
            'IMAP_PORT': 993,
            'IMAP_USE_SSL': True,
            'MAIL_USERNAME': '<EMAIL>',
            'MAIL_PASSWORD': 'M@kerere1',
            'IMAP_SENT_FOLDER': 'INBOX.Sent'
        }
        
        imap_service = IMAPService(imap_config)
        
        # Get recent sent emails
        sent_emails = imap_service.get_sent_emails(limit=5, days_back=1)
        
        if sent_emails:
            print(f"✅ Found {len(sent_emails)} emails in sent folder:")
            
            for i, email in enumerate(sent_emails, 1):
                print(f"\n   Email {i}:")
                print(f"      Subject: {email.get('subject', 'No Subject')}")
                print(f"      To: {email.get('to', 'Unknown')}")
                print(f"      Date: {email.get('date', 'Unknown')}")
                
                # Check if this is our test email
                if 'Enhanced SMTP Test' in email.get('subject', ''):
                    print(f"      🎉 Found our test email!")
                    return True
            
            print(f"\n✅ IMAP retrieval working, but test email may still be processing")
            return True
        else:
            print(f"⚠️ No emails found in sent folder yet")
            print(f"   Email may still be processing or server may have delay")
            return False
            
    except Exception as e:
        print(f"❌ IMAP retrieval test failed: {str(e)}")
        return False

def test_bulk_sending():
    """Test bulk email sending with sent folder integration"""
    print("\n📧 Testing Bulk Email Sending...")
    print("-" * 40)
    
    try:
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        
        config = {
            'MAIL_SERVER': 'mail.24seven.site',
            'MAIL_PORT': 465,
            'MAIL_USE_SSL': True,
            'MAIL_USERNAME': '<EMAIL>',
            'MAIL_PASSWORD': 'M@kerere1',
            'MAIL_DEFAULT_SENDER': '<EMAIL>',
            'IMAP_SERVER': 'mail.24seven.site',
            'IMAP_PORT': 993,
            'IMAP_USE_SSL': True,
            'IMAP_SENT_FOLDER': 'INBOX.Sent',
            'SAVE_TO_SENT_FOLDER': True
        }
        
        smtp_service = EnhancedSMTPService(config)
        
        # Create test emails
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        emails_to_send = [
            {
                'to_email': '<EMAIL>',
                'subject': f'Bulk Test Email 1 - {timestamp}',
                'html_body': f'<h3>Bulk Test Email 1</h3><p>Sent at {timestamp}</p>',
                'text_body': f'Bulk Test Email 1\nSent at {timestamp}'
            },
            {
                'to_email': '<EMAIL>',
                'subject': f'Bulk Test Email 2 - {timestamp}',
                'html_body': f'<h3>Bulk Test Email 2</h3><p>Sent at {timestamp}</p>',
                'text_body': f'Bulk Test Email 2\nSent at {timestamp}'
            }
        ]
        
        # Send bulk emails
        results = smtp_service.send_bulk_emails(emails_to_send, delay_between_emails=1)
        
        successful_sends = sum(1 for r in results if r['success'])
        saved_to_sent = sum(1 for r in results if r.get('saved_to_sent', False))
        
        print(f"✅ Bulk sending completed:")
        print(f"   Emails sent: {successful_sends}/{len(emails_to_send)}")
        print(f"   Saved to sent folder: {saved_to_sent}/{len(emails_to_send)}")
        
        return successful_sends == len(emails_to_send)
        
    except Exception as e:
        print(f"❌ Bulk sending test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 24Seven Assistants - Enhanced SMTP with Sent Folder Test")
    print("=" * 70)
    print("This test will send emails and save them to the IMAP sent folder")
    print()
    
    # Test 1: Single email with sent folder integration
    single_success = test_enhanced_smtp()
    
    # Test 2: Bulk email sending
    bulk_success = test_bulk_sending()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"Single Email Test:  {'✅ PASS' if single_success else '❌ FAIL'}")
    print(f"Bulk Email Test:    {'✅ PASS' if bulk_success else '❌ FAIL'}")
    
    if single_success and bulk_success:
        print("\n🎉 All tests passed! Enhanced SMTP with sent folder integration is working!")
        print("\n💡 Next steps:")
        print("1. Check your email inbox for test emails")
        print("2. Check the web interface: http://localhost:5000/emails/sent")
        print("3. You should now see sent emails in the IMAP sent folder")
        print("4. The system will automatically save all campaign emails to sent folder")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")
    
    print(f"\n📧 Check your email and IMAP sent folder for the test emails!")

if __name__ == "__main__":
    main()
