#!/usr/bin/env python3
"""
Start Application - Simple Version
==================================
Start the 24Seven Assistants application with Gmail configuration
"""

import os
import sys

def setup_environment():
    """Set up environment variables manually"""
    print("🔧 Setting up Gmail configuration...")
    
    # Gmail configuration from .env file
    os.environ['MAIL_SERVER'] = 'smtp.gmail.com'
    os.environ['MAIL_PORT'] = '587'
    os.environ['MAIL_USE_TLS'] = 'true'
    os.environ['MAIL_USE_SSL'] = 'false'
    os.environ['MAIL_USERNAME'] = '<EMAIL>'
    os.environ['MAIL_PASSWORD'] = 'wkff biod dzyv obon'
    
    # Additional Flask configuration
    os.environ['SECRET_KEY'] = 'your-secret-key-here'
    os.environ['DATABASE_URL'] = 'sqlite:///sales_system.db'
    
    print("✅ Environment variables configured")
    print("📧 Email server: smtp.gmail.com:587")
    print("📧 Username: <EMAIL>")
    print("📧 TLS: Enabled")

def start_application():
    """Start the Flask application"""
    try:
        print("\n🚀 Starting 24Seven Assistants Sales System...")
        print("=" * 60)
        
        # Import the application
        from unified_sales_system import app
        
        print("✅ Application loaded successfully")
        print("\n🌐 Web Interface: http://localhost:5000")
        print("📊 Campaigns: http://localhost:5000/campaigns")
        print("👥 Contacts: http://localhost:5000/contacts")
        print("🧪 SMTP Test: http://localhost:5000/test-smtp")
        print("\n⚡ Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Start the Flask development server
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n⚡ Application stopped by user")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """Main function"""
    print("🚀 24SEVEN ASSISTANTS - SIMPLE STARTUP")
    print("=" * 60)
    print("Starting with Gmail SMTP configuration...")
    print("=" * 60)
    
    # Set up environment
    setup_environment()
    
    # Start application
    success = start_application()
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚡ Startup interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Startup failed: {e}")
        sys.exit(1)
