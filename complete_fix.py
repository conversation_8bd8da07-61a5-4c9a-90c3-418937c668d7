#!/usr/bin/env python3
"""
Complete Fix for Campaign Group Issue
====================================
This will fix the campaign group selection issue completely.
"""

import sqlite3
import json
import os

def complete_fix():
    """Complete fix for the campaign group issue"""
    try:
        db_path = "sales_system.db"
        if not os.path.exists(db_path):
            print(f"❌ Database file not found: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 COMPLETE FIX FOR CAMPAIGN GROUP ISSUE")
        print("=" * 50)
        
        # 1. Check if "test" group exists, if not create it
        cursor.execute("SELECT id, name FROM contact_groups WHERE name = 'test'")
        test_group = cursor.fetchone()
        
        if not test_group:
            print("📝 Creating 'test' group...")
            cursor.execute("""
                INSERT INTO contact_groups (name, description, is_active, created_at)
                VALUES ('test', 'Test group for campaign', 1, datetime('now'))
            """)
            test_group_id = cursor.lastrowid
            print(f"✅ Created 'test' group with ID: {test_group_id}")
        else:
            test_group_id = test_group[0]
            print(f"✅ Found existing 'test' group with ID: {test_group_id}")
        
        # 2. Find a contact to add to the test group (preferably alan wolf)
        cursor.execute("""
            SELECT id, first_name, last_name, email 
            FROM contacts 
            WHERE (email LIKE '%alan%' OR first_name LIKE '%alan%' OR last_name LIKE '%wolf%')
            AND is_active = 1 
            LIMIT 1
        """)
        alan_contact = cursor.fetchone()
        
        if not alan_contact:
            # If alan not found, use the first active contact
            cursor.execute("""
                SELECT id, first_name, last_name, email 
                FROM contacts 
                WHERE is_active = 1 
                LIMIT 1
            """)
            alan_contact = cursor.fetchone()
        
        if not alan_contact:
            print("❌ No active contacts found")
            return False
        
        contact_id, first_name, last_name, email = alan_contact
        print(f"👤 Using contact: {first_name} {last_name} ({email})")
        
        # 3. Add contact to test group (if not already there)
        cursor.execute("""
            SELECT id FROM contact_group_memberships 
            WHERE contact_id = ? AND group_id = ?
        """, (contact_id, test_group_id))
        
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO contact_group_memberships (contact_id, group_id, added_at)
                VALUES (?, ?, datetime('now'))
            """, (contact_id, test_group_id))
            print(f"✅ Added {first_name} {last_name} to 'test' group")
        else:
            print(f"✅ {first_name} {last_name} already in 'test' group")
        
        # 4. Create or update the "24seven Assistants" campaign
        cursor.execute("SELECT id, name FROM email_campaigns WHERE name = '24seven Assistants'")
        campaign = cursor.fetchone()
        
        if not campaign:
            print("📝 Creating '24seven Assistants' campaign...")
            cursor.execute("""
                INSERT INTO email_campaigns (
                    name, subject, template_name, status, 
                    daily_send_limit, created_at
                ) VALUES (
                    '24seven Assistants',
                    '24Seven Assistants - Meet Sarah, Your AI Sales Assistant',
                    'introduction',
                    'draft',
                    100,
                    datetime('now')
                )
            """)
            campaign_id = cursor.lastrowid
            print(f"✅ Created campaign with ID: {campaign_id}")
        else:
            campaign_id = campaign[0]
            print(f"✅ Found existing campaign with ID: {campaign_id}")
        
        # 5. Set proper recipient criteria for the campaign
        recipient_criteria = {
            'type': 'groups',
            'group_ids': [test_group_id]
        }
        
        criteria_json = json.dumps(recipient_criteria)
        
        cursor.execute("""
            UPDATE email_campaigns 
            SET recipient_criteria = ?, status = 'draft'
            WHERE id = ?
        """, (criteria_json, campaign_id))
        
        print(f"✅ Updated campaign recipient criteria")
        print(f"   Criteria: {criteria_json}")
        
        # 6. Commit all changes
        conn.commit()
        
        # 7. Verify the fix
        print(f"\n🔍 VERIFICATION:")
        
        # Check group membership
        cursor.execute("""
            SELECT c.first_name, c.last_name, c.email
            FROM contacts c
            JOIN contact_group_memberships cgm ON c.id = cgm.contact_id
            WHERE cgm.group_id = ? AND c.is_active = 1
        """, (test_group_id,))
        
        group_contacts = cursor.fetchall()
        print(f"   Contacts in 'test' group: {len(group_contacts)}")
        for contact in group_contacts:
            print(f"     - {contact[0]} {contact[1]} ({contact[2]})")
        
        # Check campaign criteria
        cursor.execute("SELECT recipient_criteria FROM email_campaigns WHERE id = ?", (campaign_id,))
        saved_criteria = cursor.fetchone()[0]
        print(f"   Campaign criteria: {saved_criteria}")
        
        conn.close()
        
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"✅ 'test' group created/verified with {len(group_contacts)} contact(s)")
        print(f"✅ '24seven Assistants' campaign configured to send to 'test' group only")
        print(f"✅ Campaign status set to 'draft' and ready to send")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"   1. Go to your campaigns page in the web interface")
        print(f"   2. Find the '24seven Assistants' campaign")
        print(f"   3. Click 'Send Campaign'")
        print(f"   4. It will now send to ONLY {len(group_contacts)} contact(s) in the 'test' group")
        print(f"   5. Check the server logs to see the debugging output")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in complete fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    complete_fix()
