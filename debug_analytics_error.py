#!/usr/bin/env python3
"""
Debug Analytics Error
====================
This script tests the analytics functions to identify the specific error
causing the comprehensive analytics to fail.
"""

import sys
import os
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_analytics_functions():
    """Test each analytics function individually"""
    print("🔍 Testing Analytics Functions")
    print("=" * 50)
    
    try:
        # Import the unified sales system
        from unified_sales_system import (
            get_unified_analytics, 
            get_session_analytics, 
            get_sales_pipeline_analytics, 
            get_sales_cycle_analytics
        )
        print("✅ Successfully imported analytics functions")
        
        # Test get_unified_analytics
        print("\n📊 Testing get_unified_analytics()...")
        try:
            analytics = get_unified_analytics()
            print(f"✅ get_unified_analytics() returned: {type(analytics)}")
            print(f"   Keys: {list(analytics.keys()) if isinstance(analytics, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ get_unified_analytics() failed: {e}")
            traceback.print_exc()
        
        # Test get_session_analytics
        print("\n💬 Testing get_session_analytics()...")
        try:
            session_data = get_session_analytics()
            print(f"✅ get_session_analytics() returned: {type(session_data)}")
            print(f"   Keys: {list(session_data.keys()) if isinstance(session_data, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ get_session_analytics() failed: {e}")
            traceback.print_exc()
        
        # Test get_sales_pipeline_analytics
        print("\n📈 Testing get_sales_pipeline_analytics()...")
        try:
            pipeline_data = get_sales_pipeline_analytics()
            print(f"✅ get_sales_pipeline_analytics() returned: {type(pipeline_data)}")
            print(f"   Keys: {list(pipeline_data.keys()) if isinstance(pipeline_data, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ get_sales_pipeline_analytics() failed: {e}")
            traceback.print_exc()
        
        # Test get_sales_cycle_analytics
        print("\n⏱️ Testing get_sales_cycle_analytics()...")
        try:
            cycle_data = get_sales_cycle_analytics()
            print(f"✅ get_sales_cycle_analytics() returned: {type(cycle_data)}")
            print(f"   Keys: {list(cycle_data.keys()) if isinstance(cycle_data, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ get_sales_cycle_analytics() failed: {e}")
            traceback.print_exc()
        
        # Test comprehensive data creation
        print("\n🔄 Testing comprehensive data creation...")
        try:
            analytics = get_unified_analytics()
            session_data = get_session_analytics()
            pipeline_data = get_sales_pipeline_analytics()
            cycle_data = get_sales_cycle_analytics()
            
            comprehensive_data = {
                'unified': analytics,
                'sessions': session_data,
                'pipeline': pipeline_data,
                'cycle': cycle_data
            }
            print(f"✅ Comprehensive data created successfully")
            print(f"   Data structure: {list(comprehensive_data.keys())}")
            
        except Exception as e:
            print(f"❌ Comprehensive data creation failed: {e}")
            traceback.print_exc()
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ General error: {e}")
        traceback.print_exc()
        return False

def test_plotly_imports():
    """Test if plotly imports are working"""
    print("\n📊 Testing Plotly Imports")
    print("=" * 30)
    
    try:
        import plotly.graph_objects as go
        import plotly.utils
        import json
        print("✅ Plotly imports successful")
        
        # Test basic chart creation
        fig = go.Figure(go.Funnel(
            y=['Test 1', 'Test 2'],
            x=[100, 50],
            textinfo="value+percent initial"
        ))
        fig.update_layout(title='Test Chart')
        
        # Test JSON encoding
        chart_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
        print("✅ Plotly chart creation and JSON encoding successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Plotly error: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection"""
    print("\n🗄️ Testing Database Connection")
    print("=" * 35)
    
    try:
        from unified_sales_system import db, Contact, EmailCampaign, ChatbotSession
        
        # Test basic queries
        contact_count = Contact.query.count()
        campaign_count = EmailCampaign.query.count()
        session_count = ChatbotSession.query.count()
        
        print(f"✅ Database connection successful")
        print(f"   Contacts: {contact_count}")
        print(f"   Campaigns: {campaign_count}")
        print(f"   Sessions: {session_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🐛 ANALYTICS ERROR DEBUGGING")
    print("=" * 60)
    
    # Run all tests
    db_success = test_database_connection()
    plotly_success = test_plotly_imports()
    analytics_success = test_analytics_functions()
    
    print("\n📋 SUMMARY")
    print("=" * 20)
    print(f"Database: {'✅' if db_success else '❌'}")
    print(f"Plotly: {'✅' if plotly_success else '❌'}")
    print(f"Analytics: {'✅' if analytics_success else '❌'}")
    
    if all([db_success, plotly_success, analytics_success]):
        print("\n🎉 All tests passed! The issue might be in the template or route.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
