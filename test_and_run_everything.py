#!/usr/bin/env python3
"""
Test and Run Everything
=======================
Comprehensive test and startup script for 24Seven Assistants Sales System
"""

import os
import sys
import time
import subprocess
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def load_env_file():
    """Load and validate .env file"""
    print("🔧 Step 1: Loading .env file...")
    print("-" * 40)

    try:
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value

        print(f"✅ Loaded {len(env_vars)} environment variables")

        # Validate email configuration
        required_vars = ['MAIL_SERVER', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_PASSWORD']
        missing_vars = [var for var in required_vars if var not in env_vars]

        if missing_vars:
            print(f"❌ Missing required variables: {missing_vars}")
            return False

        print("📧 Email configuration:")
        print(f"   Server: {env_vars['MAIL_SERVER']}:{env_vars['MAIL_PORT']}")
        print(f"   Username: {env_vars['MAIL_USERNAME']}")
        print(f"   Password: {'*' * len(env_vars['MAIL_PASSWORD'])}")
        print(f"   TLS: {env_vars.get('MAIL_USE_TLS', 'true')}")
        print(f"   SSL: {env_vars.get('MAIL_USE_SSL', 'false')}")

        return True

    except FileNotFoundError:
        print("❌ .env file not found")
        return False
    except Exception as e:
        print(f"❌ Error loading .env file: {e}")
        return False

def test_smtp_connection():
    """Test SMTP connection with Gmail"""
    print("\n🔗 Step 2: Testing SMTP Connection...")
    print("-" * 40)

    try:
        smtp_server = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
        smtp_port = int(os.environ.get('MAIL_PORT', '587'))
        username = os.environ.get('MAIL_USERNAME')
        password = os.environ.get('MAIL_PASSWORD')
        use_tls = os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true'

        print(f"Connecting to {smtp_server}:{smtp_port}...")

        # Create connection
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)

        if use_tls:
            print("Starting TLS...")
            context = ssl.create_default_context()
            server.starttls(context=context)

        print("Authenticating...")
        server.login(username, password)

        print("✅ SMTP connection successful!")
        server.quit()
        return True

    except Exception as e:
        print(f"❌ SMTP connection failed: {e}")
        return False

def send_test_email():
    """Send a test email"""
    print("\n📧 Step 3: Sending Test Email...")
    print("-" * 40)

    try:
        smtp_server = os.environ.get('MAIL_SERVER')
        smtp_port = int(os.environ.get('MAIL_PORT'))
        username = os.environ.get('MAIL_USERNAME')
        password = os.environ.get('MAIL_PASSWORD')

        # Create connection
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
        context = ssl.create_default_context()
        server.starttls(context=context)
        server.login(username, password)

        # Create test message
        msg = MIMEMultipart()
        msg['From'] = f"24Seven Assistants <{username}>"
        msg['To'] = username  # Send to self
        msg['Subject'] = "✅ System Test - Email Campaign Ready!"

        body = f"""
🎉 SUCCESS! Email Campaign System Test Complete

This email confirms that your 24Seven Assistants email campaign system is working correctly.

✅ Environment variables loaded from .env file
✅ SMTP connection to Gmail successful
✅ Email authentication working
✅ Email delivery functional

📧 Configuration Details:
• Server: {smtp_server}:{smtp_port}
• Username: {username}
• TLS: Enabled
• Authentication: Successful

🚀 Your email campaign system is now ready to:
• Send email campaigns to your contact list
• Track email opens and clicks
• Generate chatbot links for sales conversations
• Monitor conversion rates and analytics

Next steps:
1. The application will start automatically after this test
2. Go to http://localhost:5000/campaigns
3. Create or send email campaigns
4. Monitor results in the dashboard

Best regards,
24Seven Assistants Sales System
Test completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}
        """

        msg.attach(MIMEText(body, 'plain'))

        print(f"Sending test email to {username}...")
        server.send_message(msg)
        server.quit()

        print("✅ Test email sent successfully!")
        print(f"📬 Check your inbox at {username}")
        return True

    except Exception as e:
        print(f"❌ Test email failed: {e}")
        return False

def check_dependencies():
    """Check if required Python packages are installed"""
    print("\n📦 Step 4: Checking Dependencies...")
    print("-" * 40)

    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_mail',
        'requests',
        'python-dotenv'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n⚠️ Missing packages: {missing_packages}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False

    print("✅ All dependencies available")
    return True

def test_flask_app():
    """Test Flask application import"""
    print("\n🌐 Step 5: Testing Flask Application...")
    print("-" * 40)

    try:
        print("Importing unified_sales_system...")
        from unified_sales_system import app

        print("Testing app context...")
        with app.app_context():
            print("✅ Flask application loaded successfully")

            # Check email configuration in Flask
            email_config = {
                'MAIL_SERVER': app.config.get('MAIL_SERVER'),
                'MAIL_PORT': app.config.get('MAIL_PORT'),
                'MAIL_USERNAME': app.config.get('MAIL_USERNAME'),
                'MAIL_USE_TLS': app.config.get('MAIL_USE_TLS'),
            }

            print("📧 Flask email configuration:")
            for key, value in email_config.items():
                print(f"   {key}: {value}")

        return True

    except Exception as e:
        print(f"❌ Flask application test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_application():
    """Start the Flask application"""
    print("\n🚀 Step 6: Starting Application...")
    print("-" * 40)

    try:
        print("Loading application with Gmail configuration...")

        from unified_sales_system import app

        print("✅ Application loaded successfully")
        print("\n" + "=" * 60)
        print("🎉 24SEVEN ASSISTANTS SALES SYSTEM")
        print("=" * 60)
        print("📧 Email Server: Gmail (smtp.gmail.com)")
        print("🌐 Web Interface: http://localhost:5000")
        print("📊 Campaigns: http://localhost:5000/campaigns")
        print("👥 Contacts: http://localhost:5000/contacts")
        print("⚡ Press Ctrl+C to stop")
        print("=" * 60)

        # Start the application
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)

    except KeyboardInterrupt:
        print("\n\n⚡ Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test and run function"""
    print("🚀 24SEVEN ASSISTANTS - COMPREHENSIVE TEST & RUN")
    print("=" * 60)
    print("Testing all components and starting the application...")
    print("=" * 60)

    # Step 1: Load environment variables
    env_success = load_env_file()
    if not env_success:
        print("\n❌ Environment setup failed. Please check your .env file.")
        return False

    # Step 2: Test SMTP connection
    smtp_success = test_smtp_connection()
    if not smtp_success:
        print("\n❌ SMTP test failed. Please check your Gmail credentials.")
        return False

    # Step 3: Send test email
    email_success = send_test_email()
    if not email_success:
        print("\n❌ Test email failed.")
        return False

    # Step 4: Check dependencies
    deps_success = check_dependencies()
    if not deps_success:
        print("\n❌ Dependencies check failed.")
        return False

    # Step 5: Test Flask app
    flask_success = test_flask_app()
    if not flask_success:
        print("\n❌ Flask application test failed.")
        return False

    # Summary before starting
    print("\n" + "=" * 60)
    print("📊 PRE-STARTUP TEST SUMMARY")
    print("=" * 60)
    print("✅ Environment Variables: LOADED")
    print("✅ SMTP Connection: WORKING")
    print("✅ Test Email: SENT")
    print("✅ Dependencies: AVAILABLE")
    print("✅ Flask Application: READY")
    print("\n🎉 ALL TESTS PASSED! Starting application...")

    # Wait a moment
    time.sleep(2)

    # Step 6: Start application
    start_application()

    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚡ Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
