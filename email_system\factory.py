"""
Email System Factory
===================
Factory for creating and initializing email system components.
"""

import logging
from typing import <PERSON><PERSON>, Optional
from .config import get_email_config, validate_email_config, print_email_config
from .enhanced_smtp_service import EnhancedSMTPService
from .email_templates import EmailTemplateManager

logger = logging.getLogger(__name__)


class EmailSystemFactory:
    """Factory for creating email system components"""

    def __init__(self, config: dict = None, db_session=None):
        """
        Initialize email system factory

        Args:
            config: Optional email configuration dictionary
            db_session: Database session for campaign manager and tracker
        """
        self.config = config or get_email_config()
        self.db_session = db_session
        self._smtp_service = None
        self._template_manager = None
        self._campaign_manager = None
        self._email_tracker = None

    def validate_config(self) -> Tuple[bool, str]:
        """Validate the email configuration"""
        return validate_email_config(self.config)

    def print_config(self):
        """Print the current configuration"""
        print_email_config(self.config)

    def create_smtp_service(self) -> EnhancedSMTPService:
        """Create and return SMTP service"""
        if self._smtp_service is None:
            logger.info("Creating EnhancedSMTPService...")
            self._smtp_service = EnhancedSMTPService(self.config)
        return self._smtp_service

    def create_template_manager(self) -> EmailTemplateManager:
        """Create and return email template manager"""
        if self._template_manager is None:
            logger.info("Creating EmailTemplateManager...")
            self._template_manager = EmailTemplateManager()
        return self._template_manager

    def create_campaign_manager(self):
        """Create and return campaign manager"""
        if self._campaign_manager is None:
            if self.db_session is None:
                raise ValueError("Database session required for CampaignManager")

            logger.info("Creating CampaignManager...")
            # Import locally to avoid circular imports
            from .campaign_manager import CampaignManager

            smtp_service = self.create_smtp_service()
            template_manager = self.create_template_manager()
            self._campaign_manager = CampaignManager(
                self.db_session,
                smtp_service,
                template_manager
            )
        return self._campaign_manager

    def create_email_tracker(self):
        """Create and return email tracker"""
        if self._email_tracker is None:
            if self.db_session is None:
                raise ValueError("Database session required for EmailTracker")

            logger.info("Creating EmailTracker...")
            # Import locally to avoid circular imports
            from .email_tracker import EmailTracker

            self._email_tracker = EmailTracker(self.db_session)
        return self._email_tracker

    def test_smtp_connection(self) -> Tuple[bool, str]:
        """Test SMTP and IMAP connections"""
        try:
            smtp_service = self.create_smtp_service()
            return smtp_service.test_connection()
        except Exception as e:
            error_msg = f"Failed to test SMTP connection: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def send_test_email(self, to_email: str = None) -> Tuple[bool, str]:
        """
        Send a test email to verify the system is working

        Args:
            to_email: Email address to send test to. If None, sends to default sender.

        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            smtp_service = self.create_smtp_service()
            template_manager = self.create_template_manager()

            # Use default sender if no email provided
            test_email = to_email or self.config['MAIL_DEFAULT_SENDER']

            # Render test template
            template_data = template_manager.render_template('introduction', {
                'contact_name': 'Test User',
                'company_name': 'Test Company',
                'agent_name': 'Sarah',
                'reply_email': self.config['MAIL_DEFAULT_SENDER'],
                'phone_number': '+****************',
                'industry': 'Technology'
            })

            # Send test email
            success, message_id, error_msg = smtp_service.send_email(
                to_email=test_email,
                subject=f"[TEST] {template_data['subject']}",
                html_body=template_data['html_body'],
                text_body=template_data['text_body'],
                from_name="24Seven Assistants Test",
                from_email=self.config['MAIL_DEFAULT_SENDER']
            )

            if success:
                return True, f"Test email sent successfully to {test_email}. Message ID: {message_id}"
            else:
                return False, f"Failed to send test email: {error_msg}"

        except Exception as e:
            error_msg = f"Test email failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def create_complete_system(self) -> dict:
        """
        Create all email system components and return them as a dictionary

        Returns:
            Dictionary with all email system components
        """
        if self.db_session is None:
            raise ValueError("Database session required for complete system")

        logger.info("Creating complete email system...")

        return {
            'smtp_service': self.create_smtp_service(),
            'template_manager': self.create_template_manager(),
            'campaign_manager': self.create_campaign_manager(),
            'email_tracker': self.create_email_tracker(),
            'config': self.config
        }


def create_email_system(config: dict = None, db_session=None) -> dict:
    """
    Convenience function to create complete email system

    Args:
        config: Optional email configuration dictionary
        db_session: Database session for campaign manager and tracker

    Returns:
        Dictionary with all email system components
    """
    factory = EmailSystemFactory(config, db_session)

    # Validate configuration
    is_valid, error_msg = factory.validate_config()
    if not is_valid:
        raise ValueError(f"Invalid email configuration: {error_msg}")

    return factory.create_complete_system()


def test_email_system(config: dict = None, test_email: str = None) -> Tuple[bool, str]:
    """
    Test the email system configuration and connectivity

    Args:
        config: Optional email configuration dictionary
        test_email: Optional email address to send test to

    Returns:
        Tuple of (success: bool, message: str)
    """
    try:
        factory = EmailSystemFactory(config)

        # Validate configuration
        is_valid, error_msg = factory.validate_config()
        if not is_valid:
            return False, f"Configuration validation failed: {error_msg}"

        # Test SMTP connection
        smtp_success, smtp_msg = factory.test_smtp_connection()
        if not smtp_success:
            return False, f"SMTP connection test failed: {smtp_msg}"

        # Send test email if requested
        if test_email:
            email_success, email_msg = factory.send_test_email(test_email)
            if not email_success:
                return False, f"Test email failed: {email_msg}"
            return True, f"All tests passed. {smtp_msg} {email_msg}"
        else:
            return True, f"Connection test passed. {smtp_msg}"

    except Exception as e:
        error_msg = f"Email system test failed: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
