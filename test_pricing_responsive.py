#!/usr/bin/env python3
"""
Test Pricing Section Responsive Design
=====================================
Test script to verify the pricing section displays correctly on all devices.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_system.email_templates import EmailTemplateManager

def test_pricing_responsive():
    """Test the responsive pricing section"""
    print("🧪 Testing Pricing Section Responsive Design")
    print("=" * 60)
    
    try:
        # Initialize template manager
        template_manager = EmailTemplateManager()
        
        # Test context
        test_context = {
            'contact_name': '<PERSON>',
            'company_name': 'Test Company Ltd',
            'agent_name': '<PERSON>',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-session-123',
            'session_id': 'test-session-123'
        }
        
        # Test introduction template
        print("📧 Testing Introduction Template Pricing Section...")
        intro_result = template_manager.render_template('introduction', test_context)
        
        # Check for responsive design features
        html_content = intro_result['html_body']
        
        pricing_checks = [
            ('Pricing section exists', 'pricing-section' in html_content),
            ('Pricing grid layout', 'pricing-grid' in html_content),
            ('Pricing cards', 'pricing-card' in html_content),
            ('Flex-wrap enabled', 'flex-wrap: wrap' in html_content),
            ('Max-width constraint', 'max-width: 300px' in html_content),
            ('Min-width constraint', 'min-width: 250px' in html_content),
            ('Overflow protection', 'overflow: hidden' in html_content),
            ('Mobile responsive', '@media only screen and (max-width: 768px)' in html_content),
            ('Small mobile responsive', '@media only screen and (max-width: 480px)' in html_content),
            ('Extra small responsive', '@media only screen and (max-width: 360px)' in html_content),
            ('Column layout on mobile', 'flex-direction: column' in html_content),
            ('Full width on mobile', 'width: 100%' in html_content),
            ('Reduced padding on small screens', 'padding: 15px 8px' in html_content)
        ]
        
        print("   Pricing Responsive Features:")
        all_passed = True
        for check_name, passed in pricing_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Check specific pricing content
        content_checks = [
            ('Small Business pricing', 'UGX 250K setup' in html_content),
            ('Medium Business pricing', 'UGX 500K setup' in html_content),
            ('Large Enterprise pricing', 'UGX 3M setup' in html_content),
            ('Monthly pricing display', 'UGX 100K/month' in html_content),
            ('Pricing descriptions', 'Perfect for startups' in html_content)
        ]
        
        print("\n   Pricing Content:")
        for check_name, passed in content_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Save test HTML for manual inspection
        test_filename = 'pricing_responsive_test.html'
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write(intro_result['html_body'])
        
        print(f"\n📁 Test HTML saved to: {test_filename}")
        print("   Open this file in your browser and resize the window to test responsiveness")
        
        # Create a simple test page with viewport controls
        test_page_content = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Responsive Test</title>
    <style>
        .test-controls {{
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            font-family: Arial, sans-serif;
        }}
        
        .test-controls button {{
            margin: 3px;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 12px;
        }}
        
        .test-controls button:hover {{
            background: #0056b3;
        }}
        
        .test-container {{
            transition: all 0.3s ease;
            margin: 0 auto;
            border: 2px solid #ddd;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="test-controls">
        <div style="font-weight: bold; margin-bottom: 10px;">📱 Responsive Test</div>
        <button onclick="setWidth('100%')">Full Width</button><br>
        <button onclick="setWidth('800px')">Desktop (800px)</button><br>
        <button onclick="setWidth('768px')">Tablet (768px)</button><br>
        <button onclick="setWidth('480px')">Mobile (480px)</button><br>
        <button onclick="setWidth('360px')">Small (360px)</button><br>
        <button onclick="setWidth('320px')">Tiny (320px)</button>
    </div>

    <div class="test-container" id="testContainer">
        {intro_result['html_body']}
    </div>

    <script>
        function setWidth(width) {{
            const container = document.getElementById('testContainer');
            container.style.width = width;
            container.style.maxWidth = width;
            
            if (width !== '100%') {{
                container.style.margin = '20px auto';
            }}
        }}
        
        // Set initial width
        setWidth('800px');
    </script>
</body>
</html>
        '''
        
        interactive_filename = 'pricing_interactive_test.html'
        with open(interactive_filename, 'w', encoding='utf-8') as f:
            f.write(test_page_content)
        
        print(f"📱 Interactive test page: {interactive_filename}")
        print("   This page includes responsive testing controls")
        
        if all_passed:
            print("\n🎉 All Pricing Responsive Tests Passed!")
            print("\n📱 Responsive Breakpoints:")
            print("   • Desktop: 800px+ (3 cards in a row)")
            print("   • Tablet: 768px (cards stack vertically)")
            print("   • Mobile: 480px (full width cards)")
            print("   • Small: 360px (compact padding)")
            print("   • Tiny: 320px (minimal spacing)")
            
            print("\n🔧 Key Fixes Applied:")
            print("   • Added flex-wrap to prevent overflow")
            print("   • Set max-width: 300px for desktop cards")
            print("   • Full width (100%) on mobile devices")
            print("   • Reduced padding on small screens")
            print("   • Added overflow: hidden protection")
            print("   • Improved gap spacing for all sizes")
            
            return True
        else:
            print("\n❌ Some pricing responsive tests failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing pricing responsive design: {e}")
        return False

if __name__ == "__main__":
    success = test_pricing_responsive()
    sys.exit(0 if success else 1)
