#!/usr/bin/env python3
"""
Create Database with Correct <PERSON>hema
This script creates the database with all the required columns
"""

import sqlite3
import os
from datetime import datetime

def create_database():
    """Create the database with the correct schema"""
    db_path = 'unified_sales.db'

    print("🚀 Creating database with correct schema...")

    # Remove existing database if it exists
    if os.path.exists(db_path):
        print(f"📁 Removing existing database: {db_path}")
        os.remove(db_path)

    # Connect to the database (this will create it)
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Create contacts table
    print("📋 Creating contacts table...")
    cursor.execute('''
        CREATE TABLE contacts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(200),
            job_title VARCHAR(150),
            source VARCHAR(100),
            status VARCHAR(50) DEFAULT 'new',
            lead_score REAL DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            do_not_email BOOLEAN DEFAULT 0,
            is_customer BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            email_campaign_id INTEGER,
            chatbot_session_id VARCHAR(100),
            first_email_sent DATETIME,
            email_opened DATETIME,
            chatbot_link_clicked DATETIME,
            chatbot_conversation_started DATETIME,
            current_sales_stage VARCHAR(50) DEFAULT 'email_sent',
            sales_stage_progression TEXT,
            conversion_completed DATETIME,
            total_interaction_time INTEGER DEFAULT 0,
            FOREIGN KEY (email_campaign_id) REFERENCES email_campaigns (id)
        )
    ''')

    # Create email_campaigns table with ALL required columns
    print("📧 Creating email_campaigns table...")
    cursor.execute('''
        CREATE TABLE email_campaigns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            template_name VARCHAR(100) NOT NULL,
            status VARCHAR(50) DEFAULT 'draft',
            total_recipients INTEGER DEFAULT 0,
            emails_sent INTEGER DEFAULT 0,
            emails_delivered INTEGER DEFAULT 0,
            emails_failed INTEGER DEFAULT 0,
            emails_opened INTEGER DEFAULT 0,
            chatbot_links_clicked INTEGER DEFAULT 0,
            chatbot_conversations_started INTEGER DEFAULT 0,
            sales_conversations_completed INTEGER DEFAULT 0,
            conversions_achieved INTEGER DEFAULT 0,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            retry_failed_only BOOLEAN DEFAULT 1,
            last_retry_at DATETIME,
            daily_send_limit INTEGER DEFAULT 100,
            emails_sent_today INTEGER DEFAULT 0,
            last_send_date DATE,
            send_schedule VARCHAR(50) DEFAULT 'immediate',
            scheduled_start_date DATE,
            scheduled_start_time TIME,
            send_days_of_week VARCHAR(20) DEFAULT '1,2,3,4,5',
            is_recurring BOOLEAN DEFAULT 0,
            batch_status VARCHAR(50) DEFAULT 'not_started',
            next_batch_date DATETIME,
            total_batches_planned INTEGER DEFAULT 1,
            batches_completed INTEGER DEFAULT 0,
            recipient_criteria TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            started_at DATETIME,
            completed_at DATETIME
        )
    ''')

    # Create other required tables
    print("📊 Creating other tables...")

    # Sales stages table
    cursor.execute('''
        CREATE TABLE sales_stages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            "order" INTEGER NOT NULL,
            probability_percent REAL DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Email failures table
    cursor.execute('''
        CREATE TABLE email_failures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            contact_id INTEGER NOT NULL,
            recipient_email VARCHAR(255) NOT NULL,
            error_message TEXT,
            error_type VARCHAR(100),
            retry_count INTEGER DEFAULT 0,
            last_retry_at DATETIME,
            resolved BOOLEAN DEFAULT 0,
            resolved_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Chatbot sessions table
    cursor.execute('''
        CREATE TABLE chatbot_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) UNIQUE NOT NULL,
            contact_id INTEGER,
            email_campaign_id INTEGER,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            ended_at DATETIME,
            current_stage VARCHAR(50) DEFAULT 'opening',
            current_task VARCHAR(200),
            stage_progression TEXT,
            total_messages INTEGER DEFAULT 0,
            user_messages INTEGER DEFAULT 0,
            bot_messages INTEGER DEFAULT 0,
            objections_handled INTEGER DEFAULT 0,
            engagement_level VARCHAR(20) DEFAULT 'medium',
            interaction_quality_score REAL DEFAULT 0.0,
            completed_successfully BOOLEAN DEFAULT 0,
            conversion_achieved BOOLEAN DEFAULT 0,
            final_stage_reached VARCHAR(50),
            abandonment_reason VARCHAR(200),
            opening_started_at DATETIME,
            opening_completed_at DATETIME,
            trust_started_at DATETIME,
            trust_completed_at DATETIME,
            discovery_started_at DATETIME,
            discovery_completed_at DATETIME,
            demonstration_started_at DATETIME,
            demonstration_completed_at DATETIME,
            close_started_at DATETIME,
            close_completed_at DATETIME,
            FOREIGN KEY (contact_id) REFERENCES contacts (id),
            FOREIGN KEY (email_campaign_id) REFERENCES email_campaigns (id)
        )
    ''')

    # Chat messages table for persisting individual messages
    print("💬 Creating chat_messages table...")
    cursor.execute('''
        CREATE TABLE chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) NOT NULL,
            message_type VARCHAR(20) NOT NULL,
            content TEXT NOT NULL,
            stage VARCHAR(50),
            task VARCHAR(200),
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            message_order INTEGER NOT NULL,
            FOREIGN KEY (session_id) REFERENCES chatbot_sessions (session_id)
        )
    ''')

    # Activities table
    cursor.execute('''
        CREATE TABLE activities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contact_id INTEGER,
            session_id VARCHAR(100),
            activity_type VARCHAR(50) NOT NULL,
            subject VARCHAR(255),
            description TEXT,
            extra_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Email logs table
    cursor.execute('''
        CREATE TABLE email_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            contact_id INTEGER NOT NULL,
            recipient_email VARCHAR(255) NOT NULL,
            recipient_name VARCHAR(255),
            subject VARCHAR(255) NOT NULL,
            email_body_html TEXT,
            email_body_text TEXT,
            sent_at DATETIME,
            delivered_at DATETIME,
            opened_at DATETIME,
            first_clicked_at DATETIME,
            replied_at DATETIME,
            bounced_at DATETIME,
            unsubscribed_at DATETIME,
            status VARCHAR(50) DEFAULT 'pending',
            error_message TEXT,
            open_count INTEGER DEFAULT 0,
            click_count INTEGER DEFAULT 0,
            user_agent VARCHAR(500),
            ip_address VARCHAR(45),
            message_id VARCHAR(255),
            thread_id VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Contact groups table
    cursor.execute('''
        CREATE TABLE contact_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by VARCHAR(100),
            is_active BOOLEAN DEFAULT 1,
            color VARCHAR(7) DEFAULT '#007bff'
        )
    ''')

    # Contact group memberships table
    cursor.execute('''
        CREATE TABLE contact_group_memberships (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contact_id INTEGER NOT NULL,
            group_id INTEGER NOT NULL,
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            added_by VARCHAR(100),
            FOREIGN KEY (contact_id) REFERENCES contacts (id),
            FOREIGN KEY (group_id) REFERENCES contact_groups (id),
            UNIQUE(contact_id, group_id)
        )
    ''')

    # Campaign groups table
    cursor.execute('''
        CREATE TABLE campaign_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            group_id INTEGER NOT NULL,
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            added_by VARCHAR(100),
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (group_id) REFERENCES contact_groups (id),
            UNIQUE(campaign_id, group_id)
        )
    ''')

    # Create indexes for better performance
    print("🔍 Creating indexes...")
    cursor.execute('CREATE INDEX idx_contacts_email ON contacts(email)')
    cursor.execute('CREATE INDEX idx_contacts_status ON contacts(status)')
    cursor.execute('CREATE INDEX idx_contacts_is_active ON contacts(is_active)')
    cursor.execute('CREATE INDEX idx_email_logs_campaign_id ON email_logs(campaign_id)')
    cursor.execute('CREATE INDEX idx_email_logs_contact_id ON email_logs(contact_id)')
    cursor.execute('CREATE INDEX idx_email_logs_status ON email_logs(status)')

    # Insert some default data
    print("📝 Inserting default data...")

    # Insert default sales stages
    stages = [
        ('Email Sent', 1, 10.0),
        ('Email Opened', 2, 20.0),
        ('Link Clicked', 3, 30.0),
        ('Opening', 4, 40.0),
        ('Trust', 5, 50.0),
        ('Discovery', 6, 60.0),
        ('Demonstration', 7, 70.0),
        ('Close', 8, 80.0),
        ('Converted', 9, 100.0)
    ]

    for name, order, probability in stages:
        cursor.execute('''
            INSERT INTO sales_stages (name, "order", probability_percent)
            VALUES (?, ?, ?)
        ''', (name, order, probability))

    # Commit all changes
    conn.commit()
    conn.close()

    print(f"✅ Database created successfully: {db_path}")
    return True

def verify_database():
    """Verify the database was created correctly"""
    db_path = 'unified_sales.db'

    if not os.path.exists(db_path):
        print("❌ Database file not found after creation")
        return False

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Check email_campaigns table schema
    cursor.execute("PRAGMA table_info(email_campaigns)")
    columns = cursor.fetchall()

    print("📋 Email campaigns table schema:")
    print("-" * 50)
    for column in columns:
        print(f"  {column[1]:<25} {column[2]:<15} {'NOT NULL' if column[3] else 'NULL':<8} {f'DEFAULT {column[4]}' if column[4] else ''}")

    # Check if all required columns exist
    column_names = [col[1] for col in columns]
    required_columns = [
        'daily_send_limit', 'emails_sent_today', 'last_send_date',
        'send_schedule', 'recipient_criteria', 'emails_failed'
    ]

    missing_columns = [col for col in required_columns if col not in column_names]
    if missing_columns:
        print(f"❌ Missing columns: {missing_columns}")
        return False
    else:
        print("✅ All required columns present")

    conn.close()
    return True

if __name__ == "__main__":
    print("🚀 Database Creation Tool")
    print("=" * 50)

    success = create_database()
    if success:
        print("\n📊 Verifying database...")
        verify_database()
        print("\n✅ Database creation completed successfully!")
        print("You can now restart the Flask application and create campaigns.")
    else:
        print("\n❌ Database creation failed.")
