#!/usr/bin/env python3
"""
Debug View Campaign Issue
"""

import requests
import json

def debug_view_campaign():
    """Debug the view campaign issue"""
    print("🔍 Debugging View Campaign Issue")
    print("=" * 40)
    
    # Test different campaign IDs
    campaign_ids = [1, 2, 3, 4, 5, 6, 7, 8]
    
    for campaign_id in campaign_ids:
        print(f"\n🎯 Testing campaign ID: {campaign_id}")
        
        try:
            response = requests.get(f"http://localhost:5000/campaigns/{campaign_id}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ SUCCESS - Campaign view loaded")
                if "Campaign Details" in response.text:
                    print("   ✅ Campaign details page rendered correctly")
                else:
                    print("   ⚠️ Response received but may not be campaign details page")
            elif response.status_code == 302:
                print("   ❌ REDIRECT - Error occurred")
                # Check for flash messages in cookies
                if 'session=' in response.headers.get('Set-Cookie', ''):
                    print("   📝 Flash message likely present (error)")
            elif response.status_code == 404:
                print("   ❌ NOT FOUND - Campaign doesn't exist")
            else:
                print(f"   ❌ UNEXPECTED STATUS: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
    
    # Test if campaigns exist
    print(f"\n📋 Checking campaigns list...")
    try:
        response = requests.get("http://localhost:5000/campaigns")
        if response.status_code == 200:
            # Count campaigns in the response
            import re
            campaign_ids_found = re.findall(r'value="(\d+)".*campaign-checkbox', response.text)
            print(f"   ✅ Found {len(campaign_ids_found)} campaigns: {campaign_ids_found}")
            
            # Test the first available campaign
            if campaign_ids_found:
                first_id = campaign_ids_found[0]
                print(f"\n🎯 Testing first available campaign: {first_id}")
                
                test_response = requests.get(f"http://localhost:5000/campaigns/{first_id}")
                print(f"   Status: {test_response.status_code}")
                
                if test_response.status_code == 200:
                    print("   ✅ SUCCESS!")
                    # Check content
                    if "Campaign Details" in test_response.text:
                        print("   ✅ Campaign details page loaded correctly")
                    elif "Error" in test_response.text:
                        print("   ❌ Error page displayed")
                    else:
                        print("   ⚠️ Unknown page content")
                else:
                    print(f"   ❌ Failed with status: {test_response.status_code}")
        else:
            print(f"   ❌ Failed to get campaigns list: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")

def test_direct_database_access():
    """Test direct database access to debug the issue"""
    print(f"\n🗄️ Testing Direct Database Access")
    print("=" * 35)
    
    try:
        import sqlite3
        import os
        
        db_path = 'unified_sales.db'
        if not os.path.exists(db_path):
            print("   ❌ Database file not found")
            return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check campaigns table
        cursor.execute("SELECT id, name, status FROM email_campaigns ORDER BY id")
        campaigns = cursor.fetchall()
        
        print(f"   📊 Found {len(campaigns)} campaigns in database:")
        for campaign in campaigns:
            print(f"      ID: {campaign[0]}, Name: {campaign[1]}, Status: {campaign[2]}")
        
        # Check email_logs table
        cursor.execute("SELECT COUNT(*) FROM email_logs")
        email_logs_count = cursor.fetchone()[0]
        print(f"   📧 Email logs count: {email_logs_count}")
        
        # Check activities table
        try:
            cursor.execute("SELECT COUNT(*) FROM activities")
            activities_count = cursor.fetchone()[0]
            print(f"   📝 Activities count: {activities_count}")
        except sqlite3.OperationalError as e:
            print(f"   ⚠️ Activities table issue: {str(e)}")
        
        # Check contacts table
        cursor.execute("SELECT COUNT(*) FROM contacts")
        contacts_count = cursor.fetchone()[0]
        print(f"   👥 Contacts count: {contacts_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Database access error: {str(e)}")

def test_flask_app_health():
    """Test Flask app health"""
    print(f"\n🏥 Testing Flask App Health")
    print("=" * 30)
    
    endpoints = [
        ('/', 'Dashboard'),
        ('/campaigns', 'Campaigns List'),
        ('/contacts', 'Contacts List'),
        ('/campaigns/create', 'Create Campaign'),
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:5000{endpoint}")
            if response.status_code == 200:
                print(f"   ✅ {name}: Working")
            else:
                print(f"   ❌ {name}: Status {response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error - {str(e)}")

if __name__ == "__main__":
    print("🚀 View Campaign Debug Tool")
    print("=" * 50)
    
    debug_view_campaign()
    test_direct_database_access()
    test_flask_app_health()
    
    print("\n" + "=" * 50)
    print("🔍 Debug Summary:")
    print("   • Check if specific campaign IDs exist")
    print("   • Verify database contains campaigns")
    print("   • Test Flask app endpoints")
    print("   • Look for patterns in failures")
    print("\n💡 Next steps:")
    print("   • Check Flask application logs")
    print("   • Verify database schema")
    print("   • Test with fresh campaign creation")
