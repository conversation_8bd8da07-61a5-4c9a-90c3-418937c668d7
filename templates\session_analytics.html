{% extends "base.html" %}

{% block title %}Session Analytics - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line text-primary"></i> Chatbot Session Analytics</h1>
    <div>
        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Main Analytics
        </a>
    </div>
</div>

<!-- Session Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="metric-value">{{ session_data.sessions_started }}</div>
                <div class="metric-label">Total Sessions</div>
                <small><i class="fas fa-play"></i> Sessions started</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">{{ session_data.converted_sessions }}</div>
                <div class="metric-label">Conversions</div>
                <small><i class="fas fa-check-circle"></i> {{ "%.1f"|format(session_data.conversion_rate or 0) }}% conversion rate</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">{{ session_data.active_sessions }}</div>
                <div class="metric-label">Active Sessions</div>
                <small><i class="fas fa-clock"></i> Currently in progress</small>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">{{ session_data.completed_sessions }}</div>
                <div class="metric-label">Completed</div>
                <small><i class="fas fa-flag-checkered"></i> {{ "%.1f"|format(session_data.completion_rate or 0) }}% completion rate</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="chart-container">
            <h5><i class="fas fa-funnel-dollar"></i> Session Progression Funnel</h5>
            <div id="sessionFunnelChart"></div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="chart-container">
            <h5><i class="fas fa-chart-pie"></i> Engagement Levels</h5>
            <div id="engagementChart"></div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="chart-container">
            <h5><i class="fas fa-clock"></i> Average Stage Durations</h5>
            <div id="durationsChart"></div>
        </div>
    </div>
</div>

<!-- Stage Performance Table -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table"></i> Stage Performance Details</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Stage</th>
                                <th>Sessions Started</th>
                                <th>Sessions Completed</th>
                                <th>Completion Rate</th>
                                <th>Avg Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Opening</strong></td>
                                <td><span class="badge bg-primary">{{ session_data.sessions_started }}</span></td>
                                <td><span class="badge bg-success">{{ session_data.opening_completed }}</span></td>
                                <td>
                                    {% set opening_rate = (session_data.opening_completed / session_data.sessions_started * 100) if session_data.sessions_started > 0 else 0 %}
                                    {{ "%.1f"|format(opening_rate) }}%
                                </td>
                                <td>
                                    {% if session_data.avg_stage_durations.get('Opening') %}
                                        {{ "%.1f"|format(session_data.avg_stage_durations['Opening']) }}m
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if opening_rate >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif opening_rate >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Trust</strong></td>
                                <td><span class="badge bg-primary">{{ session_data.opening_completed }}</span></td>
                                <td><span class="badge bg-success">{{ session_data.trust_completed }}</span></td>
                                <td>
                                    {% set trust_rate = (session_data.trust_completed / session_data.opening_completed * 100) if session_data.opening_completed > 0 else 0 %}
                                    {{ "%.1f"|format(trust_rate) }}%
                                </td>
                                <td>
                                    {% if session_data.avg_stage_durations.get('Trust') %}
                                        {{ "%.1f"|format(session_data.avg_stage_durations['Trust']) }}m
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if trust_rate >= 70 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif trust_rate >= 50 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Discovery</strong></td>
                                <td><span class="badge bg-primary">{{ session_data.trust_completed }}</span></td>
                                <td><span class="badge bg-success">{{ session_data.discovery_completed }}</span></td>
                                <td>
                                    {% set discovery_rate = (session_data.discovery_completed / session_data.trust_completed * 100) if session_data.trust_completed > 0 else 0 %}
                                    {{ "%.1f"|format(discovery_rate) }}%
                                </td>
                                <td>
                                    {% if session_data.avg_stage_durations.get('Discovery') %}
                                        {{ "%.1f"|format(session_data.avg_stage_durations['Discovery']) }}m
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if discovery_rate >= 70 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif discovery_rate >= 50 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Demonstration</strong></td>
                                <td><span class="badge bg-primary">{{ session_data.discovery_completed }}</span></td>
                                <td><span class="badge bg-success">{{ session_data.demo_completed }}</span></td>
                                <td>
                                    {% set demo_rate = (session_data.demo_completed / session_data.discovery_completed * 100) if session_data.discovery_completed > 0 else 0 %}
                                    {{ "%.1f"|format(demo_rate) }}%
                                </td>
                                <td>
                                    {% if session_data.avg_stage_durations.get('Demonstration') %}
                                        {{ "%.1f"|format(session_data.avg_stage_durations['Demonstration']) }}m
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if demo_rate >= 60 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif demo_rate >= 40 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Close</strong></td>
                                <td><span class="badge bg-primary">{{ session_data.demo_completed }}</span></td>
                                <td><span class="badge bg-success">{{ session_data.close_completed }}</span></td>
                                <td>
                                    {% set close_rate = (session_data.close_completed / session_data.demo_completed * 100) if session_data.demo_completed > 0 else 0 %}
                                    {{ "%.1f"|format(close_rate) }}%
                                </td>
                                <td>
                                    {% if session_data.avg_stage_durations.get('Close') %}
                                        {{ "%.1f"|format(session_data.avg_stage_durations['Close']) }}m
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if close_rate >= 50 %}
                                        <span class="badge bg-success">Excellent</span>
                                    {% elif close_rate >= 30 %}
                                        <span class="badge bg-warning">Good</span>
                                    {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sessions -->
{% if session_data.recent_sessions %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Sessions</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Session ID</th>
                                <th>Contact</th>
                                <th>Current Stage</th>
                                <th>Messages</th>
                                <th>Engagement</th>
                                <th>Started</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in session_data.recent_sessions %}
                            <tr>
                                <td><code>{{ session.session_id[:8] }}...</code></td>
                                <td>
                                    {% if session.contact %}
                                        {{ session.contact.full_name }}
                                    {% else %}
                                        Unknown
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ session.current_stage.title() if session.current_stage else 'Unknown' }}</span>
                                </td>
                                <td>{{ session.total_messages or 0 }}</td>
                                <td>
                                    {% if session.engagement_level == 'high' %}
                                        <span class="badge bg-success">High</span>
                                    {% elif session.engagement_level == 'medium' %}
                                        <span class="badge bg-warning">Medium</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Low</span>
                                    {% endif %}
                                </td>
                                <td>{{ session.started_at.strftime('%Y-%m-%d %H:%M') if session.started_at else 'Unknown' }}</td>
                                <td>
                                    {% if session.conversion_achieved %}
                                        <span class="badge bg-success">Converted</span>
                                    {% elif session.completed_successfully %}
                                        <span class="badge bg-info">Completed</span>
                                    {% elif session.ended_at %}
                                        <span class="badge bg-secondary">Ended</span>
                                    {% else %}
                                        <span class="badge bg-primary">Active</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
.metric-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 1rem;
    opacity: 0.9;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.chart-container h5 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 10px;
}
</style>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Render session funnel chart
        {% if charts.session_funnel %}
        var sessionFunnelData = {{ charts.session_funnel|safe }};
        Plotly.newPlot('sessionFunnelChart', sessionFunnelData.data, sessionFunnelData.layout, {responsive: true});
        {% endif %}

        // Render engagement chart
        {% if charts.engagement %}
        var engagementData = {{ charts.engagement|safe }};
        Plotly.newPlot('engagementChart', engagementData.data, engagementData.layout, {responsive: true});
        {% endif %}

        // Render durations chart
        {% if charts.durations %}
        var durationsData = {{ charts.durations|safe }};
        Plotly.newPlot('durationsChart', durationsData.data, durationsData.layout, {responsive: true});
        {% endif %}
    });

    // Auto-refresh every 60 seconds
    setInterval(function() {
        location.reload();
    }, 60000);
</script>
{% endblock %}
