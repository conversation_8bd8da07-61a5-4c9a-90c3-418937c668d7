"""
Email Campaign Models
====================
Manages email campaigns and tracking for 24Seven Assistants introduction emails.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, <PERSON><PERSON>ey, JSON
from sqlalchemy.orm import relationship



class EmailCampaign(db.Model):
    """Email campaign management model"""

    __tablename__ = 'email_campaigns'

    id = Column(Integer, primary_key=True)

    # Campaign Information
    name = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=False)
    template_name = Column(String(100), nullable=False)  # Template identifier

    # Campaign Content
    email_body_html = Column(Text, nullable=True)
    email_body_text = Column(Text, nullable=True)

    # Campaign Settings
    sender_name = Column(String(100), default="24Seven Assistants Sales Team")
    sender_email = Column(String(255), default="<EMAIL>")
    reply_to_email = Column(String(255), nullable=True)

    # Targeting
    target_audience = Column(String(255), nullable=True)  # Description of target
    contact_filters = Column(JSON, nullable=True)  # JSON filters for contact selection
    recipient_criteria = Column(Text, nullable=True)  # JSON string for recipient selection criteria

    # Scheduling
    scheduled_at = Column(DateTime, nullable=True)
    scheduled_start_date = Column(DateTime, nullable=True)  # For scheduled campaigns
    scheduled_start_time = Column(DateTime, nullable=True)  # For scheduled campaigns
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)

    # Status
    status = Column(String(50), default='draft', index=True)  # draft, scheduled, running, completed, paused, cancelled

    # Statistics
    total_recipients = Column(Integer, default=0)
    emails_sent = Column(Integer, default=0)
    emails_delivered = Column(Integer, default=0)
    emails_opened = Column(Integer, default=0)
    emails_clicked = Column(Integer, default=0)
    emails_replied = Column(Integer, default=0)
    emails_bounced = Column(Integer, default=0)
    emails_unsubscribed = Column(Integer, default=0)
    emails_failed = Column(Integer, default=0)  # Failed deliveries

    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(100), nullable=True)

    # Configuration
    send_delay_seconds = Column(Integer, default=2)  # Delay between emails
    max_emails_per_hour = Column(Integer, default=100)
    daily_send_limit = Column(Integer, default=500)
    batch_size = Column(Integer, default=50)
    batch_delay_minutes = Column(Integer, default=5)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    retry_failed_only = Column(Boolean, default=True)
    send_schedule = Column(String(255), nullable=True)

    # Batch Processing
    batch_status = Column(String(50), default='not_started')  # not_started, in_progress, paused, completed, failed
    next_batch_date = Column(DateTime, nullable=True)
    total_batches_planned = Column(Integer, default=1)
    batches_completed = Column(Integer, default=0)

    # Daily Sending Tracking
    emails_sent_today = Column(Integer, default=0)
    last_send_date = Column(DateTime, nullable=True)

    # Additional Campaign Metrics
    total_recipients = Column(Integer, default=0)
    emails_delivered = Column(Integer, default=0)
    chatbot_links_clicked = Column(Integer, default=0)
    chatbot_conversations_started = Column(Integer, default=0)
    conversions_achieved = Column(Integer, default=0)

    # Scheduling Options
    send_days_of_week = Column(String(20), default='1,2,3,4,5')  # Monday-Friday
    is_recurring = Column(Boolean, default=False)
    last_retry_at = Column(DateTime, nullable=True)

    # Relationships
    email_logs = relationship("EmailLog", back_populates="campaign", cascade="all, delete-orphan")

    def __repr__(self):
        return f'<EmailCampaign {self.name} - {self.status}>'

    def can_send_today(self):
        """Check if campaign can send emails today based on daily limit"""
        from datetime import date
        today = date.today()

        # If last send date is not today, reset the counter
        if not self.last_send_date or self.last_send_date.date() != today:
            self.emails_sent_today = 0
            return True

        # Check if we've reached the daily limit
        return self.emails_sent_today < self.daily_send_limit

    def get_remaining_sends_today(self):
        """Return how many emails can still be sent today respecting the daily limit."""
        from datetime import date
        today = date.today()
        # Reset counter if day changed
        if not self.last_send_date or self.last_send_date.date() != today:
            return self.daily_send_limit
        return max(0, self.daily_send_limit - self.emails_sent_today)

    def increment_daily_send_count(self, count: int = 1):
        """Increment the counter of emails sent today and update last_send_date."""
        from datetime import date
        today = date.today()
        if not self.last_send_date or self.last_send_date.date() != today:
            # New day – reset counter first
            self.emails_sent_today = 0
        self.emails_sent_today += count
        self.last_send_date = datetime.utcnow()

    def calculate_estimated_days(self, total_contacts):
        """Calculate estimated days to complete campaign based on daily limit"""
        if self.daily_send_limit <= 0:
            return 1

        import math
        return math.ceil(total_contacts / self.daily_send_limit)

    @property
    def open_rate(self):
        """Calculate email open rate"""
        if self.emails_delivered > 0:
            return (self.emails_opened / self.emails_delivered) * 100
        return 0.0

    @property
    def click_rate(self):
        """Calculate email click rate"""
        if self.emails_delivered > 0:
            return (self.emails_clicked / self.emails_delivered) * 100
        return 0.0

    @property
    def reply_rate(self):
        """Calculate email reply rate"""
        if self.emails_delivered > 0:
            return (self.emails_replied / self.emails_delivered) * 100
        return 0.0

    @property
    def bounce_rate(self):
        """Calculate email bounce rate"""
        if self.emails_sent > 0:
            return (self.emails_bounced / self.emails_sent) * 100
        return 0.0

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'subject': self.subject,
            'template_name': self.template_name,
            'sender_name': self.sender_name,
            'sender_email': self.sender_email,
            'reply_to_email': self.reply_to_email,
            'target_audience': self.target_audience,
            'contact_filters': self.contact_filters,
            'scheduled_at': self.scheduled_at.isoformat() if self.scheduled_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'status': self.status,
            'total_recipients': self.total_recipients,
            'emails_sent': self.emails_sent,
            'emails_delivered': self.emails_delivered,
            'emails_opened': self.emails_opened,
            'emails_clicked': self.emails_clicked,
            'emails_replied': self.emails_replied,
            'emails_bounced': self.emails_bounced,
            'emails_unsubscribed': self.emails_unsubscribed,
            'emails_failed': self.emails_failed,
            'open_rate': self.open_rate,
            'click_rate': self.click_rate,
            'reply_rate': self.reply_rate,
            'bounce_rate': self.bounce_rate,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'send_delay_seconds': self.send_delay_seconds,
            'max_emails_per_hour': self.max_emails_per_hour,
            'daily_send_limit': self.daily_send_limit,
            'batch_size': self.batch_size,
            'batch_delay_minutes': self.batch_delay_minutes,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'retry_failed_only': self.retry_failed_only,
            'send_schedule': self.send_schedule
        }

class EmailLog(db.Model):
    """Individual email tracking model"""

    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)

    # Relationships
    campaign_id = Column(Integer, ForeignKey('email_campaigns.id'), nullable=False, index=True)
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=False, index=True)

    # Email Details
    recipient_email = Column(String(255), nullable=False, index=True)
    recipient_name = Column(String(255), nullable=True)
    subject = Column(String(255), nullable=False)

    # Content
    email_body_html = Column(Text, nullable=True)
    email_body_text = Column(Text, nullable=True)

    # Tracking
    sent_at = Column(DateTime, nullable=True, index=True)
    delivered_at = Column(DateTime, nullable=True)
    opened_at = Column(DateTime, nullable=True)
    first_clicked_at = Column(DateTime, nullable=True)
    replied_at = Column(DateTime, nullable=True)
    bounced_at = Column(DateTime, nullable=True)
    unsubscribed_at = Column(DateTime, nullable=True)

    # Status
    status = Column(String(50), default='pending', index=True)  # pending, sent, delivered, opened, clicked, replied, bounced, failed
    error_message = Column(Text, nullable=True)

    # Tracking Data
    open_count = Column(Integer, default=0)
    click_count = Column(Integer, default=0)
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)

    # Message IDs for tracking
    message_id = Column(String(255), nullable=True, index=True)
    thread_id = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    campaign = relationship("EmailCampaign", back_populates="email_logs")
    contact = relationship("Contact")

    def __repr__(self):
        return f'<EmailLog {self.recipient_email} - {self.status}>'

    def mark_as_sent(self, message_id=None):
        """Mark email as sent"""
        self.status = 'sent'
        self.sent_at = datetime.utcnow()
        if message_id:
            self.message_id = message_id
        self.updated_at = datetime.utcnow()

    def mark_as_delivered(self):
        """Mark email as delivered"""
        self.status = 'delivered'
        self.delivered_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def mark_as_opened(self, user_agent=None, ip_address=None):
        """Mark email as opened"""
        if not self.opened_at:  # First open
            self.opened_at = datetime.utcnow()
            self.status = 'opened'

        self.open_count += 1
        if user_agent:
            self.user_agent = user_agent
        if ip_address:
            self.ip_address = ip_address
        self.updated_at = datetime.utcnow()

    def mark_as_clicked(self):
        """Mark email as clicked"""
        if not self.first_clicked_at:  # First click
            self.first_clicked_at = datetime.utcnow()
            self.status = 'clicked'

        self.click_count += 1
        self.updated_at = datetime.utcnow()

    def mark_as_replied(self):
        """Mark email as replied"""
        self.status = 'replied'
        self.replied_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def mark_as_bounced(self, error_message=None):
        """Mark email as bounced"""
        self.status = 'bounced'
        self.bounced_at = datetime.utcnow()
        if error_message:
            self.error_message = error_message
        self.updated_at = datetime.utcnow()

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'campaign_id': self.campaign_id,
            'contact_id': self.contact_id,
            'recipient_email': self.recipient_email,
            'recipient_name': self.recipient_name,
            'subject': self.subject,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'delivered_at': self.delivered_at.isoformat() if self.delivered_at else None,
            'opened_at': self.opened_at.isoformat() if self.opened_at else None,
            'first_clicked_at': self.first_clicked_at.isoformat() if self.first_clicked_at else None,
            'replied_at': self.replied_at.isoformat() if self.replied_at else None,
            'bounced_at': self.bounced_at.isoformat() if self.bounced_at else None,
            'unsubscribed_at': self.unsubscribed_at.isoformat() if self.unsubscribed_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'open_count': self.open_count,
            'click_count': self.click_count,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'message_id': self.message_id,
            'thread_id': self.thread_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
