"""
SMTP Service
===========
Handles SMTP email sending for 24Seven Assistants campaigns.
"""

import smtplib
import ssl
import time
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class SMTPService:
    """SMTP email service for sending introduction emails"""
    
    def __init__(self, config: Dict):
        """Initialize SMTP service with configuration"""
        self.smtp_server = config.get('MAIL_SERVER', 'smtp.gmail.com')
        self.smtp_port = config.get('MAIL_PORT', 587)
        self.use_tls = config.get('MAIL_USE_TLS', True)
        self.username = config.get('MAIL_USERNAME')
        self.password = config.get('MAIL_PASSWORD')
        self.default_sender = config.get('MAIL_DEFAULT_SENDER')
        self.delay_seconds = config.get('EMAIL_DELAY_SECONDS', 2)
        
        # Validate configuration
        if not all([self.username, self.password, self.default_sender]):
            raise ValueError("SMTP configuration incomplete. Need username, password, and default sender.")
    
    def create_connection(self) -> smtplib.SMTP:
        """Create and authenticate SMTP connection"""
        try:
            # Create SMTP connection
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                # Enable TLS encryption
                context = ssl.create_default_context()
                server.starttls(context=context)
            
            # Authenticate
            server.login(self.username, self.password)
            
            logger.info(f"SMTP connection established to {self.smtp_server}:{self.smtp_port}")
            return server
            
        except Exception as e:
            logger.error(f"Failed to create SMTP connection: {str(e)}")
            raise
    
    def send_email(self, 
                   to_email: str,
                   subject: str,
                   html_body: str,
                   text_body: str = None,
                   from_email: str = None,
                   from_name: str = None,
                   reply_to: str = None,
                   attachments: List[Dict] = None) -> Tuple[bool, str, str]:
        """
        Send a single email
        
        Returns:
            Tuple of (success: bool, message_id: str, error_message: str)
        """
        try:
            # Set defaults
            from_email = from_email or self.default_sender
            from_name = from_name or "24Seven Assistants Sales Team"
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = to_email
            
            if reply_to:
                msg['Reply-To'] = reply_to
            
            # Add text version if provided
            if text_body:
                text_part = MIMEText(text_body, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)
            
            # Send email
            with self.create_connection() as server:
                server.send_message(msg)
                message_id = msg['Message-ID']
                
                logger.info(f"Email sent successfully to {to_email}")
                return True, message_id, ""
                
        except Exception as e:
            error_msg = f"Failed to send email to {to_email}: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg
    
    def send_bulk_emails(self, 
                        email_list: List[Dict],
                        delay_between_emails: int = None) -> List[Dict]:
        """
        Send bulk emails with delay between sends
        
        Args:
            email_list: List of email dictionaries with keys:
                - to_email, subject, html_body, text_body, etc.
            delay_between_emails: Seconds to wait between emails
        
        Returns:
            List of results with success status and details
        """
        delay = delay_between_emails or self.delay_seconds
        results = []
        
        logger.info(f"Starting bulk email send to {len(email_list)} recipients")
        
        try:
            # Create single connection for all emails
            with self.create_connection() as server:
                for i, email_data in enumerate(email_list):
                    try:
                        # Send individual email
                        success, message_id, error_msg = self._send_single_email_with_server(
                            server, email_data
                        )
                        
                        result = {
                            'index': i,
                            'to_email': email_data.get('to_email'),
                            'success': success,
                            'message_id': message_id,
                            'error_message': error_msg,
                            'sent_at': datetime.utcnow().isoformat()
                        }
                        results.append(result)
                        
                        # Log progress
                        if (i + 1) % 10 == 0:
                            logger.info(f"Sent {i + 1}/{len(email_list)} emails")
                        
                        # Delay between emails (except for last email)
                        if i < len(email_list) - 1 and delay > 0:
                            time.sleep(delay)
                            
                    except Exception as e:
                        error_msg = f"Failed to send email {i+1}: {str(e)}"
                        logger.error(error_msg)
                        
                        result = {
                            'index': i,
                            'to_email': email_data.get('to_email'),
                            'success': False,
                            'message_id': '',
                            'error_message': error_msg,
                            'sent_at': datetime.utcnow().isoformat()
                        }
                        results.append(result)
                        
        except Exception as e:
            logger.error(f"Bulk email send failed: {str(e)}")
            # Add failed results for remaining emails
            for i in range(len(results), len(email_list)):
                results.append({
                    'index': i,
                    'to_email': email_list[i].get('to_email'),
                    'success': False,
                    'message_id': '',
                    'error_message': f"Bulk send failed: {str(e)}",
                    'sent_at': datetime.utcnow().isoformat()
                })
        
        logger.info(f"Bulk email send completed. Success: {sum(1 for r in results if r['success'])}/{len(results)}")
        return results
    
    def _send_single_email_with_server(self, server: smtplib.SMTP, email_data: Dict) -> Tuple[bool, str, str]:
        """Send single email using existing server connection"""
        try:
            # Extract email data
            to_email = email_data['to_email']
            subject = email_data['subject']
            html_body = email_data['html_body']
            text_body = email_data.get('text_body')
            from_email = email_data.get('from_email', self.default_sender)
            from_name = email_data.get('from_name', "24Seven Assistants Sales Team")
            reply_to = email_data.get('reply_to')
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = to_email
            
            if reply_to:
                msg['Reply-To'] = reply_to
            
            # Add text version if provided
            if text_body:
                text_part = MIMEText(text_body, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # Add HTML version
            html_part = MIMEText(html_body, 'html', 'utf-8')
            msg.attach(html_part)
            
            # Send email
            server.send_message(msg)
            message_id = msg['Message-ID']
            
            return True, message_id, ""
            
        except Exception as e:
            return False, "", str(e)
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict):
        """Add attachment to email message"""
        try:
            filename = attachment['filename']
            content = attachment['content']
            content_type = attachment.get('content_type', 'application/octet-stream')
            
            part = MIMEBase(*content_type.split('/'))
            part.set_payload(content)
            encoders.encode_base64(part)
            
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            logger.error(f"Failed to add attachment {attachment.get('filename')}: {str(e)}")
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test SMTP connection and authentication"""
        try:
            with self.create_connection() as server:
                logger.info("SMTP connection test successful")
                return True, "Connection successful"
                
        except Exception as e:
            error_msg = f"SMTP connection test failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
