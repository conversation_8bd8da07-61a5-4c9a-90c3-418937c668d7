#!/usr/bin/env python3
"""
Import 100PPDA Contacts Script
==============================
This script creates a contact group called "100PPDA" and imports the first 100 contacts
from the PPDA suppliers CSV file.
"""

import os
import sys
import csv
import re
from datetime import datetime

# Ensure we're in the correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Import Flask app and models
try:
    from unified_sales_system import app, db
    from models.contact import Contact
    from models.contact_group import ContactGroup
    from models.contact_group_membership import ContactGroupMembership
    print("✅ Successfully imported Flask app and models")
except Exception as e:
    print(f"❌ Error importing modules: {e}")
    sys.exit(1)

def clean_phone_number(phone):
    """Clean and format phone number for Uganda"""
    if not phone:
        return None
    
    # Convert to string and remove all non-digit characters except +
    phone = re.sub(r'[^\d+]', '', str(phone))
    
    # Handle Uganda phone numbers
    if phone.startswith('256'):
        phone = '+' + phone
    elif phone.startswith('0') and len(phone) == 10:
        phone = '+256' + phone[1:]
    elif len(phone) == 9 and not phone.startswith('+'):
        phone = '+256' + phone
    
    return phone if phone else None

def import_contacts():
    """Import contacts from CSV and create 100PPDA group"""
    
    csv_file = '100ppda_contacts.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    print(f"✅ Found CSV file: {csv_file}")
    
    with app.app_context():
        try:
            # Create or get the 100PPDA group
            group = ContactGroup.query.filter_by(name='100PPDA').first()
            if group:
                print(f"✅ Found existing group: {group.name}")
                # Clear existing memberships for fresh import
                ContactGroupMembership.query.filter_by(group_id=group.id).delete()
                print("🔄 Cleared existing group memberships")
            else:
                group = ContactGroup(
                    name='100PPDA',
                    description='First 100 PPDA suppliers for email campaign targeting',
                    color='#28a745',  # Green color
                    created_by='import_script'
                )
                db.session.add(group)
                db.session.flush()  # Get the ID
                print(f"✅ Created new group: {group.name}")
            
            # Import contacts from CSV
            contacts_added = 0
            contacts_updated = 0
            contacts_skipped = 0
            errors = []
            
            with open(csv_file, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)
                
                for row_num, row in enumerate(csv_reader, start=2):
                    try:
                        # Extract data from CSV
                        first_name = row.get('first_name', '').strip()
                        last_name = row.get('last_name', '').strip()
                        email = row.get('email', '').strip().lower()
                        phone = clean_phone_number(row.get('phone', ''))
                        company = row.get('company', '').strip()
                        website = row.get('website', '').strip()
                        
                        # Skip if no email
                        if not email:
                            errors.append(f'Row {row_num}: Missing email')
                            contacts_skipped += 1
                            continue
                        
                        # Use company name as fallback for names
                        if not first_name and not last_name and company:
                            # Split company name for first/last name
                            words = company.split()
                            if len(words) >= 2:
                                first_name = words[0]
                                last_name = ' '.join(words[1:])
                            else:
                                first_name = company
                                last_name = 'Company'
                        
                        # Ensure we have at least some name
                        if not first_name:
                            first_name = 'Unknown'
                        if not last_name:
                            last_name = 'Contact'
                        
                        # Check if contact already exists
                        existing_contact = Contact.query.filter_by(email=email).first()
                        
                        if existing_contact:
                            # Update existing contact
                            existing_contact.first_name = first_name
                            existing_contact.last_name = last_name
                            existing_contact.company = company or existing_contact.company
                            existing_contact.phone = phone or existing_contact.phone
                            existing_contact.website = website or existing_contact.website
                            existing_contact.source = 'ppda_import'
                            existing_contact.updated_at = datetime.utcnow()
                            contact = existing_contact
                            contacts_updated += 1
                            print(f"🔄 Updated: {email}")
                        else:
                            # Create new contact
                            contact = Contact(
                                first_name=first_name,
                                last_name=last_name,
                                email=email,
                                phone=phone,
                                company=company,
                                website=website,
                                source='ppda_import',
                                status='new',
                                lead_score=50.0  # Default score
                            )
                            db.session.add(contact)
                            db.session.flush()  # Get the ID
                            contacts_added += 1
                            print(f"➕ Added: {email}")
                        
                        # Add contact to group
                        membership = ContactGroupMembership(
                            contact_id=contact.id,
                            group_id=group.id,
                            added_by='import_script'
                        )
                        db.session.add(membership)
                        
                    except Exception as e:
                        error_msg = f'Row {row_num}: Error processing contact - {str(e)}'
                        errors.append(error_msg)
                        print(f"❌ Error: {error_msg}")
                        contacts_skipped += 1
                        continue
            
            # Commit all changes
            db.session.commit()
            
            # Print summary
            print("\n" + "="*60)
            print("📊 IMPORT SUMMARY")
            print("="*60)
            print(f"📁 Group: {group.name}")
            print(f"➕ Contacts added: {contacts_added}")
            print(f"🔄 Contacts updated: {contacts_updated}")
            print(f"⏭️  Contacts skipped: {contacts_skipped}")
            print(f"📊 Total contacts in group: {contacts_added + contacts_updated}")
            
            if errors:
                print(f"\n⚠️  Errors encountered: {len(errors)}")
                for error in errors[:5]:  # Show first 5 errors
                    print(f"   - {error}")
                if len(errors) > 5:
                    print(f"   ... and {len(errors) - 5} more errors")
            
            print(f"\n✅ Group '{group.name}' is ready for email campaigns!")
            print("="*60)
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error during import: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    print("🚀 Starting 100PPDA contact import...")
    print("="*60)
    
    success = import_contacts()
    
    if success:
        print("\n🎉 Import completed successfully!")
        print("\n📋 Next steps:")
        print("1. 🌐 Visit the web interface to view the contacts")
        print("2. 👥 Go to Groups section to see the '100PPDA' group")
        print("3. 📧 Create an email campaign targeting this group")
        print("4. 🎯 Send emails to all 100 contacts in the group")
    else:
        print("\n💥 Import failed. Please check the errors above.")
    
    print("\n🏁 Script finished.")
