"""
Performance Metrics
==================
Overall sales performance metrics and KPIs.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy import func, and_, or_
from models.opportunity import Opportunity
from models.contact import Contact
from models.activity import Activity
from models.sales_stage import SalesStage

class PerformanceMetrics:
    """Overall sales performance metrics calculator"""
    
    def __init__(self, db_session):
        """Initialize with database session"""
        self.db = db_session
    
    def get_performance_summary(self, days: int = 30) -> Dict:
        """
        Get comprehensive performance summary
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with performance metrics
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Opportunity metrics
        total_opportunities = self.db.query(Opportunity).count()
        new_opportunities = self.db.query(Opportunity).filter(
            Opportunity.created_at >= start_date
        ).count()
        
        open_opportunities = self.db.query(Opportunity).filter(
            Opportunity.status == 'open'
        ).count()
        
        won_opportunities = self.db.query(Opportunity).filter(
            and_(
                Opportunity.status == 'won',
                Opportunity.actual_close_date >= start_date
            )
        ).count()
        
        lost_opportunities = self.db.query(Opportunity).filter(
            and_(
                Opportunity.status == 'lost',
                Opportunity.actual_close_date >= start_date
            )
        ).count()
        
        # Revenue metrics
        pipeline_value = self.db.query(
            func.sum(Opportunity.estimated_value)
        ).filter(
            Opportunity.status == 'open'
        ).scalar() or 0.0
        
        weighted_pipeline_value = self.db.query(
            func.sum(Opportunity.estimated_value * Opportunity.probability_percent / 100)
        ).filter(
            Opportunity.status == 'open'
        ).scalar() or 0.0
        
        closed_won_value = self.db.query(
            func.sum(Opportunity.actual_value)
        ).filter(
            and_(
                Opportunity.status == 'won',
                Opportunity.actual_close_date >= start_date
            )
        ).scalar() or 0.0
        
        # Contact metrics
        total_contacts = self.db.query(Contact).count()
        new_contacts = self.db.query(Contact).filter(
            Contact.created_at >= start_date
        ).count()
        
        qualified_contacts = self.db.query(Contact).filter(
            Contact.status == 'qualified'
        ).count()
        
        # Activity metrics
        total_activities = self.db.query(Activity).filter(
            Activity.created_at >= start_date
        ).count()
        
        total_emails = self.db.query(Activity).filter(
            and_(
                Activity.activity_type == 'email',
                Activity.created_at >= start_date
            )
        ).count()
        
        total_calls = self.db.query(Activity).filter(
            and_(
                Activity.activity_type == 'call',
                Activity.created_at >= start_date
            )
        ).count()
        
        total_meetings = self.db.query(Activity).filter(
            and_(
                Activity.activity_type == 'meeting',
                Activity.created_at >= start_date
            )
        ).count()
        
        # Conversion rates
        lead_to_opp_rate = 0.0
        if total_contacts > 0:
            lead_to_opp_rate = (total_opportunities / total_contacts) * 100
        
        opp_to_customer_rate = 0.0
        if total_opportunities > 0:
            total_won = self.db.query(Opportunity).filter(
                Opportunity.status == 'won'
            ).count()
            opp_to_customer_rate = (total_won / total_opportunities) * 100
        
        overall_conversion_rate = 0.0
        if total_contacts > 0:
            total_customers = self.db.query(Contact).filter(
                Contact.is_customer == True
            ).count()
            overall_conversion_rate = (total_customers / total_contacts) * 100
        
        # Average deal size
        avg_deal_size = 0.0
        if won_opportunities > 0:
            avg_deal_size = closed_won_value / won_opportunities
        
        # Sales cycle metrics
        avg_sales_cycle_days = self._calculate_avg_sales_cycle(days)
        
        return {
            'total_opportunities': total_opportunities,
            'new_opportunities': new_opportunities,
            'open_opportunities': open_opportunities,
            'won_opportunities': won_opportunities,
            'lost_opportunities': lost_opportunities,
            'pipeline_value': round(pipeline_value, 2),
            'weighted_pipeline_value': round(weighted_pipeline_value, 2),
            'closed_won_value': round(closed_won_value, 2),
            'total_contacts': total_contacts,
            'new_contacts': new_contacts,
            'qualified_contacts': qualified_contacts,
            'total_activities': total_activities,
            'total_emails': total_emails,
            'total_calls': total_calls,
            'total_meetings': total_meetings,
            'lead_to_opp_rate': round(lead_to_opp_rate, 2),
            'opp_to_customer_rate': round(opp_to_customer_rate, 2),
            'overall_conversion_rate': round(overall_conversion_rate, 2),
            'avg_deal_size': round(avg_deal_size, 2),
            'avg_sales_cycle_days': round(avg_sales_cycle_days, 1)
        }
    
    def _calculate_avg_sales_cycle(self, days: int) -> float:
        """Calculate average sales cycle length"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get won opportunities with close dates
        won_opportunities = self.db.query(Opportunity).filter(
            and_(
                Opportunity.status == 'won',
                Opportunity.actual_close_date >= start_date,
                Opportunity.actual_close_date.isnot(None),
                Opportunity.created_at.isnot(None)
            )
        ).all()
        
        if not won_opportunities:
            return 0.0
        
        total_days = 0
        for opp in won_opportunities:
            cycle_days = (opp.actual_close_date - opp.created_at.date()).days
            total_days += cycle_days
        
        return total_days / len(won_opportunities)
    
    def get_activity_performance(self, days: int = 30) -> Dict:
        """
        Get activity performance metrics
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with activity performance data
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Activity counts by type
        activity_counts = self.db.query(
            Activity.activity_type,
            func.count(Activity.id).label('count')
        ).filter(
            Activity.created_at >= start_date
        ).group_by(Activity.activity_type).all()
        
        # Activity counts by day
        daily_activities = self.db.query(
            func.date(Activity.created_at).label('date'),
            func.count(Activity.id).label('count')
        ).filter(
            Activity.created_at >= start_date
        ).group_by(func.date(Activity.created_at)).all()
        
        # AI vs Human activities
        ai_activities = self.db.query(Activity).filter(
            and_(
                Activity.created_at >= start_date,
                Activity.ai_generated == True
            )
        ).count()
        
        human_activities = self.db.query(Activity).filter(
            and_(
                Activity.created_at >= start_date,
                Activity.ai_generated == False
            )
        ).count()
        
        return {
            'activity_counts': {row.activity_type: row.count for row in activity_counts},
            'daily_activities': {row.date.isoformat(): row.count for row in daily_activities},
            'ai_activities': ai_activities,
            'human_activities': human_activities,
            'total_activities': ai_activities + human_activities
        }
    
    def get_revenue_trends(self, days: int = 90) -> Dict:
        """
        Get revenue trends over time
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with revenue trend data
        """
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)
        
        # Monthly revenue (won deals)
        monthly_revenue = self.db.query(
            func.date_trunc('month', Opportunity.actual_close_date).label('month'),
            func.sum(Opportunity.actual_value).label('revenue')
        ).filter(
            and_(
                Opportunity.status == 'won',
                Opportunity.actual_close_date >= start_date,
                Opportunity.actual_value.isnot(None)
            )
        ).group_by(
            func.date_trunc('month', Opportunity.actual_close_date)
        ).all()
        
        # Pipeline value over time (current snapshot)
        current_pipeline = self.db.query(
            func.sum(Opportunity.estimated_value)
        ).filter(
            Opportunity.status == 'open'
        ).scalar() or 0.0
        
        return {
            'monthly_revenue': {
                row.month.isoformat(): float(row.revenue) for row in monthly_revenue
            },
            'current_pipeline_value': float(current_pipeline)
        }
    
    def get_top_performers(self, days: int = 30) -> Dict:
        """
        Get top performing contacts and opportunities
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with top performers
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Top opportunities by value
        top_opportunities = self.db.query(Opportunity).filter(
            Opportunity.created_at >= start_date
        ).order_by(
            Opportunity.estimated_value.desc()
        ).limit(10).all()
        
        # Most active contacts (by activity count)
        most_active_contacts = self.db.query(
            Contact,
            func.count(Activity.id).label('activity_count')
        ).join(
            Activity, Contact.id == Activity.contact_id
        ).filter(
            Activity.created_at >= start_date
        ).group_by(Contact.id).order_by(
            func.count(Activity.id).desc()
        ).limit(10).all()
        
        return {
            'top_opportunities': [opp.to_dict() for opp in top_opportunities],
            'most_active_contacts': [
                {
                    'contact': contact.to_dict(),
                    'activity_count': activity_count
                }
                for contact, activity_count in most_active_contacts
            ]
        }
