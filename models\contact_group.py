"""
Contact Group Model
==================
Manages contact groups for email campaigns and organization.
"""

from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime, Boolean, Text
from sqlalchemy.orm import relationship

# Import db from a separate module to avoid circular imports
try:
    from unified_sales_system import db
except ImportError:
    # Fallback for when this module is imported before the main app
    db = None

class ContactGroup(db.Model if db else object):
    """Contact group model for organizing contacts"""
    
    __tablename__ = 'contact_groups'
    
    id = Column(Integer, primary_key=True)
    
    # Basic Information
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), default='#007bff')  # Hex color code
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(100), nullable=True)
    
    def __repr__(self):
        return f'<ContactGroup {self.name}>'
    
    @property
    def contact_count(self):
        """Get number of contacts in this group"""
        if not db:
            return 0
        try:
            from models.contact_group_membership import ContactGroupMembership
            return ContactGroupMembership.query.filter_by(group_id=self.id).count()
        except:
            return 0
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'color': self.color,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'contact_count': self.contact_count
        }
    
    def get_contacts(self):
        """Get all contacts in this group"""
        if not db:
            return []
        try:
            from models.contact_group_membership import ContactGroupMembership
            from models.contact import Contact
            memberships = ContactGroupMembership.query.filter_by(group_id=self.id).all()
            contact_ids = [m.contact_id for m in memberships]
            return Contact.query.filter(Contact.id.in_(contact_ids)).all()
        except:
            return []
    
    def add_contact(self, contact_id, added_by=None):
        """Add a contact to this group"""
        if not db:
            return False
        try:
            from models.contact_group_membership import ContactGroupMembership
            
            # Check if already exists
            existing = ContactGroupMembership.query.filter_by(
                contact_id=contact_id, 
                group_id=self.id
            ).first()
            
            if existing:
                return True  # Already in group
            
            membership = ContactGroupMembership(
                contact_id=contact_id,
                group_id=self.id,
                added_by=added_by
            )
            db.session.add(membership)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False
    
    def remove_contact(self, contact_id):
        """Remove a contact from this group"""
        if not db:
            return False
        try:
            from models.contact_group_membership import ContactGroupMembership
            membership = ContactGroupMembership.query.filter_by(
                contact_id=contact_id, 
                group_id=self.id
            ).first()
            
            if membership:
                db.session.delete(membership)
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            return False
