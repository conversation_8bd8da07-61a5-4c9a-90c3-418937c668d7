import os
import json
import gradio as gr
from openai import OpenAI

# --- 1. PASTE THE CONFIG_DATA AND SalesBot CLASS HERE ---

# All your JSON configurations in one place
CONFIG_DATA = {
    "company_config": {
        "agent_ interactions.
3.  **Event Handlers**: Instead of a `while` loop, we'll have functions that are triggered by user actions (like clicking a button).

---

### The Complete Gradio App Code

Save this code as a single Python file (e.g., `app.py`).

```python
import gradio as gr
import os
import json
from openai import OpenAI

# --- 1. PASTE THE CONFIG_DATA DICTIONARY HERE ---
# This is the same large configuration dictionary from the previous script.
CONFIG_DATA = {
    "company_config": {
        "agent_name": "<PERSON>",
        "company": "24Seven Assistants",
        "system_prompt": (
            "You are <PERSON>, a helpful and expert sales agent for 2name": "<PERSON>",
        "company": "24Seven Assistants",
        "system_prompt": (
            "You are <PERSON>, a helpful and expert sales agent for 24Seven Assistants. Your goal is to guide the prospect through a sales conversation. "
            "You must strictly follow the rules and context provided below.\n"
            "Base Rules:\n"
            "1) Ask exactly ONE clear, comprehensive question per message.\n"
            "2) Never bundle multiple tasks in one message.\n"
            "3) Use the provided frameworks for objections or4Seven Assistants. Your goal is to guide the prospect through a sales conversation. "
            "You must strictly follow the rules and context provided below.\n"
            "Base Rules:\n"
            "1) Ask exactly ONE clear, comprehensive question per message.\n"
            "2) Never bundle multiple tasks in one message.\n"
            "3) Use the provided frameworks for objections or referrals when they arise.\n"
            "4) Focus on the key benefits: 24/7 availability, QR codes, our online assistants directory, and no-code setup."
        ),
        "context": {
            "company_description": "We create virtual assistants that never sleep - helping businesses serve referrals when they arise.\n"
            "4) Focus on the key benefits: 24/7 availability, QR codes, our online assistants directory, and no-code setup."
        ),
        "context": {
            "company_description": "We create virtual assistants that never sleep - helping businesses serve customers 24/7 even when customers 24/7 even when everyone goes home. Think of it like having a helpful employee who works nights, weekends, and holidays without ever getting tired.",
            "key_benefits": ["24/7 customer service availability", "QR everyone goes home. Think of it like having a helpful employee who works nights, weekends, and holidays without ever getting tired.",
            "key_benefits": ["24/7 customer service availability", "QR codes for instant access", codes for instant access", "Online assistants directory integration", "No-code setup process", "Capture leads outside business hours", "Reduce missed opportunities"],
            "success_stories": {"LocalCafe": {"problem": "losing potential customers who called "Online assistants directory integration", "No-code setup process", "Capture leads outside business hours", "Reduce missed opportunities after hours", "solution": "24Seven Assistant handling after-hours inquiries", "results": "40% increase in reservations"}},
            "pricing": {"small_business": {"setup": "UGX 25"],
            "success_stories": {"LocalCafe": {"problem": "losing potential customers who called after hours0,000", "monthly": "UGX 100,000"}, "medium_", "solution": "24Seven Assistant handling after-hours inquiries", "results": "40% increase inbusiness": {"setup": "UGX 250,000", "monthly": "UGX  reservations"}},
            "pricing": {"small_business": {"setup": "UGX 250,250,000"}, "enterprise": {"setup": "UGX 3,000,000", "monthly": "UGX 100,000"}, "medium_business":000", "monthly": "UGX 1,000,000"}},
         {"setup": "UGX 250,000", "monthly": "UGX 25}
    },
    "sales_stages": [
        {"id": "opening", "objective": "0,000"}, "enterprise": {"setup": "UGX 3,000,00Introduce yourself, the company, the purpose of the chat, and then ask for permission to continue.", "tasks": [{"0", "monthly": "UGX 1,000,000"}},
        }
task": "introduce yourself, company purpose of initiating chat and request permission to continue chat.", "examples": ["Hello {{    },
    "sales_stages": [
        {"id": "opening", "objective": "Introduce yourselfname}}! I'm {{agent_name}} from {{company}}. We create virtual assistants that never sleep, helping, the company, the purpose of the chat, and then ask for permission to continue.", "tasks": [{"task": businesses serve customers 24/7. Would you be open to a brief chat about how we can help you capture more leads "introduce yourself, company purpose of initiating chat and request permission to continue chat.", "examples": ["Hello {{name}} outside of business hours?"]}]},
        {"id": "trust", "objective": "Build rapport and credibility by! I'm {{agent_name}} from {{company}}. We create virtual assistants that never sleep, helping businesses serve sharing a story and asking a leading question about their challenges.", "tasks": [{"task": "Share LocalCafe story and ask about similar challenges", "examples": ["Let me share a quick story about LocalCafe... they saw a 4 customers 24/7. Would you be open to a brief chat about how we can help you capture more leads outside of business hours?"]}]},
        {"id": "trust", "objective": "Build rapport and credibility by0% increase in reservations... Have you ever faced challenges with customers trying to reach you after business hours?"]}]},
        {"id sharing a story and asking a leading question about their challenges.", "tasks": [{"task": "Share LocalCafe story and ask about similar": "discovery", "objective": "Understand their present challenges and future plans, then ask a leading question into the demonstration challenges", "examples": ["Let me share a quick story about LocalCafe... they saw a 40% increase in stage.", "tasks": [{"task": "Ask about current challenges", "examples": ["What challenges do you face with reservations... Have you ever faced challenges with customers trying to reach you after business hours?"]}]},
        {"id": "discovery", "objective": "Understand their present challenges and future plans, then ask a leading question into the demonstration stage.", "tasks customer service coverage, especially during nights or weekends?"]}, {"task": "Ask about their ideal future state", "examples": ["If you could serve customers 24/7 without hiring night staff, what impact would that have on your": [{"task": "Ask about current challenges", "examples": ["What challenges do you face with customer service coverage, business?"]}, {"task": "Ask if they want to hear a solution", "examples": ["Would you be interested in learning more about how our virtual assistants work?"]}]},
        {"id": "demonstration", "objective": especially during nights or weekends?"]}, {"task": "Ask about their ideal future state", "examples": ["If you "Connect the product to their pains and present pricing options.", "tasks": [{"task": "Restate their main problem could serve customers 24/7 without hiring night staff, what impact would that have on your business?"]}, {"task": "Ask if they want to hear a solution", "examples": ["Would you be interested in learning more about how", "examples": ["So if I understand correctly, you're losing potential customers during after-hours, is that our virtual assistants work?"]}]},
        {"id": "demonstration", "objective": "Connect the product to right?"]}, {"task": "Explain how a feature solves it", "examples": ["Here's how it works: customers scan their pains and present pricing options.", "tasks": [{"task": "Restate their main problem", "examples": ["So if I a QR code or find you in our online assistants directory, then chat with your virtual assistant 24/7..."]}, understand correctly, you're losing potential customers during after-hours, is that right?"]}, {"task": "Explain {"task": "Ask if they want to see a plan and present options", "examples": ["We have packages for all business sizes: Small businesses start at UGX 250,000 setup + UGX 1 how a feature solves it", "examples": ["Here's how it works: customers scan a QR code or find you00,000/month, Medium at UGX 250,000 setup + UGX 2 in our online assistants directory, then chat with your virtual assistant 24/7..."]}, {"task": "Ask if they want50,000/month, and Enterprise at UGX 3,000,000 to see a plan and present options", "examples": ["We have packages for all business sizes: Small businesses start at setup + UGX 1,000,000/month. Which of these options sounds like the best UGX 250,000 setup + UGX 100,000/month fit?"]}]},
        {"id": "closing", "objective": "Thank them for choosing a plan and connect, Medium at UGX 250,000 setup + UGX 250,000/month them to a manager to finalize the transaction.", "tasks": [{"task": "Thank prospect for choosing a plan and request to connect them, and Enterprise at UGX 3,000,000 setup + UGX 1,00 to the manager", "examples": ["Excellent, thank you for choosing the {{plan_type}} plan. Our manager0,000/month. Which of these options sounds like the best fit?"]}]},
        {"id can call you to finalize everything. What's the best number and time to reach you?"]}]}
    ],
    "": "closing", "objective": "Thank them for choosing a plan and connect them to a manager to finalize the transaction.", "tasksobjection_handling": { "framework_summary": { "name": "Steve Schiffman's Objection Handling Framework": [{"task": "Thank prospect for choosing a plan and request to connect them to the manager", "examples": ["", "steps": [{"step": 1, "name": "Acknowledge and Cushion"}, {"step": Excellent, thank you for choosing the {{plan_type}} plan. Our manager can call you to finalize everything. What2, "name": "Question to Clarify"}, {"step": 3, "name": "Answer the Real's the best number and time to reach you?"]}]}
    ],
    "objection_handling": Objection"}, {"step": 4, "name": "Confirm and Close"}] }, "common_objections": [{" { "framework_summary": { "name": "Steve Schiffman's Objection Handling Framework", "steps": [{"step": 1, "name": "Acknowledge and Cushion"}, {"step": 2, "name":objection_type": "Price", "initial_statement": "It's too expensive.", "handler": {"c "Question to Clarify"}, {"step": 3, "name": "Answer the Real Objection"}, {"step":ushion": "I understand that budget is always a key consideration.", "question": "When you say it's 4, "name": "Confirm and Close"}] }, "common_objections": [{"objection_type": too expensive, what budget did you have in mind for solving this?", "answer": "Let's consider the cost of inaction. "Price", "initial_statement": "It's too expensive.", "handler": {"cushion": " If our assistant captures just a few customers a month that you're currently missing, it not only pays for itself but generates a profitI understand that budget is always a key consideration.", "question": "When you say it's too expensive, what budget did you.", "confirm_and_close": "Does that clarify the value? With that in mind, which plan would be the best fit have in mind for solving this?", "answer": "Let's consider the cost of inaction. If our assistant captures just a few?"}}] },
    "referral_handling": { "internal_referral": { "objective": "Get customers a month that you're currently missing, it not only pays for itself but generates a profit.", "confirm_and_close referred to the correct decision-maker.", "stages": [{"id": "request_internal_referral", "tasks": "Does that clarify the value? With that in mind, which plan would be the best fit?"}}] },
": [{"task": "Ask the current contact to refer you to the appropriate person.", "examples": ["Thank you. Who would    "referral_handling": { "internal_referral": { "objective": "Get referred to the correct be the best person in your organization to speak with about improving after-hours customer engagement?"]}]}, {"id": " decision-maker.", "stages": [{"id": "request_internal_referral", "tasks": [{"task":re_engagement_with_decision_maker", "tasks": [{"task": "Re-introduce yourself to the new contact.", "Ask the current contact to refer you to the appropriate person.", "examples": ["Thank you. Who would be the best person "examples": ["Hello {{decision_maker_name}}, {{original_contact_name}} suggested I reach out..."]}] in your organization to speak with about improving after-hours customer engagement?"]}]}, {"id": "re_engagement_}] }, "external_referral": { "objective": "Ask the new customer for referrals to other potential clients.",with_decision_maker", "tasks": [{"task": "Re-introduce yourself to the new contact.", "examples "stages": [{"id": "request_external_referral", "tasks": [{"task": "Ask the new": ["Hello {{decision_maker_name}}, {{original_contact_name}} suggested I reach out..."]}]}] }, "external_referral": { "objective": "Ask the new customer for referrals to other potential clients.", customer if they know anyone else who could benefit.", "examples": ["We're thrilled to have you on board! Do "stages": [{"id": "request_external_referral", "tasks": [{"task": "Ask the new customer if they you happen to know anyone in your network who might also benefit from a 24/7 virtual assistant?"]}]}] } }
}


# --- 2. PASTE THE SalesBot CLASS HERE ---
# This is the same class know anyone else who could benefit.", "examples": ["We're thrilled to have you on board! Do you happen to know from the previous script, unchanged.
class SalesBot:
    def __init__(self, config, api_key): anyone in your network who might also benefit from a 24/7 virtual assistant?"]}]}] } }
}

class
        self.config = config
        self.client = OpenAI(api_key=api_key)
 SalesBot:
    def __init__(self, config, api_key):
        self.config = config
        self.client = OpenAI(api_key=api_key)
        self.chat_history =        self.chat_history = []
        self.current_stage_index = 0
        self. []
        self.current_stage_index = 0
        self.prospect_name = "thereprospect_name = "there"

    def _build_system_prompt(self):
        current_stage = self"

    def _build_system_prompt(self):
        current_stage = self.config["sales_stages.config["sales_stages"][self.current_stage_index]
        stage_prompt = f"""
--- YOUR CURRENT STAGE: {current_stage['id'].upper()} ---
Your primary objective right now is: {current"][self.current_stage_index]
        stage_prompt = f"""
--- YOUR CURRENT STAGE:_stage['objective']}
You must follow the tasks for this stage. Ask one question at a time to complete the tasks.
 {current_stage['id'].upper()} ---
Your primary objective right now is: {current_stage['objective']}
YouTasks for this stage:
{json.dumps(current_stage['tasks'], indent=2)}

IMPORTANT: When you must follow the tasks for this stage. Ask one question at a time to complete the tasks.
Tasks for this stage have successfully completed all tasks for this stage and the user has agreed to move forward,
end your VERY LAST message for:
{json.dumps(current_stage['tasks'], indent=2)}

IMPORTANT: When you have successfully completed all tasks this stage with the exact string: [STAGE_COMPLETE]
"""
        base_prompt = self.config[" for this stage and the user has agreed to move forward,
end your VERY LAST message for this stage with the exactcompany_config"]["system_prompt"]
        context_prompt = f"\n--- COMPANY AND PRODUCT CONTEXT -- string: [STAGE_COMPLETE]
"""
        # Combine all parts into the final system prompt
        final_prompt = (-\n{json.dumps(self.config['company_config']['context'], indent=2)}"
        objection_prompt = f"\n--- OBJECTION HANDLING FRAMEWORK ---\n{json.dumps(self.config['ob
            f"{self.config['company_config']['system_prompt']}\n"
            f"--- COMPANY AND PRODUCT CONTEXT ---\n{json.dumps(self.config['company_config']['context'], indent=2jection_handling'], indent=2)}"
        referral_prompt = f"\n--- REFERRAL HANDLING FRAMEWORK ---\n{json.dumps(self.config['referral_handling'], indent=2)}"
        
)}\n"
            f"{stage_prompt}\n"
            f"--- OBJECTION HANDLING FRAMEWORK (Use if you detect an objection) ---\n{json.dumps(self.config['objection_handling'],        return f"{base_prompt}{context_prompt}{stage_prompt}{objection_prompt}{referral_ indent=2)}\n"
            f"--- REFERRAL HANDLING FRAMEWORK (Use if the user needs to referprompt}"

    def _get_ai_response(self):
        system_prompt = self._build_ you or if you ask for a referral) ---\n{json.dumps(self.config['referral_system_prompt()
        messages = [{"role": "system", "content": system_prompt}] + self.chat_history
        try:
            completion = self.client.chat.completions.create(
handling'], indent=2)}"
        )
        return final_prompt

    def _get_ai_response(self):
        system_prompt = self._build_system_prompt()
        messages = [{"role": "system                model="gpt-4-turbo-preview", messages=messages, temperature=0.7
            )
", "content": system_prompt}] + self.chat_history
        try:
            completion = self.            return completion.choices[0].message.content
        except Exception as e:
            return f"Anclient.chat.completions.create(
                model="gpt-4-turbo-preview", messages=messages, temperature error occurred: {e}"

    def start_conversation(self, prospect_name="there"):
        self.prospect=0.7,
            )
            return completion.choices[0].message.content
        except Exception as_name = prospect_name
        first_message_template = self.config['sales_stages'][0]['tasks'][0]['examples'][0]
        formatted_message = first_message_template.replace("{{name}}", self e:
            return f"An error occurred: {e}"

    def start_conversation(self, prospect_name="there"):
        self.prospect_name = prospect_name
        first_message_template = self..prospect_name)\
                                                  .replace("{{agent_name}}", self.config['company_config']['agent_name'])\
                                                  .replace("{{company}}", self.config['company_config']['company'])
        selfconfig['sales_stages'][0]['tasks'][0]['examples'][0]
        formatted_message = first_message_template.replace("{{name}}", self.prospect_name)\
                                                  .replace("{{agent_name.chat_history.append({"role": "assistant", "content": formatted_message})
        return formatted_message

    def chat(self, user_input):
        self.chat_history.append({"role":}}", self.config['company_config']['agent_name'])\
                                                  .replace("{{company}}", self.config['company_config']['company'])
        self.chat_history.append({"role": "assistant", "content": "user", "content": user_input})
        ai_response = self._get_ai_response() formatted_message})
        return formatted_message

    def chat(self, user_input):
        self
        if "[STAGE_COMPLETE]" in ai_response:
            ai_response = ai_response.replace.chat_history.append({"role": "user", "content": user_input})
        ai_response("[STAGE_COMPLETE]", "").strip()
            if self.current_stage_index < len(self.config = self._get_ai_response()
        if "[STAGE_COMPLETE]" in ai_response:
            ["sales_stages"]) - 1:
                self.current_stage_index += 1
                next_stage_id = self.config["sales_stages"][self.current_stage_index]["id"]
ai_response = ai_response.replace("[STAGE_COMPLETE]", "").strip()
            if self.current_stage_index < len(self.config["sales_stages"]) - 1:
                self.current_                print(f"\n[SYSTEM: Stage complete. Advancing to '{next_stage_id}'.]\n")
            stage_index += 1
                # Optional: log stage change to console for debugging
                print(f"[else:
                print("\n[SYSTEM: Final stage complete. Conversation concluded.]\n")
        self.SYSTEM: Advancing to stage '{self.config['sales_stages'][self.current_stage_index]['id']}'.]")chat_history.append({"role": "assistant", "content": ai_response})
        return ai_response
        self.chat_history.append({"role": "assistant", "content": ai_response})
        return ai

# --- 3. GRADIO APP IMPLEMENTATION ---

# Ensure you have your OpenAI API key
api_key = os_response

# --- 2. GRADIO APP IMPLEMENTATION ---

# Check for API Key
api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI.getenv("OPENAI_API_KEY")
if not api_key:
    raise ValueError("OPENAI_API_KEY environment variable not set. Please set it before running.")

def start_chat(prospect_name):_API_KEY environment variable not set. Please set it before running the app.")

def start_chat(name):
    """Initializes the bot and starts the conversation."""
    if not prospect_name.strip():
        
    """
    Initializes the bot, gets the first message, and updates the UI to show the chat window# You can handle this with a gr.Warning or just return
        return None, [], gr.update(visible=True.
    """
    if not name:
        # You can add a gr.Warning or just return if), gr.update(visible=False)

    bot = SalesBot(config=CONFIG_DATA, api_key= the name is empty
        return None, None, gr.update(visible=True), gr.update(visible=Falseapi_key)
    first_message = bot.start_conversation(prospect_name)
    
)

    bot_instance = SalesBot(config=CONFIG_DATA, api_key=api_key)
    first    # Gradio chat history is a list of lists: [[user_msg, bot_msg], ...]
    initial_message = bot_instance.start_conversation(name)
    
    # Initial chat history for the chatbot_history = [[None, first_message]]
    
    # Hide the startup UI and show the chat UI
     component
    initial_chat_history = [[None, first_message]]
    
    # Update UI:return bot, initial_history, gr.update(visible=False), gr.update(visible=True)


def handle_chat_interaction(user_message, history, bot_state):
    """Handles a single turn of the chat."""
    bot = bot_state
    bot_response = bot.chat(user_message)
 Hide name input, show chat interface
    return (
        bot_instance,
        initial_chat_history,
        gr.update(visible=False), # Hide the name input row
        gr.update(visible=True),    history.append([user_message, bot_response])
    return "", history, bot # Return updated bot state


  # Show the chat interface row
    )

def user_chat(user_message, history, bot_instancewith gr.Blocks(theme=gr.themes.Soft(), title="24Seven Assistants Sales Bot") as app):
    """
    Handles a single turn of the user-bot conversation.
    """
    # Append user:
    gr.Markdown("# 24Seven Assistants Sales Bot")
    gr.Markdown("Chat with Sarah message to chatbot history
    history.append([user_message, None])
    yield history, bot_instance #, our AI sales agent, to learn how we can help your business.")

    # State object to hold the bot instance Yield to show user message immediately

    # Get bot's response
    bot_response = bot_instance.chat(user for the session
    bot_state = gr.State()

    # UI for starting the conversation
    with gr.Row_message)
    
    # Update the last entry in the history with the bot's response
    history(visible=True) as startup_ui:
        with gr.Column():
            name_input = gr[-1][1] = bot_response
    
    yield history, bot_instance

def clear_and.Textbox(label="What is your name?", placeholder="Enter your name here...")
            start_button = gr_restart():
    """Resets the UI to its initial state."""
    return None, [], gr.update(.Button("Start Conversation", variant="primary")

    # UI for the actual chat
    with gr.Column(visible=visible=True), gr.update(visible=False)


# --- 3. BUILD THE GRADIO UI ---

with grFalse) as chat_ui:
        chatbot = gr.Chatbot(label="Conversation with Sarah", height=5.Blocks(theme=gr.themes.Soft(), title="24Seven Assistants Sales Bot") as demo:
    00)
        with gr.Row():
            msg_input = gr.Textbox(
                label="Your Message",bot_state = gr.State(None) # To store the SalesBot instance

    gr.Markdown("# 
                placeholder="Type your message here and press Enter...",
                scale=7,
            )
            send_button = gr.Button("Send", variant="primary", scale=1)

    # Event handlers
    start_🤖 24Seven Assistants Sales Bot")
    gr.Markdown("This is a demonstration of a conversational sales agentbutton.click(
        fn=start_chat,
        inputs=[name_input],
        outputs=[bot. Start by entering your name.")

    # Initial UI for getting the user's name
    with gr._state, chatbot, startup_ui, chat_ui]
    )
    
    msg_input.submitRow(variant="panel", visible=True) as name_input_row:
        with gr.Column((
        fn=handle_chat_interaction,
        inputs=[msg_input, chatbot, bot_state],
        outputs=[msg_input, chatbot, bot_state]
    )
    
    send_button.clickscale=3):
            name_box = gr.Textbox(label="What is your name?", placeholder="e(
        fn=handle_chat_interaction,
        inputs=[msg_input, chatbot, bot_state.g., Alex")
        with gr.Column(scale=1, min_width=100):
            start],
        outputs=[msg_input, chatbot, bot_state]
    )

if __name__ ==_btn = gr.Button("▶️ Start Chat", variant="primary")

    # Main Chat UI (init "__main__":
    app.launch()