#!/usr/bin/env python3
"""
Run Campaign Test
================
Simple script to start the sales system and run campaign tests.
"""

import subprocess
import time
import sys
import os
import threading
import requests

def check_server_running(url="http://localhost:5000", timeout=30):
    """Check if server is running"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(1)
    return False

def start_server():
    """Start the unified sales system server"""
    print("🚀 Starting 24Seven Assistants Sales System...")
    
    try:
        # Start the server in a subprocess
        process = subprocess.Popen([
            sys.executable, "unified_sales_system.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("⏳ Waiting for server to start...")
        
        # Wait for server to be ready
        if check_server_running():
            print("✅ Server is running at http://localhost:5000")
            return process
        else:
            print("❌ Server failed to start within timeout")
            process.terminate()
            return None
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def run_smtp_test():
    """Run SMTP connection test"""
    print("\n🔌 Running SMTP Connection Test...")
    try:
        result = subprocess.run([
            sys.executable, "test_campaign_email_sending.py"
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ SMTP test timed out")
        return False
    except Exception as e:
        print(f"❌ SMTP test failed: {e}")
        return False

def run_campaign_test():
    """Run campaign creation and sending test"""
    print("\n📧 Running Campaign Test...")
    try:
        result = subprocess.run([
            sys.executable, "create_and_test_campaign.py"
        ], capture_output=True, text=True, timeout=120)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ Campaign test timed out")
        return False
    except Exception as e:
        print(f"❌ Campaign test failed: {e}")
        return False

def main():
    """Main function"""
    print("🧪 24Seven Assistants - Campaign Testing Suite")
    print("=" * 60)
    
    # Check if files exist
    required_files = [
        "unified_sales_system.py",
        "test_campaign_email_sending.py", 
        "create_and_test_campaign.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return
    
    print("✅ All required files found")
    
    # Start server
    server_process = start_server()
    if not server_process:
        print("❌ Cannot proceed without server")
        return
    
    try:
        # Run tests
        print("\n" + "=" * 60)
        print("🧪 RUNNING TESTS")
        print("=" * 60)
        
        # Test 1: SMTP Connection
        smtp_success = run_smtp_test()
        
        # Test 2: Campaign System
        campaign_success = run_campaign_test()
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        print(f"SMTP Test:     {'✅ PASS' if smtp_success else '❌ FAIL'}")
        print(f"Campaign Test: {'✅ PASS' if campaign_success else '❌ FAIL'}")
        
        if smtp_success and campaign_success:
            print("\n🎉 All tests passed! Email campaigns are working correctly.")
            print("\n💡 Next steps:")
            print("1. Check your email inbox (<EMAIL>) for test messages")
            print("2. Visit the web interface: http://localhost:5000")
            print("3. Check the analytics dashboard for campaign results")
        else:
            print("\n⚠️ Some tests failed. Check the output above for details.")
        
        print(f"\n🌐 Web interface: http://localhost:5000")
        print("📊 Analytics: http://localhost:5000/analytics")
        print("📧 Campaigns: http://localhost:5000/campaigns")
        print("\nPress Ctrl+C to stop the server")
        
        # Keep server running
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n👋 Stopping server...")
            
    finally:
        # Clean up
        if server_process:
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()

if __name__ == "__main__":
    main()
