#!/usr/bin/env python3
"""
Quick SMTP Test
==============
Simple script to test SMTP configuration without running the full system.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from datetime import datetime

def test_smtp_connection():
    """Test SMTP connection with user's configuration"""
    print("🔌 Testing SMTP Connection...")
    print("-" * 40)
    
    # User's SMTP configuration
    config = {
        'server': 'mail.24seven.site',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'use_ssl': True
    }
    
    print(f"Server: {config['server']}:{config['port']}")
    print(f"Username: {config['username']}")
    print(f"SSL: {config['use_ssl']}")
    print()
    
    try:
        # Create connection
        if config['use_ssl']:
            print("Creating SSL connection...")
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(config['server'], config['port'], context=context)
        else:
            print("Creating TLS connection...")
            server = smtplib.SMTP(config['server'], config['port'])
            server.starttls()
        
        print("✅ Connection established")
        
        # Login
        print("Authenticating...")
        server.login(config['username'], config['password'])
        print("✅ Authentication successful")
        
        # Test sending a simple email
        print("Sending test email...")
        
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = 'SMTP Test - 24Seven Assistants'
        msg['From'] = f"24Seven Assistants <{config['username']}>"
        msg['To'] = config['username']  # Send to self
        
        # Email content
        text_content = f"""
SMTP Test Email

This is a test email to verify that the SMTP configuration is working correctly.

Configuration tested:
- Server: {config['server']}
- Port: {config['port']}
- SSL: {config['use_ssl']}
- Username: {config['username']}

Test sent at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

If you receive this email, your SMTP configuration is working correctly!

Best regards,
24Seven Assistants Sales System
        """
        
        html_content = f"""
<html>
<body>
    <h2>SMTP Test Email</h2>
    <p>This is a test email to verify that the SMTP configuration is working correctly.</p>
    
    <h3>Configuration tested:</h3>
    <ul>
        <li><strong>Server:</strong> {config['server']}</li>
        <li><strong>Port:</strong> {config['port']}</li>
        <li><strong>SSL:</strong> {config['use_ssl']}</li>
        <li><strong>Username:</strong> {config['username']}</li>
    </ul>
    
    <p><strong>Test sent at:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <p>If you receive this email, your SMTP configuration is working correctly!</p>
    
    <p>Best regards,<br>
    <strong>24Seven Assistants Sales System</strong></p>
</body>
</html>
        """
        
        # Attach content
        text_part = MIMEText(text_content, 'plain', 'utf-8')
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(text_part)
        msg.attach(html_part)
        
        # Send email
        server.send_message(msg)
        print("✅ Test email sent successfully")
        
        # Close connection
        server.quit()
        print("✅ Connection closed")
        
        print("\n🎉 SMTP test completed successfully!")
        print(f"📧 Check your inbox at {config['username']} for the test email")
        
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ Authentication failed: {e}")
        print("   Check your username and password")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ Connection failed: {e}")
        print("   Check your server and port settings")
        return False
        
    except smtplib.SMTPException as e:
        print(f"❌ SMTP error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function"""
    print("🧪 24Seven Assistants - Quick SMTP Test")
    print("=" * 50)
    print("Testing SMTP configuration: mail.24seven.site")
    print()
    
    success = test_smtp_connection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SMTP configuration is working correctly!")
        print("You can now proceed to test email campaigns.")
    else:
        print("❌ SMTP configuration has issues.")
        print("Please check your email settings and try again.")

if __name__ == "__main__":
    main()
