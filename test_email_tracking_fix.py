#!/usr/bin/env python3
"""
Email Tracking Fix Test
======================
Test script to verify email open and click tracking is working properly.
"""

import requests
import time
import json

BASE_URL = "http://localhost:5000"

def test_email_tracking():
    """Test email tracking functionality"""
    print("🔍 TESTING EMAIL TRACKING SYSTEM")
    print("=" * 50)

    # Create a test contact with session ID
    session_id = "test-email-tracking-session-001"
    print(f"📝 Creating test contact with session ID: {session_id}")

    # Create contact via track-session endpoint
    track_data = {
        'session_id': session_id,
        'stage': 'email_sent',
        'contact_name': 'Test Email User',
        'contact_email': '<EMAIL>',
        'action': 'conversation_started'
    }

    try:
        track_response = requests.post(f"{BASE_URL}/api/track-session", json=track_data)
        if track_response.status_code == 200:
            print("✅ Test contact created successfully")
            response_data = track_response.json()
            print(f"   Contact ID: {response_data.get('contact_id')}")
        else:
            print(f"❌ Failed to create test contact: {track_response.status_code}")
            print(f"   Response: {track_response.text}")
    except Exception as e:
        print(f"❌ Error creating test contact: {e}")
        print(f"📝 Using default session ID anyway: {session_id}")

    print(f"\n📧 Testing Email Open Tracking")
    print("-" * 30)

    # Test email open tracking
    try:
        print(f"🔍 Accessing tracking pixel: /track/open/{session_id}")
        response = requests.get(f"{BASE_URL}/track/open/{session_id}")

        if response.status_code == 200:
            print("✅ Email open tracking pixel accessed successfully")
            print(f"   Response type: {response.headers.get('Content-Type')}")
            print(f"   Response size: {len(response.content)} bytes")

            # Check if it's a valid PNG
            if response.headers.get('Content-Type') == 'image/png':
                print("✅ Valid PNG tracking pixel returned")
            else:
                print(f"⚠️ Unexpected content type: {response.headers.get('Content-Type')}")
        else:
            print(f"❌ Email open tracking failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error accessing tracking pixel: {e}")

    print(f"\n🔗 Testing Email Click Tracking")
    print("-" * 30)

    # Test email click tracking (chatbot entry)
    try:
        print(f"🔍 Accessing chatbot link: /chatbot/{session_id}")
        response = requests.get(f"{BASE_URL}/chatbot/{session_id}", allow_redirects=False)

        if response.status_code in [302, 301]:  # Redirect expected
            print("✅ Email click tracking redirect working")
            print(f"   Redirect to: {response.headers.get('Location')}")
        elif response.status_code == 200:
            print("✅ Email click tracking page loaded")
        else:
            print(f"❌ Email click tracking failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error accessing chatbot link: {e}")

    # Wait a moment for data to be processed
    print(f"\n⏳ Waiting for tracking data to be processed...")
    time.sleep(2)

    print(f"\n📊 Checking Analytics Data")
    print("-" * 30)

    # Check analytics data
    try:
        response = requests.get(f"{BASE_URL}/api/analytics/stage-progression")
        if response.status_code == 200:
            analytics_data = response.json()
            print("✅ Analytics endpoint accessible")

            # Check for email metrics
            daily_analytics = analytics_data.get('daily_analytics', [])
            if daily_analytics:
                latest_analytics = daily_analytics[-1]
                print(f"📈 Latest analytics data:")
                print(f"   Date: {latest_analytics.get('date')}")
                print(f"   Total opportunities: {latest_analytics.get('total_opportunities', 0)}")
                print(f"   New opportunities: {latest_analytics.get('new_opportunities', 0)}")
                print(f"   Won opportunities: {latest_analytics.get('won_opportunities', 0)}")
            else:
                print("⚠️ No daily analytics data found")

        else:
            print(f"❌ Analytics endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking analytics: {e}")

    print(f"\n🔍 Checking Email Campaign Metrics")
    print("-" * 30)

    # Check email campaign metrics directly
    try:
        # Check if we can access the unified analytics
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Main dashboard accessible")

            # Look for email metrics in the page content
            if "emails_opened" in response.text.lower():
                print("✅ Email metrics found in dashboard")
            else:
                print("⚠️ Email metrics not visible in dashboard")
        else:
            print(f"❌ Dashboard access failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking dashboard: {e}")

    print(f"\n📋 Summary")
    print("=" * 50)
    print("✅ Email tracking test completed")
    print("💡 Next steps:")
    print("   1. Check the analytics dashboard at http://localhost:5000/analytics")
    print("   2. Verify email open/click rates are updating")
    print("   3. Check contact pages for tracking data")
    print("   4. Monitor application logs for tracking events")

if __name__ == "__main__":
    test_email_tracking()
