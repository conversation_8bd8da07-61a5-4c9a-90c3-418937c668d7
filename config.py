"""
24Seven Assistants Sales Department Configuration
================================================
Central configuration for the complete sales system including SMTP, database,
and analytics settings.
"""

import os
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Base configuration class"""
    
    # Application Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///sales_department.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Email/SMTP Configuration for 24Seven Assistants
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or '<EMAIL>'
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or 'your-email-password'
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    
    # SambaNova AI Configuration (from your existing setup)
    SAMBA_API_KEY = os.environ.get('SAMBA_API_KEY') or '76273dc7-7c75-417f-8cfe-ef88ad56db78'
    SAMBA_BASE_URL = os.environ.get('SAMBA_BASE_URL') or 'https://api.sambanova.ai/v1'
    SAMBA_MODEL = os.environ.get('SAMBA_MODEL') or 'Meta-Llama-3.3-70B-Instruct'
    SAMBA_TEMPERATURE = float(os.environ.get('SAMBA_TEMPERATURE', '0.3'))
    
    # Redis Configuration (for Celery background tasks)
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Celery Configuration
    CELERY_BROKER_URL = REDIS_URL
    CELERY_RESULT_BACKEND = REDIS_URL
    
    # Sales Department Settings
    COMPANY_NAME = "24Seven Assistants"
    COMPANY_DESCRIPTION = "Professional virtual assistant services available 24/7"
    SALES_EMAIL = "<EMAIL>"
    SUPPORT_EMAIL = "<EMAIL>"
    
    # Sales Process Configuration
    SALES_STAGES = [
        'Opening',
        'Trust',
        'Discovery', 
        'Demonstration',
        'Close'
    ]
    
    # Email Campaign Settings
    EMAIL_BATCH_SIZE = 50  # Number of emails to send per batch
    EMAIL_DELAY_SECONDS = 2  # Delay between emails to avoid spam
    
    # Analytics Settings
    ANALYTICS_RETENTION_DAYS = 365
    DASHBOARD_REFRESH_INTERVAL = 300  # 5 minutes
    
    # File Upload Settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = 'uploads'
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
