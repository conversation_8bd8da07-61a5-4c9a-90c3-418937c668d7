{% extends "base.html" %}

{% block title %}Integrated Sales System - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt text-primary"></i> Integrated Sales Dashboard</h1>
    <div class="text-muted">
        <i class="fas fa-clock"></i> Last updated: <span id="lastUpdated">Loading...</span>
    </div>
</div>

<!-- Integration Status Row -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4><i class="fas fa-rocket"></i> 24Seven Assistants - Complete Sales System</h4>
                        <p class="mb-0">Sales Department Dashboard + AI Sales Chatbot - Fully Integrated</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="btn btn-light btn-lg">
                            <i class="fas fa-robot"></i> Open Sales Chatbot
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ total_contacts }}</div>
                <div class="metric-label">Total Contacts</div>
                <div class="mt-2">
                    <a href="{{ url_for('contacts_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ total_opportunities }}</div>
                <div class="metric-label">Opportunities</div>
                <div class="mt-2">
                    <a href="{{ url_for('opportunities_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ active_campaigns }}</div>
                <div class="metric-label">Active Campaigns</div>
                <div class="mt-2">
                    <a href="{{ url_for('campaigns_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">{{ bot_interactions }}</div>
                <div class="metric-label">Bot Interactions</div>
                <div class="mt-2">
                    <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="btn btn-sm btn-light">
                        <i class="fas fa-robot"></i> Chat Now
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">5</div>
                <div class="metric-label">Sales Stages</div>
                <div class="mt-2">
                    <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-2">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">2</div>
                <div class="metric-label">Systems Active</div>
                <div class="mt-2">
                    <span class="badge bg-light text-dark">Dashboard</span>
                    <span class="badge bg-light text-dark">Chatbot</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Integration Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-link"></i> System Integration Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-tachometer-alt text-primary"></i> Sales Department Dashboard</h6>
                            <p class="small text-muted mb-2">Complete CRM and analytics system</p>
                            <ul class="small mb-3">
                                <li>Contact & Opportunity Management</li>
                                <li>Email Campaign System</li>
                                <li>Sales Analytics & Reporting</li>
                                <li>Stage Tracking & Pipeline</li>
                            </ul>
                            <span class="badge bg-success">✅ Active on Port 5000</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-robot text-success"></i> AI Sales Chatbot (Sarah)</h6>
                            <p class="small text-muted mb-2">Intelligent sales conversation agent</p>
                            <ul class="small mb-3">
                                <li>5-Stage Sales Process Automation</li>
                                <li>Objection Handling & Trust Building</li>
                                <li>Real-time Stage Progression</li>
                                <li>SambaNova AI Integration</li>
                            </ul>
                            <span class="badge bg-success">✅ Active - Flask Chat UI</span>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-sync-alt text-info"></i> Integration Features</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-2">
                                    <i class="fas fa-database fa-2x text-primary mb-2"></i>
                                    <h6>Shared Database</h6>
                                    <small class="text-muted">Contacts & activities sync automatically</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2">
                                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                    <h6>Real-time Analytics</h6>
                                    <small class="text-muted">Bot interactions tracked in dashboard</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2">
                                    <i class="fas fa-funnel-dollar fa-2x text-warning mb-2"></i>
                                    <h6>Stage Progression</h6>
                                    <small class="text-muted">Bot moves prospects through sales stages</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-2">
                                    <i class="fas fa-envelope fa-2x text-info mb-2"></i>
                                    <h6>Follow-up Campaigns</h6>
                                    <small class="text-muted">Automated email sequences</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-robot"></i> Start Sales Chat
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('add_contact') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-user-plus"></i> Add Contact
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('create_campaign') }}" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-envelope-open-text"></i> Create Campaign
                        </a>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-chart-bar"></i> View Analytics
                        </a>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-secondary w-100 mb-2" onclick="testSMTP()">
                            <i class="fas fa-cog"></i> Test SMTP
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100 mb-2" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Analytics Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Advanced Analytics Dashboard</h5>
            </div>
            <div class="card-body">
                <div class="row justify-content-center">
                    <div class="col-md-3">
                        <a href="{{ url_for('sales_pipeline_analytics') }}" class="btn btn-success w-100 mb-2" style="background: linear-gradient(135deg, #28a745, #20c997); border: none; color: white; font-weight: 500;">
                            <i class="fas fa-chart-line"></i> Sales Pipeline
                        </a>
                        <small class="text-muted d-block text-center">Pipeline funnel & conversion analysis</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('sales_cycle_analytics') }}" class="btn btn-info w-100 mb-2" style="background: linear-gradient(135deg, #17a2b8, #007bff); border: none; color: white; font-weight: 500;">
                            <i class="fas fa-clock"></i> Sales Cycle
                        </a>
                        <small class="text-muted d-block text-center">Cycle time & stage duration metrics</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('session_analytics') }}" class="btn btn-primary w-100 mb-2" style="background: linear-gradient(135deg, #007bff, #6f42c1); border: none; color: white; font-weight: 500;">
                            <i class="fas fa-comments"></i> Session Analytics
                        </a>
                        <small class="text-muted d-block text-center">Chatbot session insights & performance</small>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('comprehensive_analytics') }}" class="btn btn-warning w-100 mb-2" style="background: linear-gradient(135deg, #ffc107, #fd7e14); border: none; color: white; font-weight: 500;">
                            <i class="fas fa-chart-pie"></i> Complete Dashboard
                        </a>
                        <small class="text-muted d-block text-center">Comprehensive analytics overview</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Process Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-funnel-dollar"></i> 5-Stage Sales Process (Automated by Sarah)</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-door-open fa-2x text-primary mb-2"></i>
                            <h6>1. Opening</h6>
                            <small class="text-muted">Introduction & Permission</small>
                            <div class="mt-2">
                                <span class="badge bg-primary">10% Probability</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-handshake fa-2x text-success mb-2"></i>
                            <h6>2. Trust</h6>
                            <small class="text-muted">Build Rapport & Credibility</small>
                            <div class="mt-2">
                                <span class="badge bg-success">25% Probability</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-search fa-2x text-info mb-2"></i>
                            <h6>3. Discovery</h6>
                            <small class="text-muted">Understand Needs & Pain</small>
                            <div class="mt-2">
                                <span class="badge bg-info">50% Probability</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-presentation fa-2x text-warning mb-2"></i>
                            <h6>4. Demonstration</h6>
                            <small class="text-muted">Show Solution & Value</small>
                            <div class="mt-2">
                                <span class="badge bg-warning">75% Probability</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-trophy fa-2x text-danger mb-2"></i>
                            <h6>5. Close</h6>
                            <small class="text-muted">Finalize & Collect Details</small>
                            <div class="mt-2">
                                <span class="badge bg-danger">90% Probability</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-success text-white rounded">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h6>Customer!</h6>
                            <small>Success & Follow-up</small>
                            <div class="mt-2">
                                <span class="badge bg-light text-dark">100% Won</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activities</h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Subject</th>
                                    <th>Contact</th>
                                    <th>Date</th>
                                    <th>Source</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>
                                        {% if activity.activity_type == 'email' %}
                                            <i class="fas fa-envelope text-primary"></i>
                                        {% elif activity.activity_type == 'call' %}
                                            <i class="fas fa-phone text-success"></i>
                                        {% elif activity.activity_type == 'meeting' %}
                                            <i class="fas fa-calendar text-info"></i>
                                        {% elif activity.activity_type == 'chatbot' %}
                                            <i class="fas fa-robot text-success"></i>
                                        {% else %}
                                            <i class="fas fa-sticky-note text-warning"></i>
                                        {% endif %}
                                        {{ activity.activity_type.title() }}
                                    </td>
                                    <td>{{ activity.subject or 'No subject' }}</td>
                                    <td>
                                        {% if activity.contact %}
                                            {{ activity.contact.full_name }}
                                        {% else %}
                                            <span class="text-muted">No contact</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ activity.created_at.strftime('%m/%d/%Y %H:%M') }}</td>
                                    <td>
                                        {% if activity.ai_generated %}
                                            <span class="badge bg-success">AI Generated</span>
                                        {% else %}
                                            <span class="badge bg-primary">Manual</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activities found.</p>
                        <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="btn btn-success">
                            <i class="fas fa-robot"></i> Start Your First Sales Chat
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> System Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-rocket text-primary"></i> 24Seven Assistants</h6>
                    <p class="small text-muted">Complete integrated sales system</p>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-check-circle text-success"></i> Active Components</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success"></i> Sales Department Dashboard</li>
                        <li><i class="fas fa-check text-success"></i> AI Sales Chatbot (Sarah)</li>
                        <li><i class="fas fa-check text-success"></i> SMTP Email System</li>
                        <li><i class="fas fa-check text-success"></i> Sales Stage Tracking</li>
                        <li><i class="fas fa-check text-success"></i> Analytics Dashboard</li>
                        <li><i class="fas fa-check text-success"></i> Contact Management</li>
                        <li><i class="fas fa-check text-success"></i> Campaign Management</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-chart-line text-info"></i> Quick Stats</h6>
                    <ul class="list-unstyled small">
                        <li>Contacts: {{ total_contacts }}</li>
                        <li>Opportunities: {{ total_opportunities }}</li>
                        <li>Active Campaigns: {{ active_campaigns }}</li>
                        <li>Bot Interactions: {{ bot_interactions }}</li>
                        <li>Sales Stages: 5</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="{{ url_for('chat_page', session_id='new') }}" target="_blank" class="btn btn-success btn-sm mb-2 w-100">
                        <i class="fas fa-robot"></i> Open Sales Chatbot
                    </a>
                    <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-chart-bar"></i> View Full Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update the last updated time
    document.addEventListener('DOMContentLoaded', function() {
        const now = new Date();
        const timeString = now.toLocaleString();
        const lastUpdatedElement = document.getElementById('lastUpdated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = timeString;
        }
    });

    function refreshDashboard() {
        location.reload();
    }

    // Auto-refresh every 30 seconds to show real-time bot interactions
    setInterval(function() {
        // Only refresh if there are bot interactions happening
        if ({{ bot_interactions }} > 0) {
            location.reload();
        }
    }, 30000);
</script>
{% endblock %}
