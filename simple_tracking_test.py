#!/usr/bin/env python3
"""
Simple Tracking Test
Direct test of the tracking endpoint
"""

import requests
import json
import uuid

def test_tracking_endpoint():
    """Test the tracking endpoint directly"""
    print("🧪 Simple Tracking Test")
    print("=" * 30)
    
    session_id = str(uuid.uuid4())[:8]
    print(f"📝 Session ID: {session_id}")
    
    # Test minimal payload
    payload = {
        'session_id': session_id,
        'action': 'conversation_started',
        'stage': 'opening',
        'contact_name': 'Test User',
        'contact_email': '<EMAIL>'
    }
    
    print(f"📤 Sending payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            'http://localhost:5000/api/track-session',
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        print(f"📥 Response Body: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result}")
            return True
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_server_status():
    """Test if server is responding"""
    print("\n🔍 Testing Server Status")
    print("=" * 25)
    
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        print(f"✅ Server is running: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Simple Tracking Test Suite")
    print("=" * 40)
    
    # Test server first
    if test_server_status():
        # Test tracking
        test_tracking_endpoint()
    else:
        print("❌ Server not available")
