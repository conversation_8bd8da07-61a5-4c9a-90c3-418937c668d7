#!/usr/bin/env python3
"""
Test script for unified sales system
"""

print("Starting test...")

try:
    print("Importing Flask...")
    from flask import Flask
    print("✓ Flask imported")
    
    print("Importing SQLAlchemy...")
    from flask_sqlalchemy import SQLAlchemy
    print("✓ SQLAlchemy imported")
    
    print("Importing plotly...")
    import plotly.graph_objects as go
    import plotly
    print("✓ Plotly imported")
    
    print("Creating Flask app...")
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'test-key'
    print("✓ Flask app created")
    
    print("Creating database...")
    db = SQLAlchemy(app)
    print("✓ Database created")
    
    print("Creating test route...")
    @app.route('/')
    def index():
        return "Test successful!"
    
    print("✓ Route created")
    
    print("Starting server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
