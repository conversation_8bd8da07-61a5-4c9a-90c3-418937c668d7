"""
Email Tracker
============
Tracks email opens, clicks, and responses for analytics.
"""

from datetime import datetime
from typing import Dict, Optional
import logging

logger = logging.getLogger(__name__)

class EmailTracker:
    """Tracks email engagement for analytics"""

    def __init__(self, db_session):
        """Initialize email tracker"""
        self.db = db_session

    def track_email_open(self,
                        message_id: str,
                        user_agent: str = None,
                        ip_address: str = None) -> bool:
        """
        Track email open event

        Args:
            message_id: Email message ID
            user_agent: User agent string
            ip_address: IP address of opener

        Returns:
            True if tracked successfully
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailLog
            from models.activity import Activity

            email_log = self.db.query(EmailLog).filter(
                EmailLog.message_id == message_id
            ).first()

            if not email_log:
                logger.warning(f"Email log not found for message ID: {message_id}")
                return False

            # Mark as opened
            email_log.mark_as_opened(user_agent, ip_address)

            # Update campaign statistics
            campaign = email_log.campaign
            if campaign:
                campaign.emails_opened = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.opened_at.isnot(None)
                ).count()

            # Create activity record
            if email_log.contact:
                activity = Activity.create_email_activity(
                    contact_id=email_log.contact_id,
                    subject=f"Email Opened: {email_log.subject}",
                    description=f"Email opened by {email_log.recipient_email}",
                    direction='inbound',
                    email_from=email_log.recipient_email,
                    email_to=campaign.sender_email if campaign else None,
                    message_id=message_id,
                    created_by='email_tracker',
                    ai_generated=True
                )
                self.db.add(activity)

                # Update contact last activity
                email_log.contact.update_last_activity()

            self.db.commit()

            logger.info(f"Email open tracked for message ID: {message_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to track email open: {str(e)}")
            self.db.rollback()
            return False

    def track_email_click(self,
                         message_id: str,
                         clicked_url: str = None) -> bool:
        """
        Track email click event

        Args:
            message_id: Email message ID
            clicked_url: URL that was clicked

        Returns:
            True if tracked successfully
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailLog
            from models.activity import Activity

            email_log = self.db.query(EmailLog).filter(
                EmailLog.message_id == message_id
            ).first()

            if not email_log:
                logger.warning(f"Email log not found for message ID: {message_id}")
                return False

            # Mark as clicked
            email_log.mark_as_clicked()

            # Update campaign statistics
            campaign = email_log.campaign
            if campaign:
                campaign.emails_clicked = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.first_clicked_at.isnot(None)
                ).count()

            # Create activity record
            if email_log.contact:
                activity = Activity.create_email_activity(
                    contact_id=email_log.contact_id,
                    subject=f"Email Link Clicked: {email_log.subject}",
                    description=f"Link clicked in email: {clicked_url or 'Unknown URL'}",
                    direction='inbound',
                    email_from=email_log.recipient_email,
                    email_to=campaign.sender_email if campaign else None,
                    message_id=message_id,
                    created_by='email_tracker',
                    ai_generated=True
                )
                activity.activity_metadata = {'clicked_url': clicked_url}
                self.db.add(activity)

                # Update contact last activity
                email_log.contact.update_last_activity()

            self.db.commit()

            logger.info(f"Email click tracked for message ID: {message_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to track email click: {str(e)}")
            self.db.rollback()
            return False

    def track_email_reply(self,
                         original_message_id: str,
                         reply_content: str = None,
                         reply_message_id: str = None) -> bool:
        """
        Track email reply event

        Args:
            original_message_id: Original email message ID
            reply_content: Content of the reply
            reply_message_id: Message ID of the reply

        Returns:
            True if tracked successfully
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailLog
            from models.activity import Activity

            email_log = self.db.query(EmailLog).filter(
                EmailLog.message_id == original_message_id
            ).first()

            if not email_log:
                logger.warning(f"Email log not found for message ID: {original_message_id}")
                return False

            # Mark as replied
            email_log.mark_as_replied()

            # Update campaign statistics
            campaign = email_log.campaign
            if campaign:
                campaign.emails_replied = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.replied_at.isnot(None)
                ).count()

            # Create activity record
            if email_log.contact:
                activity = Activity.create_email_activity(
                    contact_id=email_log.contact_id,
                    subject=f"Email Reply: {email_log.subject}",
                    description=reply_content or "Reply received to email campaign",
                    direction='inbound',
                    email_from=email_log.recipient_email,
                    email_to=campaign.sender_email if campaign else None,
                    message_id=reply_message_id,
                    thread_id=email_log.thread_id,
                    created_by='email_tracker',
                    ai_generated=False
                )
                self.db.add(activity)

                # Update contact status to contacted if it was new
                if email_log.contact.status == 'new':
                    email_log.contact.status = 'contacted'

                # Update contact last activity and last contacted
                email_log.contact.update_last_activity()
                email_log.contact.last_contacted = datetime.utcnow()

            self.db.commit()

            logger.info(f"Email reply tracked for message ID: {original_message_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to track email reply: {str(e)}")
            self.db.rollback()
            return False

    def track_email_bounce(self,
                          message_id: str,
                          bounce_reason: str = None) -> bool:
        """
        Track email bounce event

        Args:
            message_id: Email message ID
            bounce_reason: Reason for bounce

        Returns:
            True if tracked successfully
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailLog

            email_log = self.db.query(EmailLog).filter(
                EmailLog.message_id == message_id
            ).first()

            if not email_log:
                logger.warning(f"Email log not found for message ID: {message_id}")
                return False

            # Mark as bounced
            email_log.mark_as_bounced(bounce_reason)

            # Update campaign statistics
            campaign = email_log.campaign
            if campaign:
                campaign.emails_bounced = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.bounced_at.isnot(None)
                ).count()

            # Update contact email status
            if email_log.contact and bounce_reason and 'permanent' in bounce_reason.lower():
                email_log.contact.do_not_email = True
                logger.info(f"Marked contact {email_log.contact.email} as do not email due to permanent bounce")

            self.db.commit()

            logger.info(f"Email bounce tracked for message ID: {message_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to track email bounce: {str(e)}")
            self.db.rollback()
            return False

    def track_unsubscribe(self,
                         message_id: str = None,
                         email_address: str = None) -> bool:
        """
        Track unsubscribe event

        Args:
            message_id: Email message ID (if available)
            email_address: Email address unsubscribing

        Returns:
            True if tracked successfully
        """
        try:
            # Import models locally to avoid circular imports
            from models.email_campaign import EmailLog
            from models.activity import Activity

            email_log = None

            if message_id:
                email_log = self.db.query(EmailLog).filter(
                    EmailLog.message_id == message_id
                ).first()
            elif email_address:
                email_log = self.db.query(EmailLog).filter(
                    EmailLog.recipient_email == email_address
                ).order_by(EmailLog.created_at.desc()).first()

            if not email_log:
                logger.warning(f"Email log not found for unsubscribe tracking")
                return False

            # Mark as unsubscribed
            email_log.unsubscribed_at = datetime.utcnow()
            email_log.updated_at = datetime.utcnow()

            # Update campaign statistics
            campaign = email_log.campaign
            if campaign:
                campaign.emails_unsubscribed = self.db.query(EmailLog).filter(
                    EmailLog.campaign_id == campaign.id,
                    EmailLog.unsubscribed_at.isnot(None)
                ).count()

            # Update contact to do not email
            if email_log.contact:
                email_log.contact.do_not_email = True
                email_log.contact.updated_at = datetime.utcnow()

                # Create activity record
                activity = Activity.create_note_activity(
                    contact_id=email_log.contact_id,
                    subject="Unsubscribed from emails",
                    description=f"Contact unsubscribed from email campaigns",
                    created_by='email_tracker',
                    ai_generated=True
                )
                self.db.add(activity)

            self.db.commit()

            logger.info(f"Unsubscribe tracked for email: {email_log.recipient_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to track unsubscribe: {str(e)}")
            self.db.rollback()
            return False

    def generate_tracking_pixel_url(self, message_id: str, base_url: str) -> str:
        """
        Generate tracking pixel URL for email opens

        Args:
            message_id: Email message ID
            base_url: Base URL of the application

        Returns:
            Tracking pixel URL
        """
        return f"{base_url}/track/open/{message_id}.png"

    def generate_click_tracking_url(self, message_id: str, original_url: str, base_url: str) -> str:
        """
        Generate click tracking URL

        Args:
            message_id: Email message ID
            original_url: Original URL to redirect to
            base_url: Base URL of the application

        Returns:
            Click tracking URL
        """
        import urllib.parse
        encoded_url = urllib.parse.quote(original_url, safe='')
        return f"{base_url}/track/click/{message_id}?url={encoded_url}"
