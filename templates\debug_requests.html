{% extends "base.html" %}

{% block title %}Request Logs - Debug Dashboard{% endblock %}

{% block extra_css %}
<style>
    .request-row {
        border-left: 3px solid #007bff;
    }
    .slow-request {
        border-left: 3px solid #ffc107;
        background-color: #fff3cd;
    }
    .error-request {
        border-left: 3px solid #dc3545;
        background-color: #f8d7da;
    }
    .method-badge {
        min-width: 60px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-globe text-primary"></i> Request Logs</h1>
    <div>
        <a href="{{ url_for('debug.debug_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Debug Dashboard
        </a>
        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs('requests')">
            <i class="fas fa-trash"></i> Clear Request Logs
        </button>
    </div>
</div>

<!-- Summary Stats -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total }}</h4>
                        <small class="text-muted">Total Requests</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">{{ logs|selectattr('status_code', 'lt', 400)|list|length }}</h4>
                        <small class="text-muted">Successful</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-danger">{{ logs|selectattr('status_code', 'ge', 400)|list|length }}</h4>
                        <small class="text-muted">Errors</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ logs|selectattr('duration', 'gt', 2.0)|list|length }}</h4>
                        <small class="text-muted">Slow Requests</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Request Logs Table -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> Request Details</h5>
    </div>
    <div class="card-body">
        {% if logs %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Method</th>
                        <th>URL</th>
                        <th>Duration</th>
                        <th>Status</th>
                        <th>IP Address</th>
                        <th>User Agent</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr class="{% if log.duration > 2.0 %}slow-request{% elif log.status_code >= 400 %}error-request{% else %}request-row{% endif %}">
                        <td>
                            <small>{{ log.timestamp }}</small>
                        </td>
                        <td>
                            <span class="badge method-badge bg-{% if log.method == 'GET' %}primary{% elif log.method == 'POST' %}success{% elif log.method == 'PUT' %}warning{% elif log.method == 'DELETE' %}danger{% else %}secondary{% endif %}">
                                {{ log.method }}
                            </span>
                        </td>
                        <td>
                            <code>{{ log.url[:80] }}{% if log.url|length > 80 %}...{% endif %}</code>
                        </td>
                        <td>
                            <span class="{% if log.duration > 2.0 %}text-warning fw-bold{% elif log.duration > 1.0 %}text-warning{% endif %}">
                                {{ "%.3f"|format(log.duration) }}s
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{% if log.status_code < 300 %}success{% elif log.status_code < 400 %}warning{% else %}danger{% endif %}">
                                {{ log.status_code }}
                            </span>
                        </td>
                        <td>
                            <small>{{ log.ip_address or 'N/A' }}</small>
                        </td>
                        <td>
                            <small title="{{ log.user_agent }}">
                                {{ log.user_agent[:50] }}{% if log.user_agent|length > 50 %}...{% endif %}
                            </small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if has_prev or has_next %}
        <nav aria-label="Request logs pagination">
            <ul class="pagination justify-content-center">
                {% if has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('debug.debug_requests', page=prev_num) }}">Previous</a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">Page {{ page }}</span>
                </li>
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('debug.debug_requests', page=next_num) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center text-muted py-4">
            <i class="fas fa-inbox fa-3x mb-3"></i>
            <h5>No Request Logs</h5>
            <p>No requests have been logged yet.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearLogs(type) {
    if (confirm('Are you sure you want to clear ' + type + ' logs?')) {
        fetch('/debug/api/clear-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({type: type})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error clearing logs: ' + error);
        });
    }
}
</script>
{% endblock %}
