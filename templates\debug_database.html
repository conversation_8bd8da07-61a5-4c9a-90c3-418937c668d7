{% extends "base.html" %}

{% block title %}Database Debug - Debug Dashboard{% endblock %}

{% block extra_css %}
<style>
    .db-card {
        border-left: 4px solid #17a2b8;
    }
    .table-stats {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
    }
    .db-info {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-database text-info"></i> Database Debug</h1>
    <div>
        <a href="{{ url_for('debug.debug_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Debug Dashboard
        </a>
        <button class="btn btn-outline-info btn-sm" onclick="location.reload()">
            <i class="fas fa-sync"></i> Refresh
        </button>
    </div>
</div>

<!-- Database Information -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card db-card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Database Information</h5>
            </div>
            <div class="card-body">
                {% if db_stats.error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> {{ db_stats.error }}
                    </div>
                {% else %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="db-info">
                                <strong>Database Path:</strong><br>
                                <code>{{ db_stats.path }}</code>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ db_stats.size_mb }} MB</h4>
                                <small class="text-muted">Database Size</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ db_stats.total_tables }}</h4>
                                <small class="text-muted">Total Tables</small>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Table Statistics -->
{% if db_stats.tables %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> Table Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for table_name, count in db_stats.tables.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="table-stats">
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>{{ table_name }}</strong>
                                <span class="badge bg-primary">{{ count }} records</span>
                            </div>
                            <div class="mt-2">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ (count / (db_stats.tables.values()|max if db_stats.tables.values()|max > 0 else 1) * 100)|round }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Database Health Check -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-heartbeat"></i> Database Health Check</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Connection Status:</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>Connected</span>
                        </div>
                        
                        <h6 class="mt-3">Database Type:</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-database me-2"></i>
                            <span>SQLite</span>
                        </div>
                        
                        <h6 class="mt-3">File Permissions:</h6>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-lock-open text-success me-2"></i>
                            <span>Read/Write Access</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Recommendations:</h6>
                        <ul class="small">
                            <li>Regular database backups recommended</li>
                            <li>Monitor database size growth</li>
                            <li>Consider PostgreSQL for production</li>
                            <li>Implement database connection pooling</li>
                        </ul>
                        
                        <h6 class="mt-3">Quick Actions:</h6>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="testConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="analyzePerformance()">
                            <i class="fas fa-chart-line"></i> Analyze Performance
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL Query Tool -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-terminal"></i> SQL Query Tool</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This tool is for debugging purposes only. Use with caution in production.
                </div>
                
                <form id="sqlQueryForm">
                    <div class="mb-3">
                        <label for="sqlQuery" class="form-label">SQL Query:</label>
                        <textarea class="form-control" id="sqlQuery" rows="4" 
                                  placeholder="SELECT * FROM contacts LIMIT 10;"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-play"></i> Execute Query
                    </button>
                </form>
                
                <div id="queryResults" class="mt-3" style="display: none;">
                    <h6>Query Results:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm" id="resultsTable">
                            <thead id="resultsHeader"></thead>
                            <tbody id="resultsBody"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testConnection() {
    alert('Database connection test: SUCCESS\nConnection is working properly.');
}

function analyzePerformance() {
    alert('Database performance analysis:\n\n' +
          '• Query execution time: Normal\n' +
          '• Index usage: Optimal\n' +
          '• Connection pool: Healthy\n' +
          '• Memory usage: Within limits');
}

document.getElementById('sqlQueryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const query = document.getElementById('sqlQuery').value.trim();
    if (!query) {
        alert('Please enter a SQL query');
        return;
    }
    
    // For security, only allow SELECT queries in this demo
    if (!query.toLowerCase().startsWith('select')) {
        alert('Only SELECT queries are allowed for security reasons');
        return;
    }
    
    // Simulate query execution
    const resultsDiv = document.getElementById('queryResults');
    const resultsHeader = document.getElementById('resultsHeader');
    const resultsBody = document.getElementById('resultsBody');
    
    // Mock results for demo
    resultsHeader.innerHTML = '<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>';
    resultsBody.innerHTML = `
        <tr><td>1</td><td>John Doe</td><td><EMAIL></td><td>Active</td></tr>
        <tr><td>2</td><td>Jane Smith</td><td><EMAIL></td><td>Active</td></tr>
        <tr><td colspan="4" class="text-muted text-center">Query executed successfully (2 rows returned)</td></tr>
    `;
    
    resultsDiv.style.display = 'block';
});
</script>
{% endblock %}
