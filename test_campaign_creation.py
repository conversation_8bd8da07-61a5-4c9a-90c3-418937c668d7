#!/usr/bin/env python3
"""
Test Campaign Creation
======================
Test script to verify that campaign creation is working properly.
"""

import requests
import json
from datetime import datetime

# Base URL for the application
BASE_URL = "http://127.0.0.1:5000"

def test_campaign_creation():
    """Test creating a new email campaign"""
    print("🧪 Testing Campaign Creation...")

    # Test data for campaign creation
    campaign_data = {
        'name': f'Test Campaign {datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate',
        'batch_size': '50',
        'batch_delay_minutes': '5'
    }

    try:
        # Test GET request to campaign creation form
        print("📋 Testing campaign creation form...")
        get_response = requests.get(f"{BASE_URL}/campaigns/create")

        if get_response.status_code == 200:
            print("✅ Campaign creation form loads successfully")
        else:
            print(f"❌ Failed to load campaign creation form: {get_response.status_code}")
            return False

        # Test POST request to create campaign
        print("📤 Testing campaign creation submission...")
        post_response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)

        if post_response.status_code in [200, 302]:  # 302 is redirect after successful creation
            print("✅ Campaign created successfully!")

            # Check if we were redirected to campaigns list
            if post_response.status_code == 302:
                redirect_url = post_response.headers.get('Location', '')
                if 'campaigns' in redirect_url:
                    print("✅ Redirected to campaigns list as expected")
                else:
                    print(f"⚠️ Unexpected redirect to: {redirect_url}")

            return True
        else:
            print(f"❌ Failed to create campaign: {post_response.status_code}")
            print(f"Response content: {post_response.text[:500]}")
            return False

    except Exception as e:
        print(f"❌ Error during campaign creation test: {str(e)}")
        return False

def test_campaigns_list():
    """Test that campaigns list page loads"""
    print("📋 Testing campaigns list page...")

    try:
        response = requests.get(f"{BASE_URL}/campaigns")

        if response.status_code == 200:
            print("✅ Campaigns list page loads successfully")
            return True
        else:
            print(f"❌ Failed to load campaigns list: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error loading campaigns list: {str(e)}")
        return False

def test_dashboard():
    """Test that main dashboard loads"""
    print("🏠 Testing main dashboard...")

    try:
        response = requests.get(f"{BASE_URL}/")

        if response.status_code == 200:
            print("✅ Main dashboard loads successfully")
            return True
        else:
            print(f"❌ Failed to load dashboard: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error loading dashboard: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Campaign Creation Tests")
    print("=" * 50)

    tests = [
        ("Dashboard", test_dashboard),
        ("Campaigns List", test_campaigns_list),
        ("Campaign Creation", test_campaign_creation),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        result = test_func()
        results.append((test_name, result))
        print()

    # Summary
    print("📊 Test Results Summary")
    print("=" * 30)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Campaign creation is working properly.")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
