#!/usr/bin/env python3
"""
Diagnose Database Issue
======================
Find out what database files exist and which one contains the contacts.
"""

import sqlite3
import os
import glob

def find_database_files():
    """Find all database files in the current directory"""
    print("🔍 Searching for database files...")
    
    # Look for common database file patterns
    patterns = ['*.db', '*.sqlite', '*.sqlite3']
    db_files = []
    
    for pattern in patterns:
        files = glob.glob(pattern)
        db_files.extend(files)
    
    # Remove duplicates and sort
    db_files = sorted(list(set(db_files)))
    
    print(f"📋 Found {len(db_files)} database files:")
    for db_file in db_files:
        size = os.path.getsize(db_file) if os.path.exists(db_file) else 0
        print(f"   • {db_file} ({size} bytes)")
    
    return db_files

def check_database_contents(db_file):
    """Check what tables and data exist in a database file"""
    print(f"\n🔍 Checking contents of: {db_file}")
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"   📋 Tables ({len(tables)}): {', '.join(tables)}")
        
        # Check contacts table specifically
        if 'contacts' in tables:
            cursor.execute("SELECT COUNT(*) FROM contacts")
            contact_count = cursor.fetchone()[0]
            print(f"   👥 Contacts: {contact_count} records")
            
            if contact_count > 0:
                cursor.execute("SELECT id, first_name, last_name, email FROM contacts LIMIT 3")
                sample_contacts = cursor.fetchall()
                print(f"   📝 Sample contacts:")
                for contact in sample_contacts:
                    print(f"      ID: {contact[0]}, Name: {contact[1]} {contact[2]}, Email: {contact[3]}")
        else:
            print(f"   ❌ No 'contacts' table found")
        
        # Check email_campaigns table
        if 'email_campaigns' in tables:
            cursor.execute("SELECT COUNT(*) FROM email_campaigns")
            campaign_count = cursor.fetchone()[0]
            print(f"   📧 Email campaigns: {campaign_count} records")
        
        conn.close()
        return 'contacts' in tables and contact_count > 0 if 'contacts' in tables else False
        
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
        return False

def check_flask_app_database():
    """Check what database the Flask app is configured to use"""
    print("\n🔍 Checking Flask app database configuration...")
    
    try:
        # Try to import the Flask app and check its database URI
        import sys
        sys.path.append('.')
        
        from unified_sales_system import app
        
        with app.app_context():
            db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', 'Not set')
            print(f"   📋 Flask SQLALCHEMY_DATABASE_URI: {db_uri}")
            
            # Extract the database file path from the URI
            if db_uri.startswith('sqlite:///'):
                db_path = db_uri.replace('sqlite:///', '')
                print(f"   📁 Database file path: {db_path}")
                
                if os.path.exists(db_path):
                    print(f"   ✅ Database file exists")
                    return db_path
                else:
                    print(f"   ❌ Database file does not exist")
                    return None
            else:
                print(f"   ⚠️ Not using SQLite database")
                return None
                
    except Exception as e:
        print(f"   ❌ Error checking Flask app: {e}")
        return None

def test_direct_database_connection():
    """Test connecting directly to the database used in bulk delete"""
    print("\n🧪 Testing direct database connection...")
    
    # This is the path used in the bulk delete function
    db_path = 'unified_sales.db'
    
    print(f"   📁 Testing connection to: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"   ❌ File does not exist: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test the exact query used in bulk delete
        cursor.execute("SELECT id FROM contacts LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            print(f"   ✅ Successfully connected and found contacts")
            print(f"   📝 Sample contact ID: {result[0]}")
        else:
            print(f"   ⚠️ Connected but no contacts found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Error connecting: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🚀 DATABASE DIAGNOSTIC TOOL")
    print("=" * 60)
    print("This tool will help identify the database connection issue.")
    print("=" * 60)
    
    # Step 1: Find all database files
    db_files = find_database_files()
    
    # Step 2: Check contents of each database file
    active_databases = []
    for db_file in db_files:
        has_contacts = check_database_contents(db_file)
        if has_contacts:
            active_databases.append(db_file)
    
    # Step 3: Check Flask app configuration
    flask_db_path = check_flask_app_database()
    
    # Step 4: Test direct connection
    direct_connection_works = test_direct_database_connection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    print(f"Database files found: {len(db_files)}")
    print(f"Databases with contacts: {len(active_databases)}")
    if active_databases:
        print(f"Active databases: {', '.join(active_databases)}")
    
    if flask_db_path:
        print(f"Flask app database: {flask_db_path}")
    
    print(f"Direct connection test: {'✅ PASSED' if direct_connection_works else '❌ FAILED'}")
    
    # Recommendations
    print("\n🔧 RECOMMENDATIONS:")
    
    if not direct_connection_works:
        if not os.path.exists('unified_sales.db'):
            print("• The bulk delete function is looking for 'unified_sales.db' but it doesn't exist")
            if active_databases:
                print(f"• Consider updating the bulk delete function to use: {active_databases[0]}")
            else:
                print("• No database with contacts found - you may need to create/import contacts")
        else:
            print("• The 'unified_sales.db' file exists but has issues")
    
    if flask_db_path and flask_db_path != 'unified_sales.db':
        print(f"• Flask app uses '{flask_db_path}' but bulk delete uses 'unified_sales.db'")
        print("• These should be the same database file")
    
    if len(active_databases) > 1:
        print("• Multiple databases with contacts found - this could cause confusion")
        print("• Consider consolidating to a single database")
    
    return len(active_databases) > 0 and direct_connection_works

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
