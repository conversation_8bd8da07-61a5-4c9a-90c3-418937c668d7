"""
Email Analytics
==============
Analytics for email campaign performance and tracking.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy import func, and_
from models.email_campaign import EmailCampaign, EmailLog

class EmailAnalytics:
    """Analytics for email campaign performance"""
    
    def __init__(self, db_session):
        """Initialize with database session"""
        self.db = db_session
    
    def get_campaign_performance_summary(self, days: int = 30) -> Dict:
        """
        Get overall email campaign performance summary
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with email performance metrics
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get campaigns in the period
        campaigns = self.db.query(EmailCampaign).filter(
            EmailCampaign.created_at >= start_date
        ).all()
        
        # Get email logs in the period
        email_logs = self.db.query(EmailLog).filter(
            EmailLog.created_at >= start_date
        ).all()
        
        # Calculate aggregate metrics
        total_campaigns = len(campaigns)
        total_sent = sum(campaign.emails_sent for campaign in campaigns)
        total_delivered = sum(campaign.emails_delivered for campaign in campaigns)
        total_opened = sum(campaign.emails_opened for campaign in campaigns)
        total_clicked = sum(campaign.emails_clicked for campaign in campaigns)
        total_replied = sum(campaign.emails_replied for campaign in campaigns)
        total_bounced = sum(campaign.emails_bounced for campaign in campaigns)
        total_unsubscribed = sum(campaign.emails_unsubscribed for campaign in campaigns)
        
        # Calculate rates
        avg_open_rate = 0.0
        avg_click_rate = 0.0
        avg_reply_rate = 0.0
        avg_bounce_rate = 0.0
        
        if campaigns:
            open_rates = [c.open_rate for c in campaigns if c.emails_delivered > 0]
            click_rates = [c.click_rate for c in campaigns if c.emails_delivered > 0]
            reply_rates = [c.reply_rate for c in campaigns if c.emails_delivered > 0]
            bounce_rates = [c.bounce_rate for c in campaigns if c.emails_sent > 0]
            
            avg_open_rate = sum(open_rates) / len(open_rates) if open_rates else 0.0
            avg_click_rate = sum(click_rates) / len(click_rates) if click_rates else 0.0
            avg_reply_rate = sum(reply_rates) / len(reply_rates) if reply_rates else 0.0
            avg_bounce_rate = sum(bounce_rates) / len(bounce_rates) if bounce_rates else 0.0
        
        return {
            'total_campaigns': total_campaigns,
            'total_sent': total_sent,
            'total_delivered': total_delivered,
            'total_opened': total_opened,
            'total_clicked': total_clicked,
            'total_replied': total_replied,
            'total_bounced': total_bounced,
            'total_unsubscribed': total_unsubscribed,
            'avg_open_rate': round(avg_open_rate, 2),
            'avg_click_rate': round(avg_click_rate, 2),
            'avg_reply_rate': round(avg_reply_rate, 2),
            'avg_bounce_rate': round(avg_bounce_rate, 2),
            'campaigns': [campaign.to_dict() for campaign in campaigns]
        }
    
    def get_campaign_details(self, campaign_id: int) -> Dict:
        """
        Get detailed analytics for a specific campaign
        
        Args:
            campaign_id: ID of the campaign to analyze
            
        Returns:
            Dictionary with detailed campaign metrics
        """
        campaign = self.db.query(EmailCampaign).get(campaign_id)
        if not campaign:
            return {}
        
        # Get email logs for this campaign
        email_logs = self.db.query(EmailLog).filter(
            EmailLog.campaign_id == campaign_id
        ).all()
        
        # Group by status
        status_counts = {}
        for log in email_logs:
            status = log.status
            if status not in status_counts:
                status_counts[status] = 0
            status_counts[status] += 1
        
        # Get engagement timeline
        engagement_timeline = []
        for log in email_logs:
            if log.opened_at:
                engagement_timeline.append({
                    'type': 'opened',
                    'timestamp': log.opened_at.isoformat(),
                    'contact_email': log.recipient_email
                })
            if log.first_clicked_at:
                engagement_timeline.append({
                    'type': 'clicked',
                    'timestamp': log.first_clicked_at.isoformat(),
                    'contact_email': log.recipient_email
                })
            if log.replied_at:
                engagement_timeline.append({
                    'type': 'replied',
                    'timestamp': log.replied_at.isoformat(),
                    'contact_email': log.recipient_email
                })
        
        # Sort timeline by timestamp
        engagement_timeline.sort(key=lambda x: x['timestamp'])
        
        return {
            'campaign': campaign.to_dict(),
            'status_counts': status_counts,
            'engagement_timeline': engagement_timeline,
            'total_logs': len(email_logs)
        }
    
    def get_email_performance_trends(self, days: int = 30) -> Dict:
        """
        Get email performance trends over time
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with daily email performance data
        """
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)
        
        daily_data = {}
        current_date = start_date
        
        while current_date <= end_date:
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            # Count emails sent on this day
            sent_count = self.db.query(EmailLog).filter(
                and_(
                    EmailLog.sent_at >= day_start,
                    EmailLog.sent_at <= day_end
                )
            ).count()
            
            # Count emails opened on this day
            opened_count = self.db.query(EmailLog).filter(
                and_(
                    EmailLog.opened_at >= day_start,
                    EmailLog.opened_at <= day_end
                )
            ).count()
            
            # Count emails clicked on this day
            clicked_count = self.db.query(EmailLog).filter(
                and_(
                    EmailLog.first_clicked_at >= day_start,
                    EmailLog.first_clicked_at <= day_end
                )
            ).count()
            
            # Count emails replied on this day
            replied_count = self.db.query(EmailLog).filter(
                and_(
                    EmailLog.replied_at >= day_start,
                    EmailLog.replied_at <= day_end
                )
            ).count()
            
            daily_data[current_date.isoformat()] = {
                'sent': sent_count,
                'opened': opened_count,
                'clicked': clicked_count,
                'replied': replied_count
            }
            
            current_date += timedelta(days=1)
        
        return daily_data
    
    def get_template_performance(self) -> List[Dict]:
        """
        Get performance metrics by email template
        
        Returns:
            List of template performance data
        """
        # Group campaigns by template
        template_performance = self.db.query(
            EmailCampaign.template_name,
            func.count(EmailCampaign.id).label('campaign_count'),
            func.sum(EmailCampaign.emails_sent).label('total_sent'),
            func.sum(EmailCampaign.emails_delivered).label('total_delivered'),
            func.sum(EmailCampaign.emails_opened).label('total_opened'),
            func.sum(EmailCampaign.emails_clicked).label('total_clicked'),
            func.sum(EmailCampaign.emails_replied).label('total_replied'),
            func.avg(EmailCampaign.emails_opened * 100.0 / EmailCampaign.emails_delivered).label('avg_open_rate'),
            func.avg(EmailCampaign.emails_clicked * 100.0 / EmailCampaign.emails_delivered).label('avg_click_rate'),
            func.avg(EmailCampaign.emails_replied * 100.0 / EmailCampaign.emails_delivered).label('avg_reply_rate')
        ).filter(
            EmailCampaign.emails_delivered > 0
        ).group_by(
            EmailCampaign.template_name
        ).all()
        
        results = []
        for row in template_performance:
            results.append({
                'template_name': row.template_name,
                'campaign_count': row.campaign_count,
                'total_sent': row.total_sent or 0,
                'total_delivered': row.total_delivered or 0,
                'total_opened': row.total_opened or 0,
                'total_clicked': row.total_clicked or 0,
                'total_replied': row.total_replied or 0,
                'avg_open_rate': round(row.avg_open_rate or 0, 2),
                'avg_click_rate': round(row.avg_click_rate or 0, 2),
                'avg_reply_rate': round(row.avg_reply_rate or 0, 2)
            })
        
        return results
    
    def get_best_performing_campaigns(self, limit: int = 10) -> List[Dict]:
        """
        Get best performing campaigns by reply rate
        
        Args:
            limit: Number of campaigns to return
            
        Returns:
            List of top performing campaigns
        """
        campaigns = self.db.query(EmailCampaign).filter(
            EmailCampaign.emails_delivered > 0
        ).order_by(
            (EmailCampaign.emails_replied * 100.0 / EmailCampaign.emails_delivered).desc()
        ).limit(limit).all()
        
        return [campaign.to_dict() for campaign in campaigns]
