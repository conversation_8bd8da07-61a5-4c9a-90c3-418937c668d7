{% extends "base.html" %}

{% block title %}Contact Groups - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-layer-group"></i> Contact Groups</h2>
                <div>
                    <a href="{{ url_for('create_group') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Group
                    </a>
                    <a href="{{ url_for('contacts_list') }}" class="btn btn-secondary">
                        <i class="fas fa-users"></i> View Contacts
                    </a>
                </div>
            </div>

            {% if groups_with_counts %}
            <div class="row">
                {% for item in groups_with_counts %}
                {% set group = item.group %}
                {% set contact_count = item.contact_count %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center" style="background-color: {{ group.color }}; color: white;">
                            <h5 class="mb-0">{{ group.name }}</h5>
                            <span class="badge bg-light text-dark">{{ contact_count }}</span>
                        </div>
                        <div class="card-body">
                            {% if group.description %}
                            <p class="card-text">{{ group.description }}</p>
                            {% else %}
                            <p class="card-text text-muted">No description provided.</p>
                            {% endif %}

                            <div class="text-muted small">
                                <i class="fas fa-calendar"></i> Created: {{ group.created_at.strftime('%Y-%m-%d') }}
                                {% if group.created_by %}
                                <br><i class="fas fa-user"></i> By: {{ group.created_by }}
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{{ url_for('edit_group', group_id=group.id) }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <button class="btn btn-outline-danger btn-sm" onclick="confirmDeleteGroup({{ group.id }}, '{{ group.name }}')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination would go here if needed -->

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Contact Groups</h4>
                <p class="text-muted">Create your first contact group to organize your contacts for targeted campaigns.</p>
                <a href="{{ url_for('create_group') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Your First Group
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the group "<span id="groupNameToDelete"></span>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> This will remove the group but will not delete the contacts. Contacts will remain in the system.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteGroupForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Group
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteGroup(groupId, groupName) {
    document.getElementById('groupNameToDelete').textContent = groupName;
    document.getElementById('deleteGroupForm').action = `/groups/${groupId}/delete`;

    const modal = new bootstrap.Modal(document.getElementById('deleteGroupModal'));
    modal.show();
}
</script>
{% endblock %}
