#!/usr/bin/env python3
"""
Verify Database Setup
====================
Check that the database and tables are properly set up for the Flask app.
"""

import os
import sys

def verify_database_setup():
    """Verify that the database and tables are properly set up"""
    print("🔍 VERIFYING DATABASE SETUP")
    print("=" * 50)
    
    try:
        # Import the Flask app
        from unified_sales_system import app, db, Contact, EmailCampaign
        
        print("✅ Successfully imported Flask app and models")
        
        # Check database URI
        with app.app_context():
            db_uri = app.config['SQLALCHEMY_DATABASE_URI']
            print(f"📋 Database URI: {db_uri}")
            
            # Extract database file path
            if db_uri.startswith('sqlite:///'):
                db_file = db_uri.replace('sqlite:///', '')
                print(f"📁 Database file: {db_file}")
                
                # Check if file exists
                if os.path.exists(db_file):
                    size = os.path.getsize(db_file)
                    print(f"✅ Database file exists ({size} bytes)")
                else:
                    print(f"❌ Database file does not exist: {db_file}")
                    print("   Creating database and tables...")
                    
                    # Create all tables
                    db.create_all()
                    print("✅ Database and tables created")
                
                # Test database connection
                try:
                    # Try to query contacts
                    contact_count = Contact.query.count()
                    print(f"👥 Contacts in database: {contact_count}")
                    
                    # Try to query campaigns
                    campaign_count = EmailCampaign.query.count()
                    print(f"📧 Email campaigns in database: {campaign_count}")
                    
                    # Test raw SQL connection (same as bulk delete uses)
                    conn = db.engine.raw_connection()
                    cursor = conn.cursor()
                    
                    # Check if contacts table exists
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='contacts'")
                    contacts_table_exists = cursor.fetchone() is not None
                    print(f"🔍 Contacts table exists (raw SQL): {contacts_table_exists}")
                    
                    if contacts_table_exists:
                        # Test selecting from contacts table
                        cursor.execute("SELECT COUNT(*) FROM contacts")
                        raw_contact_count = cursor.fetchone()[0]
                        print(f"👥 Contacts count (raw SQL): {raw_contact_count}")
                    
                    conn.close()
                    
                    if contacts_table_exists:
                        print("\n✅ DATABASE SETUP IS CORRECT!")
                        print("   The bulk delete should now work properly.")
                        return True
                    else:
                        print("\n❌ CONTACTS TABLE MISSING!")
                        print("   This explains the 'no such table: contacts' error.")
                        return False
                    
                except Exception as e:
                    print(f"❌ Error testing database connection: {e}")
                    return False
            else:
                print(f"⚠️ Not using SQLite database: {db_uri}")
                return False
                
    except Exception as e:
        print(f"❌ Error importing Flask app: {e}")
        return False

def create_sample_contact():
    """Create a sample contact for testing"""
    print("\n🧪 Creating sample contact for testing...")
    
    try:
        from unified_sales_system import app, db, Contact
        
        with app.app_context():
            # Check if we already have contacts
            existing_count = Contact.query.count()
            
            if existing_count > 0:
                print(f"ℹ️ Already have {existing_count} contacts, skipping sample creation")
                return True
            
            # Create a sample contact
            sample_contact = Contact(
                first_name="Test",
                last_name="Contact",
                email="<EMAIL>",
                phone="256 **********",
                company="Test Company",
                source="manual_entry"
            )
            
            db.session.add(sample_contact)
            db.session.commit()
            
            print(f"✅ Created sample contact: {sample_contact.full_name} ({sample_contact.email})")
            print(f"   Contact ID: {sample_contact.id}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error creating sample contact: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 DATABASE SETUP VERIFICATION")
    print("=" * 60)
    print("This script verifies that the database is properly set up")
    print("and will fix the 'no such table: contacts' error.")
    print("=" * 60)
    
    # Step 1: Verify database setup
    setup_ok = verify_database_setup()
    
    # Step 2: Create sample contact if needed
    if setup_ok:
        sample_ok = create_sample_contact()
    else:
        sample_ok = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if setup_ok and sample_ok:
        print("✅ DATABASE SETUP IS COMPLETE!")
        print("\nNext steps:")
        print("1. Restart your Flask application")
        print("2. Try deleting contacts again")
        print("3. The 'no such table: contacts' error should be resolved")
        print("\n🎉 Contact deletion should now work properly!")
    else:
        print("❌ DATABASE SETUP HAS ISSUES!")
        print("\nPlease check the error messages above and:")
        print("1. Make sure the Flask app can start properly")
        print("2. Check file permissions in the directory")
        print("3. Try running this script again")
    
    return setup_ok and sample_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
