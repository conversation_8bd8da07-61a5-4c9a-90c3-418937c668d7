"""salesbot.api
Helper to call the SambaNova API using simple HTTP requests. The returned
payload is assumed to be OpenAI-compatible (chat.completions) as documented by
SambaNova.
"""
from __future__ import annotations

import os
from typing import List, Dict, Any, Optional

import requests

# Model/Provider selection via environment variables.
# If GEMINI_* variables are present we prefer those, otherwise fallback to SAMBA_* variables.
_GEMINI_KEY = os.getenv("GEMINI_API_KEY")
_GEMINI_BASE = os.getenv("GEMINI_BASE_URL", "https://generativelanguage.googleapis.com/v1beta/openai")
_GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-lite-preview-06-17")

if _GEMINI_KEY:
    # Use Google Gemini
    API_KEY: str = _GEMINI_KEY
    BASE_URL: str = _GEMINI_BASE
    MODEL: str = _GEMINI_MODEL
else:
    # Default to SambaNova
    API_KEY: str = os.getenv("SAMBA_API_KEY", "76273dc7-7c75-417f-8cfe-ef88ad56db78")
    BASE_URL: str = os.getenv("SAMBA_BASE_URL", "https://api.sambanova.ai/v1")
    MODEL: str = os.getenv("SAMBA_MODEL", "Meta-Llama-3.3-70B-Instruct")

def chat_completion(
    messages: List[Dict[str, str]],
    temperature: float = 0.7,
    model: Optional[str] = None,
    api_key: Optional[str] = None,
    base_url: Optional[str] = None,
) -> str:
    """Return assistant message for a list of messages using SambaNova."""
    model = model or MODEL
    api_key = api_key or API_KEY
    base_url = base_url or BASE_URL

    try:
        resp = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            },
            json={
                "model": model,
                "messages": messages,
                "temperature": temperature,
            },
            timeout=60,
        )
        resp.raise_for_status()
        data: Dict[str, Any] = resp.json()
        return data["choices"][0]["message"]["content"]
    except Exception as exc:  # pragma: no cover – simple demo error handling
        return f"[API-ERROR] {exc}"
