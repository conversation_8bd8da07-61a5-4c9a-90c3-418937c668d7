#!/usr/bin/env python3
"""
Test the view_campaign route directly to identify the exact issue
"""

import requests
import sys

def test_view_campaign_route():
    """Test the view_campaign route step by step"""
    
    print("🔍 Testing View Campaign Route")
    print("=" * 50)
    
    # Step 1: Check if <PERSON><PERSON><PERSON> is running
    try:
        response = requests.get('http://localhost:5000/')
        print(f"✅ Flask is running (status: {response.status_code})")
    except Exception as e:
        print(f"❌ Flask is not running: {e}")
        return False
    
    # Step 2: Get campaigns list to see what campaigns exist
    try:
        response = requests.get('http://localhost:5000/campaigns')
        if response.status_code == 200:
            print(f"✅ Campaigns list accessible")
            
            # Extract campaign IDs from the response
            import re
            campaign_ids = re.findall(r'/campaigns/(\d+)', response.text)
            if campaign_ids:
                print(f"📋 Found campaign IDs: {campaign_ids}")
                
                # Test the first few campaigns
                for campaign_id in campaign_ids[:3]:
                    print(f"\n🎯 Testing campaign {campaign_id}...")
                    
                    # Test with allow_redirects=False to see the exact response
                    test_response = requests.get(f'http://localhost:5000/campaigns/{campaign_id}', allow_redirects=False)
                    
                    print(f"   Status: {test_response.status_code}")
                    
                    if test_response.status_code == 200:
                        print(f"   ✅ SUCCESS - Campaign {campaign_id} loads correctly")
                        
                        # Check if it contains expected content
                        if "Campaign Details" in test_response.text:
                            print(f"   ✅ Contains 'Campaign Details'")
                        else:
                            print(f"   ⚠️  Missing 'Campaign Details' - might be wrong page")
                            
                        return True
                        
                    elif test_response.status_code == 302:
                        location = test_response.headers.get('Location', 'Unknown')
                        print(f"   ❌ REDIRECT to: {location}")
                        
                        # Check for error messages in cookies
                        cookies = test_response.cookies
                        if cookies:
                            session_cookie = cookies.get('session')
                            if session_cookie:
                                print(f"   🍪 Session cookie: {session_cookie[:50]}...")
                                
                    elif test_response.status_code == 404:
                        print(f"   ❌ NOT FOUND - Campaign {campaign_id} doesn't exist")
                        
                    else:
                        print(f"   ❌ UNEXPECTED STATUS: {test_response.status_code}")
                        print(f"   Response preview: {test_response.text[:200]}")
                        
            else:
                print("❌ No campaign IDs found in campaigns list")
                print(f"Response preview: {response.text[:500]}")
                
        else:
            print(f"❌ Cannot access campaigns list: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing campaigns: {e}")
        return False
    
    # Step 3: Test with a known campaign ID that should exist
    print(f"\n🧪 Testing with specific campaign IDs...")
    for test_id in [1, 2, 3]:
        try:
            print(f"   Testing campaign {test_id}...")
            response = requests.get(f'http://localhost:5000/campaigns/{test_id}', allow_redirects=False)
            
            if response.status_code == 200:
                print(f"   ✅ Campaign {test_id} works!")
                return True
            elif response.status_code == 302:
                print(f"   ❌ Campaign {test_id} redirects")
            elif response.status_code == 404:
                print(f"   ⚠️  Campaign {test_id} not found")
            else:
                print(f"   ❌ Campaign {test_id} status: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error testing campaign {test_id}: {e}")
    
    return False

def test_database_directly():
    """Test database queries directly"""
    print(f"\n🗄️  Testing Database Directly")
    print("=" * 50)
    
    try:
        # Import Flask app and models
        from unified_sales_system import app, db, EmailCampaign
        
        with app.app_context():
            print("✅ App context created")
            
            # Test basic campaign query
            campaigns = EmailCampaign.query.all()
            print(f"📊 Found {len(campaigns)} campaigns in database")
            
            if campaigns:
                campaign = campaigns[0]
                print(f"📋 First campaign: ID={campaign.id}, Name='{campaign.name}'")
                
                # Test the exact query that view_campaign route uses
                test_campaign = EmailCampaign.query.get(campaign.id)
                if test_campaign:
                    print(f"✅ Campaign {campaign.id} can be retrieved by ID")
                    return True
                else:
                    print(f"❌ Campaign {campaign.id} cannot be retrieved by ID")
                    
            else:
                print("❌ No campaigns found in database")
                
                # Create a test campaign
                print("📝 Creating test campaign...")
                test_campaign = EmailCampaign(
                    name="Test Campaign",
                    subject="Test Subject",
                    template_name="introduction",
                    status="draft"
                )
                db.session.add(test_campaign)
                db.session.commit()
                
                print(f"✅ Test campaign created with ID: {test_campaign.id}")
                return True
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 View Campaign Route Test")
    print("=" * 50)
    
    # Test HTTP route
    http_success = test_view_campaign_route()
    
    # Test database
    db_success = test_database_directly()
    
    print(f"\n📊 Results:")
    print(f"   HTTP Route: {'✅ PASS' if http_success else '❌ FAIL'}")
    print(f"   Database: {'✅ PASS' if db_success else '❌ FAIL'}")
    
    if not http_success and db_success:
        print(f"\n💡 Conclusion: Database works but HTTP route fails")
        print(f"   This suggests an issue in the view_campaign route logic")
        print(f"   Possible causes:")
        print(f"   • Template rendering error")
        print(f"   • Missing template variables")
        print(f"   • Exception in route logic")
        print(f"   • Pagination issues")
    elif not db_success:
        print(f"\n💡 Conclusion: Database issue detected")
    else:
        print(f"\n💡 Conclusion: Everything should be working!")
