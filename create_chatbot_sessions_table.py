#!/usr/bin/env python3
"""
Create ChatbotSession Table
Adds the missing chatbot_sessions table to the existing database
"""

import sqlite3
import os

def create_chatbot_sessions_table():
    """Create the chatbot_sessions table with all required fields"""
    db_path = 'sales_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print(f"🔄 Adding chatbot_sessions table to: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create chatbot_sessions table with all fields
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chatbot_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(100) UNIQUE NOT NULL,
                contact_id INTEGER,
                email_campaign_id INTEGER,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ended_at DATETIME,
                current_stage VARCHAR(50) DEFAULT 'opening',
                current_task VARCHAR(200),
                stage_progression TEXT,
                total_messages INTEGER DEFAULT 0,
                user_messages INTEGER DEFAULT 0,
                bot_messages INTEGER DEFAULT 0,
                objections_handled INTEGER DEFAULT 0,
                engagement_level VARCHAR(20) DEFAULT 'medium',
                interaction_quality_score REAL DEFAULT 0.0,
                completed_successfully BOOLEAN DEFAULT 0,
                conversion_achieved BOOLEAN DEFAULT 0,
                final_stage_reached VARCHAR(50),
                abandonment_reason VARCHAR(200),
                opening_started_at DATETIME,
                opening_completed_at DATETIME,
                trust_started_at DATETIME,
                trust_completed_at DATETIME,
                discovery_started_at DATETIME,
                discovery_completed_at DATETIME,
                demonstration_started_at DATETIME,
                demonstration_completed_at DATETIME,
                close_started_at DATETIME,
                close_completed_at DATETIME
            )
        ''')
        
        print("✅ chatbot_sessions table created successfully")
        
        # Verify the table was created
        cursor.execute("PRAGMA table_info(chatbot_sessions)")
        columns = cursor.fetchall()
        
        print(f"📋 Table created with {len(columns)} columns:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Error creating table: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 ChatbotSession Table Creation Tool")
    print("=" * 50)
    
    success = create_chatbot_sessions_table()
    if success:
        print("\n✅ Table creation completed successfully!")
        print("You can now restart the Flask application.")
    else:
        print("\n❌ Table creation failed.")
