"""
Email System Package
===================
SMTP email system for 24Seven Assistants introduction campaigns.
"""

# Import only modules that don't have circular dependencies
from .smtp_service import SMTPService
from .enhanced_smtp_service import EnhancedSMTPService
from .email_templates import EmailTemplateManager
from .imap_service import IMAPService
from .config import get_email_config, validate_email_config, DEFAULT_CONFIG

# Import factory functions that handle lazy loading
def create_email_system(config=None, db_session=None):
    """Create complete email system with lazy imports"""
    from .factory import create_email_system as _create_email_system
    return _create_email_system(config, db_session)

def test_email_system(config=None, test_email=None):
    """Test email system with lazy imports"""
    from .factory import test_email_system as _test_email_system
    return _test_email_system(config, test_email)

def EmailSystemFactory(*args, **kwargs):
    """Create EmailSystemFactory with lazy imports"""
    from .factory import EmailSystemFactory as _EmailSystemFactory
    return _EmailSystemFactory(*args, **kwargs)

def CampaignManager(*args, **kwargs):
    """Create CampaignManager with lazy imports"""
    from .campaign_manager import CampaignManager as _CampaignManager
    return _CampaignManager(*args, **kwargs)

def EmailTracker(*args, **kwargs):
    """Create EmailTracker with lazy imports"""
    from .email_tracker import EmailTracker as _EmailTracker
    return _EmailTracker(*args, **kwargs)

__all__ = [
    'SMTPService',
    'EnhancedSMTPService',
    'EmailTemplateManager',
    'CampaignManager',
    'EmailTracker',
    'IMAPService',
    'get_email_config',
    'validate_email_config',
    'DEFAULT_CONFIG',
    'EmailSystemFactory',
    'create_email_system',
    'test_email_system'
]
