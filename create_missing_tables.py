#!/usr/bin/env python3
"""
Create Missing Database Tables
Creates all the tables needed for the unified sales system
"""

import sqlite3
import os

def create_missing_tables():
    """Create all missing tables for the unified sales system"""
    db_path = 'sales_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    print(f"🔄 Creating missing tables in: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check existing tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 Existing tables: {existing_tables}")
        
        tables_created = 0
        
        # Create contacts table
        if 'contacts' not in existing_tables:
            print("📋 Creating contacts table...")
            cursor.execute('''
                CREATE TABLE contacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
                    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    phone VARCHAR(20),
                    company VARCHAR(200),
                    job_title VARCHAR(150),
                    source VARCHAR(100),
                    status VARCHAR(50) DEFAULT 'new',
                    lead_score REAL DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    do_not_email BOOLEAN DEFAULT 0,
                    is_customer BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    email_campaign_id INTEGER,
                    chatbot_session_id VARCHAR(100),
                    first_email_sent DATETIME,
                    email_opened DATETIME,
                    chatbot_link_clicked DATETIME,
                    chatbot_conversation_started DATETIME,
                    conversion_completed DATETIME,
                    current_sales_stage VARCHAR(50)
                )
            ''')
            tables_created += 1
            print("✅ contacts table created")
        
        # Create email_campaigns table
        if 'email_campaigns' not in existing_tables:
            print("📧 Creating email_campaigns table...")
            cursor.execute('''
                CREATE TABLE email_campaigns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(255) NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    template_name VARCHAR(100) NOT NULL,
                    status VARCHAR(50) DEFAULT 'draft',
                    total_recipients INTEGER DEFAULT 0,
                    emails_sent INTEGER DEFAULT 0,
                    emails_delivered INTEGER DEFAULT 0,
                    emails_failed INTEGER DEFAULT 0,
                    emails_opened INTEGER DEFAULT 0,
                    chatbot_links_clicked INTEGER DEFAULT 0,
                    chatbot_conversations_started INTEGER DEFAULT 0,
                    sales_conversations_completed INTEGER DEFAULT 0,
                    conversions_achieved INTEGER DEFAULT 0,
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3,
                    retry_failed_only BOOLEAN DEFAULT 1,
                    last_retry_at DATETIME,
                    daily_send_limit INTEGER DEFAULT 100,
                    emails_sent_today INTEGER DEFAULT 0,
                    last_send_date DATE,
                    send_schedule VARCHAR(50) DEFAULT 'immediate',
                    scheduled_start_date DATE,
                    scheduled_start_time TIME,
                    send_days_of_week VARCHAR(20) DEFAULT '1,2,3,4,5',
                    is_recurring BOOLEAN DEFAULT 0,
                    batch_status VARCHAR(50) DEFAULT 'not_started',
                    next_batch_date DATETIME,
                    total_batches_planned INTEGER DEFAULT 1,
                    batches_completed INTEGER DEFAULT 0,
                    recipient_criteria TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    started_at DATETIME,
                    completed_at DATETIME
                )
            ''')
            tables_created += 1
            print("✅ email_campaigns table created")
        
        # Create activities table
        if 'activities' not in existing_tables:
            print("📝 Creating activities table...")
            cursor.execute('''
                CREATE TABLE activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    contact_id INTEGER,
                    session_id VARCHAR(100),
                    activity_type VARCHAR(50) NOT NULL,
                    subject VARCHAR(255),
                    description TEXT,
                    extra_data TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (contact_id) REFERENCES contacts (id)
                )
            ''')
            tables_created += 1
            print("✅ activities table created")
        
        # Create email_logs table
        if 'email_logs' not in existing_tables:
            print("📧 Creating email_logs table...")
            cursor.execute('''
                CREATE TABLE email_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    campaign_id INTEGER NOT NULL,
                    contact_id INTEGER NOT NULL,
                    recipient_email VARCHAR(255) NOT NULL,
                    recipient_name VARCHAR(255),
                    subject VARCHAR(255) NOT NULL,
                    email_body_html TEXT,
                    email_body_text TEXT,
                    sent_at DATETIME,
                    delivered_at DATETIME,
                    opened_at DATETIME,
                    first_clicked_at DATETIME,
                    replied_at DATETIME,
                    bounced_at DATETIME,
                    unsubscribed_at DATETIME,
                    status VARCHAR(50) DEFAULT 'pending',
                    error_message TEXT,
                    open_count INTEGER DEFAULT 0,
                    click_count INTEGER DEFAULT 0,
                    user_agent VARCHAR(500),
                    ip_address VARCHAR(45),
                    message_id VARCHAR(255),
                    thread_id VARCHAR(255),
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
                    FOREIGN KEY (contact_id) REFERENCES contacts (id)
                )
            ''')
            tables_created += 1
            print("✅ email_logs table created")
        
        # Create email_events table
        if 'email_events' not in existing_tables:
            print("📧 Creating email_events table...")
            cursor.execute('''
                CREATE TABLE email_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    contact_id INTEGER NOT NULL,
                    campaign_id INTEGER,
                    event_type VARCHAR(50) NOT NULL,
                    description TEXT,
                    event_metadata TEXT,
                    event_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (contact_id) REFERENCES contacts (id),
                    FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id)
                )
            ''')
            tables_created += 1
            print("✅ email_events table created")
        
        # Create chat_events table
        if 'chat_events' not in existing_tables:
            print("💬 Creating chat_events table...")
            cursor.execute('''
                CREATE TABLE chat_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id VARCHAR(100) NOT NULL,
                    contact_id INTEGER NOT NULL,
                    event_type VARCHAR(50) NOT NULL,
                    stage_name VARCHAR(50),
                    event_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (contact_id) REFERENCES contacts (id)
                )
            ''')
            tables_created += 1
            print("✅ chat_events table created")
        
        # Create indexes for better performance
        print("🔍 Creating indexes...")
        try:
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_contacts_status ON contacts(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_contacts_is_active ON contacts(is_active)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_email_logs_campaign_id ON email_logs(campaign_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_email_logs_contact_id ON email_logs(contact_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chatbot_sessions_session_id ON chatbot_sessions(session_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chatbot_sessions_contact_id ON chatbot_sessions(contact_id)')
            print("✅ Indexes created")
        except Exception as e:
            print(f"⚠️  Index creation warning: {e}")
        
        conn.commit()
        print(f"\n📊 Summary: {tables_created} tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 Missing Tables Creation Tool")
    print("=" * 50)
    
    success = create_missing_tables()
    if success:
        print("\n✅ All tables created successfully!")
        print("You can now restart the Flask application.")
    else:
        print("\n❌ Table creation failed.")
