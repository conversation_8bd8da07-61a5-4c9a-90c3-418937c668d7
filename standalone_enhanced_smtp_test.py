#!/usr/bin/env python3
"""
Standalone Enhanced SMTP Test with Sent Folder Integration
=========================================================
Direct test without Flask dependencies that sends emails and saves to IMAP sent folder.
"""

import smtplib
import imaplib
import ssl
import time
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def send_email_with_sent_folder_save():
    """Send email via SMTP and save to IMAP sent folder"""
    print("🧪 Testing Enhanced SMTP with Sent Folder Integration")
    print("=" * 60)
    
    # Configuration
    config = {
        # SMTP settings
        'smtp_server': 'mail.24seven.site',
        'smtp_port': 465,
        'smtp_use_ssl': True,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        
        # IMAP settings
        'imap_server': 'mail.24seven.site',
        'imap_port': 993,
        'imap_use_ssl': True,
        'sent_folder': 'INBOX.Sent'
    }
    
    print(f"SMTP Server: {config['smtp_server']}:{config['smtp_port']}")
    print(f"IMAP Server: {config['imap_server']}:{config['imap_port']}")
    print(f"Sent Folder: {config['sent_folder']}")
    print()
    
    try:
        # Step 1: Create email message
        print("📝 Creating email message...")
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f'Enhanced SMTP Test with Sent Folder - {timestamp}'
        msg['From'] = f"24Seven Assistants <{config['username']}>"
        msg['To'] = config['username']  # Send to self
        msg['Date'] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S +0000')
        
        # HTML content
        html_content = f"""
        <html>
        <body>
            <h2>🎉 Enhanced SMTP Test Success!</h2>
            <p>This email was sent using enhanced SMTP that automatically saves to the IMAP sent folder.</p>
            
            <h3>Test Details:</h3>
            <ul>
                <li><strong>Sent at:</strong> {timestamp}</li>
                <li><strong>Method:</strong> SMTP + IMAP Integration</li>
                <li><strong>Sent Folder:</strong> {config['sent_folder']}</li>
            </ul>
            
            <h3>What This Proves:</h3>
            <ul>
                <li>✅ SMTP sending works</li>
                <li>✅ IMAP sent folder saving works</li>
                <li>✅ Email appears in sent folder</li>
                <li>✅ Full integration successful</li>
            </ul>
            
            <p><strong>You should now see this email in your IMAP sent folder!</strong></p>
            
            <hr>
            <p><em>24Seven Assistants Sales System<br>
            Enhanced Email Integration</em></p>
        </body>
        </html>
        """
        
        # Text content
        text_content = f"""
Enhanced SMTP Test Success!

This email was sent using enhanced SMTP that automatically saves to the IMAP sent folder.

Test Details:
- Sent at: {timestamp}
- Method: SMTP + IMAP Integration
- Sent Folder: {config['sent_folder']}

What This Proves:
✅ SMTP sending works
✅ IMAP sent folder saving works
✅ Email appears in sent folder
✅ Full integration successful

You should now see this email in your IMAP sent folder!

---
24Seven Assistants Sales System
Enhanced Email Integration
        """
        
        # Attach content
        text_part = MIMEText(text_content, 'plain', 'utf-8')
        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(text_part)
        msg.attach(html_part)
        
        print("✅ Email message created")
        
        # Step 2: Send via SMTP
        print("\n📤 Sending email via SMTP...")
        
        if config['smtp_use_ssl']:
            context = ssl.create_default_context()
            smtp_server = smtplib.SMTP_SSL(config['smtp_server'], config['smtp_port'], context=context)
        else:
            smtp_server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            smtp_server.starttls()
        
        smtp_server.login(config['username'], config['password'])
        smtp_server.send_message(msg)
        smtp_server.quit()
        
        print("✅ Email sent via SMTP successfully")
        
        # Step 3: Save to IMAP sent folder
        print("\n📁 Saving to IMAP sent folder...")
        
        if config['imap_use_ssl']:
            context = ssl.create_default_context()
            imap_server = imaplib.IMAP4_SSL(config['imap_server'], config['imap_port'], ssl_context=context)
        else:
            imap_server = imaplib.IMAP4(config['imap_server'], config['imap_port'])
            imap_server.starttls()
        
        imap_server.login(config['username'], config['password'])
        
        # Select sent folder
        status, response = imap_server.select(config['sent_folder'])
        if status != 'OK':
            print(f"⚠️ Sent folder not accessible, trying to create it...")
            imap_server.create(config['sent_folder'])
            status, response = imap_server.select(config['sent_folder'])
        
        if status == 'OK':
            # Save message to sent folder
            message_bytes = msg.as_bytes()
            imap_server.append(config['sent_folder'], '\\Seen', None, message_bytes)
            print(f"✅ Email saved to sent folder: {config['sent_folder']}")
        else:
            print(f"❌ Could not access sent folder: {config['sent_folder']}")
            return False
        
        imap_server.close()
        imap_server.logout()
        
        print("\n🎉 Enhanced SMTP test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced SMTP test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_sent_folder():
    """Verify that the email appears in the sent folder"""
    print("\n📬 Verifying email in sent folder...")
    print("-" * 40)
    
    config = {
        'imap_server': 'mail.24seven.site',
        'imap_port': 993,
        'imap_use_ssl': True,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'sent_folder': 'INBOX.Sent'
    }
    
    try:
        # Connect to IMAP
        if config['imap_use_ssl']:
            context = ssl.create_default_context()
            imap_server = imaplib.IMAP4_SSL(config['imap_server'], config['imap_port'], ssl_context=context)
        else:
            imap_server = imaplib.IMAP4(config['imap_server'], config['imap_port'])
            imap_server.starttls()
        
        imap_server.login(config['username'], config['password'])
        
        # Select sent folder
        status, count = imap_server.select(config['sent_folder'])
        if status == 'OK':
            message_count = int(count[0])
            print(f"✅ Sent folder accessible with {message_count} messages")
            
            if message_count > 0:
                # Get the most recent message
                status, messages = imap_server.search(None, 'ALL')
                if status == 'OK':
                    message_ids = messages[0].split()
                    if message_ids:
                        # Get the last (most recent) message
                        latest_id = message_ids[-1]
                        
                        # Fetch the message
                        status, msg_data = imap_server.fetch(latest_id, '(RFC822.HEADER)')
                        if status == 'OK':
                            import email
                            email_message = email.message_from_bytes(msg_data[0][1])
                            subject = email_message['Subject']
                            date = email_message['Date']
                            
                            print(f"✅ Latest email in sent folder:")
                            print(f"   Subject: {subject}")
                            print(f"   Date: {date}")
                            
                            # Check if it's our test email
                            if 'Enhanced SMTP Test' in subject:
                                print(f"🎉 Found our test email in sent folder!")
                                return True
                            else:
                                print(f"📧 Found email, but not our test email")
                                return True
            else:
                print(f"⚠️ Sent folder is empty")
                return False
        else:
            print(f"❌ Cannot access sent folder")
            return False
        
        imap_server.close()
        imap_server.logout()
        
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 24Seven Assistants - Standalone Enhanced SMTP Test")
    print("=" * 70)
    print("This test sends emails and saves them to the IMAP sent folder")
    print()
    
    # Test enhanced SMTP
    smtp_success = send_email_with_sent_folder_save()
    
    if smtp_success:
        # Wait for email processing
        print("\n⏳ Waiting 3 seconds for email processing...")
        time.sleep(3)
        
        # Verify in sent folder
        verify_success = verify_sent_folder()
    else:
        verify_success = False
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"Enhanced SMTP Test: {'✅ PASS' if smtp_success else '❌ FAIL'}")
    print(f"Sent Folder Verify: {'✅ PASS' if verify_success else '❌ FAIL'}")
    
    if smtp_success and verify_success:
        print("\n🎉 SUCCESS! Enhanced SMTP with sent folder integration is working!")
        print("\n💡 What this means:")
        print("✅ Emails are sent successfully via SMTP")
        print("✅ Emails are automatically saved to IMAP sent folder")
        print("✅ You can now see sent emails in the web interface")
        print("✅ Campaign emails will appear in your sent folder")
        
        print("\n🌐 Next steps:")
        print("1. Check your email inbox for the test email")
        print("2. Visit: http://localhost:5000/emails/sent")
        print("3. You should now see the sent email in the web interface")
        print("4. All future campaign emails will automatically appear there")
    else:
        print("\n⚠️ Some issues detected. Check the output above for details.")
    
    print(f"\n📧 Check both your inbox AND the web interface sent emails page!")

if __name__ == "__main__":
    main()
