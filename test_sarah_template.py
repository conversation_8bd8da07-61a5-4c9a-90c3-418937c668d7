#!/usr/bin/env python3
"""
Test Sarah Template
==================
Test the updated "Meet Sarah" template with pricing and interactive chat.
"""

from email_system.email_templates import EmailTemplate<PERSON>anager

def test_sarah_template():
    """Test the Sarah template"""
    print("🧪 Testing Sarah Template")
    print("=" * 50)

    # Create template manager
    template_manager = EmailTemplateManager()

    # Test context
    test_context = {
        'contact_name': '<PERSON>',
        'company_name': 'Test Company Ltd',
        'agent_name': '<PERSON>',
        'reply_email': '<EMAIL>',
        'phone_number': '+256 **********',
        'industry': 'Technology',
        'chat_url': 'http://localhost:5000/chat/test-session-123',
        'session_id': 'test-session-123'
    }

    # Render introduction template
    try:
        rendered = template_manager.render_template('introduction', test_context)
        
        print("✅ Template rendered successfully!")
        print(f"📧 Subject: {rendered['subject']}")
        print(f"📝 HTML length: {len(rendered['html_body'])} characters")
        print(f"📄 Text length: {len(rendered['text_body'])} characters")
        
        # Check for key elements
        html_body = rendered['html_body']
        
        checks = [
            ('Meet <PERSON>', 'Meet Sarah' in html_body),
            ('Our Personal AI Sales Assistant', 'Our Personal AI Sales Assistant' in html_body),
            ('UGX 250K setup', 'UGX 250K setup' in html_body),
            ('UGX 500K setup', 'UGX 500K setup' in html_body),
            ('UGX 3M setup', 'UGX 3M setup' in html_body),
            ('Try Our AI Assistant Demo', 'Try Our AI Assistant Demo' in html_body),
            ('Type your message here', 'Type your message here' in html_body),
            ('chat_url', test_context['chat_url'] in html_body),
            ('Blue border input', '#007bff' in html_body),
            ('White background input', 'background: white' in html_body),
            ('Dark text color', 'color: #333' in html_body)
        ]
        
        print("\n🔍 Template Content Checks:")
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
        
        # Save HTML for inspection
        with open('sarah_template_test.html', 'w', encoding='utf-8') as f:
            f.write(rendered['html_body'])
        
        print(f"\n📁 HTML saved to: sarah_template_test.html")
        
        all_passed = all(check[1] for check in checks)
        if all_passed:
            print("\n🎉 All checks passed! Template is ready.")
        else:
            print("\n⚠️  Some checks failed. Please review the template.")
            
    except Exception as e:
        print(f"❌ Error rendering template: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sarah_template()
