#!/usr/bin/env python3
"""
Test IMAP Functionality
=======================
Test script to verify IMAP connection and sent email retrieval.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imap_connection():
    """Test IMAP connection"""
    print("🔌 Testing IMAP Connection...")
    print("-" * 40)
    
    try:
        from email_system.imap_service import IMAPService
        
        # IMAP configuration (same as your SMTP)
        imap_config = {
            'IMAP_SERVER': 'mail.24seven.site',
            'IMAP_PORT': 993,
            'IMAP_USE_SSL': True,
            'MAIL_USERNAME': '<EMAIL>',
            'MAIL_PASSWORD': 'M@kerere1',
            'IMAP_SENT_FOLDER': 'Sent'
        }
        
        print(f"Server: {imap_config['IMAP_SERVER']}:{imap_config['IMAP_PORT']}")
        print(f"Username: {imap_config['MAIL_USERNAME']}")
        print(f"SSL: {imap_config['IMAP_USE_SSL']}")
        print()
        
        # Initialize IMAP service
        imap_service = IMAPService(imap_config)
        
        # Test connection
        success, message = imap_service.test_connection()
        
        if success:
            print("✅ IMAP connection successful!")
            print(f"   {message}")
            return True, imap_service
        else:
            print(f"❌ IMAP connection failed: {message}")
            return False, None
            
    except Exception as e:
        print(f"❌ IMAP test error: {str(e)}")
        return False, None

def test_folder_list(imap_service):
    """Test getting folder list"""
    print("\n📁 Testing Folder List...")
    print("-" * 40)
    
    try:
        folders = imap_service.get_folder_list()
        
        if folders:
            print(f"✅ Found {len(folders)} folders:")
            for i, folder in enumerate(folders, 1):
                print(f"   {i}. {folder}")
            return True
        else:
            print("⚠️ No folders found")
            return False
            
    except Exception as e:
        print(f"❌ Folder list error: {str(e)}")
        return False

def test_sent_emails(imap_service):
    """Test retrieving sent emails"""
    print("\n📧 Testing Sent Email Retrieval...")
    print("-" * 40)
    
    try:
        # Get recent sent emails
        sent_emails = imap_service.get_sent_emails(limit=10, days_back=7)
        
        if sent_emails:
            print(f"✅ Found {len(sent_emails)} sent emails:")
            print()
            
            for i, email in enumerate(sent_emails, 1):
                print(f"Email {i}:")
                print(f"   Subject: {email.get('subject', 'No Subject')}")
                print(f"   To: {email.get('to', 'Unknown')}")
                print(f"   Date: {email.get('date', 'Unknown')}")
                print(f"   Message ID: {email.get('message_id', 'None')[:50]}...")
                
                # Show content preview
                if email.get('body_text'):
                    preview = email['body_text'][:100].replace('\n', ' ')
                    print(f"   Text Preview: {preview}...")
                elif email.get('body_html'):
                    # Strip HTML tags for preview
                    import re
                    preview = re.sub('<[^<]+?>', '', email['body_html'])[:100].replace('\n', ' ')
                    print(f"   HTML Preview: {preview}...")
                
                print()
            
            return True
        else:
            print("⚠️ No sent emails found")
            print("   This could mean:")
            print("   - No emails sent in the last 7 days")
            print("   - Sent folder has a different name")
            print("   - IMAP access to sent folder is restricted")
            return False
            
    except Exception as e:
        print(f"❌ Sent email retrieval error: {str(e)}")
        return False

def test_email_search(imap_service):
    """Test email search functionality"""
    print("\n🔍 Testing Email Search...")
    print("-" * 40)
    
    try:
        # Search for emails containing "24Seven" or "test"
        search_terms = ["24Seven", "test", "assistant"]
        
        for term in search_terms:
            print(f"Searching for '{term}'...")
            results = imap_service.search_sent_emails(
                search_term=term,
                days_back=30
            )
            
            if results:
                print(f"   ✅ Found {len(results)} emails containing '{term}'")
                for email in results[:3]:  # Show first 3
                    print(f"      - {email.get('subject', 'No Subject')}")
            else:
                print(f"   ⚠️ No emails found containing '{term}'")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Email search error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 24Seven Assistants - IMAP Functionality Test")
    print("=" * 60)
    print("Testing IMAP connection and sent email retrieval...")
    print()
    
    # Test 1: IMAP Connection
    connection_success, imap_service = test_imap_connection()
    
    if not connection_success:
        print("\n❌ Cannot proceed without IMAP connection")
        print("\n💡 Troubleshooting:")
        print("1. Check if IMAP is enabled on your email account")
        print("2. Verify server settings (mail.24seven.site:993)")
        print("3. Confirm username and password are correct")
        print("4. Check if SSL/TLS is properly configured")
        return
    
    # Test 2: Folder List
    folder_success = test_folder_list(imap_service)
    
    # Test 3: Sent Emails
    sent_success = test_sent_emails(imap_service)
    
    # Test 4: Email Search
    search_success = test_email_search(imap_service)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 IMAP TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"IMAP Connection:     {'✅ PASS' if connection_success else '❌ FAIL'}")
    print(f"Folder List:         {'✅ PASS' if folder_success else '❌ FAIL'}")
    print(f"Sent Email Retrieval: {'✅ PASS' if sent_success else '❌ FAIL'}")
    print(f"Email Search:        {'✅ PASS' if search_success else '❌ FAIL'}")
    
    if all([connection_success, folder_success]):
        print("\n🎉 IMAP functionality is working!")
        print("\n💡 Next steps:")
        print("1. Start the sales system: python unified_sales_system.py")
        print("2. Visit: http://localhost:5000/emails/sent")
        print("3. View your sent emails in the web interface")
        
        if not sent_success:
            print("\n⚠️ Note: No sent emails found, but IMAP is working.")
            print("   Send some test emails first, then check again.")
    else:
        print("\n⚠️ Some IMAP functionality issues detected.")
        print("   Check the error messages above for troubleshooting.")

if __name__ == "__main__":
    main()
