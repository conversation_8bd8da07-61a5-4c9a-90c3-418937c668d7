<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contacts - 24Seven Assistants</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Theme CSS -->
    <link rel="stylesheet" href="/static/css/dark-theme.css">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <!-- jQuery (required for bootstrap-select) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    

    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #667eea;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --danger-color: #f44336;
            --dark-color: #333;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            padding-top: 20px;
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-new { background-color: #e3f2fd; color: #1976d2; }
        .status-contacted { background-color: #fff3e0; color: #f57c00; }
        .status-qualified { background-color: #e8f5e8; color: #388e3c; }
        .status-customer { background-color: #f3e5f5; color: #7b1fa2; }
        .status-lost { background-color: #ffebee; color: #d32f2f; }

        .status-open { background-color: #e3f2fd; color: #1976d2; }
        .status-won { background-color: #e8f5e8; color: #388e3c; }
        .status-draft { background-color: #f5f5f5; color: #616161; }
        .status-running { background-color: #fff3e0; color: #f57c00; }
        .status-completed { background-color: #e8f5e8; color: #388e3c; }
    </style>

    
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-rocket"></i> 24Seven Assistants
            </a>
            <span class="navbar-text">
                Sales Department System
            </span>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link " href="/">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link " href="/analytics">
                        <i class="fas fa-chart-line me-2"></i> Analytics
                    </a>
                    <a class="nav-link active" href="/contacts">
                        <i class="fas fa-users me-2"></i> Contacts
                    </a>
                    <a class="nav-link " href="/groups">
                        <i class="fas fa-layer-group me-2"></i> Groups
                    </a>
                    <a class="nav-link " href="/opportunities">
                        <i class="fas fa-bullseye me-2"></i> Opportunities
                    </a>
                    <a class="nav-link " href="/campaigns">
                        <i class="fas fa-envelope me-2"></i> Email Campaigns
                    </a>
                    <a class="nav-link " href="/emails/sent">
                        <i class="fas fa-paper-plane me-2"></i> Sent Emails (IMAP)
                    </a>
                    <hr class="text-white">
                    <a class="nav-link " href="/debug/">
                        <i class="fas fa-bug me-2"></i> Debug Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="testSMTP()">
                        <i class="fas fa-cog me-2"></i> Test SMTP
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <!-- Flash Messages -->
                
                    
                

                
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users text-primary"></i> Contact Management</h1>
    <div>
        <button id="bulkDeleteContactsBtn" class="btn btn-danger me-2" style="display: none;" onclick="bulkDeleteContacts()">
            <i class="fas fa-trash"></i> Delete Selected
        </button>
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContactModal">
            <i class="fas fa-plus"></i> Add Contact
        </a>
    </div>
</div>

<!-- Contact Statistics - Synchronized Analytics -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Total Contacts</p>
                <i class="fas fa-users fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Emails Sent</p>
                <i class="fas fa-envelope fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Emails Opened</p>
                <small>0% rate</small>
                <br><i class="fas fa-envelope-open fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Links Clicked</p>
                <small>0% rate</small>
                <br><i class="fas fa-mouse-pointer fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Conversations</p>
                <small>0% rate</small>
                <br><i class="fas fa-robot fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h4 class="card-title">0</h4>
                <p class="card-text">Conversions</p>
                <small>0% overall</small>
                <br><i class="fas fa-trophy fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Contacts Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> All Contacts
        </h5>
    </div>
    <div class="card-body">
        
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Contacts Found</h4>
            <p class="text-muted">Start by adding your first contact or importing from a campaign.</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContactModal">
                <i class="fas fa-plus"></i> Add First Contact
            </button>
        </div>
        
    </div>
</div>

<!-- Add Contact Modal -->
<div class="modal fade" id="addContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Contact</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="/contacts/add" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="job_title" class="form-label">Job Title</label>
                                <input type="text" class="form-control" id="job_title" name="job_title">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="source" class="form-label">Source</label>
                        <select class="form-select" id="source" name="source">
                            <option value="manual_entry">Manual Entry</option>
                            <option value="website">Website</option>
                            <option value="referral">Referral</option>
                            <option value="social_media">Social Media</option>
                            <option value="email_campaign">Email Campaign</option>
                            <option value="chatbot">Chatbot</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Contact</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">⚠️ Confirm Complete Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteContactName"></strong>?</p>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> This will permanently delete:</h6>
                    <ul class="mb-0">
                        <li>Contact information and profile</li>
                        <li>All chatbot conversations and sessions</li>
                        <li>All activity history and tracking data</li>
                        <li>Email campaign statistics (will be updated)</li>
                        <li>Sales stage progression history</li>
                    </ul>
                </div>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteContactForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete All Data
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteContact(contactId, contactName) {
    document.getElementById('deleteContactName').textContent = contactName;
    document.getElementById('deleteContactForm').action = '/contacts/' + contactId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteContactModal')).show();
}

// Bulk delete functionality for contacts
function toggleSelectAllContacts() {
    const selectAll = document.getElementById('selectAllContacts');
    const checkboxes = document.querySelectorAll('.contact-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkDeleteContactsButtons();
}

function updateBulkDeleteContactsButtons() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
    const bulkDeleteBtn = document.getElementById('bulkDeleteContactsBtn');

    if (checkedBoxes.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
    } else {
        bulkDeleteBtn.style.display = 'none';
    }

    // Update select all checkbox state
    const selectAll = document.getElementById('selectAllContacts');
    const allCheckboxes = document.querySelectorAll('.contact-checkbox');
    if (allCheckboxes.length > 0) {
        selectAll.checked = checkedBoxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;
    }
}

function bulkDeleteContacts() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Please select contacts to delete using the checkboxes.');
        return false;
    }

    const confirmMessage = `⚠️ BULK DELETE: This will permanently delete ${checkedBoxes.length} contact(s) and ALL related data including:

• Contact information and profiles
• All chatbot conversations and sessions
• All activity history and tracking data
• Email campaign statistics (will be updated)
• Sales stage progression history

This action cannot be undone. Are you absolutely sure?`;

    if (confirm(confirmMessage)) {
        // Clear any existing contact_ids inputs
        const existingInputs = document.querySelectorAll('#bulkDeleteContactsForm input[name="contact_ids"]');
        existingInputs.forEach(input => input.remove());

        // Add selected contact IDs to the form
        const form = document.getElementById('bulkDeleteContactsForm');
        checkedBoxes.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'contact_ids';
            hiddenInput.value = checkbox.value;
            form.appendChild(hiddenInput);
        });

        form.submit();
        return true;
    }
    return false;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkDeleteContactsButtons();
});
</script>

            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Bootstrap Select JS (needs Bootstrap bundle) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>

    <script>
        // Test SMTP Configuration
        function testSMTP() {
            fetch('/api/test-smtp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('SMTP Test Successful: ' + data.message);
                } else {
                    alert('SMTP Test Failed: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error testing SMTP: ' + error);
            });
        }

        // Auto-refresh dashboard every 5 minutes
        if (window.location.pathname === '/' || window.location.pathname.includes('analytics')) {
            setTimeout(function() {
                location.reload();
            }, 300000); // 5 minutes
        }
    </script>

    
<script>
        document.addEventListener('DOMContentLoaded', function () {
            // Convert all Bootstrap form-selects into bootstrap-select pickers for dark styling
            document.querySelectorAll('select.form-select').forEach(function (el) {
                el.classList.add('selectpicker');
            });
            if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                $('.selectpicker').selectpicker();
            }
        });
    </script>
</body>
</html>