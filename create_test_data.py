#!/usr/bin/env python3
"""
Create comprehensive test data for sales pipeline and cycle analytics
"""

import requests
import json
import time
import random
from datetime import datetime, timedelta

def create_test_data():
    """Create test data for sales pipeline analytics"""
    
    # Test sessions with different progression patterns
    test_sessions = [
        {'session_id': 'pipeline_demo_001', 'name': '<PERSON>', 'email': '<EMAIL>', 'max_stage': 5, 'convert': True},
        {'session_id': 'pipeline_demo_002', 'name': '<PERSON>', 'email': '<EMAIL>', 'max_stage': 4, 'convert': False},
        {'session_id': 'pipeline_demo_003', 'name': '<PERSON>', 'email': '<EMAIL>', 'max_stage': 3, 'convert': False},
        {'session_id': 'pipeline_demo_004', 'name': '<PERSON>', 'email': '<EMAIL>', 'max_stage': 5, 'convert': True},
        {'session_id': 'pipeline_demo_005', 'name': '<PERSON>', 'email': '<EMAIL>', 'max_stage': 2, 'convert': False},
        {'session_id': 'pipeline_demo_006', 'name': '<PERSON> <PERSON>', 'email': '<EMAIL>', 'max_stage': 5, 'convert': True},
        {'session_id': 'pipeline_demo_007', 'name': 'Tom Garcia', 'email': '<EMAIL>', 'max_stage': 1, 'convert': False},
        {'session_id': 'pipeline_demo_008', 'name': 'Amy Chen', 'email': '<EMAIL>', 'max_stage': 4, 'convert': False},
    ]

    stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']
    base_url = 'http://localhost:5000/api/track-session'

    print('🚀 Creating comprehensive test data for sales pipeline analytics...')
    print('=' * 60)

    for i, session in enumerate(test_sessions):
        print(f'\n📊 Creating session {i+1}: {session["name"]} (Max stage: {session["max_stage"]}, Convert: {session["convert"]})')
        
        message_count = 1
        
        # Progress through stages up to max_stage
        for stage_idx, stage in enumerate(stages[:session['max_stage']]):
            print(f'  🎯 Processing {stage} stage...')
            
            # Start stage
            data = {
                'session_id': session['session_id'],
                'stage': stage,
                'task': f'{stage}_start',
                'contact_name': session['name'],
                'contact_email': session['email'],
                'action': 'stage_progression',
                'message_count': message_count,
                'user_message': f'User starting {stage} conversation',
                'bot_response': f'Bot welcoming user to {stage} stage'
            }
            
            try:
                response = requests.post(base_url, json=data, timeout=5)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f'    ✅ Started {stage} stage successfully')
                    else:
                        print(f'    ⚠️ API returned success=false: {result.get("message", "Unknown error")}')
                else:
                    print(f'    ❌ HTTP {response.status_code}: {response.text[:100]}')
            except Exception as e:
                print(f'    ❌ Request failed: {str(e)}')
                continue
            
            # Add some interactions in this stage
            stage_messages = random.randint(2, 5)
            for msg in range(stage_messages):
                message_count += 2
                
                interaction_data = data.copy()
                interaction_data.update({
                    'message_count': message_count,
                    'task': f'{stage}_interaction',
                    'user_message': f'User question {msg+1} about {stage}',
                    'bot_response': f'Bot answer {msg+1} for {stage} stage'
                })
                
                try:
                    requests.post(base_url, json=interaction_data, timeout=5)
                except:
                    pass  # Continue even if some interactions fail
            
            # Complete stage if not the last one or if converting
            if stage_idx < session['max_stage'] - 1 or (stage == 'close' and session['convert']):
                message_count += 1
                completion_data = data.copy()
                completion_data.update({
                    'task': f'{stage}_completed',
                    'action': 'stage_completed',
                    'message_count': message_count
                })
                
                try:
                    response = requests.post(base_url, json=completion_data, timeout=5)
                    if response.status_code == 200:
                        print(f'    ✅ Completed {stage} stage')
                except:
                    print(f'    ⚠️ Failed to complete {stage} stage')
            
            # Small delay between stages
            time.sleep(0.2)
        
        # Handle conversion for successful sessions
        if session['convert'] and session['max_stage'] == 5:
            print(f'  🎉 Processing conversion for {session["name"]}...')
            conversion_data = {
                'session_id': session['session_id'],
                'stage': 'close',
                'task': 'conversion_completed',
                'contact_name': session['name'],
                'contact_email': session['email'],
                'action': 'stage_progression',
                'conversion_completed': True,
                'conversion_value': round(random.uniform(1000, 5000), 2),
                'message_count': message_count + 2
            }
            
            try:
                response = requests.post(base_url, json=conversion_data, timeout=5)
                if response.status_code == 200:
                    print(f'    ✅ Conversion completed! Value: ${conversion_data["conversion_value"]}')
                else:
                    print(f'    ❌ Conversion failed: {response.text[:100]}')
            except Exception as e:
                print(f'    ❌ Conversion request failed: {str(e)}')
        else:
            final_stage = stages[session['max_stage']-1] if session['max_stage'] > 0 else 'opening'
            print(f'  ⏹️ Session ended at {final_stage} stage (no conversion)')

    print('\n' + '=' * 60)
    print('🎉 Test data creation completed!')
    print('📊 Analytics dashboards now have comprehensive data showing:')
    print('   • Sales pipeline progression through all stages')
    print('   • Stage-to-stage conversion rates')
    print('   • Sales cycle timing and drop-off analysis')
    print('   • Performance metrics for each stage')
    print('\n🔗 View the analytics at:')
    print('   • Main Analytics: http://localhost:5000/analytics')
    print('   • Sales Pipeline: http://localhost:5000/analytics/sales-pipeline')
    print('   • Sales Cycle: http://localhost:5000/analytics/sales-cycle')
    print('   • Session Analytics: http://localhost:5000/analytics/sessions')

if __name__ == '__main__':
    create_test_data()
