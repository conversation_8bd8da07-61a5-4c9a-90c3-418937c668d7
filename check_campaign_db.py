#!/usr/bin/env python3
"""
Check Campaign Database
=======================
Check campaigns and groups in the database.
"""

import sqlite3
import os
import json

def check_database():
    """Check the database directly"""
    try:
        # Connect to SQLite database
        db_path = "sales_department.db"
        if not os.path.exists(db_path):
            print(f"❌ Database file not found: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 DATABASE ANALYSIS")
        print("=" * 40)
        
        # Check campaigns
        print("\n📋 CAMPAIGNS:")
        cursor.execute("SELECT id, name, status, recipient_criteria FROM email_campaigns")
        campaigns = cursor.fetchall()
        
        for campaign in campaigns:
            campaign_id, name, status, criteria = campaign
            print(f"   ID: {campaign_id}")
            print(f"   Name: {name}")
            print(f"   Status: {status}")
            print(f"   Recipient Criteria: {criteria}")
            print("   " + "-" * 30)
        
        # Check contact groups
        print("\n👥 CONTACT GROUPS:")
        cursor.execute("SELECT id, name, description FROM contact_groups WHERE is_active = 1")
        groups = cursor.fetchall()
        
        for group in groups:
            group_id, name, description = group
            
            # Count members
            cursor.execute("SELECT COUNT(*) FROM contact_group_memberships WHERE group_id = ?", (group_id,))
            member_count = cursor.fetchone()[0]
            
            print(f"   ID: {group_id}")
            print(f"   Name: {name}")
            print(f"   Description: {description}")
            print(f"   Members: {member_count}")
            print("   " + "-" * 20)
        
        # Check contacts in 100ppda group specifically
        print("\n📧 CONTACTS IN '100ppda' GROUP:")
        cursor.execute("""
            SELECT c.id, c.first_name, c.last_name, c.email 
            FROM contacts c
            JOIN contact_group_memberships cgm ON c.id = cgm.contact_id
            JOIN contact_groups cg ON cgm.group_id = cg.id
            WHERE cg.name = '100ppda' AND c.is_active = 1
        """)
        contacts_in_group = cursor.fetchall()
        
        if contacts_in_group:
            for contact in contacts_in_group:
                contact_id, first_name, last_name, email = contact
                print(f"   {first_name} {last_name} ({email}) - ID: {contact_id}")
        else:
            print("   No contacts found in 100ppda group")
        
        # Check all active contacts
        print("\n📧 ALL ACTIVE CONTACTS:")
        cursor.execute("SELECT id, first_name, last_name, email FROM contacts WHERE is_active = 1 AND do_not_email = 0")
        all_contacts = cursor.fetchall()
        
        print(f"   Total active contacts: {len(all_contacts)}")
        for contact in all_contacts[:5]:  # Show first 5
            contact_id, first_name, last_name, email = contact
            print(f"   {first_name} {last_name} ({email}) - ID: {contact_id}")
        
        if len(all_contacts) > 5:
            print(f"   ... and {len(all_contacts) - 5} more")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def fix_campaign():
    """Fix the campaign recipient criteria"""
    try:
        db_path = "sales_department.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔧 FIXING CAMPAIGN")
        print("=" * 30)
        
        # Find the campaign
        cursor.execute("SELECT id, name FROM email_campaigns WHERE name = '24seven Assistants'")
        campaign = cursor.fetchone()
        
        if not campaign:
            print("❌ Campaign not found")
            return False
        
        campaign_id, campaign_name = campaign
        print(f"✅ Found campaign: {campaign_name} (ID: {campaign_id})")
        
        # Find the 100ppda group
        cursor.execute("SELECT id FROM contact_groups WHERE name = '100ppda'")
        group = cursor.fetchone()
        
        if not group:
            print("❌ Group '100ppda' not found")
            return False
        
        group_id = group[0]
        print(f"✅ Found group: 100ppda (ID: {group_id})")
        
        # Update campaign recipient criteria
        recipient_criteria = json.dumps({
            'type': 'groups',
            'group_ids': [group_id]
        })
        
        cursor.execute(
            "UPDATE email_campaigns SET recipient_criteria = ?, status = 'draft' WHERE id = ?",
            (recipient_criteria, campaign_id)
        )
        
        conn.commit()
        conn.close()
        
        print(f"✅ Campaign updated!")
        print(f"   New recipient criteria: {recipient_criteria}")
        print(f"   Status reset to 'draft'")
        print("\n💡 Now you can go to the campaigns page and send it again!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing campaign: {e}")
        return False

if __name__ == "__main__":
    check_database()
    print("\n" + "=" * 50)
    fix_campaign()
