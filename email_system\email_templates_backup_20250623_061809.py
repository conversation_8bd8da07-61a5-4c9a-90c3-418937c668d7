"""
Enhanced Email Templates with Responsive Design
==============================================
Improved email templates for 24Seven Assistants with desktop and mobile optimization.
"""

from jinja2 import Template, Environment, BaseLoader
from typing import Dict, Any, Optional, List
import os
import json

class EnhancedEmailTemplateManager:
    """Enhanced email template manager with responsive design"""

    def __init__(self):
        """Initialize enhanced template manager"""
        self.templates = {}
        self.load_enhanced_templates()

    def load_enhanced_templates(self):
        """Load enhanced responsive email templates"""

        # Enhanced Introduction Email Template with responsive design
        self.templates['introduction'] = {
            'name': '24Seven Assistants - <PERSON> <PERSON>, Your AI Sales Assistant',
            'subject': 'Meet Sarah - Our Personal AI Sales Assistant',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24Seven Assistants - Meet <PERSON></title>
    <style>
        /* Desktop-first responsive design */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .email-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header-title {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            margin: 15px 0 0 0;
            font-size: 20px;
            opacity: 0.95;
        }

        .content-section {
            background: #f8f9fa;
            padding: 40px 30px;
        }

        .info-card {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #1976d2;
        }

        .service-item {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #4caf50;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .service-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .features-list {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .pricing-section {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .pricing-grid {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
        }

        .pricing-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 12px;
            flex: 1;
            min-width: 200px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .pricing-card h4 {
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .pricing-amount {
            font-size: 20px;
            font-weight: bold;
            margin: 8px 0;
        }

        .chat-demo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .chat-interface {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .chat-input-group {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #007bff;
            border-radius: 25px;
            font-size: 16px;
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .cta-button {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            display: inline-block;
            box-shadow: 0 6px 20px rgba(76,175,80,0.3);
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76,175,80,0.4);
            color: white;
            text-decoration: none;
        }

        .signature-section {
            border-top: 2px solid #e0e0e0;
            padding-top: 25px;
            margin-top: 35px;
        }

        .footer-section {
            text-align: center;
            margin-top: 25px;
            color: #666;
            font-size: 14px;
            padding: 20px;
        }

        /* Mobile Responsive Design */
        @media only screen and (max-width: 768px) {
            body {
                max-width: 100%;
                padding: 10px;
                font-size: 14px;
            }

            .header-section {
                padding: 25px 20px;
            }

            .header-title {
                font-size: 24px;
            }

            .header-subtitle {
                font-size: 16px;
            }

            .content-section {
                padding: 25px 20px;
            }

            .info-card {
                padding: 20px;
                margin: 20px 0;
            }

            .service-item {
                padding: 15px;
                margin: 12px 0;
            }

            .pricing-grid {
                flex-direction: column;
                gap: 15px;
            }

            .pricing-card {
                min-width: auto;
                margin-bottom: 15px;
            }

            .pricing-section {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .chat-demo-section {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .chat-interface {
                padding: 15px;
                margin: 15px 0;
            }

            .chat-input-group {
                gap: 10px;
            }

            .chat-input {
                padding: 12px 16px;
                font-size: 14px;
            }

            .chat-button {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }

            .cta-button {
                padding: 15px 25px;
                font-size: 14px;
                display: block;
                text-align: center;
                margin: 20px 0;
            }

            .signature-section {
                padding-top: 20px;
                margin-top: 25px;
            }
        }

        @media only screen and (max-width: 480px) {
            body {
                padding: 5px;
            }

            .header-section {
                padding: 20px 15px;
            }

            .header-title {
                font-size: 22px;
            }

            .header-subtitle {
                font-size: 14px;
            }

            .content-section {
                padding: 20px 15px;
            }

            .info-card {
                padding: 15px;
            }

            .service-item {
                padding: 12px;
            }

            .pricing-section {
                padding: 20px 15px;
            }

            .chat-demo-section {
                padding: 20px 15px;
            }

            .chat-interface {
                padding: 12px;
            }

            .chat-input {
                padding: 10px 14px;
                font-size: 13px;
            }

            .chat-button {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header-section">
            <h1 class="header-title">👋 Meet Sarah</h1>
            <p class="header-subtitle">Our Personal AI Sales Assistant</p>
        </div>

        <div class="content-section">
            <p style="margin-top: 0; font-size: 16px;">Hi {{ contact_name }},</p>

            <p>I hope this email finds you well. My name is Sarah, and I'm reaching out from <strong>24Seven Assistants</strong> because I believe we can help you streamline your business operations and achieve remarkable growth and efficiency.</p>

            <p>Many businesses struggle with providing round the clock customer service. That's where we come in: we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.</p>

            <div class="info-card">
                <h3 style="color: #1976d2; margin-top: 0;">🤖 Why 24Seven Assistants?</h3>
                <p style="margin-bottom: 0;">We provide world-class virtual assistant services that work around the clock to support your business operations, allowing you to focus on what matters most - growing your business.</p>
            </div>

            <h3 style="color: #ff9800; margin-bottom: 20px;">📋 Our Core Services:</h3>

            <div class="service-item">
                <strong>📞 Administrative Support</strong><br>
                Email management, scheduling, data entry, and document preparation
            </div>

            <div class="service-item" style="border-left-color: #2196f3;">
                <strong>🎧 Customer Service</strong><br>
                24/7 customer support, live chat, and phone assistance
            </div>

            <div class="service-item" style="border-left-color: #ff9800;">
                <strong>📊 Lead qualification and follow-up</strong><br>
                Appointment scheduling and sales pipeline management
            </div>

            <div class="service-item" style="border-left-color: #9c27b0;">
                <strong>📧 Email and phone support</strong><br>
                Professional communication management
            </div>

            <h3 style="color: #4caf50; margin: 30px 0 20px 0;">💡 What Makes Us Different:</h3>
            <div class="features-list">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Available 24/7:</strong> We work around the clock for maximum productivity</li>
                    <li><strong>Flexible plans that grow with your business</strong></li>
                    <li><strong>Easy integration with your current systems</strong></li>
                    <li><strong>Competitive pricing in UGX currency</strong></li>
                </ul>
            </div>

            <div class="pricing-section">
                <h3 style="margin-top: 0; text-align: center; font-size: 24px;">💰 Our Pricing Plans</h3>

                <div class="pricing-grid">
                    <div class="pricing-card">
                        <h4>Small Business</h4>
                        <p class="pricing-amount">UGX 250K setup</p>
                        <p style="margin: 5px 0;">UGX 100K/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Perfect for startups</p>
                    </div>

                    <div class="pricing-card">
                        <h4>Medium Business</h4>
                        <p class="pricing-amount">UGX 500K setup</p>
                        <p style="margin: 5px 0;">UGX 250K/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Ideal for growth</p>
                    </div>

                    <div class="pricing-card">
                        <h4>Large Enterprise</h4>
                        <p class="pricing-amount">UGX 3M setup</p>
                        <p style="margin: 5px 0;">UGX 1M/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Complete solution</p>
                    </div>
                </div>
            </div>

            <div class="chat-demo-section">
                <h3 style="margin-top: 0; text-align: center; font-size: 22px;">💬 Try Our AI Assistant Demo</h3>
                <p style="text-align: center; margin-bottom: 20px;">Experience our AI assistant in action! Click the chat button below to start a conversation:</p>

                <div class="chat-interface">
                    <div class="chat-input-group">
                        <input type="text" class="chat-input" placeholder="Type your message here..." readonly>
                        <a href="{{ chat_url }}" class="chat-button" target="_blank">➤</a>
                    </div>
                </div>

                <p style="text-align: center; margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">
                    <a href="{{ chat_url }}" style="color: white; text-decoration: underline;">Click here to start chatting with Sarah →</a>
                </p>
            </div>

            <p>I'd love to schedule a brief call to discuss how 24Seven Assistants can specifically help {{ company_name }} achieve its goals. Are you available for a 15-minute conversation this week?</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="mailto:{{ reply_email }}" class="cta-button">
                    📞 Schedule a Call
                </a>
            </div>

            <div class="signature-section">
                <p style="margin-bottom: 5px;"><strong>Best regards,</strong></p>
                <p style="margin: 5px 0;"><strong>{{ agent_name }}</strong></p>
                <p style="margin: 5px 0;">24Seven Assistants</p>
                <p style="margin: 5px 0;">📧 {{ reply_email }}</p>
                <p style="margin: 5px 0;">📞 {{ phone_number }}</p>
            </div>
        </div>
    </div>

    <div class="footer-section">
        <p>© 2024 24Seven Assistants. Professional Virtual Assistant Services.</p>
    </div>
</body>
</html>
            ''',
            'text_template': '''
Hi {{ contact_name }},

I hope this email finds you well. My name is Sarah, and I'm reaching out from 24Seven Assistants because I believe we can help you streamline your business operations and achieve remarkable growth and efficiency.

Many businesses struggle with providing round the clock customer service. That's where we come in: we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.

Why 24Seven Assistants?
We provide world-class virtual assistant services that work around the clock to support your business operations, allowing you to focus on what matters most - growing your business.

Our Core Services:
• Administrative Support: Email management, scheduling, data entry, and document preparation
• Customer Service: 24/7 customer support, live chat, and phone assistance
• Lead qualification and follow-up: Appointment scheduling
• Email and phone support: Professional communication management

What Makes Us Different:
• Available 24/7: We work around the clock for maximum productivity
• Flexible plans that grow with your business
• Easy integration with your current systems
• Competitive pricing in UGX currency

Our Pricing Plans:
• Small Business: UGX 250K setup, UGX 100K/month
• Medium Business: UGX 500K setup, UGX 250K/month
• Large Enterprise: UGX 3M setup, UGX 1M/month

Try Our AI Assistant Demo:
Visit {{ chat_url }} to chat with Sarah and experience our AI assistant in action!

I'd love to schedule a brief call to discuss how 24Seven Assistants can specifically help {{ company_name }} achieve its goals. Are you available for a 15-minute conversation this week?

Best regards,
{{ agent_name }}
24Seven Assistants
{{ reply_email }}
{{ phone_number }}

© 2024 24Seven Assistants. Professional Virtual Assistant Services.
            '''
        }

        # Enhanced Customer Support Solutions Email Template
        self.templates['customer_support'] = {
            'name': '24Seven Assistants - Meet Sarah, Your AI Sales Assistant',
            'subject': 'Customer Support Solutions for {{ contact_name }}',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24Seven Assistants - Meet Sarah</title>
    <style>
        /* Reuse the same responsive CSS from introduction template */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .email-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header-title {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            margin: 15px 0 0 0;
            font-size: 20px;
            opacity: 0.95;
        }

        .content-section {
            background: white;
            padding: 40px 30px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #2c3e50;
        }

        .pricing-card-alt {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #4caf50;
        }

        .chat-demo-enhanced {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #dee2e6;
        }

        .chat-preview {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .chat-message {
            background: #e3f2fd;
            padding: 18px;
            border-radius: 12px;
            margin-bottom: 15px;
            border-left: 4px solid #2196f3;
        }

        .chat-input-enhanced {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #007bff;
            border-radius: 25px;
            font-size: 16px;
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-send-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .chat-send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40,167,69,0.3);
            color: white;
            text-decoration: none;
        }

        .info-tip {
            background: #fff3cd;
            padding: 18px;
            border-radius: 10px;
            margin-top: 18px;
            border-left: 4px solid #ffc107;
            font-size: 14px;
        }

        .signature-section {
            border-top: 2px solid #e0e0e0;
            padding-top: 25px;
            margin-top: 35px;
        }

        .footer-section {
            background: #f8f9fa;
            padding: 25px;
            text-align: center;
            border-radius: 0 0 15px 15px;
            font-size: 12px;
            color: #666;
        }

        /* Mobile Responsive Design */
        @media only screen and (max-width: 768px) {
            body {
                max-width: 100%;
                padding: 10px;
                font-size: 14px;
            }

            .header-section {
                padding: 25px 20px;
            }

            .header-title {
                font-size: 24px;
            }

            .header-subtitle {
                font-size: 16px;
            }

            .content-section {
                padding: 25px 20px;
            }

            .info-card, .pricing-card-alt {
                padding: 20px;
                margin: 20px 0;
            }

            .chat-demo-enhanced {
                padding: 20px;
                margin: 20px 0;
            }

            .chat-preview {
                padding: 18px;
            }

            .chat-message {
                padding: 15px;
            }

            .chat-input-enhanced {
                gap: 10px;
            }

            .chat-input {
                padding: 12px 16px;
                font-size: 14px;
            }

            .chat-send-btn {
                padding: 12px 20px;
                font-size: 13px;
            }

            .signature-section {
                padding-top: 20px;
                margin-top: 25px;
            }
        }

        @media only screen and (max-width: 480px) {
            body {
                padding: 5px;
            }

            .header-section {
                padding: 20px 15px;
            }

            .header-title {
                font-size: 22px;
            }

            .header-subtitle {
                font-size: 14px;
            }

            .content-section {
                padding: 20px 15px;
            }

            .info-card, .pricing-card-alt {
                padding: 15px;
            }

            .chat-demo-enhanced {
                padding: 15px;
            }

            .chat-preview {
                padding: 15px;
            }

            .chat-input {
                padding: 10px 14px;
                font-size: 13px;
            }

            .chat-send-btn {
                padding: 10px 16px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header-section">
            <h1 class="header-title">🤖 Meet Sarah</h1>
            <p class="header-subtitle">Our Personal AI Sales Assistant</p>
        </div>

        <div class="content-section">
            <p style="margin-top: 0; font-size: 16px;">Hi {{ contact_name }},</p>

            <p>I hope this email finds you well! I'm Sarah from 24Seven Assistants, and I wanted to personally reach out about our customer support solutions.</p>

            <p>Many businesses struggle with providing round-the-clock customer service. That's where we come in - we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.</p>

            <div class="info-card">
                <h3 style="color: #2c3e50; margin-top: 0;">Our Services Include:</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Customer support and live chat</li>
                    <li>Administrative task management</li>
                    <li>Lead qualification and follow-up</li>
                    <li>Appointment scheduling</li>
                    <li>Email and phone support</li>
                </ul>
            </div>

            <div class="info-card">
                <h3 style="color: #2c3e50; margin-top: 0;">What Makes Us Different:</h3>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Assistants trained specifically for your industry</li>
                    <li>Flexible plans that grow with your business</li>
                    <li>Easy integration with your current systems</li>
                    <li>Competitive pricing in UGX currency</li>
                </ul>
            </div>

            <div class="pricing-card-alt">
                <h3 style="color: #2c3e50; margin-top: 0;">Our Pricing Plans:</h3>

                <div style="margin: 20px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 8px;">Small Business Package:</h4>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Setup: UGX 250,000</li>
                        <li>Monthly: UGX 100,000</li>
                        <li>Perfect for startups and small businesses</li>
                    </ul>
                </div>

                <div style="margin: 20px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 8px;">Medium Business Package:</h4>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Setup: UGX 500,000</li>
                        <li>Monthly: UGX 250,000</li>
                        <li>Ideal for growing companies</li>
                    </ul>
                </div>

                <div style="margin: 20px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 8px;">Large Enterprise Package:</h4>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Setup: UGX 3,000,000</li>
                        <li>Monthly: UGX 1,000,000</li>
                        <li>Comprehensive solution for large organizations</li>
                    </ul>
                </div>
            </div>

            <div class="chat-demo-enhanced">
                <div style="font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 20px; text-align: center;">💬 Start Chat with Sarah</div>

                <div class="chat-preview">
                    <div class="chat-message">
                        <strong>Sarah:</strong> Hello! I'm Sarah from 24Seven Assistants. We create virtual assistants that never sleep - helping businesses serve customers 24/7 even when everyone goes home. Think of it like having a helpful employee who works nights, weekends, and holidays without ever getting tired. Would you be interested in learning how this could help your business serve customers around the clock?
                    </div>

                    <div class="chat-input-enhanced">
                        <input type="text" class="chat-input" placeholder="Type your message here..." readonly>
                        <a href="{{ chat_url }}" class="chat-send-btn" target="_blank">Send</a>
                    </div>

                    <div class="info-tip">
                        💡 <strong>This will open our full chat interface where you can have a complete conversation with Sarah</strong>
                    </div>
                </div>
            </div>

            <p>Sarah will introduce herself, learn about your business needs, and show you exactly how 24Seven Assistants can help you scale your operations. The conversation takes just 5-10 minutes.</p>

            <p><strong>Pro Tip:</strong> Sarah is powered by advanced AI and has helped hundreds of businesses. She's particularly great at understanding unique challenges and providing tailored solutions.</p>

            <p>Ready to see how AI can transform your business operations?</p>

            <div class="signature-section">
                <p style="margin-bottom: 5px;"><strong>Best regards,</strong></p>
                <p style="margin: 5px 0;"><strong>Sarah</strong></p>
                <p style="margin: 5px 0;">24Seven Assistants</p>
                <p style="margin: 5px 0;">📧 <EMAIL></p>
                <p style="margin: 5px 0;">📞 +256 **********</p>
                <p style="margin: 5px 0;">🌐 <a href="http://www.24seven.site">http://www.24seven.site</a></p>
            </div>
        </div>

        <div class="footer-section">
            <p style="margin: 0;">This email was sent as part of our AI sales assistant demonstration.</p>
            <p style="margin: 5px 0 0 0;">Click tracking and conversation analytics are enabled for performance optimization.</p>
        </div>
    </div>

    <!-- Email Open Tracking Pixel -->
    <img src="http://localhost:5000/track/open/{{ session_id }}" width="1" height="1" style="display:none;" alt="">
</body>
</html>
            ''',
            'text_template': '''
Customer Support Solutions for {{ contact_name }}

Hi {{ contact_name }},

I hope this message finds you well. I'm Sarah from 24Seven Assistants, and I wanted to personally reach out about our customer support solutions.

Many businesses struggle with providing round-the-clock customer service. That's where we come in - we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.

OUR SERVICES INCLUDE:
• Customer support and live chat
• Administrative task management
• Lead qualification and follow-up
• Appointment scheduling
• Email and phone support

WHAT MAKES US DIFFERENT:
• Assistants trained specifically for your industry
• Flexible plans that grow with your business
• Easy integration with your current systems
• Competitive pricing in UGX currency

OUR PRICING PLANS:

Small Business Package:
• Setup: UGX 250,000
• Monthly: UGX 100,000
• Perfect for startups and small businesses

Medium Business Package:
• Setup: UGX 500,000
• Monthly: UGX 250,000
• Ideal for growing companies

Large Enterprise Package:
• Setup: UGX 3,000,000
• Monthly: UGX 1,000,000
• Comprehensive solution for large organizations

I'd love to show you how this works. Our assistant Sarah can demonstrate our capabilities and help determine which package would be best for your business.

Start a conversation with Sarah: {{ chat_url }}

Sarah will introduce herself and explain how we can help your specific business needs.

Best regards,
Sarah
24Seven Assistants

Email: <EMAIL>
Website: http://www.24seven.site
Phone: +256 **********

P.S. If you'd prefer to schedule a call instead, Sarah can arrange that too.
            '''
        }

        # Enhanced Follow-up Email Template
        self.templates['followup'] = {
            'name': '24Seven Assistants Follow-up',
            'subject': 'Quick Follow-up: Virtual Assistant Services for {{ company_name }}',
            'html_template': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Follow-up: 24Seven Assistants</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .email-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content-section {
            background: #f9f9f9;
            padding: 40px 30px;
        }

        .highlight-card {
            background: linear-gradient(135deg, #e8f4fd 0%, #bbdefb 100%);
            padding: 25px;
            border-left: 5px solid #2196F3;
            margin: 25px 0;
            border-radius: 10px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 30px;
            margin: 25px 0;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 6px 20px rgba(33,150,243,0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(33,150,243,0.4);
            color: white;
            text-decoration: none;
        }

        .footer-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }

        /* Mobile Responsive Design */
        @media only screen and (max-width: 768px) {
            body {
                max-width: 100%;
                padding: 10px;
                font-size: 14px;
            }

            .header-section {
                padding: 25px 20px;
            }

            .content-section {
                padding: 25px 20px;
            }

            .highlight-card {
                padding: 20px;
                margin: 20px 0;
            }

            .cta-button {
                padding: 15px 25px;
                font-size: 14px;
                display: block;
                text-align: center;
                margin: 20px 0;
            }
        }

        @media only screen and (max-width: 480px) {
            body {
                padding: 5px;
            }

            .header-section {
                padding: 20px 15px;
            }

            .content-section {
                padding: 20px 15px;
            }

            .highlight-card {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header-section">
            <h2 style="margin: 0; font-size: 28px;">24Seven Assistants</h2>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Professional Virtual Assistant Services</p>
        </div>

        <div class="content-section">
            <p style="margin-top: 0;">Hi {{ contact_name }},</p>

            <p>I wanted to follow up on my previous email about how 24Seven Assistants can help {{ company_name }} streamline operations and boost productivity.</p>

            <div class="highlight-card">
                <h3 style="margin-top: 0; color: #1976D2;">🚀 Quick Reminder - What We Offer:</h3>
                <p style="margin-bottom: 0;"><strong>24/7 Virtual Assistant Services</strong> that can save {{ company_name }} up to 70% on operational costs while providing round-the-clock support.</p>
            </div>

            <p>I understand you're busy, so I'll keep this brief. Many of our clients initially hesitated but later told us that partnering with 24Seven Assistants was one of their best business decisions.</p>

            <p><strong>Would a quick 10-minute call work for you this week?</strong> I can show you exactly how we've helped similar companies in {{ industry }} achieve remarkable results.</p>

            <div style="text-align: center;">
                <a href="mailto:{{ reply_email }}?subject=Yes, let's schedule a call - {{ company_name }}" class="cta-button">
                    📞 Let's Talk
                </a>
            </div>

            <p>No pressure - just a friendly conversation about your business needs and how we might be able to help.</p>

            <p>Best regards,<br>
            <strong>{{ agent_name }}</strong><br>
            24Seven Assistants<br>
            {{ reply_email }}</p>
        </div>

        <div class="footer-section">
            <p><small>If you'd prefer not to receive these emails, <a href="#">click here to unsubscribe</a></small></p>
        </div>
    </div>
</body>
</html>
            ''',
            'text_template': '''
Hi {{ contact_name }},

I wanted to follow up on my previous email about how 24Seven Assistants can help {{ company_name }} streamline operations and boost productivity.

QUICK REMINDER - WHAT WE OFFER:
24/7 Virtual Assistant Services that can save {{ company_name }} up to 70% on operational costs while providing round-the-clock support.

I understand you're busy, so I'll keep this brief. Many of our clients initially hesitated but later told us that partnering with 24Seven Assistants was one of their best business decisions.

Would a quick 10-minute call work for you this week? I can show you exactly how we've helped similar companies in {{ industry }} achieve remarkable results.

No pressure - just a friendly conversation about your business needs and how we might be able to help.

Best regards,
{{ agent_name }}
24Seven Assistants
{{ reply_email }}
            '''
        }

    def render_template(self, template_name: str, context: Dict[str, Any]) -> Dict[str, str]:
        """
        Render email template with context variables

        Args:
            template_name: Name of the template to render
            context: Dictionary of variables to substitute

        Returns:
            Dictionary with rendered subject, html_body, and text_body
        """
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")

        template_data = self.templates[template_name]

        # Set default context values
        default_context = {
            'agent_name': 'Sarah',
            'company_name': 'Your Company',
            'contact_name': 'there',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'industry': 'your industry',
            'chat_url': 'http://localhost:5000/chat/session_id_placeholder',
            'session_id': 'session_id_placeholder'
        }

        # Merge with provided context
        render_context = {**default_context, **context}

        # Render templates
        subject_template = Template(template_data['subject'])
        html_template = Template(template_data['html_template'])
        text_template = Template(template_data['text_template'])

        return {
            'subject': subject_template.render(**render_context),
            'html_body': html_template.render(**render_context),
            'text_body': text_template.render(**render_context)
        }

    def get_template_list(self) -> List[Dict[str, str]]:
        """Get list of available templates"""
        return [
            {
                'name': template_name,
                'display_name': template_data['name'],
                'subject': template_data['subject']
            }
            for template_name, template_data in self.templates.items()
        ]

    def add_custom_template(self, name: str, display_name: str, subject: str,
                           html_template: str, text_template: str):
        """Add a custom email template"""
        self.templates[name] = {
            'name': display_name,
            'subject': subject,
            'html_template': html_template,
            'text_template': text_template
        }