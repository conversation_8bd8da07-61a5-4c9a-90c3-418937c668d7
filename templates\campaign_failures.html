{% extends "base.html" %}

{% block title %}Campaign Failures - {{ campaign.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-exclamation-triangle text-warning"></i> Campaign Failures</h2>
                    <p class="text-muted mb-0">{{ campaign.name }}</p>
                </div>
                <div>
                    <a href="{{ url_for('campaigns_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Campaigns
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Summary -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Campaign Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ campaign.total_recipients }}</h4>
                                <small class="text-muted">Total Recipients</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ campaign.emails_sent }}</h4>
                                <small class="text-muted">Emails Sent</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger">{{ campaign.emails_failed }}</h4>
                                <small class="text-muted">Emails Failed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ campaign.retry_count }}/{{ campaign.max_retries }}</h4>
                                <small class="text-muted">Retry Attempts</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    {% if campaign.status in ['failed', 'partial_failure'] and campaign.retry_count < campaign.max_retries %}
                    <form action="{{ url_for('retry_campaign', campaign_id=campaign.id) }}" method="POST" class="mb-2">
                        <input type="hidden" name="retry_type" value="failed_only">
                        <button type="submit" class="btn btn-warning btn-sm w-100" onclick="return confirm('Retry only failed emails?')">
                            <i class="fas fa-redo"></i> Retry Failed Emails
                        </button>
                    </form>
                    {% endif %}

                    <form action="{{ url_for('skip_failed_emails', campaign_id=campaign.id) }}" method="POST" class="mb-2">
                        <button type="submit" class="btn btn-outline-secondary btn-sm w-100" onclick="return confirm('Skip all failed emails and mark campaign as completed?')">
                            <i class="fas fa-forward"></i> Skip Failed & Complete
                        </button>
                    </form>

                    <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-eye"></i> View Campaign Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Failure Statistics -->
    {% if failure_stats %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Failure Statistics by Error Type
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for error_type, stats in failure_stats.items() %}
                        <div class="col-md-2 col-sm-4 col-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <h5 class="text-danger">{{ stats.count }}</h5>
                                <small class="text-muted">{{ error_type.replace('_', ' ').title() }}</small>
                                <br>
                                <small class="text-success">{{ stats.resolved }} resolved</small>
                                <br>
                                <small class="text-warning">{{ stats.unresolved }} pending</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Failures List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Detailed Failure Log
                    </h5>
                </div>
                <div class="card-body">
                    {% if failures %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Contact</th>
                                    <th>Email</th>
                                    <th>Error Type</th>
                                    <th>Error Message</th>
                                    <th>Retry Count</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Last Retry</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for failure in failures %}
                                <tr class="{% if failure.resolved %}table-success{% else %}table-warning{% endif %}">
                                    <td>
                                        {% if failure.contact %}
                                        <a href="{{ url_for('view_contact', contact_id=failure.contact.id) }}" class="text-decoration-none">
                                            {{ failure.contact.full_name }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted">Unknown Contact</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="font-monospace text-info">{{ failure.recipient_email }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if failure.error_type == 'invalid_email' %}danger{% elif failure.error_type == 'connection_error' %}warning{% elif failure.error_type == 'auth_error' %}info{% elif failure.error_type == 'rate_limit' %}secondary{% elif failure.error_type == 'blocked' %}dark{% else %}primary{% endif %}">
                                            {{ failure.error_type.replace('_', ' ').title() if failure.error_type else 'Unknown' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-danger fw-bold" title="{{ failure.error_message }}">
                                            {{ failure.error_message[:50] + '...' if failure.error_message and failure.error_message|length > 50 else failure.error_message or 'No details' }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ failure.retry_count }}</span>
                                    </td>
                                    <td>
                                        {% if failure.resolved %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Resolved
                                        </span>
                                        {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock"></i> Pending
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-light">{{ failure.created_at.strftime('%Y-%m-%d %H:%M') if failure.created_at else '-' }}</small>
                                    </td>
                                    <td>
                                        <small class="text-light">{{ failure.last_retry_at.strftime('%Y-%m-%d %H:%M') if failure.last_retry_at else '-' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h4 class="text-success">No Failures Found</h4>
                        <p class="text-muted">This campaign has no email failures to display.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.font-monospace {
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}
