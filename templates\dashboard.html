{% extends "base.html" %}

{% block title %}Dashboard - 24Seven Assistants Sales Department{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt text-primary"></i> Sales Dashboard</h1>
    <div class="text-muted">
        <i class="fas fa-clock"></i> Last updated: <span id="lastUpdated">Loading...</span>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ total_contacts }}</div>
                <div class="metric-label">Total Contacts</div>
                <div class="mt-2">
                    <a href="{{ url_for('contacts_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ total_opportunities }}</div>
                <div class="metric-label">Opportunities</div>
                <div class="mt-2">
                    <a href="{{ url_for('opportunities_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">{{ active_campaigns }}</div>
                <div class="metric-label">Active Campaigns</div>
                <div class="mt-2">
                    <a href="{{ url_for('campaigns_list') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="metric-value">5</div>
                <div class="metric-label">Sales Stages</div>
                <div class="mt-2">
                    <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{{ url_for('add_contact') }}" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-user-plus"></i> Add Contact
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('create_campaign') }}" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-envelope-open-text"></i> Create Campaign
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-chart-bar"></i> View Analytics
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="testSMTP()">
                            <i class="fas fa-cog"></i> Test SMTP
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Process Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-funnel-dollar"></i> Sales Process Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-door-open fa-2x text-primary mb-2"></i>
                            <h6>Opening</h6>
                            <small class="text-muted">Initial Contact</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-handshake fa-2x text-success mb-2"></i>
                            <h6>Trust</h6>
                            <small class="text-muted">Build Rapport</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-search fa-2x text-info mb-2"></i>
                            <h6>Discovery</h6>
                            <small class="text-muted">Understand Needs</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-presentation fa-2x text-warning mb-2"></i>
                            <h6>Demonstration</h6>
                            <small class="text-muted">Show Solution</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-trophy fa-2x text-danger mb-2"></i>
                            <h6>Close</h6>
                            <small class="text-muted">Win the Deal</small>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-success text-white rounded">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h6>Customer</h6>
                            <small>Success!</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history"></i> Recent Activities</h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Subject</th>
                                    <th>Contact</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>
                                        {% if activity.activity_type == 'email' %}
                                            <i class="fas fa-envelope text-primary"></i>
                                        {% elif activity.activity_type == 'call' %}
                                            <i class="fas fa-phone text-success"></i>
                                        {% elif activity.activity_type == 'meeting' %}
                                            <i class="fas fa-calendar text-info"></i>
                                        {% else %}
                                            <i class="fas fa-sticky-note text-warning"></i>
                                        {% endif %}
                                        {{ activity.activity_type.title() }}
                                    </td>
                                    <td>{{ activity.subject or 'No subject' }}</td>
                                    <td>
                                        {% if activity.contact %}
                                            {{ activity.contact.full_name }}
                                        {% else %}
                                            <span class="text-muted">No contact</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ activity.created_at.strftime('%m/%d/%Y %H:%M') }}</td>
                                    <td>
                                        <span class="status-badge status-{{ activity.status }}">
                                            {{ activity.status.title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activities found.</p>
                        <a href="{{ url_for('add_contact') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Your First Contact
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-rocket text-primary"></i> 24Seven Assistants</h6>
                    <p class="small text-muted">Professional virtual assistant services available 24/7</p>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-cogs text-success"></i> Features Active</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success"></i> SMTP Email System</li>
                        <li><i class="fas fa-check text-success"></i> Sales Stage Tracking</li>
                        <li><i class="fas fa-check text-success"></i> Analytics Dashboard</li>
                        <li><i class="fas fa-check text-success"></i> Contact Management</li>
                        <li><i class="fas fa-check text-success"></i> Campaign Management</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6><i class="fas fa-chart-line text-info"></i> Quick Stats</h6>
                    <ul class="list-unstyled small">
                        <li>Contacts: {{ total_contacts }}</li>
                        <li>Opportunities: {{ total_opportunities }}</li>
                        <li>Active Campaigns: {{ active_campaigns }}</li>
                        <li>Sales Stages: 5</li>
                    </ul>
                </div>

                <div class="text-center">
                    <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-bar"></i> View Full Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update the last updated time
    document.addEventListener('DOMContentLoaded', function() {
        const now = new Date();
        const timeString = now.toLocaleString();
        const lastUpdatedElement = document.getElementById('lastUpdated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = timeString;
        }
    });
</script>
{% endblock %}
