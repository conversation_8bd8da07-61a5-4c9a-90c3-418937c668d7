#!/usr/bin/env python3
"""
Simple SMTP Test
===============
Quick test to verify SMTP connection with the updated password.
"""

import smtplib
import ssl
from email.mime.text import MIMEText

def test_smtp():
    """Test SMTP connection"""
    print("🔍 Testing SMTP Connection...")
    
    # Configuration from .env file
    config = {
        'server': 'smtp.gmail.com',
        'port': 587,
        'username': '<EMAIL>',
        'password': 'wkff biod dzyv obon',
    }
    
    try:
        # Create connection
        server = smtplib.SMTP(config['server'], config['port'], timeout=30)
        server.starttls(context=ssl.create_default_context())
        print("✅ TLS connection established")
        
        # Authenticate
        server.login(config['username'], config['password'])
        print("✅ Authentication successful!")
        
        # Send test email
        msg = MIMEText("SMTP test successful! Email campaigns should now work.")
        msg['Subject'] = "SMTP Test - 24Seven Assistants"
        msg['From'] = config['username']
        msg['To'] = config['username']
        
        server.send_message(msg)
        print("✅ Test email sent!")
        
        server.quit()
        print("🎉 SMTP TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ SMTP test failed: {e}")
        return False

if __name__ == "__main__":
    test_smtp()
