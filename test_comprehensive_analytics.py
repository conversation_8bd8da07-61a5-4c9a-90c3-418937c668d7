#!/usr/bin/env python3
"""
Test Comprehensive Analytics Route
=================================
This script tests the comprehensive analytics route directly to identify the issue.
"""

import requests
import json

def test_comprehensive_analytics():
    """Test the comprehensive analytics endpoint"""
    print("🧪 Testing Comprehensive Analytics Route")
    print("=" * 50)
    
    try:
        # Test the comprehensive analytics endpoint
        response = requests.get('http://localhost:5000/analytics/comprehensive', allow_redirects=False)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 302:
            print(f"❌ Redirected to: {response.headers.get('Location')}")
            print("This indicates an error in the comprehensive analytics function")
            
            # Try to get the error from the regular analytics page
            print("\n🔍 Checking regular analytics page for comparison...")
            analytics_response = requests.get('http://localhost:5000/analytics')
            if analytics_response.status_code == 200:
                print("✅ Regular analytics page works fine")
            else:
                print(f"❌ Regular analytics also failing: {analytics_response.status_code}")
                
        elif response.status_code == 200:
            print("✅ Comprehensive analytics loaded successfully!")
            content = response.text
            if "Error loading comprehensive analytics" in content:
                print("⚠️ Page loaded but contains error message")
            else:
                print("✅ Page loaded without error messages")
                
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_individual_analytics_endpoints():
    """Test each analytics function endpoint individually"""
    print("\n🔍 Testing Individual Analytics Functions")
    print("=" * 50)
    
    endpoints = [
        '/analytics',
        '/analytics/sessions', 
        '/analytics/sales-pipeline',
        '/analytics/sales-cycle'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://localhost:5000{endpoint}', allow_redirects=False)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"{status} {endpoint}: {response.status_code}")
            
            if response.status_code == 302:
                print(f"   Redirected to: {response.headers.get('Location')}")
                
        except Exception as e:
            print(f"❌ {endpoint}: Error - {e}")

def check_server_logs():
    """Check if we can get any error information"""
    print("\n📋 Checking Server Status")
    print("=" * 30)
    
    try:
        # Test basic server health
        response = requests.get('http://localhost:5000/')
        if response.status_code == 200:
            print("✅ Server is running and responding")
        else:
            print(f"⚠️ Server responding with status: {response.status_code}")
            
        # Test if debug endpoints are available
        debug_response = requests.get('http://localhost:5000/debug', allow_redirects=False)
        if debug_response.status_code == 200:
            print("✅ Debug endpoints are available")
        else:
            print(f"⚠️ Debug endpoints status: {debug_response.status_code}")
            
    except Exception as e:
        print(f"❌ Server check failed: {e}")

if __name__ == "__main__":
    print("🐛 COMPREHENSIVE ANALYTICS DEBUGGING")
    print("=" * 60)
    
    check_server_logs()
    test_individual_analytics_endpoints()
    test_comprehensive_analytics()
    
    print("\n💡 RECOMMENDATIONS")
    print("=" * 20)
    print("1. Check server logs for specific error messages")
    print("2. Verify all required templates exist")
    print("3. Check for missing imports or dependencies")
    print("4. Test analytics functions individually")
