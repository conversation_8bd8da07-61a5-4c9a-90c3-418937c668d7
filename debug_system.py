"""
24Seven Assistants Sales Department - Enhanced Debugging System
==============================================================
Comprehensive debugging, logging, and monitoring system for the sales department.
"""

import os
import sys
import time
import json
import traceback
import logging
import logging.handlers
import functools
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from flask import Flask, request, g, jsonify, render_template_string
from flask_sqlalchemy import SQLAlchemy
from werkzeug.exceptions import HTTPException
import sqlite3

class DebugConfig:
    """Debug system configuration"""

    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()
    LOG_FILE = os.environ.get('LOG_FILE', 'sales_department.log')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5

    # Debug Features
    ENABLE_REQUEST_LOGGING = os.environ.get('DEBUG_REQUESTS', 'true').lower() == 'true'
    ENABLE_SQL_LOGGING = os.environ.get('DEBUG_SQL', 'true').lower() == 'true'
    ENABLE_PERFORMANCE_MONITORING = os.environ.get('DEBUG_PERFORMANCE', 'true').lower() == 'true'
    ENABLE_ERROR_TRACKING = os.environ.get('DEBUG_ERRORS', 'true').lower() == 'true'

    # Performance Thresholds
    SLOW_REQUEST_THRESHOLD = float(os.environ.get('SLOW_REQUEST_THRESHOLD', '2.0'))  # seconds
    SLOW_QUERY_THRESHOLD = float(os.environ.get('SLOW_QUERY_THRESHOLD', '1.0'))  # seconds

class DebugLogger:
    """Enhanced logging system with multiple handlers"""

    def __init__(self, app: Flask = None):
        self.app = app
        self.loggers = {}
        if app:
            self.init_app(app)

    def init_app(self, app: Flask):
        """Initialize logging for Flask app"""
        self.app = app

        # Configure root logger in an idempotent way to avoid duplicate handlers
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, DebugConfig.LOG_LEVEL))

        # Build formatter once
        formatter = logging.Formatter(DebugConfig.LOG_FORMAT)

        # Attach a stdout stream handler if not already present
        if not any(isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler) for h in root_logger.handlers):
            stream_handler = logging.StreamHandler(sys.stdout)
            stream_handler.setFormatter(formatter)
            root_logger.addHandler(stream_handler)

        # Attach (or reuse) a rotating file handler for the main log file
        if not any(isinstance(h, logging.handlers.RotatingFileHandler) and getattr(h, 'baseFilename', None) == os.path.abspath(DebugConfig.LOG_FILE) for h in root_logger.handlers):
            # delay=True keeps the file closed until the first emit, helping release the handle before rollover
            file_handler = logging.handlers.RotatingFileHandler(
                DebugConfig.LOG_FILE,
                maxBytes=DebugConfig.LOG_MAX_BYTES,
                backupCount=DebugConfig.LOG_BACKUP_COUNT,
                encoding='utf-8',
                delay=True
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)

        # Configure Flask app logger
        app.logger.setLevel(getattr(logging, DebugConfig.LOG_LEVEL))

        # Configure SQLAlchemy logging if enabled
        if DebugConfig.ENABLE_SQL_LOGGING:
            logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
            logging.getLogger('sqlalchemy.pool').setLevel(logging.INFO)

    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a named logger"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            logger.setLevel(getattr(logging, DebugConfig.LOG_LEVEL))
            self.loggers[name] = logger
        return self.loggers[name]

class RequestDebugger:
    """Request/Response debugging and monitoring"""

    def __init__(self, app: Flask = None):
        self.app = app
        self.request_logs = []
        self.max_logs = 1000
        if app:
            self.init_app(app)

    def init_app(self, app: Flask):
        """Initialize request debugging"""
        self.app = app

        if DebugConfig.ENABLE_REQUEST_LOGGING:
            app.before_request(self.before_request)
            app.after_request(self.after_request)

    def before_request(self):
        """Log request start"""
        g.start_time = time.time()
        g.request_id = f"{int(time.time() * 1000)}-{id(request)}"

        if DebugConfig.ENABLE_REQUEST_LOGGING:
            self.app.logger.info(f"[{g.request_id}] {request.method} {request.url} - Started")

            # Log request headers and data for debugging
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    if request.is_json:
                        self.app.logger.debug(f"[{g.request_id}] Request JSON: {request.get_json()}")
                    elif request.form:
                        self.app.logger.debug(f"[{g.request_id}] Request Form: {dict(request.form)}")
                except Exception as e:
                    self.app.logger.warning(f"[{g.request_id}] Could not log request data: {e}")

    def after_request(self, response):
        """Log request completion"""
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            request_id = getattr(g, 'request_id', 'unknown')

            # Log completion
            if DebugConfig.ENABLE_REQUEST_LOGGING:
                self.app.logger.info(
                    f"[{request_id}] {request.method} {request.url} - "
                    f"Completed in {duration:.3f}s with status {response.status_code}"
                )

            # Track slow requests
            if DebugConfig.ENABLE_PERFORMANCE_MONITORING and duration > DebugConfig.SLOW_REQUEST_THRESHOLD:
                self.app.logger.warning(
                    f"[{request_id}] SLOW REQUEST: {request.method} {request.url} "
                    f"took {duration:.3f}s (threshold: {DebugConfig.SLOW_REQUEST_THRESHOLD}s)"
                )

            # Store request log
            self._store_request_log(request_id, duration, response.status_code)

        return response

    def _store_request_log(self, request_id: str, duration: float, status_code: int):
        """Store request log for debugging dashboard"""
        log_entry = {
            'id': request_id,
            'timestamp': datetime.utcnow().isoformat(),
            'method': request.method,
            'url': request.url,
            'duration': duration,
            'status_code': status_code,
            'user_agent': request.headers.get('User-Agent', ''),
            'ip_address': request.remote_addr
        }

        self.request_logs.append(log_entry)

        # Keep only recent logs
        if len(self.request_logs) > self.max_logs:
            self.request_logs = self.request_logs[-self.max_logs:]

class ErrorTracker:
    """Enhanced error tracking and reporting"""

    def __init__(self, app: Flask = None):
        self.app = app
        self.error_logs = []
        self.max_errors = 500
        if app:
            self.init_app(app)

    def init_app(self, app: Flask):
        """Initialize error tracking"""
        self.app = app

        if DebugConfig.ENABLE_ERROR_TRACKING:
            app.register_error_handler(Exception, self.handle_exception)
            app.register_error_handler(HTTPException, self.handle_http_exception)

    def handle_exception(self, error):
        """Handle uncaught exceptions"""
        error_id = f"ERR-{int(time.time() * 1000)}"

        error_info = {
            'id': error_id,
            'timestamp': datetime.utcnow().isoformat(),
            'type': type(error).__name__,
            'message': str(error),
            'traceback': traceback.format_exc(),
            'request_url': request.url if request else 'N/A',
            'request_method': request.method if request else 'N/A',
            'user_agent': request.headers.get('User-Agent', '') if request else '',
            'ip_address': request.remote_addr if request else ''
        }

        # Log the error
        self.app.logger.error(f"[{error_id}] Unhandled exception: {error}", exc_info=True)

        # Store error for debugging
        self._store_error(error_info)

        # Return JSON error for API endpoints, HTML for web pages
        if request and request.path.startswith('/api/'):
            return jsonify({
                'error': 'Internal server error',
                'error_id': error_id,
                'message': str(error) if self.app.debug else 'An error occurred'
            }), 500
        else:
            return render_template_string("""
            <h1>Error Occurred</h1>
            <p>Error ID: {{ error_id }}</p>
            <p>{{ message }}</p>
            <a href="/">Return to Dashboard</a>
            """, error_id=error_id, message=str(error) if self.app.debug else 'An error occurred'), 500

    def handle_http_exception(self, error):
        """Handle HTTP exceptions"""
        error_id = f"HTTP-{int(time.time() * 1000)}"

        self.app.logger.warning(f"[{error_id}] HTTP {error.code}: {error.description}")

        if request and request.path.startswith('/api/'):
            return jsonify({
                'error': error.description,
                'error_id': error_id,
                'status_code': error.code
            }), error.code
        else:
            return error

    def _store_error(self, error_info: Dict[str, Any]):
        """Store error for debugging dashboard"""
        self.error_logs.append(error_info)

        # Keep only recent errors
        if len(self.error_logs) > self.max_errors:
            self.error_logs = self.error_logs[-self.max_errors:]

class PerformanceMonitor:
    """Performance monitoring and profiling"""

    def __init__(self, app: Flask = None):
        self.app = app
        self.performance_logs = []
        self.max_logs = 1000
        if app:
            self.init_app(app)

    def init_app(self, app: Flask):
        """Initialize performance monitoring"""
        self.app = app

    def monitor_function(self, func_name: str = None):
        """Decorator to monitor function performance"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if not DebugConfig.ENABLE_PERFORMANCE_MONITORING:
                    return func(*args, **kwargs)

                start_time = time.time()
                name = func_name or f"{func.__module__}.{func.__name__}"

                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start_time

                    # Log slow functions
                    if duration > DebugConfig.SLOW_QUERY_THRESHOLD:
                        self.app.logger.warning(f"SLOW FUNCTION: {name} took {duration:.3f}s")

                    # Store performance log
                    self._store_performance_log(name, duration, True)

                    return result

                except Exception as e:
                    duration = time.time() - start_time
                    self._store_performance_log(name, duration, False, str(e))
                    raise

            return wrapper
        return decorator

    def _store_performance_log(self, function_name: str, duration: float, success: bool, error: str = None):
        """Store performance log"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'function_name': function_name,
            'duration': duration,
            'success': success,
            'error': error
        }

        self.performance_logs.append(log_entry)

        # Keep only recent logs
        if len(self.performance_logs) > self.max_logs:
            self.performance_logs = self.performance_logs[-self.max_logs:]

# Global debug system instances
debug_logger = DebugLogger()
request_debugger = RequestDebugger()
error_tracker = ErrorTracker()
performance_monitor = PerformanceMonitor()

def init_debug_system(app: Flask):
    """Initialize the complete debug system"""
    debug_logger.init_app(app)
    request_debugger.init_app(app)
    error_tracker.init_app(app)
    performance_monitor.init_app(app)

    app.logger.info("DEBUG: Debug system initialized")
    app.logger.info(f"DEBUG: Request logging: {'enabled' if DebugConfig.ENABLE_REQUEST_LOGGING else 'disabled'}")
    app.logger.info(f"DEBUG: SQL logging: {'enabled' if DebugConfig.ENABLE_SQL_LOGGING else 'disabled'}")
    app.logger.info(f"DEBUG: Performance monitoring: {'enabled' if DebugConfig.ENABLE_PERFORMANCE_MONITORING else 'disabled'}")
    app.logger.info(f"DEBUG: Error tracking: {'enabled' if DebugConfig.ENABLE_ERROR_TRACKING else 'disabled'}")

def get_debug_stats() -> Dict[str, Any]:
    """Get current debug system statistics"""
    return {
        'request_logs_count': len(request_debugger.request_logs),
        'error_logs_count': len(error_tracker.error_logs),
        'performance_logs_count': len(performance_monitor.performance_logs),
        'recent_requests': request_debugger.request_logs[-10:],
        'recent_errors': error_tracker.error_logs[-5:],
        'slow_functions': [
            log for log in performance_monitor.performance_logs[-50:]
            if log['duration'] > DebugConfig.SLOW_QUERY_THRESHOLD
        ]
    }
