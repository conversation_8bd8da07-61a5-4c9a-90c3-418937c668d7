#!/usr/bin/env python3
"""
Standalone IMAP Test
===================
Direct IMAP test without Flask dependencies.
"""

import imaplib
import email
import ssl
from datetime import datetime, timedelta
from email.header import decode_header
import re

def test_imap_connection():
    """Test IMAP connection directly"""
    print("🔌 Testing IMAP Connection...")
    print("-" * 40)
    
    # IMAP configuration
    config = {
        'server': 'mail.24seven.site',
        'port': 993,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'use_ssl': True
    }
    
    print(f"Server: {config['server']}:{config['port']}")
    print(f"Username: {config['username']}")
    print(f"SSL: {config['use_ssl']}")
    print()
    
    try:
        # Create IMAP connection
        if config['use_ssl']:
            context = ssl.create_default_context()
            server = imaplib.IMAP4_SSL(config['server'], config['port'], ssl_context=context)
        else:
            server = imaplib.IMAP4(config['server'], config['port'])
            server.starttls()
        
        print("✅ Connection established")
        
        # Login
        server.login(config['username'], config['password'])
        print("✅ Authentication successful")
        
        return True, server
        
    except imaplib.IMAP4.error as e:
        print(f"❌ IMAP error: {e}")
        return False, None
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False, None

def get_folder_list(server):
    """Get list of available folders"""
    print("\n📁 Getting Folder List...")
    print("-" * 40)
    
    try:
        status, folders = server.list()
        if status == 'OK':
            folder_names = []
            print("Available folders:")
            for i, folder in enumerate(folders, 1):
                folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else folder
                # Extract folder name (usually the last quoted part)
                match = re.search(r'"([^"]*)"$', folder_str)
                if match:
                    folder_name = match.group(1)
                    folder_names.append(folder_name)
                    print(f"   {i}. {folder_name}")
                else:
                    print(f"   {i}. {folder_str}")
            
            return folder_names
        else:
            print("❌ Failed to get folder list")
            return []
            
    except Exception as e:
        print(f"❌ Error getting folders: {e}")
        return []

def test_sent_folder_access(server):
    """Test access to sent folder"""
    print("\n📧 Testing Sent Folder Access...")
    print("-" * 40)
    
    # Try different common sent folder names
    sent_folders = ['Sent', 'INBOX.Sent', 'Sent Items', 'Sent Messages', 'Outbox']
    
    for folder in sent_folders:
        try:
            print(f"Trying folder: {folder}")
            status, count = server.select(folder)
            if status == 'OK':
                message_count = int(count[0])
                print(f"✅ Successfully accessed '{folder}' with {message_count} messages")
                return folder, message_count
            else:
                print(f"   ❌ Cannot access '{folder}'")
        except Exception as e:
            print(f"   ❌ Error accessing '{folder}': {e}")
    
    print("⚠️ Could not access any sent folder")
    return None, 0

def get_recent_emails(server, folder_name, limit=5):
    """Get recent emails from specified folder"""
    print(f"\n📬 Getting Recent Emails from '{folder_name}'...")
    print("-" * 40)
    
    try:
        # Select folder
        status, count = server.select(folder_name)
        if status != 'OK':
            print(f"❌ Cannot select folder '{folder_name}'")
            return []
        
        # Search for recent emails
        since_date = (datetime.now() - timedelta(days=30)).strftime("%d-%b-%Y")
        search_criteria = f'(SINCE "{since_date}")'
        
        status, messages = server.search(None, search_criteria)
        if status != 'OK':
            print("⚠️ Search failed, trying to get all messages")
            status, messages = server.search(None, 'ALL')
        
        if status != 'OK':
            print("❌ Cannot search messages")
            return []
        
        message_ids = messages[0].split()
        
        if not message_ids:
            print("⚠️ No messages found")
            return []
        
        # Limit the number of messages
        if len(message_ids) > limit:
            message_ids = message_ids[-limit:]  # Get most recent
        
        print(f"Found {len(message_ids)} recent messages, retrieving {len(message_ids)}...")
        
        emails = []
        for i, msg_id in enumerate(reversed(message_ids), 1):
            try:
                print(f"   Fetching email {i}/{len(message_ids)}...")
                
                # Fetch email
                status, msg_data = server.fetch(msg_id, '(RFC822)')
                if status != 'OK':
                    continue
                
                # Parse email
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                
                # Extract headers
                subject = decode_header_safe(email_message['Subject'])
                from_addr = decode_header_safe(email_message['From'])
                to_addr = decode_header_safe(email_message['To'])
                date_str = email_message['Date']
                message_id = email_message['Message-ID']
                
                # Parse date
                try:
                    email_date = email.utils.parsedate_to_datetime(date_str)
                except:
                    email_date = datetime.now()
                
                # Extract body content
                body_text, body_html = extract_body(email_message)
                
                email_data = {
                    'subject': subject,
                    'from': from_addr,
                    'to': to_addr,
                    'date': email_date,
                    'message_id': message_id,
                    'body_text': body_text[:200] + '...' if body_text and len(body_text) > 200 else body_text,
                    'body_html': body_html[:200] + '...' if body_html and len(body_html) > 200 else body_html
                }
                
                emails.append(email_data)
                
            except Exception as e:
                print(f"   ❌ Error fetching email {i}: {e}")
                continue
        
        return emails
        
    except Exception as e:
        print(f"❌ Error getting emails: {e}")
        return []

def decode_header_safe(header):
    """Safely decode email header"""
    if not header:
        return ""
    
    try:
        decoded_parts = decode_header(header)
        decoded_string = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    decoded_string += part.decode(encoding)
                else:
                    decoded_string += part.decode('utf-8', errors='ignore')
            else:
                decoded_string += part
        
        return decoded_string
    except:
        return str(header)

def extract_body(email_message):
    """Extract text and HTML body from email"""
    body_text = ""
    body_html = ""
    
    try:
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                # Skip attachments
                if "attachment" in content_disposition:
                    continue
                
                if content_type == "text/plain":
                    body_text = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    body_html = part.get_payload(decode=True).decode('utf-8', errors='ignore')
        else:
            content_type = email_message.get_content_type()
            if content_type == "text/plain":
                body_text = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            elif content_type == "text/html":
                body_html = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
    
    except Exception as e:
        print(f"   ⚠️ Error extracting body: {e}")
    
    return body_text, body_html

def main():
    """Main test function"""
    print("🧪 24Seven Assistants - Standalone IMAP Test")
    print("=" * 60)
    print("Testing IMAP connection and email retrieval...")
    print()
    
    # Test connection
    success, server = test_imap_connection()
    
    if not success:
        print("\n❌ Cannot proceed without IMAP connection")
        return
    
    try:
        # Get folder list
        folders = get_folder_list(server)
        
        # Test sent folder access
        sent_folder, message_count = test_sent_folder_access(server)
        
        if sent_folder:
            # Get recent emails
            emails = get_recent_emails(server, sent_folder, limit=5)
            
            if emails:
                print(f"\n📧 Retrieved {len(emails)} emails:")
                print("-" * 40)
                
                for i, email_data in enumerate(emails, 1):
                    print(f"\nEmail {i}:")
                    print(f"   Subject: {email_data['subject']}")
                    print(f"   From: {email_data['from']}")
                    print(f"   To: {email_data['to']}")
                    print(f"   Date: {email_data['date']}")
                    
                    if email_data['body_text']:
                        print(f"   Text: {email_data['body_text'][:100]}...")
                    elif email_data['body_html']:
                        # Strip HTML for preview
                        text_preview = re.sub('<[^<]+?>', '', email_data['body_html'])
                        print(f"   HTML: {text_preview[:100]}...")
        
        # Close connection
        server.close()
        server.logout()
        
        print("\n" + "=" * 60)
        print("📊 IMAP TEST RESULTS")
        print("=" * 60)
        print(f"✅ IMAP connection: Working")
        print(f"✅ Folder access: {len(folders)} folders found")
        if sent_folder:
            print(f"✅ Sent folder: '{sent_folder}' with {message_count} messages")
            print(f"✅ Email retrieval: {len(emails) if 'emails' in locals() else 0} emails retrieved")
        else:
            print(f"⚠️ Sent folder: Not found or inaccessible")
        
        print("\n🎉 IMAP functionality test completed!")
        print("\n💡 You can now use the web interface at:")
        print("   http://localhost:5000/emails/sent")
        
    except Exception as e:
        print(f"\n❌ Test error: {e}")
    finally:
        try:
            server.close()
            server.logout()
        except:
            pass

if __name__ == "__main__":
    main()
