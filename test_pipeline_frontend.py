#!/usr/bin/env python3
"""
Simple test to demonstrate the sales pipeline analytics frontend
"""

import requests
import json
import time

def test_pipeline_frontend():
    """Test the sales pipeline analytics with a simple session"""
    
    base_url = 'http://localhost:5000/api/track-session'
    
    print('🚀 Testing Sales Pipeline Analytics Frontend')
    print('=' * 50)
    
    # Create a simple test session that progresses through all stages
    session_data = {
        'session_id': 'frontend_pipeline_test',
        'contact_name': 'Pipeline Test User',
        'contact_email': '<EMAIL>'
    }
    
    stages = [
        {'stage': 'opening', 'task': 'greeting', 'message': 'Hello! I\'m interested in your services.'},
        {'stage': 'trust', 'task': 'building_rapport', 'message': 'Tell me more about your company.'},
        {'stage': 'discovery', 'task': 'needs_analysis', 'message': 'We need a solution for our team of 50.'},
        {'stage': 'demonstration', 'task': 'showing_features', 'message': 'Can you show me how this works?'},
        {'stage': 'close', 'task': 'asking_for_commitment', 'message': 'This looks perfect for us!'}
    ]
    
    message_count = 0
    
    for i, stage_info in enumerate(stages):
        print(f'\n📊 Stage {i+1}: {stage_info["stage"].title()}')
        
        message_count += 2
        
        # Track stage progression
        data = {
            **session_data,
            'stage': stage_info['stage'],
            'task': stage_info['task'],
            'action': 'stage_progression',
            'message_count': message_count,
            'user_message': stage_info['message'],
            'bot_response': f'Thank you for that. Let me help you with {stage_info["stage"]}.'
        }
        
        try:
            response = requests.post(base_url, json=data, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f'  ✅ {stage_info["stage"].title()} stage tracked successfully')
                    print(f'     Messages: {result.get("total_messages", "N/A")}')
                    print(f'     Engagement: {result.get("engagement_level", "N/A")}')
                else:
                    print(f'  ❌ API error: {result.get("message", "Unknown error")}')
            else:
                print(f'  ❌ HTTP {response.status_code}: {response.text[:100]}')
        except Exception as e:
            print(f'  ❌ Request failed: {str(e)}')
        
        # Small delay between stages
        time.sleep(1)
    
    # Complete the conversion
    print(f'\n🎉 Completing Conversion')
    conversion_data = {
        **session_data,
        'stage': 'close',
        'task': 'conversion_completed',
        'action': 'stage_progression',
        'conversion_completed': True,
        'conversion_value': 2500.00,
        'message_count': message_count + 2
    }
    
    try:
        response = requests.post(base_url, json=conversion_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f'  ✅ Conversion completed successfully!')
                print(f'     Value: $2,500.00')
            else:
                print(f'  ❌ Conversion failed: {result.get("message", "Unknown error")}')
        else:
            print(f'  ❌ HTTP {response.status_code}: {response.text[:100]}')
    except Exception as e:
        print(f'  ❌ Conversion request failed: {str(e)}')
    
    print('\n' + '=' * 50)
    print('🎉 Frontend test completed!')
    print('\n📊 Now you can view the analytics dashboards:')
    print('   • Sales Pipeline: http://localhost:5000/analytics/sales-pipeline')
    print('   • Sales Cycle: http://localhost:5000/analytics/sales-cycle')
    print('   • Session Analytics: http://localhost:5000/analytics/sessions')
    print('   • Main Analytics: http://localhost:5000/analytics')
    print('\n🔧 Use the frontend test page to create more test data:')
    print('   • Test Interface: file:///c:/Users/<USER>/Downloads/testsales/test_frontend.html')

if __name__ == '__main__':
    test_pipeline_frontend()
