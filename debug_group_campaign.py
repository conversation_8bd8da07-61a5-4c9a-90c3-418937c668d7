#!/usr/bin/env python3
"""
Debug Group Campaign Issue
==========================
Debug why group selection is not working properly in campaigns.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_group_campaign():
    """Debug the group campaign issue"""
    try:
        from unified_sales_system import app, db, EmailCampaign, ContactGroup, ContactGroupMembership, Contact, get_campaign_contacts
        import json
        
        with app.app_context():
            print("🔍 DEBUGGING GROUP CAMPAIGN ISSUE")
            print("=" * 50)
            
            # 1. Check all groups
            print("\n📊 ALL CONTACT GROUPS:")
            groups = ContactGroup.query.all()
            for group in groups:
                member_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()
                print(f"   ID: {group.id}, Name: '{group.name}', Members: {member_count}")
            
            # 2. Check all campaigns
            print("\n📋 ALL CAMPAIGNS:")
            campaigns = EmailCampaign.query.all()
            for campaign in campaigns:
                print(f"   ID: {campaign.id}, Name: '{campaign.name}'")
                print(f"   Recipient Criteria: {campaign.recipient_criteria}")
                
                # Test get_campaign_contacts function
                try:
                    contacts = get_campaign_contacts(campaign)
                    print(f"   Contacts returned by get_campaign_contacts: {len(contacts)}")
                    if len(contacts) <= 5:
                        for contact in contacts:
                            print(f"     - {contact.first_name} {contact.last_name} ({contact.email})")
                    else:
                        print(f"     - First 3 contacts:")
                        for contact in contacts[:3]:
                            print(f"       - {contact.first_name} {contact.last_name} ({contact.email})")
                        print(f"     - ... and {len(contacts) - 3} more")
                except Exception as e:
                    print(f"   ERROR in get_campaign_contacts: {e}")
                
                print("   " + "-" * 40)
            
            # 3. Test specific group filtering
            print("\n🧪 TESTING GROUP FILTERING:")
            test_group = ContactGroup.query.filter_by(name="test").first()
            if test_group:
                print(f"Found 'test' group (ID: {test_group.id})")
                
                # Get contacts in this group using the same logic as get_campaign_contacts
                contact_ids = db.session.query(ContactGroupMembership.contact_id).filter(
                    ContactGroupMembership.group_id == test_group.id
                ).distinct().all()
                contact_ids = [id[0] for id in contact_ids]
                
                print(f"Contact IDs in group: {contact_ids}")
                
                if contact_ids:
                    contacts_in_group = Contact.query.filter(
                        Contact.id.in_(contact_ids),
                        Contact.is_active == True,
                        Contact.do_not_email == False
                    ).all()
                    
                    print(f"Filtered contacts in group: {len(contacts_in_group)}")
                    for contact in contacts_in_group:
                        print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
                else:
                    print("No contact IDs found in group")
            else:
                print("'test' group not found")
            
            # 4. Test creating a proper campaign criteria
            print("\n🔧 TESTING CAMPAIGN CRITERIA CREATION:")
            if test_group:
                test_criteria = {
                    'type': 'groups',
                    'group_ids': [test_group.id]
                }
                print(f"Test criteria: {json.dumps(test_criteria)}")
                
                # Create a mock campaign object to test
                class MockCampaign:
                    def __init__(self, criteria):
                        self.recipient_criteria = json.dumps(criteria)
                
                mock_campaign = MockCampaign(test_criteria)
                test_contacts = get_campaign_contacts(mock_campaign)
                print(f"Contacts returned for test criteria: {len(test_contacts)}")
                for contact in test_contacts:
                    print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
            
            # 5. Check all active contacts (what "all" returns)
            print("\n📧 ALL ACTIVE CONTACTS (for comparison):")
            all_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
            print(f"Total active contacts: {len(all_contacts)}")
            if len(all_contacts) <= 10:
                for contact in all_contacts:
                    print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
            else:
                print("First 5 contacts:")
                for contact in all_contacts[:5]:
                    print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
                print(f"   ... and {len(all_contacts) - 5} more")
            
            return True
            
    except Exception as e:
        print(f"❌ Error debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_existing_campaign():
    """Fix the existing campaign to use proper group selection"""
    try:
        from unified_sales_system import app, db, EmailCampaign, ContactGroup
        import json
        
        with app.app_context():
            print("\n🔧 FIXING EXISTING CAMPAIGN")
            print("=" * 30)
            
            # Find the campaign
            campaign = EmailCampaign.query.filter_by(name="24seven Assistants").first()
            if not campaign:
                print("❌ Campaign '24seven Assistants' not found")
                return False
            
            # Find the test group
            test_group = ContactGroup.query.filter_by(name="test").first()
            if not test_group:
                print("❌ Group 'test' not found")
                return False
            
            print(f"✅ Found campaign: {campaign.name} (ID: {campaign.id})")
            print(f"✅ Found group: {test_group.name} (ID: {test_group.id})")
            
            # Update recipient criteria
            new_criteria = {
                'type': 'groups',
                'group_ids': [test_group.id]
            }
            
            campaign.recipient_criteria = json.dumps(new_criteria)
            campaign.status = 'draft'  # Reset to draft so it can be sent again
            
            db.session.commit()
            
            print(f"✅ Campaign updated!")
            print(f"   New recipient criteria: {campaign.recipient_criteria}")
            print(f"   Status: {campaign.status}")
            
            # Test the fix
            from unified_sales_system import get_campaign_contacts
            test_contacts = get_campaign_contacts(campaign)
            print(f"✅ Test result: {len(test_contacts)} contacts will receive emails")
            for contact in test_contacts:
                print(f"   - {contact.first_name} {contact.last_name} ({contact.email})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error fixing campaign: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_group_campaign()
    print("\n" + "=" * 60)
    fix_existing_campaign()
