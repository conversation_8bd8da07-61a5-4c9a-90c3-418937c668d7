#!/usr/bin/env python3
"""
Database Migration Script for ChatbotSession Table
Adds missing fields for enhanced session tracking
"""

import sqlite3
import os
from datetime import datetime

def migrate_chatbot_sessions():
    """Add missing fields to chatbot_sessions table"""
    db_path = 'sales_system.db'

    if not os.path.exists(db_path):
        print("❌ Database file not found. Please create the database first.")
        return False

    print("🔄 Starting ChatbotSession table migration...")

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Check current table schema
        cursor.execute("PRAGMA table_info(chatbot_sessions)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        print(f"📋 Current columns: {existing_columns}")

        # List of new columns to add
        new_columns = [
            ('engagement_level', 'VARCHAR(20) DEFAULT "medium"'),
            ('interaction_quality_score', 'REAL DEFAULT 0.0'),
            ('abandonment_reason', 'VARCHAR(200)'),
            ('opening_started_at', 'DATETIME'),
            ('opening_completed_at', 'DATETIME'),
            ('trust_started_at', 'DATETIME'),
            ('trust_completed_at', 'DATETIME'),
            ('discovery_started_at', 'DATETIME'),
            ('discovery_completed_at', 'DATETIME'),
            ('demonstration_started_at', 'DATETIME'),
            ('demonstration_completed_at', 'DATETIME'),
            ('close_started_at', 'DATETIME'),
            ('close_completed_at', 'DATETIME')
        ]

        # Add missing columns
        columns_added = 0
        for column_name, column_def in new_columns:
            if column_name not in existing_columns:
                try:
                    sql = f"ALTER TABLE chatbot_sessions ADD COLUMN {column_name} {column_def}"
                    cursor.execute(sql)
                    print(f"✅ Added column: {column_name}")
                    columns_added += 1
                except sqlite3.Error as e:
                    print(f"❌ Error adding column {column_name}: {e}")
            else:
                print(f"⏭️  Column {column_name} already exists")

        # Verify the migration
        cursor.execute("PRAGMA table_info(chatbot_sessions)")
        final_columns = [col[1] for col in cursor.fetchall()]

        print(f"\n📊 Migration Summary:")
        print(f"   Columns added: {columns_added}")
        print(f"   Total columns: {len(final_columns)}")
        print(f"   Final schema: {final_columns}")

        conn.commit()
        print("\n✅ Migration completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_migration():
    """Verify that the migration was successful"""
    db_path = 'sales_system.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Check if all required columns exist
        cursor.execute("PRAGMA table_info(chatbot_sessions)")
        columns = [col[1] for col in cursor.fetchall()]

        required_columns = [
            'engagement_level', 'interaction_quality_score', 'abandonment_reason',
            'opening_started_at', 'opening_completed_at',
            'trust_started_at', 'trust_completed_at',
            'discovery_started_at', 'discovery_completed_at',
            'demonstration_started_at', 'demonstration_completed_at',
            'close_started_at', 'close_completed_at'
        ]

        missing = [col for col in required_columns if col not in columns]

        if missing:
            print(f"❌ Verification failed. Missing columns: {missing}")
            return False
        else:
            print("✅ Verification passed. All required columns present.")
            return True

    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("🚀 ChatbotSession Migration Tool")
    print("=" * 50)

    success = migrate_chatbot_sessions()
    if success:
        print("\n🔍 Verifying migration...")
        verify_migration()
        print("\n✅ Migration completed successfully!")
        print("You can now restart the Flask application.")
    else:
        print("\n❌ Migration failed.")
