# Aegis - AI-Powered Autonomous Sales Platform

**Implementation Status: IN PROGRESS**
**Technology Stack: Python 3.8.2, Django 4.2**
**Document Version: 1.0**
**Date: 2023-10-27**

This document serves as the comprehensive blueprint for implementing an enterprise-grade AI-powered sales automation platform.

**Note: For detailed documentation on the Chat with Manager functionality, see the separate file: `CHAT_WITH_MANAGER_IMPLEMENTATION.md`**

Table of Contents:

Introduction & Core Objectives

Guiding Principles for Fortune 500 Readiness

High-Level Architecture

Technology Stack

Django Backend Implementation

5.1. Project Setup & Core Settings

5.2. companies App (Company Context, Settings, Branding)

5.3. users App (Authentication, Authorization, Roles, Teams)

5.4. products App (Product Catalog, Pricing, Attributes)

5.5. contacts App (Leads, Contacts, Accounts/Organizations)

5.6. sales_process App (Sales Stages, Pipelines, Funnels)

5.7. opportunities App (Core Sales Object)

5.8. activities App (Email, Calls, Meetings, Notes, AI Actions)

5.9. calendar_integration App (Internal & External Calendar Sync)

5.10. targets App (Sales Targets & Quotas)

5.11. analytics App (Reporting & Dashboards)

5.12. ai_platform App (LLM Integration, Prompt Management)

5.13. notifications App (System & User Notifications)

5.14. audit App (Audit Trails)

5.15. REST API Design (General Principles, Versioning)

5.16. Services Layer (Business Logic Abstraction)

5.17. Background Task Processing (Celery)

A2A (Agent-to-Agent) Server & AI Agent Implementation

6.1. A2A Server Setup

6.2. Base Agent Executor & Utilities

6.3. SalesOrchestratorAgent

6.4. ProspectingAgent

6.5. QualificationAgent

6.6. PresentationAgent

6.7. ObjectionHandlingAgent

6.8. NegotiationAgent

6.9. ClosingAgent

6.10. FollowUpAgent

6.11. SalesManagerAgent (AI Overseer)

6.12. Agent Configuration & Prompt Management

LLM Integration Strategy

External System Integrations

8.1. Email (Inbound/Outbound SMTP/API)

8.2. Calendar (Google, Outlook)

8.3. E-Signature (DocuSign, PandaDoc)

8.4. CRM Sync (Optional: Salesforce, HubSpot)

Security Considerations

Scalability & Performance

Data Management & Compliance

Testing Strategy

Deployment Strategy

UI/UX High-Level Guidelines (for Frontend Devs)

Future Enhancements

1. Introduction & Core Objectives

Aegis is an AI-driven platform designed to autonomously manage the entire online sales lifecycle for large enterprises. It leverages a suite of specialized AI agents, orchestrated via the Google A2A protocol, to execute sales strategies based on Schiffman's sales process (or other configurable methodologies).

Core Objectives:

Autonomy: Minimize human intervention in routine sales tasks.

Scalability: Handle tens of thousands of leads and concurrent sales activities.

Intelligence: Utilize LLMs for personalized communication, decision-making, and strategic insights.

Configurability: Allow companies to define their context, products, sales processes, and AI agent behaviors.

Transparency & Auditability: Provide complete visibility into all AI actions and conversations.

Integration: Seamlessly connect with existing enterprise tools.

Performance & Reliability: Ensure high uptime and responsiveness.

2. Guiding Principles for Fortune 500 Readiness

Security First: Data protection, access control, and threat mitigation are paramount.

Robustness & Resilience: Comprehensive error handling, retries, and fault tolerance.

Auditability: Every significant action (human or AI) must be logged.

Compliance: Adherence to data privacy regulations (GDPR, CCPA, etc.).

Customization & Extensibility: The platform must adapt to diverse business needs.

Data-Driven Decisions: Both AI and human users rely on accurate, real-time data.

Clear Separation of Concerns: Modular design for maintainability and scalability.

Comprehensive Logging & Monitoring: Essential for operations and debugging.

3. High-Level Architecture

Django Backend: Core application logic, data persistence, REST APIs, admin interface.

PostgreSQL Database: Primary data store.

Redis: Caching and Celery message broker/results backend.

Celery: Asynchronous task execution for background jobs.

A2A Server: Python-based server hosting the AI sales agents, communicating via Google's A2A protocol.

LLM Provider(s): OpenAI GPT series, Google Gemini, Anthropic Claude, or other compatible models (configurable).

Frontend: (Separate Project) React/Vue/Angular SPA consuming Django REST APIs.

External Services: Email (SendGrid/Mailgun), Calendar (Google/Outlook APIs), E-Signature (DocuSign/PandaDoc APIs).

graph TD
    User[End User/Sales Manager] -->|Browser| Frontend[Frontend SPA]
    Frontend -->|REST API (HTTPS)| Django[Django Backend API]
    Django -->|SQL| PostgreSQL[PostgreSQL DB]
    Django -->|AMQP| Redis[Redis (Cache & Celery Broker)]
    CeleryWorker[Celery Workers] -->|AMQP| Redis
    CeleryWorker -->|SQL| PostgreSQL
    CeleryWorker -->|API Calls| ExtEmail[External Email Service]
    CeleryWorker -->|API Calls| ExtCalendar[External Calendar Service]
    CeleryWorker -->|API Calls| ExtESign[External E-Signature Service]

    Django -->|A2A HTTP Call| A2AServer[A2A Server]
    A2AServer -->|LLM API| LLMProvider[LLM Provider API]
    A2AServer -->|REST API (HTTPS)| Django

    subgraph A2A Agents
        Orchestrator[SalesOrchestratorAgent]
        Prospecting[ProspectingAgent]
        Qualification[QualificationAgent]
        Presentation[PresentationAgent]
        Objection[ObjectionHandlingAgent]
        Negotiation[NegotiationAgent]
        Closing[ClosingAgent]
        FollowUp[FollowUpAgent]
        SalesManagerAI[SalesManagerAgent (AI)]
    end
    A2AServer -.-> Orchestrator
    A2AServer -.-> Prospecting
    A2AServer -.-> Qualification
    A2AServer -.-> Presentation
    A2AServer -.-> Objection
    A2AServer -.-> Negotiation
    A2AServer -.-> Closing
    A2AServer -.-> FollowUp
    A2AServer -.-> SalesManagerAI

    AdminUser[Admin User] -->|Django Admin| Django

4. Technology Stack

Backend: Python 3.10+, Django 4.2+, Django REST Framework 3.14+

A2A Server: Python 3.10+, A2A SDK

Database: PostgreSQL 15+

Caching/Broker: Redis 7+

Async Tasks: Celery 5.3+

LLM SDKs: openai, google-generativeai, anthropic

Containerization: Docker, Docker Compose (for development & deployment)

CI/CD: GitHub Actions / GitLab CI / Jenkins (TBD by deploying organization)

Monitoring: Prometheus, Grafana, Sentry (or equivalents)

5. Django Backend Implementation
5.1. Project Setup & Core Settings

django-admin startproject aegis_sales_platform

cd aegis_sales_platform

Create apps: python manage.py startapp <app_name> for each app listed below.

settings.py Key Configurations:

SECRET_KEY: From environment variable.

DEBUG: From environment variable, False in production.

ALLOWED_HOSTS: Configurable.

DATABASES: Configured for PostgreSQL, credentials from environment variables.

INSTALLED_APPS: Add rest_framework, rest_framework.authtoken (or django-rest-knox / dj_rest_auth for JWT), corsheaders, django_celery_beat, django_filters, and all created apps.

MIDDLEWARE: Include corsheaders.middleware.CorsMiddleware.

REST_FRAMEWORK:

DEFAULT_AUTHENTICATION_CLASSES: Token/JWT based.

DEFAULT_PERMISSION_CLASSES: rest_framework.permissions.IsAuthenticated.

DEFAULT_PAGINATION_CLASS & PAGE_SIZE.

DEFAULT_FILTER_BACKENDS.

DEFAULT_RENDERER_CLASSES & DEFAULT_PARSER_CLASSES.

API Versioning (URLPathVersioning or NamespaceVersioning).

CELERY_BROKER_URL, CELERY_RESULT_BACKEND: Point to Redis, from environment variables.

CELERY_BEAT_SCHEDULER: django_celery_beat.schedulers:DatabaseScheduler.

CORS_ALLOWED_ORIGINS / CORS_ALLOW_ALL_ORIGINS.

Logging configuration (JSON formatter for production).

Email backend settings (for Django's mail, separate from agent's email).

AUTH_USER_MODEL = 'users.User' (custom user model).

A2A_SERVER_URL: URL for the A2A server.

LLM_PROVIDER_API_KEYS: Securely stored, potentially using HashiCorp Vault or Django settings loaded from env.

5.2. companies App

Manages company-specific context, settings, branding, and configurations. This is key for multi-tenancy or high configurability.

Models:

Company(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

name = models.CharField(max_length=255, unique=True)

domain = models.CharField(max_length=255, unique=True, blank=True, null=True, help_text="Primary company domain for email matching, etc.")

industry = models.CharField(max_length=100, blank=True)

timezone = models.CharField(max_length=100, default='UTC')

country = models.CharField(max_length=100, blank=True)

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

is_active = models.BooleanField(default=True)

class Meta: ordering = ['name']

CompanySettings(models.Model):

company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='settings')

default_language = models.CharField(max_length=10, default='en')

default_currency = models.CharField(max_length=3, default='USD')

branding_logo_url = models.URLField(blank=True, null=True)

ai_agent_config_override = models.JSONField(default=dict, blank=True, help_text="JSON to override global AI agent prompts/settings for this company")

smtp_settings = models.JSONField(default=dict, blank=True, help_text="Company-specific SMTP: {host, port, user, pass, use_tls, from_email}")

calendar_provider = models.CharField(max_length=20, choices=[('google', 'Google Calendar'), ('outlook', 'Outlook Calendar')], blank=True, null=True)

calendar_credentials = models.JSONField(default=dict, blank=True, help_text="OAuth tokens for calendar integration")

esign_provider = models.CharField(max_length=20, choices=[('docusign', 'DocuSign'), ('pandadoc', 'PandaDoc')], blank=True, null=True)

esign_credentials = models.JSONField(default=dict, blank=True, help_text="API keys/tokens for e-signature integration")

updated_at = models.DateTimeField(auto_now=True)

Serializers: CompanySerializer, CompanySettingsSerializer.

ViewSets: CompanyViewSet, CompanySettingsViewSet (likely nested under company or for admin use).

Permissions: Ensure users can only access/modify settings for their own company.

5.3. users App

Custom user model, roles, teams, permissions.

Models:

User(AbstractUser):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='users', null=True, blank=True)

email = models.EmailField(unique=True) (ensure AbstractUser's username is not the primary identifier if email is used for login)

phone_number = models.CharField(max_length=20, blank=True)

profile_picture_url = models.URLField(blank=True, null=True)

timezone = models.CharField(max_length=100, blank=True, help_text="User's preferred timezone, overrides company if set")

USERNAME_FIELD = 'email'

REQUIRED_FIELDS = ['username'] (username can be same as email initially or a unique slug)

Role(models.Model):

name = models.CharField(max_length=100, unique=True) (e.g., "Admin", "Sales Manager", "Sales Rep", "AI Agent Service")

permissions = models.ManyToManyField('auth.Permission', blank=True)

Team(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='teams')

name = models.CharField(max_length=100)

members = models.ManyToManyField(User, related_name='teams', blank=True)

manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_teams')

class Meta: unique_together = ('company', 'name')

Assign roles to users, possibly through a UserRole through-model if complex assignments are needed or directly on User model roles = models.ManyToManyField(Role, blank=True).

Serializers: UserSerializer, RoleSerializer, TeamSerializer.

ViewSets: UserViewSet, RoleViewSet, TeamViewSet.

Authentication: Use dj_rest_auth for robust token/JWT handling (registration, login, logout, password reset).

Permissions: Implement custom permission classes (e.g., IsCompanyAdmin, IsTeamManager, IsObjectOwner).

5.4. products App

Product catalog, pricing structures, features.

Models:

ProductCategory(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='product_categories')

name = models.CharField(max_length=100)

description = models.TextField(blank=True)

parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')

Product(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='products')

category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')

name = models.CharField(max_length=200)

sku = models.CharField(max_length=100, blank=True, null=True, db_index=True)

description = models.TextField(blank=True)

long_description_html = models.TextField(blank=True, help_text="Detailed HTML description for proposals")

base_price = models.DecimalField(max_digits=12, decimal_places=2)

currency = models.CharField(max_length=3, default='USD')

attributes = models.JSONField(default=dict, blank=True, help_text="Key features, benefits, technical specs as key-value pairs")

is_active = models.BooleanField(default=True)

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

class Meta: unique_together = ('company', 'sku') (if SKU is used and must be unique per company)

PricingRule(models.Model): (For complex pricing: volume discounts, tiered pricing)

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='pricing_rules')

name = models.CharField(max_length=100)

conditions = models.JSONField(default=dict, help_text="e.g., {'quantity_min': 10, 'customer_segment': 'enterprise'}")

adjustment_type = models.CharField(max_length=20, choices=[('percentage_discount', '% Discount'), ('fixed_discount', 'Fixed Discount'), ('fixed_price', 'Fixed Price')])

adjustment_value = models.DecimalField(max_digits=12, decimal_places=2)

is_active = models.BooleanField(default=True)

Serializers: ProductCategorySerializer, ProductSerializer, PricingRuleSerializer.

ViewSets: ProductCategoryViewSet, ProductViewSet, PricingRuleViewSet.

5.5. contacts App

Leads, individual contacts, and organizations/accounts.

Models:

ContactTag(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='contact_tags')

name = models.CharField(max_length=50)

color = models.CharField(max_length=7, default='#CCCCCC', help_text="Hex color code for UI display")

class Meta: unique_together = ('company', 'name')

Organization(models.Model): (Represents a client company)

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company_context = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='client_organizations') # The Aegis user's company

name = models.CharField(max_length=255)

domain = models.CharField(max_length=255, blank=True, null=True, db_index=True)

industry = models.CharField(max_length=100, blank=True)

employee_count = models.PositiveIntegerField(null=True, blank=True)

annual_revenue = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

address = models.TextField(blank=True)

notes = models.TextField(blank=True)

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

Contact(models.Model): (Individual person, can be a Lead or a confirmed Contact)

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company_context = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='contacts_managed')

organization = models.ForeignKey(Organization, on_delete=models.SET_NULL, null=True, blank=True, related_name='contacts')

email = models.EmailField(db_index=True) # Not unique globally, but (company_context, email) should be.

first_name = models.CharField(max_length=100, blank=True)

last_name = models.CharField(max_length=100, blank=True)

full_name = models.CharField(max_length=200, blank=True, editable=False) # Auto-populated

title = models.CharField(max_length=100, blank=True)

phone_primary = models.CharField(max_length=30, blank=True)

phone_secondary = models.CharField(max_length=30, blank=True)

linkedin_url = models.URLField(blank=True, null=True)

source = models.CharField(max_length=100, blank=True, help_text="e.g., 'CSV Import', 'Webinar Q3', 'Manual Entry'")

status = models.CharField(max_length=20, choices=[('lead', 'Lead'), ('contacted', 'Contacted'), ('qualified', 'Qualified'), ('unqualified', 'Unqualified'), ('customer', 'Customer'), ('former_customer', 'Former Customer')], default='lead', db_index=True)

do_not_email = models.BooleanField(default=False)

do_not_call = models.BooleanField(default=False)

unsubscribed_at = models.DateTimeField(null=True, blank=True)

notes = models.TextField(blank=True)

tags = models.ManyToManyField(ContactTag, blank=True)

assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_contacts')

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

class Meta: unique_together = ('company_context', 'email'); ordering = ['-updated_at']

def save(self, *args, **kwargs): self.full_name = f"{self.first_name} {self.last_name}".strip(); super().save(*args, **kwargs)

Serializers: ContactTagSerializer, OrganizationSerializer, ContactSerializer (with nested serializers for tags, organization).

ViewSets: ContactTagViewSet, OrganizationViewSet, ContactViewSet.

Services: ContactImportService for CSV uploads, ContactMergeService for deduplication.

5.6. sales_process App

Defines sales stages, pipelines (sequences of stages), and funnels (for analytics).

Models:

SalesStage(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='sales_stages')

name = models.CharField(max_length=100, help_text="e.g., Prospecting, Qualification, Needs Analysis, Proposal, Negotiation, Closed Won, Closed Lost")

description = models.TextField(blank=True)

order = models.PositiveSmallIntegerField(help_text="Defines sequence in a pipeline")

probability_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.0, help_text="Default win probability for opportunities in this stage (0-100)")

expected_duration_days = models.PositiveIntegerField(default=7, help_text="Typical time an opportunity spends in this stage")

category = models.CharField(max_length=20, choices=[('open', 'Open'), ('won', 'Won'), ('lost', 'Lost')], default='open', db_index=True)

is_active = models.BooleanField(default=True)

class Meta: unique_together = ('company', 'name'); ordering = ['company', 'order']

SalesPipeline(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='sales_pipelines')

name = models.CharField(max_length=100)

stages = models.ManyToManyField(SalesStage, through='PipelineStageMembership', related_name='pipelines')

is_default = models.BooleanField(default=False, help_text="Default pipeline for new opportunities for this company")

is_active = models.BooleanField(default=True)

class Meta: unique_together = ('company', 'name')

PipelineStageMembership(models.Model): (Ensures order is specific to the pipeline if stages can be reused with different orders)

pipeline = models.ForeignKey(SalesPipeline, on_delete=models.CASCADE)

stage = models.ForeignKey(SalesStage, on_delete=models.CASCADE)

order = models.PositiveSmallIntegerField()

class Meta: unique_together = ('pipeline', 'stage'); ordering = ['pipeline', 'order']

Serializers: SalesStageSerializer, SalesPipelineSerializer (with nested stages).

ViewSets: SalesStageViewSet, SalesPipelineViewSet.

5.7. opportunities App

The central object tracking a potential sale.

Models:

Opportunity(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='opportunities')

name = models.CharField(max_length=255, help_text="e.g., 'SuperWidget Deal for Acme Corp Q3'")

primary_contact = models.ForeignKey('contacts.Contact', on_delete=models.PROTECT, related_name='opportunities')

organization = models.ForeignKey('contacts.Organization', on_delete=models.PROTECT, null=True, blank=True, related_name='opportunities')

pipeline = models.ForeignKey(SalesPipeline, on_delete=models.PROTECT, related_name='opportunities')

current_stage = models.ForeignKey(SalesStage, on_delete=models.PROTECT, related_name='opportunities_in_stage')

products = models.ManyToManyField('products.Product', through='OpportunityProduct', blank=True)

estimated_value = models.DecimalField(max_digits=12, decimal_places=2, default=0)

actual_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)

currency = models.CharField(max_length=3, default='USD')

probability_override_percent = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text="Manual override of stage probability")

weighted_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, editable=False) # Auto-calculated

expected_close_date = models.DateField(null=True, blank=True, db_index=True)

actual_close_date = models.DateField(null=True, blank=True)

status = models.CharField(max_length=10, choices=[('open', 'Open'), ('won', 'Won'), ('lost', 'Lost'), ('abandoned', 'Abandoned')], default='open', db_index=True)

lost_reason = models.CharField(max_length=255, blank=True, null=True, help_text="If status is 'lost'")

win_loss_notes = models.TextField(blank=True)

assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_opportunities')

team = models.ForeignKey('users.Team', on_delete=models.SET_NULL, null=True, blank=True, related_name='team_opportunities')

source_campaign_name = models.CharField(max_length=100, blank=True, null=True, help_text="Name of the marketing/sales campaign that generated this opp")

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

last_activity_at = models.DateTimeField(null=True, blank=True, db_index=True)

class Meta: ordering = ['-expected_close_date', '-updated_at']

def save(self, *args, **kwargs): prob = self.probability_override_percent if self.probability_override_percent is not None else self.current_stage.probability_percent; self.weighted_value = (self.estimated_value * prob) / 100; super().save(*args, **kwargs)

OpportunityProduct(models.Model): (Through model for M2M with products)

opportunity = models.ForeignKey(Opportunity, on_delete=models.CASCADE)

product = models.ForeignKey('products.Product', on_delete=models.CASCADE)

quantity = models.PositiveIntegerField(default=1)

unit_price = models.DecimalField(max_digits=12, decimal_places=2, help_text="Price at the time of adding to opp, can differ from product.base_price")

discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0)

total_price = models.DecimalField(max_digits=12, decimal_places=2, editable=False) # Auto-calculated

OpportunityStageHistory(models.Model):

opportunity = models.ForeignKey(Opportunity, on_delete=models.CASCADE, related_name='stage_history')

stage = models.ForeignKey(SalesStage, on_delete=models.PROTECT)

entered_at = models.DateTimeField(auto_now_add=True)

exited_at = models.DateTimeField(null=True, blank=True)

duration_days = models.FloatField(null=True, blank=True)

changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="User or AI Agent Service Account")

class Meta: ordering = ['opportunity', '-entered_at']

Serializers: OpportunityProductSerializer, OpportunityStageHistorySerializer, OpportunitySerializer (deeply nested).

ViewSets: OpportunityViewSet.

Services: OpportunityProgressionService (handles stage changes, history logging, status updates).

5.8. activities App

Logs all interactions and AI agent actions. This is crucial for transparency and history.

Models:

Activity(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='activities')

opportunity = models.ForeignKey('opportunities.Opportunity', on_delete=models.CASCADE, null=True, blank=True, related_name='activities')

contact = models.ForeignKey('contacts.Contact', on_delete=models.CASCADE, null=True, blank=True, related_name='activities')

activity_type = models.CharField(max_length=50, choices=[ ('EMAIL_SENT_AI', 'Email Sent (AI)'), ('EMAIL_SENT_USER', 'Email Sent (User)'), ('EMAIL_RECEIVED', 'Email Received'), ('CALL_LOGGED_AI', 'Call Logged (AI)'), ('CALL_LOGGED_USER', 'Call Logged (User)'), ('MEETING_SCHEDULED_AI', 'Meeting Scheduled (AI)'), ('MEETING_SCHEDULED_USER', 'Meeting Scheduled (User)'), ('MEETING_COMPLETED', 'Meeting Completed'), ('NOTE_AI', 'Internal Note (AI)'), ('NOTE_USER', 'Internal Note (User)'), ('PROPOSAL_GENERATED_AI', 'Proposal Generated (AI)'), ('PROPOSAL_SENT_AI', 'Proposal Sent (AI)'), ('CONTRACT_GENERATED_AI', 'Contract Generated (AI)'), ('CONTRACT_SENT_AI', 'Contract Sent (AI)'), ('ESIGN_COMPLETED', 'E-Signature Completed'), ('STAGE_CHANGED_AI', 'Stage Changed (AI)'), ('STAGE_CHANGED_USER', 'Stage Changed (User)'), ('TASK_CREATED_AI', 'Task Created (AI)'), ('TASK_COMPLETED_AI', 'Task Completed (AI)'), ('SYSTEM_EVENT', 'System Event') ], db_index=True)

timestamp = models.DateTimeField(default=timezone.now, db_index=True)

created_by_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_activities')

created_by_agent_name = models.CharField(max_length=100, blank=True, null=True, help_text="e.g., 'ProspectingAgentV1'")

subject = models.CharField(max_length=255, blank=True, null=True)

details = models.JSONField(default=dict, blank=True, help_text="Structured data, e.g., email body, headers, call notes, meeting attendees, LLM prompt/response")

sentiment_score = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True, help_text="If applicable, e.g., for emails")

related_stage = models.ForeignKey('sales_process.SalesStage', on_delete=models.SET_NULL, null=True, blank=True, help_text="Stage when activity occurred")

class Meta: ordering = ['-timestamp']

Serializers: ActivitySerializer.

ViewSets: ActivityViewSet (likely read-only for most users, writable by agents/system).

Important: details JSON schema should be defined per activity_type. E.g., for EMAIL_SENT_AI: { "to": "...", "from": "...", "subject": "...", "body_html": "...", "body_text": "...", "thread_id": "...", "message_id": "...", "llm_prompt_ref": "uuid", "llm_response_ref": "uuid" }.

5.9. calendar_integration App

Internal representation of calendar events and sync logic.

Models:

CalendarEvent(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='calendar_events')

opportunity = models.ForeignKey('opportunities.Opportunity', on_delete=models.CASCADE, null=True, blank=True, related_name='calendar_events')

contact = models.ForeignKey('contacts.Contact', on_delete=models.CASCADE, null=True, blank=True, related_name='calendar_events')

organizer_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='organized_events')

organizer_agent_name = models.CharField(max_length=100, blank=True, null=True)

title = models.CharField(max_length=255)

description = models.TextField(blank=True)

start_time = models.DateTimeField(db_index=True)

end_time = models.DateTimeField(db_index=True)

location = models.CharField(max_length=255, blank=True, help_text="Physical address or meeting link")

attendees = models.JSONField(default=list, blank=True, help_text="List of email addresses or contact UUIDs")

external_provider = models.CharField(max_length=20, choices=[('google', 'Google'), ('outlook', 'Outlook')], blank=True, null=True)

external_event_id = models.CharField(max_length=255, blank=True, null=True, db_index=True)

sync_status = models.CharField(max_length=20, choices=[('pending', 'Pending Sync'), ('synced', 'Synced'), ('error', 'Sync Error')], default='pending')

last_sync_error = models.TextField(blank=True, null=True)

created_at = models.DateTimeField(auto_now_add=True)

updated_at = models.DateTimeField(auto_now=True)

class Meta: ordering = ['start_time']

Serializers: CalendarEventSerializer.

ViewSets: CalendarEventViewSet.

Services: GoogleCalendarService, OutlookCalendarService for CRUD operations with external calendars. Celery tasks for syncing.

5.10. targets App

Sales targets, quotas, and performance tracking against them.

Models:

SalesTarget(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='sales_targets')

name = models.CharField(max_length=100, help_text="e.g., 'Q3 2025 Company Target', 'John Doe - July Product X Target'")

period_start_date = models.DateField()

period_end_date = models.DateField()

target_type = models.CharField(max_length=20, choices=[('revenue', 'Revenue'), ('deal_count', 'Deal Count'), ('activity_count', 'Activity Count')])

target_value = models.DecimalField(max_digits=15, decimal_places=2)

scope_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='user_targets')

scope_team = models.ForeignKey('users.Team', on_delete=models.CASCADE, null=True, blank=True, related_name='team_targets')

scope_product = models.ForeignKey('products.Product', on_delete=models.CASCADE, null=True, blank=True, related_name='product_targets')

scope_details = models.JSONField(default=dict, blank=True, help_text="Additional scoping, e.g., region, campaign")

achieved_value = models.DecimalField(max_digits=15, decimal_places=2, default=0, editable=False)

achievement_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0, editable=False)

last_calculated_at = models.DateTimeField(null=True, blank=True)

class Meta: ordering = ['period_start_date', 'name']

Serializers: SalesTargetSerializer.

ViewSets: SalesTargetViewSet.

Services: TargetCalculationService (Celery task to periodically update achieved_value by querying opportunities).

5.11. analytics App

No models here initially. This app will contain DRF views/viewsets that aggregate data from other models for reporting.

ViewSets/APIViews:

FunnelAnalyticsView: Calculates conversion rates between stages.

Input: pipeline_id, date_range.

Output: { "stage_name": "...", "entered_count": X, "converted_count": Y, "conversion_rate": Z% } for each stage.

PipelineHealthView: Snapshot of current pipeline value, average deal size, velocity.

SalesCycleAnalyticsView: Average time to close, time spent in each stage.

ActivityMetricsView: Emails sent, calls logged, meetings scheduled per user/team/AI agent.

TargetPerformanceView: Compares SalesTarget.achieved_value vs target_value.

These views will perform complex queries, aggregations, and potentially use caching.

5.12. ai_platform App

Manages LLM integration, prompt templates, and AI model configurations.

Models:

LLMProvider(models.Model):

name = models.CharField(max_length=50, unique=True, help_text="e.g., OpenAI, Google, Anthropic")

api_base_url = models.URLField(blank=True, null=True)

is_active = models.BooleanField(default=True)

AIModel(models.Model):

provider = models.ForeignKey(LLMProvider, on_delete=models.CASCADE, related_name='models')

model_name = models.CharField(max_length=100, help_text="e.g., gpt-4-turbo, gemini-1.5-pro, claude-3-opus")

api_identifier = models.CharField(max_length=100, help_text="Actual ID used in API calls")

capabilities = models.JSONField(default=list, blank=True, help_text="e.g., ['text_generation', 'json_output', 'function_calling']")

is_default_for_provider = models.BooleanField(default=False)

is_active = models.BooleanField(default=True)

class Meta: unique_together = ('provider', 'model_name')

PromptTemplate(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='prompt_templates')

name = models.CharField(max_length=100, help_text="Unique name for this prompt, e.g., 'ProspectingAgent_InitialEmail_V1'")

description = models.TextField(blank=True)

agent_association = models.CharField(max_length=50, blank=True, help_text="e.g., ProspectingAgent, QualificationAgent")

stage_association = models.CharField(max_length=50, blank=True, help_text="e.g., InitialOutreach, BANTCheck")

template_text = models.TextField(help_text="The prompt text with {{placeholders}}")

expected_variables = models.JSONField(default=list, blank=True, help_text="List of placeholder names used in template_text")

output_format_schema = models.JSONField(default=dict, blank=True, help_text="JSON schema for expected LLM output, if structured output is needed")

version = models.PositiveIntegerField(default=1)

is_active = models.BooleanField(default=True)

llm_model_preference = models.ForeignKey(AIModel, on_delete=models.SET_NULL, null=True, blank=True)

temperature = models.FloatField(default=0.7, null=True, blank=True)

max_tokens = models.PositiveIntegerField(default=1024, null=True, blank=True)

class Meta: unique_together = ('company', 'name', 'version'); ordering = ['company', 'name', '-version']

LLMInteractionLog(models.Model): (For auditing and fine-tuning)

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='llm_logs')

prompt_template_used = models.ForeignKey(PromptTemplate, on_delete=models.SET_NULL, null=True, blank=True)

model_used = models.ForeignKey(AIModel, on_delete=models.SET_NULL, null=True, blank=True)

timestamp = models.DateTimeField(auto_now_add=True)

input_prompt_rendered = models.TextField()

input_variables = models.JSONField(default=dict)

output_response_raw = models.TextField()

output_response_parsed = models.JSONField(null=True, blank=True)

tokens_used_prompt = models.PositiveIntegerField(null=True, blank=True)

tokens_used_completion = models.PositiveIntegerField(null=True, blank=True)

latency_ms = models.PositiveIntegerField(null=True, blank=True)

cost = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)

related_activity = models.ForeignKey('activities.Activity', on_delete=models.SET_NULL, null=True, blank=True)

status_code = models.PositiveSmallIntegerField(null=True, blank=True)

error_message = models.TextField(blank=True, null=True)

Serializers: LLMProviderSerializer, AIModelSerializer, PromptTemplateSerializer, LLMInteractionLogSerializer.

ViewSets: Admin-focused for managing providers, models, templates. Logs are mostly system-generated.

Services: LLMService (a wrapper to interact with different LLM providers, handle prompt rendering, logging).

5.13. notifications App

Handles system notifications to users (e.g., new lead assigned, task due, AI needs review).

Models:

Notification(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

recipient_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='notifications_sent')

notification_type = models.CharField(max_length=50, db_index=True)

message = models.TextField()

related_object_content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)

related_object_id = models.UUIDField(null=True, blank=True)

related_object = GenericForeignKey('related_object_content_type', 'related_object_id')

is_read = models.BooleanField(default=False, db_index=True)

read_at = models.DateTimeField(null=True, blank=True)

created_at = models.DateTimeField(auto_now_add=True)

delivery_methods = models.JSONField(default=list, help_text="e.g., ['in_app', 'email']")

Serializers: NotificationSerializer.

ViewSets: NotificationViewSet (list for user, mark as read).

Services: NotificationService (creates and sends notifications via various channels).

5.14. audit App

Comprehensive audit trails for critical actions.

Models:

AuditLog(models.Model):

uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, db_index=True)

company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='audit_logs')

user_actor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="User performing action")

agent_actor_name = models.CharField(max_length=100, blank=True, null=True, help_text="AI Agent performing action")

action = models.CharField(max_length=100, db_index=True, help_text="e.g., 'opportunity_created', 'email_sent_ai', 'settings_updated'")

timestamp = models.DateTimeField(auto_now_add=True, db_index=True)

target_content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)

target_object_id = models.UUIDField(null=True, blank=True)

target_object = GenericForeignKey('target_content_type', 'target_object_id')

details_before = models.JSONField(null=True, blank=True, help_text="State of object before change")

details_after = models.JSONField(null=True, blank=True, help_text="State of object after change")

ip_address = models.GenericIPAddressField(null=True, blank=True)

context_notes = models.TextField(blank=True, help_text="Additional context if needed")

Serializers: AuditLogSerializer (read-only).

ViewSets: AuditLogViewSet (admin/auditor access).

Implementation: Use Django signals (post_save, post_delete) or explicit calls in services to create audit log entries.

5.15. REST API Design (General Principles, Versioning)

Use DRF ViewSets (ModelViewSet, ReadOnlyModelViewSet) where possible.

Use django-filters for robust filtering.

Standardize response formats (e.g., JSend or similar).

Implement API versioning from the start (e.g., /api/v1/...).

Secure all endpoints with appropriate authentication and permission classes.

Use HATEOAS principles where beneficial (links to related resources).

5.16. Services Layer (Business Logic Abstraction)

Create Python classes/modules in each app (e.g., companies/services.py) to encapsulate complex business logic, keeping ViewSets and Celery tasks thin.

Example: OpportunityService.change_stage(opportunity, new_stage, user_actor=None, agent_actor_name=None)

Validates transition.

Updates Opportunity.current_stage.

Creates OpportunityStageHistory record.

Creates Activity record.

Creates AuditLog record.

Updates Opportunity.last_activity_at.

Potentially triggers notifications.

5.17. Background Task Processing (Celery)

Define tasks in tasks.py within each relevant app.

Examples:

contacts.tasks.import_contacts_from_csv(company_id, file_path, user_id)

calendar_integration.tasks.sync_calendar_event(event_id)

targets.tasks.recalculate_sales_target_achievement(target_id)

activities.tasks.process_inbound_email(raw_email_data)

ai_platform.tasks.log_llm_interaction_async(...)

Use Celery Beat for scheduled tasks (e.g., daily target recalculation, A2A SalesManagerAgent daily run trigger).

Ensure tasks are idempotent where possible.

Implement robust error handling and retry mechanisms within tasks.

6. A2A (Agent-to-Agent) Server & AI Agent Implementation
6.1. A2A Server Setup

Separate Python project/directory (e.g., aegis_a2a_server).

Install a2a SDK.

__main__.py to configure and run the A2AStarletteApplication.

Register each agent executor with its AgentCard and AgentSkill.

Use InMemoryTaskStore for simplicity, or a persistent one for production.

The A2A server will make authenticated HTTP calls to the Django REST API to fetch data and persist results. It needs a service account User and Token from the Django app.

6.2. Base Agent Executor & Utilities

Create a BaseAgentExecutor(AgentExecutor):

__init__: Loads global agent settings and company-specific overrides (fetched from Django API based on company_id in task payload).

Helper method get_django_api_client(): Returns a requests.Session pre-configured with base URL and auth token.

Helper method call_llm(prompt_template_name, context_vars, company_id):

Fetches PromptTemplate from Django API.

Renders prompt.

Calls LLMService (via Django API or direct if A2A server has LLM SDKs).

Logs interaction via LLMInteractionLog.

Returns parsed LLM response.

Helper method log_activity_to_django(...).

Helper method update_opportunity_in_django(...).

All specific agents will inherit from this base.

6.3. SalesOrchestratorAgent

Trigger: A2A message from Django (e.g., when a new "Campaign" is launched or a batch of leads needs processing).

Input Payload: { "company_id": "...", "campaign_id": "...", "lead_ids": [...], "product_ids": [...], "sales_target_id": "..." }

Logic:

Fetch CompanySettings, Campaign details, Product info, SalesTarget from Django.

For each lead_id, create/verify an Opportunity in Django (or instruct Django to do so).

Batch Opportunity IDs.

For each batch, send an A2A message to ProspectingAgent with { "company_id": "...", "opportunity_ids": [...], "product_ids": [...] }.

Monitor ProspectingAgent task events (e.g., "batch_complete").

Listen for events from other agents (e.g., QualificationAgent signals "qualified_lead").

Based on these events, trigger the next appropriate agent in the Schiffman sequence (e.g., PresentationAgent for a qualified lead).

Output: A2A messages to other agents. Status updates to Django (e.g., updating Campaign progress).

6.4. ProspectingAgent

Trigger: A2A message from SalesOrchestratorAgent.

Input Payload: { "company_id": "...", "opportunity_ids": [...], "product_ids": [...] }

Logic (per opportunity):

Fetch Opportunity, Contact, CompanySettings, Product details from Django.

Select PromptTemplate for "Initial Outreach Email" (versioned, A/B testable).

call_llm() to generate personalized email body. Context vars: contact details, company details, product benefits.

Construct email (subject, body, signature from CompanySettings).

Send email via Email Service (could be a direct SMTP call using CompanySettings.smtp_settings or an API call to SendGrid/Mailgun, routed through a Django service for central management).

log_activity_to_django(): EMAIL_SENT_AI, include email content, LLMInteractionLog reference.

Update Opportunity.last_activity_at in Django.

Schedule follow-up tasks (using an internal A2A task queue or Celery via Django API) based on CompanySettings (e.g., "Follow-up in 3 days if no reply").

Output: Emails sent. Activities logged. Follow-up tasks scheduled.

6.5. QualificationAgent

Trigger: A2A message when an inbound email reply is associated with an Opportunity in a prospecting stage (Django webhook → A2A).

Input Payload: { "company_id": "...", "opportunity_id": "...", "email_activity_id": "..." }

Logic:

Fetch Opportunity, Contact, Activity (the received email), CompanySettings from Django.

Select PromptTemplate for "BANT Qualification" or similar.

call_llm() with email body. LLM should return structured JSON (e.g., { "budget_found": true, "authority_confirmed": false, "need_expressed": "high", "timeline_indicated": "3-6 months", "qualification_score": 0.75, "summary": "..." }).

log_activity_to_django(): NOTE_AI with LLM's BANT analysis.

Based on qualification_score and CompanySettings.qualification_threshold:

If qualified: Call Django service to move Opportunity to "Qualified" stage. Send A2A message to SalesOrchestratorAgent or directly to PresentationAgent.

If not qualified: Call Django service to move Opportunity to "Closed Lost" or a "Nurturing" stage with appropriate reason.

Output: Opportunity stage updated. Activities logged. A2A message to next agent if qualified.

6.6. PresentationAgent

Trigger: A2A message for a qualified Opportunity.

Input Payload: { "company_id": "...", "opportunity_id": "..." }

Logic:

Fetch Opportunity, Contact, Organization, Product(s), CompanySettings from Django.

Generate Proposal:

Select PromptTemplate for "Proposal Generation".

call_llm() to generate proposal content (text/HTML). Context: customer needs (from qualification), product details, pricing.

Optionally convert to PDF (using a library like WeasyPrint, possibly via a Django microservice).

Store proposal URL (e.g., in S3 via Django) and link it to Opportunity.

log_activity_to_django(): PROPOSAL_GENERATED_AI.

Schedule Demo/Meeting:

Select PromptTemplate for "Meeting Request Email".

call_llm() to generate email suggesting demo times (considering CompanySettings.calendar_provider availability if integrated).

Send email. log_activity_to_django(): EMAIL_SENT_AI.

If AI can access calendar: Create CalendarEvent in Django, which then syncs to external calendar. log_activity_to_django(): MEETING_SCHEDULED_AI.

Update Opportunity stage to "Presentation" or "Demo Scheduled".

Output: Proposal generated/sent. Meeting scheduled/requested. Activities logged. Opportunity stage updated.

6.7. ObjectionHandlingAgent

Trigger: A2A message when an inbound email reply (post-presentation/proposal) is flagged as an objection.

Input Payload: { "company_id": "...", "opportunity_id": "...", "email_activity_id": "..." }

Logic:

Fetch Opportunity, Activity (objection email), CompanySettings from Django.

Select PromptTemplate for "Objection Classification".

call_llm() to identify objection type (e.g., price, feature, competitor, timing).

Select PromptTemplate for "Objection Response - {{objection_type}}".

call_llm() to generate a tailored response.

Send email. log_activity_to_django(): EMAIL_SENT_AI.

If objection resolved or needs negotiation, update Opportunity stage or trigger NegotiationAgent.

Output: Response email sent. Activities logged. Opportunity stage potentially updated.

6.8. NegotiationAgent

Trigger: A2A message when an Opportunity enters a negotiation phase (e.g., after objection handling, or direct request).

Input Payload: { "company_id": "...", "opportunity_id": "...", "negotiation_context": "..." } (context might be last email or specific terms).

Logic:

Fetch Opportunity, Product(s) with PricingRules, CompanySettings (e.g., discount limits) from Django.

Select PromptTemplate for "Negotiation Response/Counter-Offer".

call_llm() to draft response. Context: current offer, customer request, allowed negotiation parameters.

Send email. log_activity_to_django(): EMAIL_SENT_AI.

If agreement reached, update Opportunity (e.g., estimated_value, custom terms) and trigger ClosingAgent.

Output: Negotiation email sent. Activities logged. Opportunity updated.

6.9. ClosingAgent

Trigger: A2A message when negotiation is complete and Opportunity is ready for closing.

Input Payload: { "company_id": "...", "opportunity_id": "..." }

Logic:

Fetch Opportunity, final agreed terms, CompanySettings (e-signature provider info) from Django.

Generate Contract:

Select PromptTemplate or use a document templating engine (e.g., DocxTemplater, or direct PDF generation) to create the contract.

Store contract URL. log_activity_to_django(): CONTRACT_GENERATED_AI.

Send for E-Signature:

Integrate with CompanySettings.esign_provider (DocuSign, PandaDoc) via their API.

Send contract for signature. log_activity_to_django(): CONTRACT_SENT_AI.

Monitor e-signature provider webhooks (via a Django endpoint). When signed:

Call Django service to update Opportunity.status to "Won", actual_value, actual_close_date.

log_activity_to_django(): ESIGN_COMPLETED, STAGE_CHANGED_AI.

Trigger FollowUpAgent.

Output: Contract sent for signature. Opportunity status updated to "Won" upon signing.

6.10. FollowUpAgent

Trigger: A2A message after an Opportunity is "Won".

Input Payload: { "company_id": "...", "opportunity_id": "..." }

Logic:

Fetch Opportunity, Contact, CompanySettings (follow-up sequences) from Django.

Select PromptTemplate for "Post-Sale Welcome/Onboarding Email".

call_llm() to generate email.

Send email. log_activity_to_django(): EMAIL_SENT_AI.

Schedule further follow-ups (e.g., 30-day check-in, satisfaction survey) based on CompanySettings via A2A task queue or Celery.

Output: Follow-up emails sent. Future follow-up tasks scheduled.

6.11. SalesManagerAgent (AI Overseer)

Trigger:

Scheduled (e.g., daily via Celery task in Django that sends A2A message).

Event-driven (e.g., significant deal closed, target missed, user chat request via Django).

Input Payload (Daily Run): { "type": "daily_run", "company_id": "..." }

Input Payload (Chat): { "type": "on_demand_chat", "company_id": "...", "user_question": "..." }

Logic (Daily Run):

Fetch comprehensive analytics from Django: SalesTarget progress, funnel conversion rates, pipeline health, stalled opportunities, agent performance metrics.

Select PromptTemplate for "Daily Sales Summary & Action Plan".

call_llm() to generate:

Summary of current state.

Insights and warnings (e.g., "Pacing behind target", "Low conversion at Qualification stage").

Recommended actions for other AI agents (e.g., "ProspectingAgent: Increase outreach for Campaign X", "QualificationAgent: Adjust BANT scoring for Product Y").

Send summary email to designated human Sales Manager(s) (from CompanySettings or User roles).

Send A2A "task" messages to other agents with specific instructions (e.g., adjust parameters, prioritize certain leads).

Logic (Chat):

Fetch relevant data from Django based on user_question.

Select PromptTemplate for "Sales Manager Chat Response".

call_llm() to generate answer.

Stream response back to Django (which proxies to user's chat UI).

Output: Daily summary email. A2A task messages to other agents. Chat responses.

6.12. Agent Configuration & Prompt Management

Global default prompts stored in ai_platform.PromptTemplate.

CompanySettings.ai_agent_config_override (JSON) allows per-company overrides of:

Specific PromptTemplate UUIDs to use for certain actions.

Thresholds (e.g., qualification_score_threshold).

Parameters (e.g., max_follow_ups).

Agents always fetch their configuration (prompts, settings) at runtime based on company_id.

7. LLM Integration Strategy

Use the ai_platform.LLMService in Django as the primary interface for LLM calls. A2A agents can call this service via a REST API endpoint.

This service handles:

Selecting the appropriate AIModel based on PromptTemplate.llm_model_preference or company defaults.

Retrieving and rendering PromptTemplate.template_text with context variables.

Making the API call to the chosen LLM provider.

Handling API errors, retries.

Parsing structured output (e.g., JSON) if output_format_schema is defined.

Logging the full interaction to LLMInteractionLog (can be async via Celery).

Support for function calling/tool use with LLMs should be built into LLMService.

8. External System Integrations
8.1. Email (Inbound/Outbound SMTP/API)

Outbound: Agents use CompanySettings.smtp_settings or an Email API Service (SendGrid, Mailgun). Centralize sending logic in an EmailService within Django, callable by agents.

Inbound:

Dedicated mailboxes per company (e.g., <EMAIL>).

Use email provider webhooks (e.g., SendGrid Inbound Parse, Mailgun Routes) to forward raw emails to a Django endpoint (/api/inbound-email/).

Django endpoint:

Authenticates webhook source.

Parses raw email (sender, recipients, subject, body, thread ID, message ID, attachments).

Identifies Company based on recipient address.

Identifies Contact based on sender email.

Attempts to link to an existing Opportunity (e.g., via thread ID, or contact's open opps).

Creates Activity (EMAIL_RECEIVED).

If linked to an Opportunity, sends A2A message to QualificationAgent or ObjectionHandlingAgent etc., depending on Opportunity.current_stage.

8.2. Calendar (Google, Outlook)

CalendarService in Django.

OAuth 2.0 for authentication, tokens stored securely in CompanySettings.calendar_credentials.

CRUD operations for events.

Two-way sync (Django CalendarEvent ↔ External Calendar) handled by Celery tasks.

Changes in Django trigger update to external.

Periodic polling or webhooks (if provider supports) for external changes trigger update to Django.

8.3. E-Signature (DocuSign, PandaDoc)

ESignatureService in Django.

API key/token auth stored in CompanySettings.esign_credentials.

Send documents for signature, track status.

Webhook endpoint in Django (/api/esign-webhook/) to receive status updates (e.g., "document signed"). Authenticate webhook.

8.4. CRM Sync (Optional: Salesforce, HubSpot)

If required, build dedicated services and Celery tasks for bi-directional sync of Contacts, Organizations, Opportunities. This is complex and needs careful mapping.

9. Security Considerations

Authentication: JWT or robust Token Auth for APIs. OAuth2 for external services.

Authorization: Granular role-based access control (RBAC). Users only see/modify data for their Company.

Input Validation: Rigorous validation for all API inputs and data from external sources.

Output Encoding: Prevent XSS in any UI.

Data Encryption: TLS for data in transit. Encryption at rest for database and sensitive files.

Secret Management: API keys, DB passwords, SECRET_KEY via environment variables or a vault system (e.g., HashiCorp Vault). CompanySettings credentials should be encrypted.

Rate Limiting: For APIs to prevent abuse.

Dependency Scanning: Regularly scan for vulnerabilities in libraries.

Regular Security Audits & Penetration Testing.

A2A Server Security: Ensure it's not publicly exposed without authentication if it handles sensitive commands directly. Ideally, it's within a private network, and Django acts as the authenticated gateway.

LLM Data Privacy: Be mindful of sending PII to LLMs. Use providers with data privacy agreements (e.g., OpenAI Data Processing Addendum, Azure OpenAI). Consider anonymization techniques if possible.

10. Scalability & Performance

Database: Proper indexing on all foreign keys and frequently queried fields. Connection pooling. Read replicas for analytics if needed.

Celery: Sufficient worker processes. Horizontal scaling of workers. Task prioritization.

Django: Stateless application servers. Horizontal scaling. Caching (Redis) for frequently accessed, rarely changed data (e.g., CompanySettings, PromptTemplates).

A2A Server: Can be scaled horizontally if agents are stateless or manage state externally (e.g., via Django DB).

API Design: Efficient queries. Use select_related and prefetch_related. Paginate large datasets.

Load Testing: Simulate high traffic to identify bottlenecks.

11. Data Management & Compliance

PII Handling: Identify and protect PII.

Data Retention Policies: Implement mechanisms for data archival and deletion based on company policies and regulations.

Opt-Outs: Respect Contact.do_not_email/do_not_call and unsubscribed_at.

GDPR/CCPA: Features for data subject access requests (DSAR), right to be forgotten.

Backups: Regular, automated, and tested database backups.

12. Testing Strategy

Unit Tests: For individual functions, methods, Django models, services. (Pytest, Django TestCase).

Integration Tests: For interactions between components (e.g., API endpoint → service → model). (DRF APITestCase).

A2A Agent Tests: Mock Django API calls and LLM responses to test agent logic.

Celery Task Tests: Ensure tasks execute correctly.

End-to-End Tests: (Selenium, Cypress - for the frontend interacting with backend).

Code Coverage: Aim for high coverage (e.g., >85%).

Static Analysis: Flake8, Black, Mypy.

13. Deployment Strategy

Containerization: Docker for all services (Django, Celery, A2A Server, Redis, PostgreSQL).

Orchestration: Kubernetes (EKS, GKE, AKS) or Docker Swarm for production.

CI/CD Pipeline: Automated testing, building, and deployment.

Environment Configuration: Separate configs for dev, staging, production.

Database Migrations: Handled by Django migrations, applied carefully during deployment.

Logging & Monitoring: Centralized logging (ELK stack, Datadog) and application performance monitoring (Sentry, Prometheus/Grafana).

14. UI/UX High-Level Guidelines (for Frontend Devs)

Dashboard: KPIs, funnel snapshot, recent AI activities, pending tasks.

Lead/Contact Management: List views, detail views, import/export.

Opportunity Management: Kanban pipeline view, list view, detail view (timeline of all activities, emails, notes).

Campaign Management: Setup, launch, monitor AI-driven campaigns.

Calendar View: Integrated calendar showing AI-scheduled and manually added events.

Reporting: Customizable dashboards for analytics.

Settings:

Company profile, branding.

Product catalog.

Sales process (stages, pipelines).

User management, roles, teams.

AI Agent configuration (prompt overrides, thresholds).

Integrations (email, calendar, e-sign).

Chat with SalesManagerAgent: Interactive interface.

Notifications Center: In-app alerts.

Real-time Updates: Use WebSockets or SSE for notifications and live data updates where appropriate.

15. Future Enhancements

Advanced A/B testing framework for prompts and email templates.

Predictive analytics (e.g., lead scoring, churn prediction).

Automated sales playbook generation and execution.

Deeper BI tool integration (Tableau, PowerBI).

Voice integration (AI call analysis, automated call logging).

Fine-tuning custom LLMs on company-specific sales data.

Multi-language support for AI communication and UI.

---

## 🔧 **TECHNICAL DEBUGGING GUIDE**

### **🏗️ DJANGO ARCHITECTURE DEEP DIVE**

#### **📋 CRITICAL SETTINGS CONFIGURATION**

**INSTALLED_APPS Order (CURRENT IMPLEMENTATION)**
```python
# aegis_sales_platform/settings.py
INSTALLED_APPS = [
    # Django Core Apps
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",

    # Custom Apps - ORDER MATTERS for dependencies
    "companies",        # FIRST - Base company model
    "users",           # SECOND - Custom user extends auth
    "contacts",        # THIRD - Depends on companies/users
    "sales_process",   # FOURTH - Stages/pipelines for opportunities
    "products",        # FIFTH - Product catalog
    "targets",         # SIXTH - Depends on users/products
    "activities",      # SEVENTH - Logging depends on all models
    "opportunities",   # EIGHTH - Central model with many FKs
    "ai_agents",       # NINTH - AI management layer
    "ai_platform",     # TENTH - LLM integration platform
    "core",           # ELEVENTH - Dashboard depends on all
]

# IMPORTANT: Custom user model currently disabled for testing
# AUTH_USER_MODEL = 'users.User'  # Commented out - using default User model
```

#### **🔗 URL ROUTING ARCHITECTURE**

**Main URLs (aegis_sales_platform/urls.py)**
```python
urlpatterns = [
    path("admin/", admin.site.urls),
    path('', include('core.urls')),                    # Root URLs
    path('contacts/', include('contacts.urls')),       # Contact management
    path('activities/', include('activities.urls')),   # Activity tracking
    path('opportunities/', include('opportunities.urls')), # Sales opportunities
    path('products/', include('products.urls')),       # Product catalog
    path('sales-process/', include('sales_process.urls')), # Sales pipeline
    path('targets/', include('targets.urls')),         # Sales targets
    path('companies/', include('companies.urls')),     # Company management
    # API and AI Agents
    path('api/v1/', include('api.urls')),              # REST API
    path('ai-agents/', include('ai_agents.urls')),     # AI agent management
]
```

**Core URLs (core/urls.py) - NO app_name**
```python
# NO app_name for root URLs
urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.placeholder_view, name='profile'),
    path('company-settings/', views.placeholder_view, name='company_settings'),
    path('analytics/', views.analytics_dashboard, name='analytics_dashboard'),
    path('ai-agents/', views.ai_agents_dashboard, name='ai_agents_dashboard'),
    path('ai-agents/configure/', views.ai_agents_configure, name='ai_agents_configure'),
    path('system-status/', views.system_status, name='system_status'),
    path('ai-activity-logs/', views.placeholder_view, name='ai_activity_logs'),
]
```

**Feature URLs (contacts/urls.py) - WITH app_name**
```python
app_name = 'contacts'  # CRITICAL for namespacing
urlpatterns = [
    path('', views.contacts_list, name='contacts_list'),
    path('create/', views.contact_create, name='contact_create'),
    path('<uuid:uuid>/', views.contact_detail, name='contact_detail'),
    path('<uuid:uuid>/edit/', views.contact_edit, name='contact_edit'),
    path('<uuid:uuid>/delete/', views.contact_delete, name='contact_delete'),
    path('<uuid:uuid>/email-thread/', views.contact_email_thread, name='contact_email_thread'),
    path('<uuid:uuid>/email/', views.contact_email, name='contact_email'),
    path('<uuid:uuid>/log-activity/', views.contact_log_activity, name='contact_log_activity'),
    path('<uuid:uuid>/add-note/', views.contact_add_note, name='contact_add_note'),
    path('import/', views.import_contacts, name='import_contacts'),
    path('export/', views.export_contacts, name='export_contacts'),
    # Organizations and Tags
    path('organizations/', views.organization_list, name='organization_list'),
    path('tags/', views.contact_tag_list, name='contact_tag_list'),
]
```

#### **🎯 CURRENT IMPLEMENTATION STATUS**

**✅ FULLY IMPLEMENTED APPS:**
- **Core App**: Dashboard, analytics, authentication, system status
- **Contacts App**: Full CRUD operations, email threads, import/export, organizations
- **Opportunities App**: Complete opportunity management with stage tracking
- **Activities App**: Activity logging and management
- **Products App**: Product catalog with CRUD operations
- **Sales Process App**: Basic sales process management (placeholder views)
- **Targets App**: Sales target tracking and dashboard
- **Companies App**: Company profile and AI context management
- **AI Agents App**: AI agent dashboard and A2A protocol integration
- **API App**: REST API endpoints for contacts, opportunities, activities

**⚠️ PLACEHOLDER IMPLEMENTATIONS:**
- **Users App**: Models defined but views not implemented
- **AI Platform App**: Models defined but views not implemented
- **Calendar Integration**: Not yet implemented
- **Notifications**: Not yet implemented
- **Audit**: Not yet implemented
- **Analytics**: Basic implementation, needs enhancement

**🔧 CURRENT CONFIGURATION:**
- Using Django's default User model (custom User model disabled)
- SQLite database for development
- Email backend configured for A2A agents
- All URL namespaces properly configured
- Company attribute handling implemented across all views

#### **🎨 TEMPLATE URL REVERSING**

**Navigation in Templates**
```html
<!-- Root URLs (no namespace) -->
<a href="{% url 'dashboard' %}">Dashboard</a>
<a href="{% url 'analytics_dashboard' %}">Analytics</a>

<!-- Namespaced URLs -->
<a href="{% url 'contacts:list' %}">Contacts</a>
<a href="{% url 'contacts:detail' contact.uuid %}">Contact Detail</a>
<a href="{% url 'opportunities:list' %}">Opportunities</a>
```

#### **🗄️ MODEL RELATIONSHIPS & DEBUGGING**

**Foreign Key Dependencies**
```python
# companies/models.py - Base model
class Company(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=200, unique=True)

# users/models.py - Extends auth
class User(AbstractUser):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

# contacts/models.py - Depends on Company
class Contact(models.Model):
    company_context = models.ForeignKey(Company, on_delete=models.CASCADE)

# opportunities/models.py - Multiple dependencies
class Opportunity(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    primary_contact = models.ForeignKey('contacts.Contact', on_delete=models.PROTECT)
    current_stage = models.ForeignKey('sales_process.SalesStage', on_delete=models.PROTECT)
```

**Common Migration Issues & Solutions**
```bash
# If you get FK constraint errors:
python manage.py makemigrations companies
python manage.py makemigrations users
python manage.py makemigrations contacts
python manage.py makemigrations sales_process
python manage.py makemigrations products
python manage.py makemigrations opportunities
python manage.py migrate

# Reset migrations if needed:
find . -path "*/migrations/*.py" -not -name "__init__.py" -delete
find . -path "*/migrations/*.pyc" -delete
python manage.py makemigrations
python manage.py migrate
```

### **🤖 AI AGENTS IMPLEMENTATION DEEP DIVE**

#### **🏗️ AI Agents Architecture**

**Base Agent Class (ai_agents/base.py)**
```python
from abc import ABC, abstractmethod
from django.utils import timezone
from activities.models import Activity

class BaseAIAgent(ABC):
    """Base class for all AI agents with common functionality"""

    def __init__(self, company_id):
        self.company_id = company_id
        self.agent_name = self.__class__.__name__
        self.status = 'idle'
        self.last_action_time = None

    @abstractmethod
    def execute_task(self, task_data):
        """Each agent must implement its core logic"""
        pass

    def log_activity(self, activity_type, details, contact=None, opportunity=None):
        """Standard activity logging for all agents"""
        Activity.objects.create(
            company_id=self.company_id,
            activity_type=activity_type,
            created_by_agent_name=self.agent_name,
            details=details,
            contact=contact,
            opportunity=opportunity,
            timestamp=timezone.now()
        )

    def update_status(self, status, action_count=None):
        """Update agent status and metrics"""
        self.status = status
        self.last_action_time = timezone.now()
        # Update metrics in database or cache
```

#### **🎯 Individual Agent Implementations**

**1. Sales Orchestrator Agent (ai_agents/orchestrator_agent.py)**
```python
from .base import BaseAIAgent
from opportunities.models import Opportunity
from contacts.models import Contact

class SalesOrchestratorAgent(BaseAIAgent):
    """Master coordinator for the entire sales process"""

    def execute_task(self, task_data):
        """
        Coordinates sales campaigns and assigns tasks to other agents

        task_data = {
            'campaign_id': 'uuid',
            'lead_ids': ['uuid1', 'uuid2'],
            'product_ids': ['uuid1', 'uuid2']
        }
        """
        campaign_id = task_data.get('campaign_id')
        lead_ids = task_data.get('lead_ids', [])

        results = {
            'opportunities_created': 0,
            'agents_triggered': [],
            'errors': []
        }

        try:
            # Create opportunities for each lead
            for lead_id in lead_ids:
                contact = Contact.objects.get(uuid=lead_id, company_context_id=self.company_id)

                # Create opportunity
                opportunity = Opportunity.objects.create(
                    company_id=self.company_id,
                    name=f"Opportunity for {contact.full_name}",
                    primary_contact=contact,
                    estimated_value=10000,  # Default value
                    status='open'
                )

                results['opportunities_created'] += 1

                # Log activity
                self.log_activity(
                    'OPPORTUNITY_CREATED_AI',
                    {'opportunity_id': str(opportunity.uuid), 'campaign_id': campaign_id},
                    contact=contact,
                    opportunity=opportunity
                )

                # Trigger Prospecting Agent
                self.trigger_prospecting_agent(opportunity.uuid)
                results['agents_triggered'].append('ProspectingAgent')

        except Exception as e:
            results['errors'].append(str(e))

        self.update_status('active', results['opportunities_created'])
        return results

    def trigger_prospecting_agent(self, opportunity_id):
        """Trigger prospecting agent for new opportunity"""
        from .prospecting_agent import ProspectingAgent

        agent = ProspectingAgent(self.company_id)
        agent.execute_task({'opportunity_id': opportunity_id})
```

**2. Prospecting Agent (ai_agents/prospecting_agent.py)**
```python
from .base import BaseAIAgent
from opportunities.models import Opportunity
from .rapport_builder import RapportBuilder

class ProspectingAgent(BaseAIAgent):
    """Handles initial outreach and lead generation"""

    def execute_task(self, task_data):
        """
        Performs initial outreach to prospects

        task_data = {
            'opportunity_id': 'uuid',
            'outreach_type': 'email|call|linkedin'
        }
        """
        opportunity_id = task_data.get('opportunity_id')
        outreach_type = task_data.get('outreach_type', 'email')

        try:
            opportunity = Opportunity.objects.get(
                uuid=opportunity_id,
                company_id=self.company_id
            )
            contact = opportunity.primary_contact

            # Build rapport and personalize message
            rapport_builder = RapportBuilder()
            personalized_message = rapport_builder.build_initial_message(
                contact=contact,
                company=opportunity.company,
                products=opportunity.products.all()
            )

            if outreach_type == 'email':
                result = self.send_email(contact, personalized_message)
            elif outreach_type == 'call':
                result = self.schedule_call(contact)
            else:
                result = self.send_linkedin_message(contact, personalized_message)

            # Log activity
            self.log_activity(
                f'{outreach_type.upper()}_SENT_AI',
                {
                    'message': personalized_message,
                    'result': result,
                    'rapport_score': rapport_builder.get_rapport_score()
                },
                contact=contact,
                opportunity=opportunity
            )

            # Schedule follow-up
            self.schedule_followup(opportunity_id, days=3)

            self.update_status('active')
            return {'success': True, 'outreach_type': outreach_type}

        except Exception as e:
            self.log_activity(
                'ERROR_AI',
                {'error': str(e), 'task': 'prospecting'},
                opportunity=opportunity if 'opportunity' in locals() else None
            )
            return {'success': False, 'error': str(e)}

    def send_email(self, contact, message):
        """Send personalized email to contact"""
        # Email sending logic here
        return {'sent': True, 'message_id': 'email_123'}

    def schedule_followup(self, opportunity_id, days=3):
        """Schedule follow-up task"""
        # Schedule follow-up logic
        pass
```

**3. Qualification Agent (ai_agents/qualification_agent.py)**
```python
from .base import BaseAIAgent
from opportunities.models import Opportunity
import json

class QualificationAgent(BaseAIAgent):
    """Performs BANT qualification and lead scoring"""

    def execute_task(self, task_data):
        """
        Analyzes prospect responses for BANT qualification

        task_data = {
            'opportunity_id': 'uuid',
            'email_content': 'response text',
            'activity_id': 'uuid'
        }
        """
        opportunity_id = task_data.get('opportunity_id')
        email_content = task_data.get('email_content', '')

        try:
            opportunity = Opportunity.objects.get(
                uuid=opportunity_id,
                company_id=self.company_id
            )

            # Perform BANT analysis using LLM
            bant_analysis = self.analyze_bant(email_content, opportunity)

            # Calculate qualification score
            qualification_score = self.calculate_qualification_score(bant_analysis)

            # Update opportunity stage based on score
            if qualification_score >= 0.7:
                self.advance_to_qualified(opportunity)
                next_agent = 'PresentationAgent'
            elif qualification_score >= 0.4:
                self.move_to_nurturing(opportunity)
                next_agent = 'FollowUpAgent'
            else:
                self.mark_unqualified(opportunity, bant_analysis['disqualification_reason'])
                next_agent = None

            # Log qualification results
            self.log_activity(
                'QUALIFICATION_ANALYSIS_AI',
                {
                    'bant_analysis': bant_analysis,
                    'qualification_score': qualification_score,
                    'next_action': next_agent
                },
                contact=opportunity.primary_contact,
                opportunity=opportunity
            )

            self.update_status('active')
            return {
                'qualified': qualification_score >= 0.7,
                'score': qualification_score,
                'next_agent': next_agent
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def analyze_bant(self, email_content, opportunity):
        """Analyze email content for BANT criteria"""
        # LLM analysis logic here
        return {
            'budget_indicated': True,
            'authority_confirmed': False,
            'need_expressed': 'high',
            'timeline_mentioned': '3-6 months',
            'qualification_score': 0.75,
            'key_insights': ['Budget of $50k mentioned', 'Decision maker not identified'],
            'disqualification_reason': None
        }

    def calculate_qualification_score(self, bant_analysis):
        """Calculate overall qualification score from BANT analysis"""
        score = 0.0

        if bant_analysis.get('budget_indicated'):
            score += 0.3
        if bant_analysis.get('authority_confirmed'):
            score += 0.3
        if bant_analysis.get('need_expressed') == 'high':
            score += 0.25
        elif bant_analysis.get('need_expressed') == 'medium':
            score += 0.15
        if bant_analysis.get('timeline_mentioned'):
            score += 0.15

        return min(score, 1.0)
```

#### **🔄 Agent Coordination & Communication**

**Agent Task Queue (ai_agents/task_queue.py)**
```python
from django.core.cache import cache
import json
from datetime import datetime, timedelta

class AgentTaskQueue:
    """Manages task distribution between AI agents"""

    def __init__(self):
        self.queue_key = 'ai_agent_tasks'

    def add_task(self, agent_name, task_data, priority='normal', delay_seconds=0):
        """Add task to agent queue"""
        task = {
            'id': f"{agent_name}_{datetime.now().timestamp()}",
            'agent': agent_name,
            'data': task_data,
            'priority': priority,
            'created_at': datetime.now().isoformat(),
            'execute_at': (datetime.now() + timedelta(seconds=delay_seconds)).isoformat(),
            'status': 'pending'
        }

        # Add to Redis queue
        queue = cache.get(self.queue_key, [])
        queue.append(task)
        cache.set(self.queue_key, queue, timeout=86400)  # 24 hours

        return task['id']

    def get_next_task(self, agent_name):
        """Get next task for specific agent"""
        queue = cache.get(self.queue_key, [])
        now = datetime.now()

        for task in queue:
            if (task['agent'] == agent_name and
                task['status'] == 'pending' and
                datetime.fromisoformat(task['execute_at']) <= now):

                task['status'] = 'processing'
                cache.set(self.queue_key, queue, timeout=86400)
                return task

        return None

    def complete_task(self, task_id, result):
        """Mark task as completed"""
        queue = cache.get(self.queue_key, [])

        for task in queue:
            if task['id'] == task_id:
                task['status'] = 'completed'
                task['result'] = result
                task['completed_at'] = datetime.now().isoformat()
                break

        cache.set(self.queue_key, queue, timeout=86400)
```

### **🎨 VIEWS & TEMPLATES DEBUGGING**

#### **📊 Dashboard Views Implementation**

**Core Dashboard View (core/views.py)**
```python
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count
from opportunities.models import Opportunity
from activities.models import Activity
from contacts.models import Contact

@login_required
def dashboard(request):
    """Main dashboard with key metrics"""
    if not request.user.company:
        return render(request, 'core/no_company.html')

    company = request.user.company

    # Calculate metrics safely with error handling
    try:
        metrics = {
            'total_opportunities': Opportunity.objects.filter(company=company).count(),
            'pipeline_value': Opportunity.objects.filter(
                company=company, status='open'
            ).aggregate(total=Sum('estimated_value'))['total'] or 0,
            'total_contacts': Contact.objects.filter(company_context=company).count(),
            'activities_today': Activity.objects.filter(
                company=company,
                timestamp__date=timezone.now().date()
            ).count(),
        }

        # Recent activities
        recent_activities = Activity.objects.filter(
            company=company
        ).order_by('-timestamp')[:10]

        # Top opportunities
        top_opportunities = Opportunity.objects.filter(
            company=company,
            status='open'
        ).order_by('-estimated_value')[:5]

    except Exception as e:
        # Fallback to default values if any error
        metrics = {'total_opportunities': 0, 'pipeline_value': 0, 'total_contacts': 0, 'activities_today': 0}
        recent_activities = []
        top_opportunities = []

    context = {
        'metrics': metrics,
        'recent_activities': recent_activities,
        'top_opportunities': top_opportunities,
    }

    return render(request, 'core/dashboard.html', context)

@login_required
def analytics_dashboard(request):
    """Analytics dashboard with charts and reports"""
    company = request.user.company

    # Prepare data for charts
    pipeline_data = []
    try:
        from sales_process.models import SalesStage
        stages = SalesStage.objects.filter(company=company).order_by('order')

        for stage in stages:
            count = Opportunity.objects.filter(
                company=company,
                current_stage=stage,
                status='open'
            ).count()
            pipeline_data.append({'stage': stage.name, 'count': count})

    except Exception as e:
        pipeline_data = []

    context = {
        'pipeline_data': pipeline_data,
        'chart_labels': [item['stage'] for item in pipeline_data],
        'chart_values': [item['count'] for item in pipeline_data],
    }

    return render(request, 'core/analytics.html', context)
```

#### **🔍 Common Debugging Issues & Solutions**

**1. NoReverseMatch Errors**
```python
# Problem: {% url 'contacts:detail' contact.id %}
# Solution: Use UUID field
{% url 'contacts:detail' contact.uuid %}

# Problem: Missing app_name in urls.py
# Solution: Add app_name to feature apps
# contacts/urls.py
app_name = 'contacts'

# Problem: Wrong URL name
# Solution: Check urlpatterns name parameter
path('<uuid:pk>/', views.contact_detail, name='detail'),  # Use 'detail' not 'contact_detail'
```

**2. Template Not Found Errors**
```python
# Check template directory structure:
templates/
├── base.html
├── core/
│   └── dashboard.html
└── contacts/
    └── contact_list.html

# Ensure TEMPLATES setting includes app directories:
'APP_DIRS': True,

# Check view render call:
return render(request, 'contacts/contact_list.html', context)  # Not just 'contact_list.html'
```

**3. Foreign Key Constraint Errors**
```python
# Problem: Creating object without required FK
contact = Contact.objects.create(
    email='<EMAIL>'
    # Missing company_context!
)

# Solution: Always include required FKs
contact = Contact.objects.create(
    email='<EMAIL>',
    company_context=request.user.company  # Required FK
)
```

**4. User Authentication Issues**
```python
# Problem: User not associated with company
if not request.user.company:
    messages.error(request, "You must be associated with a company")
    return redirect('profile')

# Solution: Ensure test users have company
# In management command or admin:
user.company = Company.objects.first()
user.save()
```

### **🚨 TROUBLESHOOTING CHECKLIST**

#### **When Dashboard Doesn't Load**
1. **Check User Company Association**
   ```python
   # In Django shell
   user = User.objects.get(email='<EMAIL>')
   print(user.company)  # Should not be None
   ```

2. **Verify Database Migrations**
   ```bash
   python manage.py showmigrations
   python manage.py migrate
   ```

3. **Check Template Paths**
   ```python
   # In settings.py
   TEMPLATES[0]['DIRS'] = [BASE_DIR / 'templates']
   TEMPLATES[0]['APP_DIRS'] = True
   ```

#### **When URLs Don't Work**
1. **Check URL Patterns Order**
   ```python
   # More specific patterns first
   path('<uuid:pk>/edit/', views.edit, name='edit'),
   path('<uuid:pk>/', views.detail, name='detail'),
   path('', views.list, name='list'),
   ```

2. **Verify Namespace Usage**
   ```html
   <!-- For apps with app_name -->
   {% url 'contacts:list' %}

   <!-- For core app (no app_name) -->
   {% url 'dashboard' %}
   ```

#### **When AI Agents Don't Work**
1. **Check Agent Registration**
   ```python
   # In ai_agents/views.py
   AVAILABLE_AGENTS = {
       'orchestrator': SalesOrchestratorAgent,
       'prospecting': ProspectingAgent,
       # ... all agents
   }
   ```

2. **Verify Task Queue**
   ```python
   # Check Redis connection
   from django.core.cache import cache
   cache.set('test', 'value')
   print(cache.get('test'))  # Should print 'value'
   ```

### **📝 DEBUGGING COMMANDS**

```bash
# Check all URLs
python manage.py show_urls

# Validate models
python manage.py check

# Create test data
python manage.py shell
>>> from companies.models import Company
>>> company = Company.objects.create(name='Test Company')

# Reset database
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser

# Check template rendering
python manage.py shell
>>> from django.template.loader import get_template
>>> template = get_template('core/dashboard.html')
>>> print(template.source)
```
```