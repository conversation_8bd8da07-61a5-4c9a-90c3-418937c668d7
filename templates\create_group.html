{% extends "base.html" %}

{% block title %}Create Group - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-plus-circle"></i> Create Contact Group</h2>
                <a href="{{ url_for('groups_list') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Groups
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-layer-group"></i> Group Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Group Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required maxlength="255" placeholder="Enter group name">
                            <div class="form-text">Choose a descriptive name for your contact group.</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Optional description of this group"></textarea>
                            <div class="form-text">Provide additional details about this group's purpose or criteria.</div>
                        </div>

                        <div class="mb-3">
                            <label for="color" class="form-label">Group Color</label>
                            <div class="d-flex align-items-center">
                                <input type="color" class="form-control form-control-color me-3" id="color" name="color" value="#007bff" title="Choose group color">
                                <span class="text-muted">Select a color to help identify this group</span>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('groups_list') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Group
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tips Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Tips for Organizing Groups</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Group Ideas:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Industry-based (Tech, Healthcare, Finance)</li>
                                <li><i class="fas fa-check text-success"></i> Company size (Startup, SMB, Enterprise)</li>
                                <li><i class="fas fa-check text-success"></i> Geographic regions</li>
                                <li><i class="fas fa-check text-success"></i> Lead sources (Website, Referral, Event)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Best Practices:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Use clear, descriptive names</li>
                                <li><i class="fas fa-check text-success"></i> Keep groups focused and specific</li>
                                <li><i class="fas fa-check text-success"></i> Use colors to categorize group types</li>
                                <li><i class="fas fa-check text-success"></i> Add descriptions for team clarity</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Group name is required.');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length > 255) {
        e.preventDefault();
        alert('Group name must be 255 characters or less.');
        document.getElementById('name').focus();
        return false;
    }
});

// Real-time character count for name field
document.getElementById('name').addEventListener('input', function() {
    const maxLength = 255;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // Find or create character count display
    let countDisplay = document.getElementById('nameCharCount');
    if (!countDisplay) {
        countDisplay = document.createElement('div');
        countDisplay.id = 'nameCharCount';
        countDisplay.className = 'form-text';
        this.parentNode.appendChild(countDisplay);
    }
    
    countDisplay.textContent = `${currentLength}/${maxLength} characters`;
    
    if (remaining < 20) {
        countDisplay.className = 'form-text text-warning';
    } else {
        countDisplay.className = 'form-text text-muted';
    }
});
</script>
{% endblock %}
