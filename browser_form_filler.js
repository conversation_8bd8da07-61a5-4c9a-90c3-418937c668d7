// JavaScript to fill out the campaign creation form automatically
// Copy and paste this into your browser console on the campaign creation page

function fillCampaignForm() {
    console.log("🚀 Filling out campaign creation form...");
    
    // Fill campaign name
    const nameField = document.getElementById('name');
    if (nameField) {
        nameField.value = 'Browser Test Campaign ' + new Date().getTime();
        console.log("✅ Campaign name filled");
    }
    
    // Select template
    const templateField = document.getElementById('template');
    if (templateField && templateField.options.length > 1) {
        templateField.selectedIndex = 1; // Select first available template
        templateField.dispatchEvent(new Event('change'));
        console.log("✅ Template selected");
    }
    
    // Fill target audience
    const audienceField = document.getElementById('target_audience');
    if (audienceField) {
        audienceField.value = 'Test audience for browser form submission';
        console.log("✅ Target audience filled");
    }
    
    // Select "All Active Contacts" (should be selected by default)
    const allContactsRadio = document.getElementById('all_contacts');
    if (allContactsRadio) {
        allContactsRadio.checked = true;
        allContactsRadio.dispatchEvent(new Event('change'));
        console.log("✅ All contacts selected");
    }
    
    // Set daily send limit
    const dailyLimitField = document.getElementById('daily_send_limit');
    if (dailyLimitField) {
        dailyLimitField.value = '100';
        console.log("✅ Daily limit set to 100");
    }
    
    // Set send schedule to immediate
    const sendScheduleField = document.getElementById('send_schedule');
    if (sendScheduleField) {
        sendScheduleField.value = 'immediate';
        console.log("✅ Send schedule set to immediate");
    }
    
    // Fill sender name
    const senderNameField = document.getElementById('sender_name');
    if (senderNameField) {
        senderNameField.value = '24Seven Assistants Sales Team';
        console.log("✅ Sender name filled");
    }
    
    console.log("✅ Form filled successfully! You can now click 'Create Campaign' button.");
    
    // Highlight the submit button
    const submitButton = document.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.style.backgroundColor = '#28a745';
        submitButton.style.border = '2px solid #ff0000';
        submitButton.style.animation = 'pulse 1s infinite';
        console.log("✅ Submit button highlighted");
    }
    
    return true;
}

function submitForm() {
    console.log("🚀 Submitting form...");
    const form = document.querySelector('form');
    if (form) {
        form.submit();
        console.log("✅ Form submitted!");
    } else {
        console.log("❌ Form not found");
    }
}

function fillAndSubmit() {
    fillCampaignForm();
    setTimeout(() => {
        console.log("🚀 Auto-submitting in 2 seconds...");
        submitForm();
    }, 2000);
}

// Add CSS for pulse animation
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

console.log("📋 Campaign Form Helper Loaded!");
console.log("📝 Available functions:");
console.log("   • fillCampaignForm() - Fill out the form");
console.log("   • submitForm() - Submit the form");
console.log("   • fillAndSubmit() - Fill and auto-submit");
console.log("");
console.log("🎯 To create a campaign automatically, run: fillAndSubmit()");

// Auto-fill the form immediately
fillCampaignForm();
