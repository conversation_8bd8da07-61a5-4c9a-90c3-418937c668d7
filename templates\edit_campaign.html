{% extends "base.html" %}

{% block title %}Edit Campaign - {{ campaign.name }} - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-edit text-primary"></i> Edit Campaign</h1>
    <div>
        <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Details
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cog"></i> Campaign Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Campaign Name</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ campaign.name }}" required>
                        <div class="form-text">Choose a descriptive name for your campaign</div>
                    </div>

                    <div class="mb-3">
                        <label for="subject" class="form-label">Email Subject Line</label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               value="{{ campaign.subject or '' }}" required>
                        <div class="form-text">This will be the subject line of your emails</div>
                    </div>

                    <div class="mb-3">
                        <label for="template_name" class="form-label">Email Template</label>
                        <select class="form-select" id="template_name" name="template_name" required>
                            <option value="">Select a template</option>
                            <option value="introduction" {% if campaign.template_name == 'introduction' %}selected{% endif %}>
                                Introduction - Meet Sarah, Your AI Sales Assistant
                            </option>
                            <option value="follow_up" {% if campaign.template_name == 'follow_up' %}selected{% endif %}>
                                Follow-up - Continue Your Sales Journey
                            </option>
                            <option value="special_offer" {% if campaign.template_name == 'special_offer' %}selected{% endif %}>
                                Special Offer - Limited Time Discount
                            </option>
                            <option value="testimonial" {% if campaign.template_name == 'testimonial' %}selected{% endif %}>
                                Success Stories - See What Our Clients Say
                            </option>
                            <option value="demo_invitation" {% if campaign.template_name == 'demo_invitation' %}selected{% endif %}>
                                Demo Invitation - See Our AI in Action
                            </option>
                        </select>
                        <div class="form-text">Choose the email template that best fits your campaign goals</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Campaign Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Current Status</label>
                    <div>
                        <span class="badge 
                            {% if campaign.status == 'draft' %}bg-secondary
                            {% elif campaign.status == 'sending' %}bg-warning
                            {% elif campaign.status == 'completed' %}bg-success
                            {% elif campaign.status == 'failed' %}bg-danger
                            {% else %}bg-info{% endif %} fs-6">
                            {{ campaign.status.title() }}
                        </span>
                    </div>
                </div>

                {% if campaign.status == 'draft' %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Draft Status</strong><br>
                    This campaign can be edited and sent to contacts.
                </div>
                {% elif campaign.status == 'sending' %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Sending in Progress</strong><br>
                    Limited editing available while campaign is being sent.
                </div>
                {% elif campaign.status == 'completed' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Campaign Completed</strong><br>
                    This campaign has finished sending. Only basic details can be edited.
                </div>
                {% elif campaign.status == 'failed' %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>Campaign Failed</strong><br>
                    There was an error with this campaign. You can edit and retry.
                </div>
                {% endif %}

                <div class="mt-3">
                    <h6>Campaign Statistics</h6>
                    <ul class="list-unstyled">
                        <li><strong>Created:</strong> {{ campaign.created_at.strftime('%Y-%m-%d %H:%M') if campaign.created_at else 'Unknown' }}</li>
                        {% if campaign.started_at %}
                        <li><strong>Started:</strong> {{ campaign.started_at.strftime('%Y-%m-%d %H:%M') }}</li>
                        {% endif %}
                        <li><strong>Recipients:</strong> {{ campaign.total_recipients or 0 }}</li>
                        <li><strong>Emails Sent:</strong> {{ campaign.emails_sent or 0 }}</li>
                        <li><strong>Links Clicked:</strong> {{ campaign.chatbot_links_clicked or 0 }}</li>
                        <li><strong>Conversions:</strong> {{ campaign.conversions_achieved or 0 }}</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tools"></i> Campaign Actions</h5>
            </div>
            <div class="card-body">
                {% if campaign.status == 'draft' %}
                <form action="{{ url_for('send_campaign', campaign_id=campaign.id) }}" method="POST" class="mb-2">
                    <button type="submit" class="btn btn-success w-100" 
                            onclick="return confirm('Send this campaign to all active contacts?')">
                        <i class="fas fa-paper-plane"></i> Send Campaign
                    </button>
                </form>
                {% endif %}

                <form action="{{ url_for('duplicate_campaign', campaign_id=campaign.id) }}" method="POST" class="mb-2">
                    <button type="submit" class="btn btn-outline-info w-100" 
                            onclick="return confirm('Create a copy of this campaign?')">
                        <i class="fas fa-copy"></i> Duplicate Campaign
                    </button>
                </form>

                {% if campaign.status in ['draft', 'failed'] %}
                <form action="{{ url_for('delete_campaign', campaign_id=campaign.id) }}" method="POST">
                    <button type="submit" class="btn btn-outline-danger w-100" 
                            onclick="return confirm('Are you sure you want to delete this campaign? This action cannot be undone.')">
                        <i class="fas fa-trash"></i> Delete Campaign
                    </button>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[method="POST"]');
    const nameInput = document.getElementById('name');
    const subjectInput = document.getElementById('subject');
    const templateSelect = document.getElementById('template_name');

    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate campaign name
        if (!nameInput.value.trim()) {
            nameInput.classList.add('is-invalid');
            isValid = false;
        } else {
            nameInput.classList.remove('is-invalid');
        }
        
        // Validate subject line
        if (!subjectInput.value.trim()) {
            subjectInput.classList.add('is-invalid');
            isValid = false;
        } else {
            subjectInput.classList.remove('is-invalid');
        }
        
        // Validate template selection
        if (!templateSelect.value) {
            templateSelect.classList.add('is-invalid');
            isValid = false;
        } else {
            templateSelect.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
