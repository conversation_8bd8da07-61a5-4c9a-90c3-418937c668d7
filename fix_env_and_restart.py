#!/usr/bin/env python3
"""
Fix Environment Variables and Restart
=====================================
Load .env variables and restart the application with correct email config
"""

import os
import sys

def load_env_file():
    """Load environment variables from .env file"""
    try:
        print("📁 Loading .env file...")
        
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        
        print(f"✅ Loaded {len(env_vars)} environment variables")
        
        # Show email configuration
        email_vars = {k: v for k, v in env_vars.items() if 'MAIL' in k}
        print("\n📧 Email configuration:")
        for key, value in email_vars.items():
            if 'PASSWORD' in key:
                print(f"   {key}: {'*' * len(value)}")
            else:
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to load .env file: {e}")
        return False

def test_flask_config():
    """Test Flask configuration with loaded environment variables"""
    try:
        print("\n🌐 Testing Flask configuration...")
        
        # Import Flask app after setting environment variables
        from unified_sales_system import app
        
        with app.app_context():
            print("✅ Flask app loaded")
            
            # Check Flask email configuration
            email_config = {
                'MAIL_SERVER': app.config.get('MAIL_SERVER'),
                'MAIL_PORT': app.config.get('MAIL_PORT'),
                'MAIL_USE_SSL': app.config.get('MAIL_USE_SSL'),
                'MAIL_USE_TLS': app.config.get('MAIL_USE_TLS'),
                'MAIL_USERNAME': app.config.get('MAIL_USERNAME'),
                'MAIL_PASSWORD': app.config.get('MAIL_PASSWORD'),
            }
            
            print("\n📧 Flask email configuration:")
            for key, value in email_config.items():
                if 'PASSWORD' in key:
                    print(f"   {key}: {'*' * len(str(value)) if value else 'NOT SET'}")
                else:
                    print(f"   {key}: {value}")
            
            # Check if Gmail configuration is loaded
            if email_config['MAIL_SERVER'] == 'smtp.gmail.com':
                print("✅ Gmail configuration loaded correctly")
                return True
            else:
                print(f"❌ Wrong mail server: {email_config['MAIL_SERVER']}")
                print("   Expected: smtp.gmail.com")
                return False
                
    except Exception as e:
        print(f"❌ Flask configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_email_system():
    """Test the email system with new configuration"""
    try:
        print("\n📧 Testing email system...")
        
        from email_system.config import get_email_config
        from email_system.enhanced_smtp_service import EnhancedSMTPService
        
        config = get_email_config()
        print(f"✅ Email config loaded: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
        
        # Test SMTP service
        smtp_service = EnhancedSMTPService(config)
        success, message = smtp_service.test_connection()
        
        if success:
            print(f"✅ Email system test passed: {message}")
            return True
        else:
            print(f"❌ Email system test failed: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Email system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_startup_script():
    """Create a startup script with environment variables"""
    try:
        print("\n📝 Creating startup script...")
        
        startup_script = """#!/usr/bin/env python3
# Startup script with environment variables loaded

import os

# Load environment variables from .env
env_vars = {}
try:
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
                os.environ[key] = value
    print(f"✅ Loaded {len(env_vars)} environment variables from .env")
except Exception as e:
    print(f"⚠️ Could not load .env file: {e}")

# Start the application
if __name__ == '__main__':
    from unified_sales_system import app
    print("🚀 Starting 24Seven Assistants Sales System with Gmail configuration")
    print("📧 Email server: smtp.gmail.com")
    print("🌐 Server: http://localhost:5000")
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
"""
        
        with open('start_with_gmail.py', 'w') as f:
            f.write(startup_script)
        
        print("✅ Created start_with_gmail.py")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create startup script: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Fix Environment Variables and Restart")
    print("=" * 50)
    
    # Step 1: Load environment variables
    env_loaded = load_env_file()
    
    # Step 2: Test Flask configuration
    if env_loaded:
        flask_success = test_flask_config()
    else:
        flask_success = False
    
    # Step 3: Test email system
    if flask_success:
        email_success = test_email_system()
    else:
        email_success = False
    
    # Step 4: Create startup script
    if email_success:
        script_created = create_startup_script()
    else:
        script_created = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FIX SUMMARY")
    print("=" * 50)
    print(f"Environment Variables: {'✅ LOADED' if env_loaded else '❌ FAILED'}")
    print(f"Flask Configuration: {'✅ CORRECT' if flask_success else '❌ WRONG'}")
    print(f"Email System: {'✅ WORKING' if email_success else '❌ FAILED'}")
    print(f"Startup Script: {'✅ CREATED' if script_created else '❌ FAILED'}")
    
    if all([env_loaded, flask_success, email_success, script_created]):
        print("\n🎉 ALL FIXES SUCCESSFUL!")
        print("\nNext steps:")
        print("1. Stop the current Flask application (Ctrl+C)")
        print("2. Run: python start_with_gmail.py")
        print("3. Try sending a campaign again")
        print("4. The application will now use Gmail SMTP")
    else:
        print("\n❌ Some fixes failed")
        print("Please check the errors above and try again")
    
    return all([env_loaded, flask_success, email_success, script_created])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
