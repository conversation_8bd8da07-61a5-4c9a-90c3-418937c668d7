#!/usr/bin/env python3
"""
Test Analytics Fix
==================
Test script to verify that stage progressions are now being tracked in analytics.
"""

import requests
import json
import time
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_SESSION_ID = "test-analytics-session-001"
TEST_CONTACT_NAME = "Test Analytics User"
TEST_CONTACT_EMAIL = "<EMAIL>"

def test_stage_progression():
    """Test stage progression tracking"""
    print("🧪 Testing Stage Progression Analytics")
    print("=" * 50)
    
    # Test stages to progress through
    stages = [
        {"stage": "opening", "task": "Introduction and rapport building"},
        {"stage": "trust", "task": "Building trust and credibility"},
        {"stage": "discovery", "task": "Understanding needs and pain points"},
        {"stage": "demonstration", "task": "Showing solution benefits"},
        {"stage": "close", "task": "Closing the deal"}
    ]
    
    for i, stage_data in enumerate(stages):
        print(f"\n📊 Testing Stage {i+1}: {stage_data['stage'].upper()}")
        print("-" * 30)
        
        # Track the stage progression
        tracking_data = {
            "session_id": TEST_SESSION_ID,
            "stage": stage_data["stage"],
            "task": stage_data["task"],
            "contact_name": TEST_CONTACT_NAME,
            "contact_email": TEST_CONTACT_EMAIL,
            "action": "stage_progression",
            "message_count": i + 1,
            "user_message": f"Test message for {stage_data['stage']} stage",
            "bot_response": f"Bot response for {stage_data['stage']} stage"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/track-session",
                json=tracking_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Stage progression tracked successfully")
                print(f"   Contact ID: {result.get('contact_id')}")
                print(f"   Stage: {result.get('stage')}")
                print(f"   Previous Stage: {result.get('previous_stage')}")
            else:
                print(f"❌ Failed to track stage progression: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error tracking stage progression: {e}")
        
        # Wait a bit between stages
        time.sleep(1)

def check_analytics():
    """Check if analytics data is being recorded"""
    print("\n🔍 Checking Analytics Data")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/api/analytics/stage-progression", timeout=10)
        
        if response.status_code == 200:
            analytics_data = response.json()
            
            if analytics_data.get('success'):
                data = analytics_data.get('data', {})
                
                print("✅ Analytics endpoint working!")
                print(f"📈 Daily Analytics Records: {len(data.get('daily_analytics', []))}")
                print(f"📊 Recent Stage Progressions: {len(data.get('recent_stage_progressions', []))}")
                print(f"🎯 Current Stage Distribution: {len(data.get('current_stage_distribution', []))}")
                
                # Show recent progressions
                progressions = data.get('recent_stage_progressions', [])
                if progressions:
                    print("\n📋 Recent Stage Progressions:")
                    for prog in progressions[:5]:  # Show first 5
                        print(f"   • {prog.get('stage_name')} - {prog.get('entered_at')} - {prog.get('changed_by')}")
                
                # Show stage distribution
                distribution = data.get('current_stage_distribution', [])
                if distribution:
                    print("\n📊 Current Stage Distribution:")
                    for stage in distribution:
                        print(f"   • {stage.get('stage_name')}: {stage.get('count')} opportunities")
                
                return True
            else:
                print(f"❌ Analytics request failed: {analytics_data.get('message')}")
                return False
        else:
            print(f"❌ Analytics endpoint error: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking analytics: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Analytics Fix Test")
    print("=" * 50)
    print(f"Testing against: {BASE_URL}")
    print(f"Session ID: {TEST_SESSION_ID}")
    print(f"Contact: {TEST_CONTACT_NAME} ({TEST_CONTACT_EMAIL})")
    print()
    
    # Test stage progressions
    test_stage_progression()
    
    # Wait a moment for data to be processed
    print("\n⏳ Waiting for data to be processed...")
    time.sleep(2)
    
    # Check analytics
    analytics_working = check_analytics()
    
    # Summary
    print("\n" + "=" * 50)
    if analytics_working:
        print("🎉 SUCCESS: Analytics tracking is working!")
        print("   Stage progressions are being recorded in the analytics system.")
    else:
        print("❌ ISSUE: Analytics tracking may not be working properly.")
        print("   Check the application logs for errors.")
    
    print("\n💡 Next steps:")
    print("   1. Check the contact page to see stage progression")
    print("   2. Visit /api/analytics/stage-progression to see raw data")
    print("   3. Check application logs for any errors")

if __name__ == "__main__":
    main()
