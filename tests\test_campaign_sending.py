"""Integration test for campaign sending flow.

Run with:
    pytest -q tests/test_campaign_sending.py

The test spins up the Flask application in testing mode, seeds a contact and a campaign,
then triggers the send endpoint and verifies that an email log entry is created and the
campaign statistics are updated accordingly.
"""
import uuid
from datetime import datetime

import pytest

from unified_sales_system import app, db, Contact, EmailCampaign, EmailLog


@pytest.fixture(autouse=True)
def app_context():
    """Provide application context and fresh in-memory database for each test."""
    app.config.update({
        "TESTING": True,
        "SQLALCHEMY_DATABASE_URI": "sqlite:///:memory:",
        "WTF_CSRF_ENABLED": False,
    })
    with app.app_context():
        db.create_all()
        yield
        db.session.remove()
        db.drop_all()


def seed_contact():
    contact = Contact(
        first_name="Test",
        last_name="User",
        email=f"test_{uuid.uuid4().hex[:8]}@example.com",
        is_active=True,
        do_not_email=False,
    )
    db.session.add(contact)
    db.session.commit()
    return contact


def seed_campaign():
    campaign = EmailCampaign(
        name="Test Campaign",
        subject="Hello from Test",
        template_name="default",
        daily_send_limit=10,
        batch_size=5,
        status="draft",
    )
    db.session.add(campaign)
    db.session.commit()
    return campaign


def test_send_campaign_endpoint(client=None):
    """Create a campaign and a contact, trigger send endpoint, verify success."""
    with app.test_client() as client:
        # Seed data
        contact = seed_contact()
        campaign = seed_campaign()

        # hit send endpoint (POST)
        resp = client.post(f"/campaigns/{campaign.id}/send", data={"batch_mode": "immediate"})
        # Expect redirect back to campaigns list (302)
        assert resp.status_code in (302, 303)

        # Reload objects
        refreshed_campaign = EmailCampaign.query.get(campaign.id)
        email_logs = EmailLog.query.filter_by(campaign_id=campaign.id, contact_id=contact.id).all()

        assert refreshed_campaign.emails_sent == 1
        assert len(email_logs) == 1
        assert email_logs[0].status == "sent"
