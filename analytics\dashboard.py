"""
Sales Dashboard
==============
Main dashboard for sales analytics and metrics visualization.
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from .stage_analytics import StageAnalytics
from .email_analytics import EmailAnalytics
from .performance_metrics import PerformanceMetrics

class SalesDashboard:
    """Main sales analytics dashboard"""
    
    def __init__(self, db_session):
        """Initialize dashboard with database session"""
        self.db = db_session
        self.stage_analytics = StageAnalytics(db_session)
        self.email_analytics = EmailAnalytics(db_session)
        self.performance_metrics = PerformanceMetrics(db_session)
    
    def generate_sales_funnel_chart(self, days: int = 30) -> go.Figure:
        """
        Generate sales funnel visualization
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Plotly figure object
        """
        funnel_data = self.stage_analytics.get_stage_funnel_data(days)
        
        # Prepare data for funnel chart
        stages = funnel_data['stages']
        stage_names = [stage['name'] for stage in stages]
        stage_counts = [stage['entries'] for stage in stages]
        
        # Create funnel chart
        fig = go.Figure(go.Funnel(
            y=stage_names,
            x=stage_counts,
            textinfo="value+percent initial",
            marker=dict(
                color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                line=dict(width=2, color='white')
            ),
            connector=dict(line=dict(color='rgb(63, 63, 63)', dash='dot', width=3))
        ))
        
        fig.update_layout(
            title=f'Sales Funnel - Last {days} Days',
            font=dict(size=14),
            height=500,
            margin=dict(l=50, r=50, t=80, b=50)
        )
        
        return fig
    
    def generate_stage_duration_chart(self, days: int = 90) -> go.Figure:
        """
        Generate stage duration analysis chart
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Plotly figure object
        """
        duration_data = self.stage_analytics.get_stage_duration_analysis(days)
        
        if not duration_data:
            # Return empty chart if no data
            fig = go.Figure()
            fig.add_annotation(
                text="No stage duration data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            fig.update_layout(title="Stage Duration Analysis")
            return fig
        
        # Prepare data
        stages = list(duration_data.keys())
        avg_durations = [duration_data[stage]['avg_days'] for stage in stages]
        min_durations = [duration_data[stage]['min_days'] for stage in stages]
        max_durations = [duration_data[stage]['max_days'] for stage in stages]
        
        # Create bar chart with error bars
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=stages,
            y=avg_durations,
            name='Average Duration',
            marker_color='#45B7D1',
            error_y=dict(
                type='data',
                symmetric=False,
                array=[max_durations[i] - avg_durations[i] for i in range(len(stages))],
                arrayminus=[avg_durations[i] - min_durations[i] for i in range(len(stages))],
                visible=True
            )
        ))
        
        fig.update_layout(
            title=f'Average Time in Each Stage - Last {days} Days',
            xaxis_title='Sales Stage',
            yaxis_title='Days',
            font=dict(size=12),
            height=400,
            margin=dict(l=50, r=50, t=80, b=50)
        )
        
        return fig
    
    def generate_conversion_matrix_heatmap(self, days: int = 60) -> go.Figure:
        """
        Generate stage-to-stage conversion heatmap
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Plotly figure object
        """
        conversion_data = self.stage_analytics.get_stage_conversion_matrix(days)
        matrix = conversion_data['matrix']
        stage_names = conversion_data['stage_names']
        
        # Convert matrix to list format for heatmap
        z_data = []
        for from_stage in stage_names:
            row = []
            for to_stage in stage_names:
                row.append(matrix[from_stage][to_stage])
            z_data.append(row)
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=stage_names,
            y=stage_names,
            colorscale='Blues',
            showscale=True,
            text=z_data,
            texttemplate="%{text}",
            textfont={"size": 12}
        ))
        
        fig.update_layout(
            title=f'Stage Transition Matrix - Last {days} Days',
            xaxis_title='To Stage',
            yaxis_title='From Stage',
            font=dict(size=12),
            height=500,
            margin=dict(l=100, r=50, t=80, b=100)
        )
        
        return fig
    
    def generate_email_performance_chart(self, days: int = 30) -> go.Figure:
        """
        Generate email campaign performance chart
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Plotly figure object
        """
        email_metrics = self.email_analytics.get_campaign_performance_summary(days)
        
        if not email_metrics['campaigns']:
            # Return empty chart if no data
            fig = go.Figure()
            fig.add_annotation(
                text="No email campaign data available",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False, font=dict(size=16)
            )
            fig.update_layout(title="Email Campaign Performance")
            return fig
        
        # Create subplot with secondary y-axis
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Email Delivery Metrics', 'Engagement Rates', 
                          'Campaign Volume', 'Response Metrics'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Email delivery metrics
        fig.add_trace(
            go.Bar(x=['Sent', 'Delivered', 'Bounced'], 
                  y=[email_metrics['total_sent'], 
                     email_metrics['total_delivered'], 
                     email_metrics['total_bounced']],
                  name='Email Delivery',
                  marker_color=['#45B7D1', '#96CEB4', '#FF6B6B']),
            row=1, col=1
        )
        
        # Engagement rates
        fig.add_trace(
            go.Bar(x=['Open Rate', 'Click Rate', 'Reply Rate'], 
                  y=[email_metrics['avg_open_rate'], 
                     email_metrics['avg_click_rate'], 
                     email_metrics['avg_reply_rate']],
                  name='Engagement Rates (%)',
                  marker_color=['#FFEAA7', '#DDA0DD', '#98D8C8']),
            row=1, col=2
        )
        
        # Campaign volume over time (if we have daily data)
        fig.add_trace(
            go.Scatter(x=['Week 1', 'Week 2', 'Week 3', 'Week 4'], 
                      y=[25, 30, 35, 28],  # Placeholder data
                      mode='lines+markers',
                      name='Weekly Campaigns',
                      line=dict(color='#45B7D1')),
            row=2, col=1
        )
        
        # Response metrics
        fig.add_trace(
            go.Bar(x=['Opened', 'Clicked', 'Replied'], 
                  y=[email_metrics['total_opened'], 
                     email_metrics['total_clicked'], 
                     email_metrics['total_replied']],
                  name='Responses',
                  marker_color=['#96CEB4', '#FFEAA7', '#FF6B6B']),
            row=2, col=2
        )
        
        fig.update_layout(
            title=f'Email Campaign Performance - Last {days} Days',
            height=600,
            showlegend=False,
            margin=dict(l=50, r=50, t=100, b=50)
        )
        
        return fig
    
    def generate_performance_overview_chart(self, days: int = 30) -> go.Figure:
        """
        Generate overall performance overview chart
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Plotly figure object
        """
        performance_data = self.performance_metrics.get_performance_summary(days)
        
        # Create subplot with multiple metrics
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Revenue Metrics', 'Opportunity Metrics', 
                          'Activity Metrics', 'Conversion Rates'),
            specs=[[{"type": "bar"}, {"type": "pie"}],
                   [{"type": "bar"}, {"type": "bar"}]]
        )
        
        # Revenue metrics
        fig.add_trace(
            go.Bar(x=['Pipeline Value', 'Weighted Value', 'Closed Won'], 
                  y=[performance_data['pipeline_value'], 
                     performance_data['weighted_pipeline_value'], 
                     performance_data['closed_won_value']],
                  name='Revenue ($)',
                  marker_color=['#45B7D1', '#96CEB4', '#FFEAA7']),
            row=1, col=1
        )
        
        # Opportunity status distribution
        fig.add_trace(
            go.Pie(labels=['Open', 'Won', 'Lost'], 
                  values=[performance_data['open_opportunities'], 
                         performance_data['won_opportunities'], 
                         performance_data['lost_opportunities']],
                  name='Opportunities',
                  marker_colors=['#45B7D1', '#96CEB4', '#FF6B6B']),
            row=1, col=2
        )
        
        # Activity metrics
        fig.add_trace(
            go.Bar(x=['Emails', 'Calls', 'Meetings'], 
                  y=[performance_data['total_emails'], 
                     performance_data['total_calls'], 
                     performance_data['total_meetings']],
                  name='Activities',
                  marker_color=['#DDA0DD', '#98D8C8', '#F7DC6F']),
            row=2, col=1
        )
        
        # Conversion rates
        fig.add_trace(
            go.Bar(x=['Lead to Opp', 'Opp to Customer', 'Overall'], 
                  y=[performance_data['lead_to_opp_rate'], 
                     performance_data['opp_to_customer_rate'], 
                     performance_data['overall_conversion_rate']],
                  name='Conversion Rates (%)',
                  marker_color=['#FF6B6B', '#4ECDC4', '#45B7D1']),
            row=2, col=2
        )
        
        fig.update_layout(
            title=f'Performance Overview - Last {days} Days',
            height=600,
            showlegend=False,
            margin=dict(l=50, r=50, t=100, b=50)
        )
        
        return fig
    
    def get_dashboard_summary(self, days: int = 30) -> Dict:
        """
        Get comprehensive dashboard summary data
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with all dashboard metrics
        """
        return {
            'stage_metrics': self.stage_analytics.calculate_stage_metrics_summary(days),
            'email_metrics': self.email_analytics.get_campaign_performance_summary(days),
            'performance_metrics': self.performance_metrics.get_performance_summary(days),
            'period_days': days,
            'generated_at': datetime.utcnow().isoformat()
        }
