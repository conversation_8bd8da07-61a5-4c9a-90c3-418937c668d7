#!/usr/bin/env python3
"""
Test Real Campaign Email Sending
================================
Test script to verify that campaigns now send real emails and save to IMAP sent folder.
"""

import sys
import os
import time
import requests
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

BASE_URL = "http://localhost:5000"

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        return response.status_code == 200
    except:
        return False

def test_send_campaign_email_function():
    """Test the send_campaign_email function directly"""
    print("🧪 Testing send_campaign_email Function...")
    print("-" * 50)
    
    try:
        # Import the function and models
        from unified_sales_system import app, db, Contact, EmailCampaign, send_campaign_email
        import uuid
        
        with app.app_context():
            # Create or get test contact
            test_contact = Contact.query.filter_by(email='<EMAIL>').first()
            if not test_contact:
                test_contact = Contact(
                    first_name='Test',
                    last_name='Campaign',
                    email='<EMAIL>',
                    company='24Seven Assistants',
                    job_title='Test Contact',
                    source='test_real_campaign',
                    status='new'
                )
                db.session.add(test_contact)
                db.session.commit()
                print("✅ Test contact created")
            else:
                print("✅ Test contact found")
            
            # Create test campaign
            test_campaign = EmailCampaign(
                name=f'Real Email Test Campaign {datetime.now().strftime("%Y%m%d_%H%M%S")}',
                subject='Test Real Email from 24Seven Assistants',
                template_name='introduction',
                status='draft'
            )
            db.session.add(test_campaign)
            db.session.commit()
            print(f"✅ Test campaign created: {test_campaign.name}")
            
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Test the send_campaign_email function
            print(f"\n📧 Sending real email to {test_contact.email}...")
            success, message = send_campaign_email(test_contact, test_campaign, session_id)
            
            if success:
                print(f"✅ Email sent successfully!")
                print(f"   Result: {message}")
                print(f"   Session ID: {session_id}")
                print(f"   Chatbot Link: http://localhost:5000/chat/{session_id}")
                return True
            else:
                print(f"❌ Email sending failed: {message}")
                return False
                
    except Exception as e:
        print(f"❌ Function test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_and_send_campaign_via_web():
    """Create and send a campaign via the web interface"""
    print("\n🌐 Testing Campaign via Web Interface...")
    print("-" * 50)
    
    try:
        # Create campaign via web interface
        campaign_data = {
            'campaign_name': f'Web Test Campaign {datetime.now().strftime("%H%M%S")}',
            'template': 'introduction',
            'recipient_type': 'all',
            'daily_send_limit': '50',
            'send_schedule': 'immediate'
        }
        
        print("📝 Creating campaign via web interface...")
        response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)
        
        if response.status_code in [200, 302]:
            print("✅ Campaign created successfully")
            
            # Wait a moment
            time.sleep(2)
            
            # Get the latest campaign ID
            campaigns_response = requests.get(f"{BASE_URL}/campaigns")
            if campaigns_response.status_code == 200:
                # Parse HTML to find campaign IDs
                import re
                content = campaigns_response.text
                pattern = r'/campaigns/(\d+)/'
                matches = re.findall(pattern, content)
                
                if matches:
                    latest_campaign_id = max([int(id) for id in matches])
                    print(f"✅ Latest campaign ID: {latest_campaign_id}")
                    
                    # Send the campaign
                    print(f"📤 Sending campaign {latest_campaign_id}...")
                    send_response = requests.post(f"{BASE_URL}/campaigns/{latest_campaign_id}/send")
                    
                    if send_response.status_code in [200, 302]:
                        print("✅ Campaign sent successfully!")
                        return True, latest_campaign_id
                    else:
                        print(f"❌ Failed to send campaign: {send_response.status_code}")
                        return False, None
                else:
                    print("❌ No campaign IDs found")
                    return False, None
            else:
                print("❌ Failed to get campaigns list")
                return False, None
        else:
            print(f"❌ Failed to create campaign: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Web interface test failed: {str(e)}")
        return False, None

def check_imap_sent_folder():
    """Check if emails appear in IMAP sent folder"""
    print("\n📬 Checking IMAP Sent Folder...")
    print("-" * 50)
    
    try:
        # Run the standalone IMAP test
        import subprocess
        result = subprocess.run([
            sys.executable, "standalone_imap_test.py"
        ], capture_output=True, text=True, timeout=30)
        
        if "Found" in result.stdout and "emails" in result.stdout:
            print("✅ IMAP test shows emails in sent folder")
            # Extract email count
            import re
            match = re.search(r'(\d+) emails', result.stdout)
            if match:
                email_count = int(match.group(1))
                print(f"   📧 {email_count} emails found in sent folder")
                return True, email_count
        
        print("⚠️ No emails found in sent folder yet")
        return False, 0
        
    except Exception as e:
        print(f"❌ IMAP check failed: {str(e)}")
        return False, 0

def main():
    """Main test function"""
    print("🚀 24Seven Assistants - Real Campaign Email Sending Test")
    print("=" * 70)
    print("Testing that campaigns now send REAL emails (not simulated)")
    print()
    
    # Check server
    if not check_server_status():
        print("❌ Server is not running. Please start it first:")
        print("   python unified_sales_system.py")
        return
    
    print("✅ Server is running")
    
    # Test 1: Direct function test
    function_success = test_send_campaign_email_function()
    
    # Wait for email processing
    if function_success:
        print("\n⏳ Waiting 5 seconds for email processing...")
        time.sleep(5)
    
    # Test 2: Web interface test
    web_success, campaign_id = create_and_send_campaign_via_web()
    
    # Wait for email processing
    if web_success:
        print("\n⏳ Waiting 5 seconds for email processing...")
        time.sleep(5)
    
    # Test 3: Check IMAP sent folder
    imap_success, email_count = check_imap_sent_folder()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 REAL CAMPAIGN EMAIL TEST RESULTS")
    print("=" * 70)
    print(f"Direct Function Test: {'✅ PASS' if function_success else '❌ FAIL'}")
    print(f"Web Interface Test:   {'✅ PASS' if web_success else '❌ FAIL'}")
    print(f"IMAP Sent Folder:     {'✅ PASS' if imap_success else '❌ FAIL'}")
    
    if campaign_id:
        print(f"Latest Campaign ID:   {campaign_id}")
    
    if email_count > 0:
        print(f"Emails in Sent Folder: {email_count}")
    
    if all([function_success, web_success, imap_success]):
        print("\n🎉 SUCCESS! Campaigns are now sending REAL emails!")
        print("\n💡 What this means:")
        print("✅ Campaign system sends actual emails (not simulated)")
        print("✅ Emails are automatically saved to IMAP sent folder")
        print("✅ You can see sent emails in the web interface")
        print("✅ Recipients will receive actual emails")
        
        print("\n🌐 Next steps:")
        print("1. Check your email inbox for test emails")
        print("2. Visit: http://localhost:5000/emails/sent")
        print("3. Create real campaigns - they will send actual emails")
        print("4. Monitor the analytics dashboard for real results")
    else:
        print("\n⚠️ Some issues detected:")
        if not function_success:
            print("- Direct email sending function has issues")
        if not web_success:
            print("- Web interface campaign sending has issues")
        if not imap_success:
            print("- IMAP sent folder integration has issues")
        
        print("\nCheck the output above for specific error details.")
    
    print(f"\n📧 Check your email inbox (<EMAIL>) for test emails!")

if __name__ == "__main__":
    main()
