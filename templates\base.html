<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}24Seven Assistants - Sales Department{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Select CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Dark Theme CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <!-- jQuery (required for bootstrap-select) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    

    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #667eea;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --danger-color: #f44336;
            --dark-color: #333;
            --light-color: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-color);
        }

        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            padding-top: 20px;
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-new { background-color: #e3f2fd; color: #1976d2; }
        .status-contacted { background-color: #fff3e0; color: #f57c00; }
        .status-qualified { background-color: #e8f5e8; color: #388e3c; }
        .status-customer { background-color: #f3e5f5; color: #7b1fa2; }
        .status-lost { background-color: #ffebee; color: #d32f2f; }

        .status-open { background-color: #e3f2fd; color: #1976d2; }
        .status-won { background-color: #e8f5e8; color: #388e3c; }
        .status-draft { background-color: #f5f5f5; color: #616161; }
        .status-running { background-color: #fff3e0; color: #f57c00; }
        .status-completed { background-color: #e8f5e8; color: #388e3c; }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-rocket"></i> 24Seven Assistants
            </a>
            <span class="navbar-text">
                Sales Department System
            </span>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link {% if request.endpoint == 'analytics_dashboard' %}active{% endif %}" href="{{ url_for('analytics_dashboard') }}">
                        <i class="fas fa-chart-line me-2"></i> Analytics
                    </a>
                    <a class="nav-link {% if request.endpoint in ['contacts_list', 'add_contact', 'upload_contacts'] %}active{% endif %}" href="{{ url_for('contacts_list') }}">
                        <i class="fas fa-users me-2"></i> Contacts
                    </a>
                    <a class="nav-link {% if request.endpoint in ['groups_list', 'create_group', 'view_group', 'edit_group'] %}active{% endif %}" href="{{ url_for('groups_list') }}">
                        <i class="fas fa-layer-group me-2"></i> Groups
                    </a>
                    <a class="nav-link {% if request.endpoint == 'opportunities_list' %}active{% endif %}" href="{{ url_for('opportunities_list') }}">
                        <i class="fas fa-bullseye me-2"></i> Opportunities
                    </a>
                    <a class="nav-link {% if request.endpoint == 'campaigns_list' %}active{% endif %}" href="{{ url_for('campaigns_list') }}">
                        <i class="fas fa-envelope me-2"></i> Email Campaigns
                    </a>
                    <a class="nav-link {% if request.endpoint == 'view_sent_emails' %}active{% endif %}" href="{{ url_for('view_sent_emails') }}">
                        <i class="fas fa-paper-plane me-2"></i> Sent Emails (IMAP)
                    </a>
                    <hr class="text-white">
                    <a class="nav-link {% if request.endpoint and request.endpoint.startswith('debug.') %}active{% endif %}" href="{{ url_for('debug.debug_dashboard') }}">
                        <i class="fas fa-bug me-2"></i> Debug Dashboard
                    </a>
                    <a class="nav-link" href="#" onclick="testSMTP()">
                        <i class="fas fa-cog me-2"></i> Test SMTP
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Bootstrap Select JS (needs Bootstrap bundle) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>

    <script>
        // Test SMTP Configuration
        function testSMTP() {
            fetch('/api/test-smtp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('SMTP Test Successful: ' + data.message);
                } else {
                    alert('SMTP Test Failed: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error testing SMTP: ' + error);
            });
        }

        // Auto-refresh dashboard every 5 minutes
        if (window.location.pathname === '/' || window.location.pathname.includes('analytics')) {
            setTimeout(function() {
                location.reload();
            }, 300000); // 5 minutes
        }
    </script>

    {% block extra_js %}{% endblock %}
<script>
        document.addEventListener('DOMContentLoaded', function () {
            // Convert all Bootstrap form-selects into bootstrap-select pickers for dark styling
            document.querySelectorAll('select.form-select').forEach(function (el) {
                el.classList.add('selectpicker');
            });
            if (typeof $ !== 'undefined' && $.fn.selectpicker) {
                $('.selectpicker').selectpicker();
            }
        });
    </script>
</body>
</html>
