#!/usr/bin/env python3
"""
Load 100PPDA Contacts Script
============================
This script creates a contact group called "100PPDA" and loads the first 100 contacts
from the all_suppliers_complete_20250602_084246.csv file into the database.

Usage:
    python load_100ppda_contacts.py

Requirements:
    - The unified_sales_system.py application must be importable
    - The CSV file must be in the same directory
    - Database must be accessible
"""

import os
import sys
import csv
import re
from datetime import datetime

# Set working directory to script location
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)
sys.path.insert(0, script_dir)

print(f"Working directory: {os.getcwd()}")
print(f"Script directory: {script_dir}")

try:
    print("Importing modules...")
    from unified_sales_system import app, db
    from models.contact import Contact
    from models.contact_group import ContactGroup
    from models.contact_group_membership import ContactGroupMembership
    print("✅ Modules imported successfully")
except ImportError as e:
    print(f"❌ Error importing required modules: {e}")
    print("Make sure you're running this script from the correct directory.")
    print(f"Current directory: {os.getcwd()}")
    print(f"Files in directory: {os.listdir('.')}")
    sys.exit(1)

def clean_phone_number(phone):
    """Clean and format phone number"""
    if not phone:
        return None

    # Remove all non-digit characters except +
    phone = re.sub(r'[^\d+]', '', str(phone))

    # Handle Uganda phone numbers
    if phone.startswith('256'):
        phone = '+' + phone
    elif phone.startswith('0') and len(phone) == 10:
        phone = '+256' + phone[1:]
    elif len(phone) == 9 and not phone.startswith('+'):
        phone = '+256' + phone

    return phone if phone else None

def extract_name_from_company(company_name):
    """Extract first and last name from company name"""
    if not company_name:
        return "Unknown", "Company"

    # Clean company name
    company_name = company_name.strip()

    # Remove common company suffixes
    suffixes = [
        'LIMITED', 'LTD', 'CO.', 'COMPANY', 'ENTERPRISES', 'ENTERPRISE',
        'SERVICES', 'SOLUTIONS', 'CONSULTANTS', 'CONSULTING', 'GROUP',
        'INTERNATIONAL', 'UGANDA', '(U)', 'SMC', 'INVESTMENTS'
    ]

    clean_name = company_name
    for suffix in suffixes:
        clean_name = re.sub(rf'\b{suffix}\b', '', clean_name, flags=re.IGNORECASE)

    clean_name = re.sub(r'\s+', ' ', clean_name).strip()
    clean_name = clean_name.strip('&-.,')

    # Split into words
    words = clean_name.split()

    if len(words) == 0:
        return "Unknown", "Company"
    elif len(words) == 1:
        return words[0], "Company"
    else:
        # Use first word as first name, rest as last name
        first_name = words[0]
        last_name = ' '.join(words[1:])
        return first_name, last_name

def load_contacts():
    """Load the first 100 contacts from CSV into 100PPDA group"""

    csv_file = 'all_suppliers_complete_20250602_084246.csv'

    if not os.path.exists(csv_file):
        print(f"Error: CSV file '{csv_file}' not found in current directory.")
        return False

    with app.app_context():
        try:
            # Create or get the 100PPDA group
            group = ContactGroup.query.filter_by(name='100PPDA').first()
            if group:
                print(f"Found existing group: {group.name}")
                # Ask user if they want to continue
                response = input("Group '100PPDA' already exists. Do you want to add contacts to it? (y/n): ")
                if response.lower() != 'y':
                    print("Operation cancelled.")
                    return False
            else:
                group = ContactGroup(
                    name='100PPDA',
                    description='First 100 contacts from PPDA suppliers CSV file - loaded for email campaign',
                    color='#28a745',  # Green color
                    created_by='csv_import_script'
                )
                db.session.add(group)
                db.session.flush()  # Get the ID
                print(f"Created new group: {group.name}")

            # Read CSV and process first 100 contacts
            contacts_added = 0
            contacts_updated = 0
            contacts_skipped = 0
            errors = []

            with open(csv_file, 'r', encoding='utf-8') as file:
                csv_reader = csv.DictReader(file)

                for row_num, row in enumerate(csv_reader, start=2):
                    # Stop after 100 contacts
                    if contacts_added + contacts_updated >= 100:
                        break

                    try:
                        # Extract data from CSV
                        company_name = row.get('company_name', '').strip()
                        email = row.get('email', '').strip().lower()
                        phone = clean_phone_number(row.get('telephone', ''))
                        website = row.get('website', '').strip()
                        address = row.get('current_address', '').strip()

                        # Skip if no email or company name
                        if not email or not company_name:
                            errors.append(f'Row {row_num}: Missing email or company name')
                            contacts_skipped += 1
                            continue

                        # Extract names from company name
                        first_name, last_name = extract_name_from_company(company_name)

                        # Check if contact already exists
                        existing_contact = Contact.query.filter_by(email=email).first()

                        if existing_contact:
                            # Update existing contact
                            existing_contact.company = company_name
                            existing_contact.phone = phone or existing_contact.phone
                            existing_contact.website = website or existing_contact.website
                            existing_contact.notes = f"Address: {address}" if address else existing_contact.notes
                            existing_contact.source = 'ppda_csv_import'
                            existing_contact.updated_at = datetime.utcnow()
                            contact = existing_contact
                            contacts_updated += 1
                            print(f"Updated contact: {email}")
                        else:
                            # Create new contact
                            contact = Contact(
                                first_name=first_name,
                                last_name=last_name,
                                email=email,
                                phone=phone,
                                company=company_name,
                                website=website,
                                notes=f"Address: {address}" if address else None,
                                source='ppda_csv_import',
                                status='new',
                                lead_score=50.0  # Default score
                            )
                            db.session.add(contact)
                            db.session.flush()  # Get the ID
                            contacts_added += 1
                            print(f"Added contact: {email}")

                        # Add contact to group (if not already in group)
                        existing_membership = ContactGroupMembership.query.filter_by(
                            contact_id=contact.id,
                            group_id=group.id
                        ).first()

                        if not existing_membership:
                            membership = ContactGroupMembership(
                                contact_id=contact.id,
                                group_id=group.id,
                                added_by='csv_import_script'
                            )
                            db.session.add(membership)

                    except Exception as e:
                        error_msg = f'Row {row_num}: Error processing contact - {str(e)}'
                        errors.append(error_msg)
                        print(f"Error: {error_msg}")
                        contacts_skipped += 1
                        continue

            # Commit all changes
            db.session.commit()

            # Print summary
            print("\n" + "="*50)
            print("IMPORT SUMMARY")
            print("="*50)
            print(f"Group: {group.name}")
            print(f"Contacts added: {contacts_added}")
            print(f"Contacts updated: {contacts_updated}")
            print(f"Contacts skipped: {contacts_skipped}")
            print(f"Total contacts in group: {contacts_added + contacts_updated}")

            if errors:
                print(f"\nErrors encountered: {len(errors)}")
                for error in errors[:10]:  # Show first 10 errors
                    print(f"  - {error}")
                if len(errors) > 10:
                    print(f"  ... and {len(errors) - 10} more errors")

            print(f"\nGroup '{group.name}' is ready for email campaigns!")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"Error during import: {str(e)}")
            return False

if __name__ == '__main__':
    print("Loading first 100 contacts from PPDA suppliers CSV into '100PPDA' group...")
    print("="*70)

    success = load_contacts()

    if success:
        print("\n✅ Import completed successfully!")
        print("\nNext steps:")
        print("1. Visit the web interface to view the contacts")
        print("2. Go to Groups section to see the '100PPDA' group")
        print("3. Create an email campaign targeting this group")
    else:
        print("\n❌ Import failed. Please check the errors above.")

    print("\nScript finished.")
