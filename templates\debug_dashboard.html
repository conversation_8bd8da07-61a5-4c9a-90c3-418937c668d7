{% extends "base.html" %}

{% block title %}Debug Dashboard - 24Seven Assistants{% endblock %}

{% block extra_css %}
<style>
    .debug-card {
        border-left: 4px solid #dc3545;
    }
    .debug-metric {
        font-size: 1.5rem;
        font-weight: bold;
        color: #dc3545;
    }
    .system-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
    }
    .log-preview {
        max-height: 300px;
        overflow-y: auto;
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 10px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
    }
    .error-item {
        border-left: 3px solid #dc3545;
        padding-left: 10px;
        margin-bottom: 10px;
    }
    .request-item {
        border-left: 3px solid #007bff;
        padding-left: 10px;
        margin-bottom: 5px;
    }
    .slow-function {
        border-left: 3px solid #ffc107;
        padding-left: 10px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-bug text-danger"></i> Debug Dashboard</h1>
    <div>
        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs('all')">
            <i class="fas fa-trash"></i> Clear All Logs
        </button>
        <a href="{{ url_for('debug.download_logs') }}" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-download"></i> Download Logs
        </a>
        <button class="btn btn-outline-success btn-sm" onclick="refreshStats()">
            <i class="fas fa-sync"></i> Refresh
        </button>
    </div>
</div>

<!-- Debug Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card debug-card">
            <div class="card-body text-center">
                <div class="debug-metric">{{ stats.request_logs_count or 0 }}</div>
                <div class="text-muted">Request Logs</div>
                <a href="{{ url_for('debug.debug_requests') }}" class="btn btn-sm btn-outline-primary mt-2">
                    View All
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card debug-card">
            <div class="card-body text-center">
                <div class="debug-metric">{{ stats.error_logs_count or 0 }}</div>
                <div class="text-muted">Error Logs</div>
                <a href="{{ url_for('debug.debug_errors') }}" class="btn btn-sm btn-outline-danger mt-2">
                    View All
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card debug-card">
            <div class="card-body text-center">
                <div class="debug-metric">{{ stats.performance_logs_count or 0 }}</div>
                <div class="text-muted">Performance Logs</div>
                <a href="{{ url_for('debug.debug_performance') }}" class="btn btn-sm btn-outline-warning mt-2">
                    View All
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card debug-card">
            <div class="card-body text-center">
                <div class="debug-metric">{{ (stats.slow_functions|length) or 0 }}</div>
                <div class="text-muted">Slow Functions</div>
                <a href="{{ url_for('debug.debug_performance') }}" class="btn btn-sm btn-outline-warning mt-2">
                    View All
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- System Information -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <div class="system-info">
                    <div class="row">
                        <div class="col-sm-6"><strong>Debug Mode:</strong></div>
                        <div class="col-sm-6">
                            <span class="badge bg-{{ 'success' if system_info.debug_mode else 'secondary' }}">
                                {{ 'Enabled' if system_info.debug_mode else 'Disabled' }}
                            </span>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6"><strong>Log Level:</strong></div>
                        <div class="col-sm-6">{{ system_info.log_level or 'INFO' }}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6"><strong>Log File:</strong></div>
                        <div class="col-sm-6">{{ system_info.log_file or 'sales_department.log' }}</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6"><strong>Slow Request Threshold:</strong></div>
                        <div class="col-sm-6">{{ system_info.slow_request_threshold or 2.0 }}s</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6"><strong>Slow Query Threshold:</strong></div>
                        <div class="col-sm-6">{{ system_info.slow_query_threshold or 1.0 }}s</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('debug.debug_database') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-database"></i> Database Info
                    </a>
                    <a href="{{ url_for('debug.debug_email_test') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-envelope"></i> Email Test
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Errors -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle text-danger"></i> Recent Errors</h5>
            </div>
            <div class="card-body">
                <div class="log-preview">
                    {% if stats.recent_errors %}
                        {% for error in stats.recent_errors %}
                        <div class="error-item">
                            <div class="d-flex justify-content-between">
                                <strong>{{ error.type }}</strong>
                                <small class="text-muted">{{ error.timestamp }}</small>
                            </div>
                            <div class="text-muted small">{{ error.message[:100] }}{% if error.message|length > 100 %}...{% endif %}</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted text-center">No recent errors</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- Recent Requests -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-globe text-primary"></i> Recent Requests</h5>
            </div>
            <div class="card-body">
                <div class="log-preview">
                    {% if stats.recent_requests %}
                        {% for request in stats.recent_requests %}
                        <div class="request-item">
                            <div class="d-flex justify-content-between">
                                <span>{{ request.method }} {{ request.url[:50] }}{% if request.url|length > 50 %}...{% endif %}</span>
                                <small class="text-muted">{{ "%.3f"|format(request.duration) }}s</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">{{ request.timestamp }}</small>
                                <span class="badge bg-{{ 'success' if request.status_code < 400 else 'danger' }}">
                                    {{ request.status_code }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted text-center">No recent requests</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Slow Functions -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock text-warning"></i> Slow Functions</h5>
            </div>
            <div class="card-body">
                <div class="log-preview">
                    {% if stats.slow_functions %}
                        {% for func in stats.slow_functions %}
                        <div class="slow-function">
                            <div class="d-flex justify-content-between">
                                <span>{{ func.function_name }}</span>
                                <small class="text-muted">{{ "%.3f"|format(func.duration) }}s</small>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">{{ func.timestamp }}</small>
                                <span class="badge bg-{{ 'success' if func.success else 'danger' }}">
                                    {{ 'Success' if func.success else 'Failed' }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted text-center">No slow functions detected</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearLogs(type) {
    if (confirm('Are you sure you want to clear ' + type + ' logs?')) {
        fetch('/debug/api/clear-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({type: type})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error clearing logs: ' + error);
        });
    }
}

function refreshStats() {
    location.reload();
}

// Auto-refresh every 30 seconds
setInterval(function() {
    fetch('/debug/api/stats')
    .then(response => response.json())
    .then(data => {
        // Update metrics without full page reload
        document.querySelector('.debug-metric').textContent = data.request_logs_count || 0;
    })
    .catch(error => console.log('Auto-refresh error:', error));
}, 30000);
</script>
{% endblock %}
