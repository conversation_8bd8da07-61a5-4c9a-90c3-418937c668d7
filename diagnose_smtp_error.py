#!/usr/bin/env python3
"""
SMTP Error Diagnosis Tool
========================
This script helps diagnose the specific SMTP error that's preventing email campaigns from working.
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def test_smtp_configurations():
    """Test different SMTP configurations to identify the issue"""
    
    configurations = [
        {
            'name': 'Gmail STARTTLS (587)',
            'server': 'smtp.gmail.com',
            'port': 587,
            'use_ssl': False,
            'use_tls': True,
            'username': '<EMAIL>',
            'password': 'ckqj rnqr rnqr rnqr'  # App password
        },
        {
            'name': 'Gmail SSL (465)',
            'server': 'smtp.gmail.com',
            'port': 465,
            'use_ssl': True,
            'use_tls': False,
            'username': '<EMAIL>',
            'password': 'ckqj rnqr rnqr rnqr'  # App password
        },
        {
            'name': '24Seven SSL (465)',
            'server': 'mail.24seven.site',
            'port': 465,
            'use_ssl': True,
            'use_tls': False,
            'username': '<EMAIL>',
            'password': 'M@kerere1'
        },
        {
            'name': '24Seven STARTTLS (587)',
            'server': 'mail.24seven.site',
            'port': 587,
            'use_ssl': False,
            'use_tls': True,
            'username': '<EMAIL>',
            'password': 'M@kerere1'
        }
    ]
    
    print("🔍 SMTP Configuration Diagnosis")
    print("=" * 50)
    
    working_configs = []
    
    for config in configurations:
        print(f"\n📧 Testing: {config['name']}")
        print(f"   Server: {config['server']}:{config['port']}")
        print(f"   SSL: {config['use_ssl']}, TLS: {config['use_tls']}")
        print(f"   Username: {config['username']}")
        
        try:
            # Test connection
            if config['use_ssl']:
                # SSL connection (port 465)
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(config['server'], config['port'], context=context, timeout=10)
            else:
                # Regular connection
                server = smtplib.SMTP(config['server'], config['port'], timeout=10)
                if config['use_tls']:
                    context = ssl.create_default_context()
                    server.starttls(context=context)
            
            print("   ✅ Connection established")
            
            # Test authentication
            server.login(config['username'], config['password'])
            print("   ✅ Authentication successful")
            
            # Test sending a simple email
            msg = MIMEText("SMTP test successful!", 'plain')
            msg['Subject'] = "SMTP Test"
            msg['From'] = config['username']
            msg['To'] = config['username']  # Send to self
            
            server.send_message(msg)
            print("   ✅ Test email sent successfully")
            
            server.quit()
            working_configs.append(config)
            print(f"   🎉 {config['name']} - WORKING!")
            
        except smtplib.SMTPAuthenticationError as e:
            print(f"   ❌ Authentication failed: {e}")
        except smtplib.SMTPConnectError as e:
            print(f"   ❌ Connection failed: {e}")
        except smtplib.SMTPServerDisconnected as e:
            print(f"   ❌ Server disconnected: {e}")
        except smtplib.SMTPException as e:
            print(f"   ❌ SMTP error: {e}")
        except Exception as e:
            print(f"   ❌ General error: {e}")
    
    print("\n" + "=" * 50)
    print("📊 DIAGNOSIS RESULTS")
    print("=" * 50)
    
    if working_configs:
        print(f"✅ Found {len(working_configs)} working configuration(s):")
        for config in working_configs:
            print(f"   • {config['name']}")
        
        print(f"\n🔧 RECOMMENDED ACTION:")
        best_config = working_configs[0]
        print(f"Use this configuration in your email system:")
        print(f"   MAIL_SERVER = '{best_config['server']}'")
        print(f"   MAIL_PORT = {best_config['port']}")
        print(f"   MAIL_USE_SSL = {best_config['use_ssl']}")
        print(f"   MAIL_USE_TLS = {best_config['use_tls']}")
        print(f"   MAIL_USERNAME = '{best_config['username']}'")
        print(f"   MAIL_PASSWORD = '{best_config['password']}'")
        
    else:
        print("❌ NO WORKING CONFIGURATIONS FOUND!")
        print("\n🔧 TROUBLESHOOTING STEPS:")
        print("1. Check your internet connection")
        print("2. Verify email credentials are correct")
        print("3. Check if Gmail App Passwords are enabled")
        print("4. Verify 24Seven email server is accessible")
        print("5. Check firewall/antivirus blocking SMTP ports")

if __name__ == "__main__":
    test_smtp_configurations()
