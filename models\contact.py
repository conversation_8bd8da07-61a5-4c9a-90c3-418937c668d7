"""
Contact Model
=============
Manages leads and contacts for the sales department.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float

from unified_sales_system import db

class Contact(db.Model):
    """Contact/Lead model for sales department"""

    __tablename__ = 'contacts'

    id = Column(Integer, primary_key=True)

    # Basic Information
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    company = Column(String(200), nullable=True)
    job_title = Column(String(150), nullable=True)

    # Contact Details
    website = Column(String(255), nullable=True)
    linkedin_url = Column(String(255), nullable=True)
    industry = Column(String(100), nullable=True)
    company_size = Column(String(50), nullable=True)  # e.g., "1-10", "11-50", etc.

    # Lead Information
    source = Column(String(100), nullable=True)  # How they found us
    status = Column(String(50), default='new', index=True)  # new, contacted, qualified, customer, lost
    lead_score = Column(Float, default=0.0)  # 0-100 scoring

    # Communication Preferences
    preferred_contact_method = Column(String(20), default='email')  # email, phone, linkedin
    timezone = Column(String(50), nullable=True)
    best_contact_time = Column(String(100), nullable=True)

    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_contacted = Column(DateTime, nullable=True)
    last_activity = Column(DateTime, nullable=True)

    # Flags
    is_active = Column(Boolean, default=True)
    do_not_email = Column(Boolean, default=False)
    do_not_call = Column(Boolean, default=False)
    is_customer = Column(Boolean, default=False)

    # Notes and Additional Info
    notes = Column(Text, nullable=True)
    tags = Column(String(500), nullable=True)  # Comma-separated tags

    # Sales Information
    estimated_budget = Column(Float, nullable=True)
    decision_maker = Column(Boolean, default=False)
    pain_points = Column(Text, nullable=True)
    current_solution = Column(Text, nullable=True)

    def __repr__(self):
        return f'<Contact {self.first_name} {self.last_name} ({self.email})>'

    @property
    def full_name(self):
        """Get full name"""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def display_company(self):
        """Get company name for display"""
        return self.company or "Unknown Company"

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'email': self.email,
            'phone': self.phone,
            'company': self.company,
            'job_title': self.job_title,
            'website': self.website,
            'linkedin_url': self.linkedin_url,
            'industry': self.industry,
            'company_size': self.company_size,
            'source': self.source,
            'status': self.status,
            'lead_score': self.lead_score,
            'preferred_contact_method': self.preferred_contact_method,
            'timezone': self.timezone,
            'best_contact_time': self.best_contact_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_contacted': self.last_contacted.isoformat() if self.last_contacted else None,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'is_active': self.is_active,
            'do_not_email': self.do_not_email,
            'do_not_call': self.do_not_call,
            'is_customer': self.is_customer,
            'notes': self.notes,
            'tags': self.tags,
            'estimated_budget': self.estimated_budget,
            'decision_maker': self.decision_maker,
            'pain_points': self.pain_points,
            'current_solution': self.current_solution
        }

    def update_last_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def add_tag(self, tag):
        """Add a tag to the contact"""
        if self.tags:
            tags_list = [t.strip() for t in self.tags.split(',')]
            if tag not in tags_list:
                tags_list.append(tag)
                self.tags = ', '.join(tags_list)
        else:
            self.tags = tag

    def remove_tag(self, tag):
        """Remove a tag from the contact"""
        if self.tags:
            tags_list = [t.strip() for t in self.tags.split(',')]
            if tag in tags_list:
                tags_list.remove(tag)
                self.tags = ', '.join(tags_list) if tags_list else None

    def get_tags_list(self):
        """Get tags as a list"""
        if self.tags:
            return [t.strip() for t in self.tags.split(',')]
        return []
