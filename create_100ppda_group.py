#!/usr/bin/env python3
"""
Simple script to create 100PPDA group and load first 100 contacts
"""

import os
import sys
import csv
import re
from datetime import datetime

# Ensure we're in the right directory
os.chdir(r'c:\Users\<USER>\Downloads\testsales')
sys.path.insert(0, os.getcwd())

print("Starting 100PPDA contact import...")
print(f"Working directory: {os.getcwd()}")

# Import Flask app
try:
    from unified_sales_system import app, db
    from models.contact import Contact
    from models.contact_group import ContactGroup
    from models.contact_group_membership import ContactGroupMembership
    print("✅ Successfully imported Flask app and models")
except Exception as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def clean_phone(phone):
    """Clean phone number"""
    if not phone:
        return None
    phone = re.sub(r'[^\d+]', '', str(phone))
    if phone.startswith('256'):
        phone = '+' + phone
    elif phone.startswith('0') and len(phone) == 10:
        phone = '+256' + phone[1:]
    elif len(phone) == 9:
        phone = '+256' + phone
    return phone if phone else None

def extract_names(company):
    """Extract first/last name from company"""
    if not company:
        return "Unknown", "Company"
    
    # Clean company name
    company = company.strip()
    suffixes = ['LIMITED', 'LTD', 'CO.', 'COMPANY', 'ENTERPRISES', 'SERVICES', 'SOLUTIONS', 'UGANDA', '(U)', 'SMC']
    
    clean_name = company
    for suffix in suffixes:
        clean_name = re.sub(rf'\b{suffix}\b', '', clean_name, flags=re.IGNORECASE)
    
    clean_name = re.sub(r'\s+', ' ', clean_name).strip()
    clean_name = clean_name.strip('&-.,')
    
    words = clean_name.split()
    if len(words) == 0:
        return "Unknown", "Company"
    elif len(words) == 1:
        return words[0], "Company"
    else:
        return words[0], ' '.join(words[1:])

def main():
    """Main function"""
    csv_file = 'all_suppliers_complete_20250602_084246.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return False
    
    print(f"✅ Found CSV file: {csv_file}")
    
    with app.app_context():
        try:
            # Create or get group
            group = ContactGroup.query.filter_by(name='100PPDA').first()
            if group:
                print(f"✅ Found existing group: {group.name}")
            else:
                group = ContactGroup(
                    name='100PPDA',
                    description='First 100 PPDA suppliers for email campaign',
                    color='#28a745',
                    created_by='import_script'
                )
                db.session.add(group)
                db.session.flush()
                print(f"✅ Created new group: {group.name}")
            
            # Process CSV
            contacts_added = 0
            contacts_updated = 0
            contacts_skipped = 0
            
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row_num, row in enumerate(reader, start=2):
                    if contacts_added + contacts_updated >= 100:
                        break
                    
                    try:
                        company_name = row.get('company_name', '').strip()
                        email = row.get('email', '').strip().lower()
                        phone = clean_phone(row.get('telephone', ''))
                        website = row.get('website', '').strip()
                        
                        if not email or not company_name:
                            contacts_skipped += 1
                            continue
                        
                        first_name, last_name = extract_names(company_name)
                        
                        # Check if contact exists
                        contact = Contact.query.filter_by(email=email).first()
                        
                        if contact:
                            # Update existing
                            contact.company = company_name
                            contact.phone = phone or contact.phone
                            contact.website = website or contact.website
                            contact.source = 'ppda_import'
                            contact.updated_at = datetime.utcnow()
                            contacts_updated += 1
                            print(f"Updated: {email}")
                        else:
                            # Create new
                            contact = Contact(
                                first_name=first_name,
                                last_name=last_name,
                                email=email,
                                phone=phone,
                                company=company_name,
                                website=website,
                                source='ppda_import',
                                status='new',
                                lead_score=50.0
                            )
                            db.session.add(contact)
                            db.session.flush()
                            contacts_added += 1
                            print(f"Added: {email}")
                        
                        # Add to group
                        membership = ContactGroupMembership.query.filter_by(
                            contact_id=contact.id,
                            group_id=group.id
                        ).first()
                        
                        if not membership:
                            membership = ContactGroupMembership(
                                contact_id=contact.id,
                                group_id=group.id,
                                added_by='import_script'
                            )
                            db.session.add(membership)
                        
                    except Exception as e:
                        print(f"Error processing row {row_num}: {e}")
                        contacts_skipped += 1
                        continue
            
            # Commit changes
            db.session.commit()
            
            print("\n" + "="*50)
            print("IMPORT SUMMARY")
            print("="*50)
            print(f"Group: {group.name}")
            print(f"Contacts added: {contacts_added}")
            print(f"Contacts updated: {contacts_updated}")
            print(f"Contacts skipped: {contacts_skipped}")
            print(f"Total in group: {contacts_added + contacts_updated}")
            print("="*50)
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error: {e}")
            return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n✅ Import completed successfully!")
        print("You can now create email campaigns targeting the '100PPDA' group.")
    else:
        print("\n❌ Import failed.")
