#!/usr/bin/env python3
"""
Fix Campaign Groups - Sync recipient_criteria with CampaignGroup records
This script ensures that campaigns with group-based recipient criteria
also have corresponding CampaignGroup records for proper UI functionality.
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_campaign_groups():
    """Fix campaigns that have group recipient criteria but missing CampaignGroup records"""
    try:
        from unified_sales_system import app, db, EmailCampaign, CampaignGroup, ContactGroup
        
        with app.app_context():
            print("\n🔧 FIXING CAMPAIGN GROUP ASSIGNMENTS")
            print("=" * 50)
            
            # Find all campaigns with group-based recipient criteria
            campaigns = EmailCampaign.query.all()
            fixed_campaigns = 0
            
            for campaign in campaigns:
                try:
                    if not campaign.recipient_criteria:
                        continue
                    
                    # Parse recipient criteria
                    criteria = json.loads(campaign.recipient_criteria)
                    
                    if criteria.get('type') == 'groups' and 'group_ids' in criteria:
                        group_ids = criteria['group_ids']
                        print(f"\n📋 Campaign: {campaign.name} (ID: {campaign.id})")
                        print(f"   Group IDs in criteria: {group_ids}")
                        
                        # Check existing CampaignGroup records
                        existing_assignments = CampaignGroup.query.filter_by(campaign_id=campaign.id).all()
                        existing_group_ids = [cg.group_id for cg in existing_assignments]
                        print(f"   Existing CampaignGroup records: {existing_group_ids}")
                        
                        # Create missing CampaignGroup records
                        groups_added = 0
                        for group_id in group_ids:
                            if group_id not in existing_group_ids:
                                # Verify the group exists
                                group = ContactGroup.query.get(group_id)
                                if group:
                                    campaign_group = CampaignGroup(
                                        campaign_id=campaign.id,
                                        group_id=group_id,
                                        added_by='fix_script',
                                        added_at=datetime.utcnow()
                                    )
                                    db.session.add(campaign_group)
                                    groups_added += 1
                                    print(f"   ✅ Added CampaignGroup record for group {group_id} ({group.name})")
                                else:
                                    print(f"   ⚠️  Group {group_id} not found, skipping")
                        
                        if groups_added > 0:
                            fixed_campaigns += 1
                            print(f"   📊 Added {groups_added} CampaignGroup records")
                        else:
                            print(f"   ✅ No missing records found")
                            
                except Exception as e:
                    print(f"   ❌ Error processing campaign {campaign.id}: {str(e)}")
                    continue
            
            # Commit all changes
            if fixed_campaigns > 0:
                db.session.commit()
                print(f"\n✅ Successfully fixed {fixed_campaigns} campaigns")
            else:
                print(f"\n✅ No campaigns needed fixing")
            
            # Verify the fix
            print(f"\n🔍 VERIFICATION")
            print("=" * 20)
            
            for campaign in campaigns:
                try:
                    if not campaign.recipient_criteria:
                        continue
                    
                    criteria = json.loads(campaign.recipient_criteria)
                    if criteria.get('type') == 'groups':
                        group_ids = criteria.get('group_ids', [])
                        campaign_groups = CampaignGroup.query.filter_by(campaign_id=campaign.id).all()
                        campaign_group_ids = [cg.group_id for cg in campaign_groups]
                        
                        print(f"Campaign: {campaign.name}")
                        print(f"  Criteria groups: {group_ids}")
                        print(f"  CampaignGroup records: {campaign_group_ids}")
                        print(f"  Match: {'✅' if set(group_ids) == set(campaign_group_ids) else '❌'}")
                        
                except Exception as e:
                    print(f"  Error verifying campaign {campaign.id}: {str(e)}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Campaign Groups Fix...")
    success = fix_campaign_groups()
    
    if success:
        print("\n🎉 Campaign groups fix completed successfully!")
        print("\nNow when you:")
        print("1. Create a campaign and select groups during creation")
        print("2. View the campaign details")
        print("3. Click 'Assign Groups'")
        print("4. You should see the groups are already assigned!")
    else:
        print("\n❌ Campaign groups fix failed!")
        sys.exit(1)
