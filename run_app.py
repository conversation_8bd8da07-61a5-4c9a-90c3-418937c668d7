#!/usr/bin/env python3
"""
Run Application with Gmail Configuration
=======================================
Simple script to run the application with proper environment loading
"""

import os
import sys

# Load environment variables from .env file
print("🔧 Loading environment variables...")
try:
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
    print("✅ Environment variables loaded")
except Exception as e:
    print(f"⚠️ Could not load .env file: {e}")

# Show email configuration
print("📧 Email configuration:")
print(f"   MAIL_SERVER: {os.environ.get('MAIL_SERVER', 'NOT SET')}")
print(f"   MAIL_PORT: {os.environ.get('MAIL_PORT', 'NOT SET')}")
print(f"   MAIL_USERNAME: {os.environ.get('MAIL_USERNAME', 'NOT SET')}")
print(f"   MAIL_PASSWORD: {'*' * len(os.environ.get('MAIL_PASSWORD', '')) if os.environ.get('MAIL_PASSWORD') else 'NOT SET'}")

print("\n🚀 Starting 24Seven Assistants Sales System...")
print("🌐 Server will be available at: http://localhost:5000")
print("📧 Using Gmail SMTP configuration")
print("⚡ Press Ctrl+C to stop")
print("=" * 60)

# Import and run the application
try:
    from unified_sales_system import app
    app.run(debug=True, host='0.0.0.0', port=5000)
except Exception as e:
    print(f"❌ Failed to start application: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
