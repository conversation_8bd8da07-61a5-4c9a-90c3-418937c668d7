#!/usr/bin/env python3
"""
Test Group Editing Fix
=====================
Quick test to verify that group editing is now working.
"""

import requests

def test_group_editing_fix():
    """Test that group editing is now working"""
    print("🧪 Testing Group Editing Fix")
    print("=" * 40)
    
    base_url = 'http://localhost:5000'
    
    try:
        # Test 1: Check groups page
        print("1. Testing groups page...")
        response = requests.get(f'{base_url}/groups', timeout=5)
        if response.status_code == 200:
            print("✅ Groups page loads successfully")
        else:
            print(f"❌ Groups page failed: {response.status_code}")
            return False
        
        # Test 2: Look for edit links
        print("2. Looking for edit links...")
        import re
        edit_links = re.findall(r'/groups/(\d+)/edit', response.text)
        
        if edit_links:
            group_id = edit_links[0]
            print(f"✅ Found edit link for group {group_id}")
            
            # Test 3: Try to access edit page
            print("3. Testing edit page access...")
            edit_response = requests.get(f'{base_url}/groups/{group_id}/edit', timeout=10)
            
            if edit_response.status_code == 200:
                print("✅ Edit page loads successfully!")
                print("🎉 Group editing is now working!")
                return True
            else:
                print(f"❌ Edit page failed: {edit_response.status_code}")
                return False
        else:
            print("ℹ️ No groups found to test editing")
            print("   Create a group first, then try editing it")
            return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_group_editing_fix()
    if success:
        print("\n✅ GROUP EDITING FIX SUCCESSFUL!")
    else:
        print("\n❌ GROUP EDITING STILL HAS ISSUES")
    exit(0 if success else 1)
