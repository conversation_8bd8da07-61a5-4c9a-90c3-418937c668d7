#!/usr/bin/env python3
"""
Final Campaign Test
==================
Complete test of the email campaign system with Gmail integration
"""

import requests
import json
import time

def test_web_interface():
    """Test if web interface is accessible"""
    print("🌐 Testing Web Interface...")
    print("-" * 40)
    
    try:
        response = requests.get("http://localhost:5000", timeout=10)
        if response.status_code == 200:
            print("✅ Web interface is accessible")
            return True
        else:
            print(f"⚠️ Web interface returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Web interface is not accessible")
        print("   Please ensure the application is running")
        return False
    except Exception as e:
        print(f"❌ Web interface error: {e}")
        return False

def test_campaigns_page():
    """Test campaigns page"""
    print("\n📧 Testing Campaigns Page...")
    print("-" * 40)
    
    try:
        response = requests.get("http://localhost:5000/campaigns", timeout=10)
        if response.status_code == 200:
            print("✅ Campaigns page is accessible")
            return True
        else:
            print(f"⚠️ Campaigns page returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Campaigns page error: {e}")
        return False

def test_contacts_page():
    """Test contacts page"""
    print("\n👥 Testing Contacts Page...")
    print("-" * 40)
    
    try:
        response = requests.get("http://localhost:5000/contacts", timeout=10)
        if response.status_code == 200:
            print("✅ Contacts page is accessible")
            return True
        else:
            print(f"⚠️ Contacts page returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Contacts page error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 24SEVEN ASSISTANTS - FINAL CAMPAIGN TEST")
    print("=" * 60)
    print("Testing the complete email campaign system")
    print("=" * 60)
    
    # Test web interface
    web_success = test_web_interface()
    
    # Test campaigns page
    campaigns_success = test_campaigns_page() if web_success else False
    
    # Test contacts page
    contacts_success = test_contacts_page() if web_success else False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL TEST SUMMARY")
    print("=" * 60)
    print(f"Web Interface: {'✅ WORKING' if web_success else '❌ FAILED'}")
    print(f"Campaigns Page: {'✅ WORKING' if campaigns_success else '❌ FAILED'}")
    print(f"Contacts Page: {'✅ WORKING' if contacts_success else '❌ FAILED'}")
    
    if all([web_success, campaigns_success, contacts_success]):
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 Manual Steps to Send <NAME_EMAIL>:")
        print("=" * 60)
        
        print("\n1️⃣ CREATE TEST CONTACT:")
        print("   • Go to: http://localhost:5000/contacts")
        print("   • Click 'Add New Contact'")
        print("   • Fill in:")
        print("     - First Name: Alex")
        print("     - Last Name: Scof")
        print("     - Email: <EMAIL>")
        print("     - Company: Test Company")
        print("   • Click 'Add Contact'")
        
        print("\n2️⃣ CREATE TEST CAMPAIGN:")
        print("   • Go to: http://localhost:5000/campaigns")
        print("   • Click 'Create New Campaign'")
        print("   • Fill in:")
        print("     - Name: Test Campaign to Gmail")
        print("     - Subject: 🚀 Test Email from 24Seven Assistants")
        print("     - Template: Introduction")
        print("     - Recipients: All contacts")
        print("   • Click 'Create Campaign'")
        
        print("\n3️⃣ SEND CAMPAIGN:")
        print("   • Find your campaign in the list")
        print("   • Click the 'Send' button")
        print("   • Wait for confirmation message")
        
        print("\n4️⃣ VERIFY SUCCESS:")
        print("   • Check Gmail <NAME_EMAIL>")
        print("   • Look for campaign email with chatbot link")
        print("   • Check campaign statistics in dashboard")
        
        print("\n✅ SYSTEM STATUS:")
        print("   • SMTP: Working (Gmail configuration)")
        print("   • Web Interface: Accessible")
        print("   • Email System: Ready")
        print("   • Campaign System: Operational")
        
        print("\n🔗 Quick Links:")
        print("   • Dashboard: http://localhost:5000")
        print("   • Campaigns: http://localhost:5000/campaigns")
        print("   • Contacts: http://localhost:5000/contacts")
        print("   • Analytics: http://localhost:5000/analytics")
        
    else:
        print("\n❌ SOME TESTS FAILED")
        print("\nTroubleshooting:")
        if not web_success:
            print("• Application may not be running")
            print("  Start with: python start_app_simple.py")
        print("• Check console for error messages")
        print("• Verify Gmail configuration in .env file")
    
    return all([web_success, campaigns_success, contacts_success])

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
