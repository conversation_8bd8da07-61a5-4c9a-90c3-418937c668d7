#!/usr/bin/env python3
"""
Quick API test for the contacts and campaigns system
"""

import requests
import json

def test_api():
    """Test the API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Quick API Test")
    print("=" * 30)
    
    # Test 1: Contact count API
    try:
        response = requests.get(f"{base_url}/api/contacts/count", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Contact count API: {data.get('count', 0)} contacts")
        else:
            print(f"❌ Contact count API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Contact count API error: {str(e)}")
    
    # Test 2: Main page accessibility
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Main page accessible")
        else:
            print(f"❌ Main page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Main page error: {str(e)}")
    
    # Test 3: Contacts page
    try:
        response = requests.get(f"{base_url}/contacts", timeout=5)
        if response.status_code == 200:
            print("✅ Contacts page accessible")
        else:
            print(f"❌ Contacts page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Contacts page error: {str(e)}")
    
    # Test 4: Campaigns page
    try:
        response = requests.get(f"{base_url}/campaigns", timeout=5)
        if response.status_code == 200:
            print("✅ Campaigns page accessible")
        else:
            print(f"❌ Campaigns page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Campaigns page error: {str(e)}")
    
    # Test 5: Chatbot session tracking API
    try:
        test_data = {
            'session_id': 'test-session-123',
            'stage': 'opening',
            'task': 'greeting',
            'contact_name': 'Test User'
        }
        response = requests.post(f"{base_url}/api/chatbot/session", 
                               json=test_data, timeout=5)
        if response.status_code == 200:
            print("✅ Chatbot session API working")
        else:
            print(f"❌ Chatbot session API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chatbot session API error: {str(e)}")
    
    print("\n🎯 Test Summary:")
    print("The system appears to be running correctly!")
    print("You can now test contact and campaign creation/deletion through the web interface.")
    print("\n📋 Next Steps:")
    print("1. Open http://localhost:5000/contacts/add to test contact creation")
    print("2. Open http://localhost:5000/campaigns/create to test campaign creation")
    print("3. Test deletion operations through the web interface")

if __name__ == "__main__":
    test_api()
