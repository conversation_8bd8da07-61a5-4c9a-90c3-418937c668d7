#!/usr/bin/env python3
"""
Test script for the enhanced debugging system
"""

import sys
import time
import requests
from datetime import datetime

def test_debug_system():
    """Test the debug system functionality"""
    base_url = "http://localhost:5000"
    
    print("🔧 Testing 24Seven Assistants Debug System")
    print("=" * 50)
    
    # Test 1: Check if debug dashboard is accessible
    print("1. Testing Debug Dashboard Access...")
    try:
        response = requests.get(f"{base_url}/debug/")
        if response.status_code == 200:
            print("   ✅ Debug dashboard accessible")
        else:
            print(f"   ❌ Debug dashboard returned status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing debug dashboard: {e}")
    
    # Test 2: Generate some test requests
    print("\n2. Generating Test Requests...")
    test_endpoints = [
        "/",
        "/analytics",
        "/contacts",
        "/campaigns",
        "/api/stats"
    ]
    
    for endpoint in test_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"   📡 {endpoint} -> {response.status_code}")
            time.sleep(0.5)  # Small delay between requests
        except Exception as e:
            print(f"   ❌ Error testing {endpoint}: {e}")
    
    # Test 3: Check debug API endpoints
    print("\n3. Testing Debug API Endpoints...")
    debug_endpoints = [
        "/debug/api/stats",
        "/debug/requests",
        "/debug/errors",
        "/debug/performance",
        "/debug/database"
    ]
    
    for endpoint in debug_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                print(f"   ✅ {endpoint} -> OK")
            else:
                print(f"   ⚠️  {endpoint} -> {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error testing {endpoint}: {e}")
    
    # Test 4: Test email functionality
    print("\n4. Testing Email Debug...")
    try:
        response = requests.get(f"{base_url}/debug/email-test")
        if response.status_code == 200:
            print("   ✅ Email test page accessible")
        else:
            print(f"   ❌ Email test page returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error accessing email test: {e}")
    
    # Test 5: Generate a slow request (for performance monitoring)
    print("\n5. Testing Performance Monitoring...")
    try:
        # This should trigger slow request logging
        response = requests.get(f"{base_url}/analytics", timeout=10)
        print(f"   📊 Analytics page loaded in response")
    except Exception as e:
        print(f"   ❌ Error testing performance: {e}")
    
    # Test 6: Check debug stats
    print("\n6. Checking Debug Statistics...")
    try:
        response = requests.get(f"{base_url}/debug/api/stats")
        if response.status_code == 200:
            stats = response.json()
            print(f"   📈 Request logs: {stats.get('request_logs_count', 0)}")
            print(f"   🚨 Error logs: {stats.get('error_logs_count', 0)}")
            print(f"   ⚡ Performance logs: {stats.get('performance_logs_count', 0)}")
        else:
            print(f"   ❌ Stats API returned {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error getting stats: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Debug System Test Complete!")
    print("\nTo view the debug dashboard, visit:")
    print(f"   {base_url}/debug/")
    print("\nDebug Features Available:")
    print("   • Request/Response Logging")
    print("   • Error Tracking & Reporting")
    print("   • Performance Monitoring")
    print("   • Database Debugging")
    print("   • Email Testing Tools")
    print("   • System Health Monitoring")

def test_error_generation():
    """Generate some test errors for debugging"""
    base_url = "http://localhost:5000"
    
    print("\n🚨 Generating Test Errors for Debug System...")
    
    # Test endpoints that might generate errors
    error_endpoints = [
        "/nonexistent-page",
        "/api/invalid-endpoint",
        "/contacts/99999",  # Non-existent contact
        "/campaigns/99999"  # Non-existent campaign
    ]
    
    for endpoint in error_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"   🔍 {endpoint} -> {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint} -> Error: {e}")
        time.sleep(0.3)
    
    print("   ✅ Test errors generated for debugging")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--errors":
        test_error_generation()
    else:
        test_debug_system()
        
        # Ask if user wants to generate test errors
        try:
            generate_errors = input("\nGenerate test errors for debugging? (y/n): ").lower().strip()
            if generate_errors in ['y', 'yes']:
                test_error_generation()
        except KeyboardInterrupt:
            print("\n👋 Test completed!")
