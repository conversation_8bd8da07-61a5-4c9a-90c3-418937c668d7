#!/usr/bin/env python3
"""
Complete System Test
===================
Test the entire campaign creation and email system end-to-end.
"""

import requests
import json
from datetime import datetime
import time

# Base URL for the application
BASE_URL = "http://127.0.0.1:5000"

def test_system_status():
    """Test that all system components are accessible"""
    print("🔍 Testing System Status...")
    
    endpoints = [
        ('Dashboard', '/'),
        ('Analytics', '/analytics'),
        ('Contacts', '/contacts'),
        ('Groups', '/groups'),
        ('Campaigns List', '/campaigns'),
        ('Campaign Creation', '/campaigns/create'),
        ('Debug Dashboard', '/debug/'),
    ]
    
    results = []
    
    for name, endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            if response.status_code == 200:
                print(f"  ✅ {name}: Accessible")
                results.append(True)
            else:
                print(f"  ❌ {name}: Error {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"  ❌ {name}: Exception - {str(e)}")
            results.append(False)
    
    success_rate = sum(results) / len(results) * 100
    print(f"📊 System Status: {sum(results)}/{len(results)} endpoints accessible ({success_rate:.1f}%)")
    
    return success_rate >= 80

def test_create_test_contact():
    """Create a test contact for email testing"""
    print("👤 Creating Test Contact...")
    
    contact_data = {
        'first_name': 'Test',
        'last_name': 'User',
        'email': '<EMAIL>',
        'company': 'Test Company',
        'job_title': 'Test Manager',
        'phone': '+1234567890',
        'source': 'manual_test',
        'status': 'new'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/contacts/add", data=contact_data)
        
        if response.status_code in [200, 302]:
            print("  ✅ Test contact created successfully")
            return True
        else:
            print(f"  ❌ Failed to create test contact: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error creating test contact: {str(e)}")
        return False

def test_campaign_creation_flow():
    """Test the complete campaign creation flow"""
    print("📧 Testing Campaign Creation Flow...")
    
    # Create a comprehensive test campaign
    campaign_data = {
        'name': f'Complete System Test Campaign {datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate',
        'batch_size': '25',
        'batch_delay_minutes': '2'
    }
    
    try:
        # Step 1: Create campaign
        print("  Step 1: Creating campaign...")
        response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)
        
        if response.status_code not in [200, 302]:
            print(f"    ❌ Campaign creation failed: {response.status_code}")
            return False
        
        print("    ✅ Campaign created successfully")
        
        # Step 2: Verify campaign appears in list
        print("  Step 2: Verifying campaign in list...")
        response = requests.get(f"{BASE_URL}/campaigns")
        
        if response.status_code == 200:
            content = response.text
            if campaign_data['name'] in content:
                print("    ✅ Campaign appears in campaigns list")
            else:
                print("    ⚠️ Campaign may not be visible in list (but creation succeeded)")
        else:
            print(f"    ❌ Cannot access campaigns list: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error in campaign creation flow: {str(e)}")
        return False

def test_smtp_configuration():
    """Test SMTP configuration"""
    print("📮 Testing SMTP Configuration...")
    
    try:
        response = requests.post(f"{BASE_URL}/api/test-smtp")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("  ✅ SMTP configuration is working")
                return True
            else:
                print(f"  ❌ SMTP test failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"  ❌ SMTP test endpoint error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing SMTP: {str(e)}")
        return False

def test_analytics_data():
    """Test that analytics are working"""
    print("📊 Testing Analytics Data...")
    
    try:
        response = requests.get(f"{BASE_URL}/analytics")
        
        if response.status_code == 200:
            print("  ✅ Analytics page accessible")
            
            # Check if analytics contain expected sections
            content = response.text.lower()
            expected_sections = ['email', 'campaign', 'conversion', 'funnel']
            
            found_sections = sum(1 for section in expected_sections if section in content)
            
            if found_sections >= 3:
                print(f"  ✅ Analytics contains expected sections ({found_sections}/{len(expected_sections)})")
                return True
            else:
                print(f"  ⚠️ Analytics may be incomplete ({found_sections}/{len(expected_sections)} sections found)")
                return True  # Still consider success if page loads
        else:
            print(f"  ❌ Analytics page not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing analytics: {str(e)}")
        return False

def test_debug_functionality():
    """Test debug dashboard functionality"""
    print("🐛 Testing Debug Functionality...")
    
    try:
        response = requests.get(f"{BASE_URL}/debug/")
        
        if response.status_code == 200:
            print("  ✅ Debug dashboard accessible")
            
            # Test debug info endpoint
            response = requests.get(f"{BASE_URL}/debug/info")
            if response.status_code == 200:
                print("  ✅ Debug info endpoint working")
                return True
            else:
                print(f"  ⚠️ Debug info endpoint issue: {response.status_code}")
                return True  # Main debug page works
        else:
            print(f"  ❌ Debug dashboard not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing debug functionality: {str(e)}")
        return False

def test_chatbot_integration():
    """Test chatbot integration"""
    print("🤖 Testing Chatbot Integration...")
    
    try:
        # Test chatbot endpoint
        test_session_id = f"test_{datetime.now().strftime('%H%M%S')}"
        response = requests.get(f"{BASE_URL}/chat/{test_session_id}")
        
        if response.status_code == 200:
            print("  ✅ Chatbot interface accessible")
            return True
        else:
            print(f"  ❌ Chatbot interface error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing chatbot: {str(e)}")
        return False

def main():
    """Run complete system test"""
    print("🚀 Starting Complete System Test")
    print("=" * 60)
    
    tests = [
        ("System Status", test_system_status),
        ("Test Contact Creation", test_create_test_contact),
        ("Campaign Creation Flow", test_campaign_creation_flow),
        ("SMTP Configuration", test_smtp_configuration),
        ("Analytics Data", test_analytics_data),
        ("Debug Functionality", test_debug_functionality),
        ("Chatbot Integration", test_chatbot_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"Result: {'✅ PASS' if result else '❌ FAIL'}")
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("📊 Complete System Test Results")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / total * 100
    print(f"\nOverall: {passed}/{total} tests passed ({success_rate:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! The complete sales system is working perfectly.")
        print("✅ Campaign creation, validation, and all integrations are functional.")
    elif passed >= total * 0.8:
        print("✅ Most tests passed! The system is working well with minor issues.")
        print("🔧 Consider reviewing failed components for optimization.")
    else:
        print("⚠️ Several tests failed. The system needs attention.")
        print("🔧 Please review the failed components before production use.")
    
    print("\n🎯 System Ready For:")
    if passed >= total * 0.8:
        print("  • Creating and managing email campaigns")
        print("  • Tracking email engagement and conversions")
        print("  • Chatbot integration for sales conversations")
        print("  • Real-time analytics and reporting")
        print("  • Debug monitoring and troubleshooting")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
