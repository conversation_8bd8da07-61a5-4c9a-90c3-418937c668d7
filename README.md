# 24Seven Assistants - Complete Sales System

A comprehensive sales system combining a full-featured Sales Department Dashboard with an AI-powered Sales Chatbot for complete sales automation and management.

## 🚀 Features

### ✅ **SMTP Email System**
- **Professional Email Templates** for 24Seven Assistants introduction campaigns
- **Bulk Email Sending** with configurable delays and batch processing
- **Email Tracking** - opens, clicks, replies, bounces, unsubscribes
- **Campaign Management** - create, schedule, pause, resume campaigns
- **Personalized Content** using Jinja2 templating

### ✅ **Sales Stage Analytics Dashboard**
- **5-Stage Sales Process** tracking (Opening → Trust → Discovery → Demonstration → Close)
- **Interactive Charts** using Plotly.js for funnel analysis, stage duration, conversion rates
- **Real-time Metrics** - pipeline value, conversion rates, bottleneck detection
- **Performance Trends** over time with customizable date ranges

### ✅ **Complete Sales Department Structure**
- **Contact Management** - leads, prospects, customers with full CRUD operations
- **Opportunity Tracking** - sales pipeline management with stage progression
- **Activity Logging** - emails, calls, meetings, notes with AI integration
- **Sales Process Automation** following proven methodologies

### ✅ **Integration with Existing Salesbot**
- **SambaNova AI Integration** using your existing API configuration
- **Compatible Architecture** that works with your current salesbot system
- **Unified Data Model** for seamless integration

## 🏗️ Architecture

Based on your project.md structure with:
- **Flask Backend** with SQLAlchemy ORM
- **PostgreSQL/SQLite Database** with comprehensive models
- **Plotly.js Analytics** with interactive dashboards
- **Bootstrap UI** with responsive design
- **Modular Design** following your proven patterns

## 📦 Installation

1. **Clone and Setup**
```bash
cd /c/Users/<USER>/Downloads/testsales
pip install -r requirements.txt
```

2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your SMTP and database settings
```

3. **Initialize Database**
```bash
python sales_department_app.py
# This will create tables and sample data
```

4. **Run the Application**
```bash
python sales_department_app.py
```

Access at: http://localhost:5000

## 🎯 Sales Process Stages

The system tracks prospects through 5 proven sales stages:

1. **Opening** (10% probability) - Initial contact and introduction
2. **Trust** (25% probability) - Building rapport and credibility
3. **Discovery** (50% probability) - Understanding needs and pain points
4. **Demonstration** (75% probability) - Presenting solutions and value
5. **Close** (90% probability) - Finalizing the deal

## 📧 Email Campaign Templates

### Introduction Email
Professional template introducing 24Seven Assistants services:
- **24/7 Availability** messaging
- **Cost savings** (up to 70% vs full-time staff)
- **Service highlights** (admin, customer service, operations, marketing)
- **Clear call-to-action** for consultation

### Follow-up Email
Gentle follow-up template for non-responders:
- **Brief and respectful** approach
- **Value reinforcement**
- **Easy response** options

## 📊 Analytics Dashboard

### Key Metrics Tracked
- **Pipeline Value** - total and weighted by probability
- **Conversion Rates** - lead to opportunity, opportunity to customer
- **Stage Performance** - time in stage, bottlenecks, progression
- **Email Performance** - open rates, click rates, reply rates
- **Activity Metrics** - emails sent, calls made, meetings held

### Interactive Charts
- **Sales Funnel** visualization with conversion rates
- **Stage Duration** analysis with min/max/average times
- **Email Performance** trends and campaign comparisons
- **Performance Overview** with multiple KPIs

## 🔧 Configuration

### SMTP Settings
Configure in `.env` file:
```
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### SambaNova AI (Your Existing Setup)
```
SAMBA_API_KEY=76273dc7-7c75-417f-8cfe-ef88ad56db78
SAMBA_BASE_URL=https://api.sambanova.ai/v1
SAMBA_MODEL=Meta-Llama-3.3-70B-Instruct
SAMBA_TEMPERATURE=0.3
```

## 🎮 Usage

### 1. Add Contacts
- Navigate to **Contacts** → **Add Contact**
- Fill in contact information
- System automatically assigns lead score

### 2. Create Email Campaign
- Go to **Email Campaigns** → **Create Campaign**
- Choose template (Introduction or Follow-up)
- Select target audience
- Send immediately or schedule

### 3. Track Sales Progress
- View **Analytics Dashboard** for comprehensive metrics
- Monitor **Opportunities** for individual deal progress
- Check **Activities** for interaction history

### 4. Monitor Performance
- **Stage Analytics** show conversion rates and bottlenecks
- **Email Analytics** track campaign performance
- **Performance Metrics** provide overall KPIs

## 🔗 Integration with Your Existing Salesbot

This system is designed to work alongside your existing salesbot:

1. **Shared Data Models** - contacts and opportunities can be linked to bot sessions
2. **Activity Tracking** - bot interactions are logged as activities
3. **Stage Progression** - bot can update opportunity stages based on conversations
4. **Email Triggers** - bot conversations can trigger email campaigns

## 📁 Project Structure

```
├── sales_department_app.py      # Main Flask application
├── config.py                    # Configuration settings
├── requirements.txt             # Python dependencies
├── models/                      # Database models
│   ├── contact.py              # Contact/Lead model
│   ├── opportunity.py          # Sales opportunity model
│   ├── sales_stage.py          # Sales stages and history
│   ├── email_campaign.py       # Email campaigns and logs
│   ├── activity.py             # Activity tracking
│   └── analytics.py            # Analytics aggregation
├── email_system/               # Email functionality
│   ├── smtp_service.py         # SMTP email sending
│   ├── email_templates.py      # Email templates
│   ├── campaign_manager.py     # Campaign management
│   └── email_tracker.py        # Email tracking
├── analytics/                  # Analytics system
│   ├── dashboard.py            # Main dashboard
│   ├── stage_analytics.py      # Sales stage analytics
│   ├── email_analytics.py      # Email performance analytics
│   └── performance_metrics.py  # Overall performance metrics
└── templates/                  # Web interface templates
    ├── base.html               # Base template
    ├── dashboard.html          # Main dashboard
    ├── analytics.html          # Analytics dashboard
    ├── contacts.html           # Contact list
    └── add_contact.html        # Add contact form
```

## 🚀 Next Steps

1. **Test SMTP Configuration** using the built-in test button
2. **Add Your First Contacts** to start building your database
3. **Create Introduction Campaign** to reach out to prospects
4. **Monitor Analytics** to track performance and optimize

## 🎯 Success Metrics

Track your sales department success with:
- **Email Open Rates** > 25%
- **Email Reply Rates** > 5%
- **Lead to Opportunity Rate** > 15%
- **Opportunity to Customer Rate** > 20%
- **Average Sales Cycle** < 30 days

## 🔧 Troubleshooting

### SMTP Issues
- Verify email credentials in `.env`
- Check if 2FA requires app password
- Test connection using built-in SMTP test

### Database Issues
- Ensure SQLite file permissions
- Check database URL in configuration
- Restart application to reinitialize

### Analytics Not Loading
- Check browser console for JavaScript errors
- Verify Plotly.js is loading correctly
- Ensure data exists for the selected time period

---

**Built for 24Seven Assistants** - Professional virtual assistant services available 24/7

This system provides everything you need to run a complete sales department with professional email campaigns, comprehensive analytics, and seamless integration with your existing salesbot infrastructure.
