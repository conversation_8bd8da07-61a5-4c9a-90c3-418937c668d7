{% extends "base.html" %}

{% block title %}Unified Analytics - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-chart-line text-primary"></i> Unified Sales Analytics</h1>
    <div>
        <a href="{{ url_for('sales_pipeline_analytics') }}" class="btn btn-success me-2">
            <i class="fas fa-chart-line"></i> Sales Pipeline
        </a>
        <a href="{{ url_for('sales_cycle_analytics') }}" class="btn btn-info me-2">
            <i class="fas fa-clock"></i> Sales Cycle
        </a>
        <a href="{{ url_for('session_analytics') }}" class="btn btn-primary me-2">
            <i class="fas fa-comments"></i> Session Analytics
        </a>
        <a href="{{ url_for('comprehensive_analytics') }}" class="btn btn-warning me-2">
            <i class="fas fa-chart-pie"></i> Complete Dashboard
        </a>
        <a href="{{ url_for('index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt"></i> Complete Sales Funnel Performance</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <h3 class="text-primary">{{ summary.funnel_metrics.emails_sent }}</h3>
                        <p class="mb-0">Emails Sent</p>
                        <small class="text-muted">Campaign Reach</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <h3 class="text-info">{{ summary.conversion_rates.email_open_rate }}%</h3>
                        <p class="mb-0">Open Rate</p>
                        <small class="text-muted">{{ summary.funnel_metrics.emails_opened }} opened</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <h3 class="text-warning">{{ summary.conversion_rates.click_rate }}%</h3>
                        <p class="mb-0">Click Rate</p>
                        <small class="text-muted">{{ summary.funnel_metrics.links_clicked }} clicked</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <h3 class="text-success">{{ summary.conversion_rates.conversation_rate }}%</h3>
                        <p class="mb-0">Conversation Rate</p>
                        <small class="text-muted">{{ summary.funnel_metrics.conversations_started }} started</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <h3 class="text-danger">{{ summary.conversion_rates.conversion_rate }}%</h3>
                        <p class="mb-0">Close Rate</p>
                        <small class="text-muted">{{ summary.funnel_metrics.conversions }} converted</small>
                    </div>
                    <div class="col-md-2 text-center">
                        <h3 class="text-dark">{{ summary.conversion_rates.overall_rate }}%</h3>
                        <p class="mb-0">Overall Rate</p>
                        <small class="text-muted">Email to Sale</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Conversion Funnel Chart -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-funnel-dollar"></i> Complete Sales Funnel</h5>
            </div>
            <div class="card-body">
                {% if charts.funnel %}
                    <div id="funnelChart"></div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No funnel data available yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bullseye"></i> Conversion Insights</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Email Performance</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-info" style="width: {{ summary.conversion_rates.email_open_rate }}%"></div>
                    </div>
                    <small>{{ summary.conversion_rates.email_open_rate }}% open rate</small>
                </div>

                <div class="mb-3">
                    <h6>Engagement Rate</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" style="width: {{ summary.conversion_rates.click_rate }}%"></div>
                    </div>
                    <small>{{ summary.conversion_rates.click_rate }}% click through</small>
                </div>

                <div class="mb-3">
                    <h6>Chatbot Effectiveness</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: {{ summary.conversion_rates.conversation_rate }}%"></div>
                    </div>
                    <small>{{ summary.conversion_rates.conversation_rate }}% start conversations</small>
                </div>

                <div class="mb-3">
                    <h6>Sales Conversion</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-danger" style="width: {{ summary.conversion_rates.conversion_rate }}%"></div>
                    </div>
                    <small>{{ summary.conversion_rates.conversion_rate }}% close rate</small>
                </div>

                <hr>

                <div class="text-center">
                    <h4 class="text-primary">{{ summary.conversion_rates.overall_rate }}%</h4>
                    <p class="mb-0">Overall Conversion</p>
                    <small class="text-muted">Email → Sale</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stage Distribution Chart -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Current Stage Distribution</h5>
            </div>
            <div class="card-body">
                {% if charts.stages %}
                    <div id="stagesChart"></div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No stage data available yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Detailed Metrics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-envelope"></i> Email Campaign Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Total Emails Sent</strong></td>
                            <td class="text-end">{{ summary.funnel_metrics.emails_sent }}</td>
                        </tr>
                        <tr>
                            <td><strong>Emails Opened</strong></td>
                            <td class="text-end">{{ summary.funnel_metrics.emails_opened }} ({{ summary.conversion_rates.email_open_rate }}%)</td>
                        </tr>
                        <tr>
                            <td><strong>Links Clicked</strong></td>
                            <td class="text-end">{{ summary.funnel_metrics.links_clicked }} ({{ summary.conversion_rates.click_rate }}%)</td>
                        </tr>
                        <tr class="table-info">
                            <td><strong>Email → Click Rate</strong></td>
                            <td class="text-end">
                                {% if summary.funnel_metrics.emails_opened > 0 %}
                                    {{ summary.conversion_rates.click_rate }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-robot"></i> Chatbot Performance</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Conversations Started</strong></td>
                            <td class="text-end">{{ summary.funnel_metrics.conversations_started }}</td>
                        </tr>
                        <tr>
                            <td><strong>Conversions Achieved</strong></td>
                            <td class="text-end">{{ summary.funnel_metrics.conversions }}</td>
                        </tr>
                        <tr>
                            <td><strong>Conversation → Sale Rate</strong></td>
                            <td class="text-end">{{ summary.conversion_rates.conversion_rate }}%</td>
                        </tr>
                        <tr class="table-success">
                            <td><strong>Click → Conversion Rate</strong></td>
                            <td class="text-end">
                                {% if summary.funnel_metrics.links_clicked > 0 %}
                                    {{ summary.conversion_rates.conversation_rate }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stage Breakdown -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list-ol"></i> Detailed Stage Breakdown</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for stage_name, count in summary.stage_distribution.items() %}
                    <div class="col-md-3 mb-3">
                        <div class="card border-left-primary">
                            <div class="card-body py-2">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            {{ stage_name }}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ count }}</div>
                                    </div>
                                    <div class="col-auto">
                                        {% if 'Email' in stage_name %}
                                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                        {% elif 'Click' in stage_name %}
                                            <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                                        {% elif 'Opening' in stage_name %}
                                            <i class="fas fa-door-open fa-2x text-gray-300"></i>
                                        {% elif 'Trust' in stage_name %}
                                            <i class="fas fa-handshake fa-2x text-gray-300"></i>
                                        {% elif 'Discovery' in stage_name %}
                                            <i class="fas fa-search fa-2x text-gray-300"></i>
                                        {% elif 'Demo' in stage_name %}
                                            <i class="fas fa-presentation fa-2x text-gray-300"></i>
                                        {% elif 'Converted' in stage_name %}
                                            <i class="fas fa-trophy fa-2x text-gray-300"></i>
                                        {% else %}
                                            <i class="fas fa-circle fa-2x text-gray-300"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Render funnel chart
        {% if charts.funnel %}
        var funnelData = {{ charts.funnel|safe }};
        Plotly.newPlot('funnelChart', funnelData.data, funnelData.layout, {responsive: true});
        {% endif %}

        // Render stages chart
        {% if charts.stages %}
        var stagesData = {{ charts.stages|safe }};
        Plotly.newPlot('stagesChart', stagesData.data, stagesData.layout, {responsive: true});
        {% endif %}
    });

    // Auto-refresh every 60 seconds
    setInterval(function() {
        location.reload();
    }, 60000);
</script>
{% endblock %}
