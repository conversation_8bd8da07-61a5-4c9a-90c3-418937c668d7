#!/usr/bin/env python3
"""
Quick Campaigns Test
===================
Quick test to check campaigns functionality.
"""

import requests

def test_campaigns():
    """Quick test of campaigns page"""
    try:
        print("Testing campaigns page...")
        response = requests.get('http://localhost:5000/campaigns', timeout=5)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            if 'email campaigns' in content:
                print("✅ Campaigns page loads with 'Email Campaigns' header")
            else:
                print("❌ Missing 'Email Campaigns' header")
            
            if 'create campaign' in content:
                print("✅ Found 'Create Campaign' button")
            else:
                print("❌ Missing 'Create Campaign' button")
            
            if 'no campaigns found' in content:
                print("ℹ️ No campaigns exist yet (empty state)")
            else:
                print("ℹ️ Campaigns may exist or different state")
            
            return True
        else:
            print(f"❌ Failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_campaigns()
    if success:
        print("\n✅ CAMPAIGNS FUNCTIONALITY APPEARS TO BE WORKING!")
        print("You can access campaigns at: http://localhost:5000/campaigns")
    else:
        print("\n❌ CAMPAIGNS FUNCTIONALITY HAS ISSUES")
    exit(0 if success else 1)
