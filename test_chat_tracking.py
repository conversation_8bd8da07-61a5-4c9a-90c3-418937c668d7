#!/usr/bin/env python3
"""
Test Chat Tracking Functionality
Tests the chat progression tracking to ensure it's working correctly
"""

import requests
import json
import uuid
from datetime import datetime

BASE_URL = 'http://localhost:5000'

def test_session_tracking():
    """Test the complete session tracking flow"""
    print("🧪 Testing Chat Session Tracking")
    print("=" * 50)
    
    # Generate test session ID
    session_id = str(uuid.uuid4())
    print(f"📝 Test Session ID: {session_id}")
    
    # Test 1: Start conversation
    print("\n1️⃣ Testing conversation start...")
    response = requests.post(f"{BASE_URL}/api/track-session", json={
        'session_id': session_id,
        'stage': 'opening',
        'task': 'greeting',
        'contact_name': 'Test User',
        'contact_email': '<EMAIL>',
        'action': 'conversation_started',
        'message_count': 1,
        'bot_response': 'Hello! Welcome to 24Seven Assistants.'
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Conversation started: {result}")
    else:
        print(f"❌ Failed to start conversation: {response.status_code} - {response.text}")
        return False
    
    # Test 2: Stage progression
    print("\n2️⃣ Testing stage progression...")
    stages = ['trust', 'discovery', 'demonstration', 'close']
    
    for i, stage in enumerate(stages, 2):
        response = requests.post(f"{BASE_URL}/api/track-session", json={
            'session_id': session_id,
            'stage': stage,
            'task': f'{stage}_task',
            'action': 'stage_progression',
            'message_count': i + 1,
            'user_message': f'User message for {stage}',
            'bot_response': f'Bot response for {stage}'
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stage {stage}: {result.get('message', 'Success')}")
        else:
            print(f"❌ Failed stage {stage}: {response.status_code} - {response.text}")
    
    # Test 3: Check dashboard data
    print("\n3️⃣ Testing dashboard data...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Dashboard accessible")
        else:
            print(f"❌ Dashboard error: {response.status_code}")
    except Exception as e:
        print(f"❌ Dashboard connection error: {e}")
    
    # Test 4: Check analytics
    print("\n4️⃣ Testing analytics...")
    try:
        response = requests.get(f"{BASE_URL}/analytics")
        if response.status_code == 200:
            print("✅ Analytics accessible")
        else:
            print(f"❌ Analytics error: {response.status_code}")
    except Exception as e:
        print(f"❌ Analytics connection error: {e}")
    
    print(f"\n✅ Test completed for session: {session_id}")
    return True

def check_database_data():
    """Check if data is being stored in the database"""
    print("\n🔍 Checking Database Data")
    print("=" * 30)
    
    import sqlite3
    
    try:
        conn = sqlite3.connect('sales_system.db')
        cursor = conn.cursor()
        
        # Check chatbot_sessions
        cursor.execute("SELECT COUNT(*) FROM chatbot_sessions")
        session_count = cursor.fetchone()[0]
        print(f"📊 Chatbot sessions: {session_count}")
        
        # Check contacts
        cursor.execute("SELECT COUNT(*) FROM contacts")
        contact_count = cursor.fetchone()[0]
        print(f"📊 Contacts: {contact_count}")
        
        # Check activities
        cursor.execute("SELECT COUNT(*) FROM activities")
        activity_count = cursor.fetchone()[0]
        print(f"📊 Activities: {activity_count}")
        
        # Check stage progressions
        cursor.execute("SELECT COUNT(*) FROM stage_progressions")
        progression_count = cursor.fetchone()[0]
        print(f"📊 Stage progressions: {progression_count}")
        
        # Show recent sessions
        cursor.execute("""
            SELECT session_id, current_stage, total_messages, engagement_level, started_at 
            FROM chatbot_sessions 
            ORDER BY started_at DESC 
            LIMIT 5
        """)
        recent_sessions = cursor.fetchall()
        
        print(f"\n📋 Recent Sessions:")
        for session in recent_sessions:
            print(f"  - {session[0][:8]}... | Stage: {session[1]} | Messages: {session[2]} | Engagement: {session[3]} | Started: {session[4]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Chat Tracking Test Suite")
    print("=" * 50)
    
    # Wait a moment for the server to be ready
    import time
    print("⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    # Run tests
    tracking_success = test_session_tracking()
    
    if tracking_success:
        # Check database
        time.sleep(2)  # Give time for data to be written
        check_database_data()
        
        print("\n✅ All tests completed!")
        print("Check the dashboard at http://localhost:5000 to see the results.")
    else:
        print("\n❌ Tests failed!")
