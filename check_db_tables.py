#!/usr/bin/env python3
"""
Check Database Tables
====================
Check what tables exist in each database.
"""

import sqlite3
import os

def check_database_tables():
    """Check tables in all database files"""
    try:
        db_files = ["unified_sales.db", "sales_system.db"]
        
        for db_file in db_files:
            if not os.path.exists(db_file):
                print(f"❌ {db_file} not found")
                continue
                
            print(f"\n📊 DATABASE: {db_file}")
            print("=" * 50)
            
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"📋 Tables found: {len(tables)}")
            for table in sorted(tables):
                print(f"   - {table}")
            
            # Check for campaigns and groups specifically
            if 'email_campaigns' in tables:
                print(f"\n📧 EMAIL CAMPAIGNS:")
                cursor.execute("SELECT id, name, recipient_criteria FROM email_campaigns")
                campaigns = cursor.fetchall()
                for campaign in campaigns:
                    print(f"   ID: {campaign[0]}, Name: {campaign[1]}")
                    print(f"   Criteria: {campaign[2]}")
                    print("   " + "-" * 30)
            
            if 'contact_groups' in tables:
                print(f"\n👥 CONTACT GROUPS:")
                cursor.execute("SELECT id, name, description FROM contact_groups")
                groups = cursor.fetchall()
                for group in groups:
                    print(f"   ID: {group[0]}, Name: {group[1]}")
                    print(f"   Description: {group[2]}")
                    
                    # Count members
                    if 'contact_group_memberships' in tables:
                        cursor.execute("SELECT COUNT(*) FROM contact_group_memberships WHERE group_id = ?", (group[0],))
                        member_count = cursor.fetchone()[0]
                        print(f"   Members: {member_count}")
                    print("   " + "-" * 20)
            
            if 'contacts' in tables:
                print(f"\n📧 CONTACTS:")
                cursor.execute("SELECT COUNT(*) FROM contacts WHERE is_active = 1")
                active_count = cursor.fetchone()[0]
                print(f"   Total active contacts: {active_count}")
                
                cursor.execute("SELECT id, first_name, last_name, email FROM contacts WHERE is_active = 1 LIMIT 5")
                contacts = cursor.fetchall()
                print("   Sample contacts:")
                for contact in contacts:
                    print(f"     - {contact[1]} {contact[2]} ({contact[3]})")
            
            conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking databases: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_database_tables()
