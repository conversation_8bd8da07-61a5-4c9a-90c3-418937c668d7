#!/usr/bin/env python3
"""
Create and Test Campaign via Web Interface
==========================================
Creates a campaign using the web interface and tests email sending.
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:5000"

def check_server_status():
    """Check if the server is running"""
    try:
        response = requests.get(BASE_URL, timeout=5)
        return response.status_code == 200
    except:
        return False

def get_contact_count():
    """Get the number of available contacts"""
    try:
        response = requests.get(f"{BASE_URL}/api/contacts/count?active=true&do_not_email=false")
        if response.status_code == 200:
            return response.json()['count']
        return 0
    except:
        return 0

def create_test_contact():
    """Create a test contact for campaign testing"""
    print("📝 Creating test contact...")
    
    contact_data = {
        'first_name': 'Test',
        'last_name': 'Contact',
        'email': '<EMAIL>',  # Send to ourselves for testing
        'company': '24Seven Assistants',
        'job_title': 'Test Contact',
        'source': 'test_campaign',
        'status': 'new'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/contacts/add", data=contact_data)
        if response.status_code in [200, 302]:  # 302 is redirect after success
            print("✅ Test contact created successfully")
            return True
        else:
            print(f"⚠️ Contact creation response: {response.status_code}")
            return True  # Might already exist
    except Exception as e:
        print(f"❌ Failed to create test contact: {e}")
        return False

def create_test_campaign():
    """Create a test email campaign"""
    print("📧 Creating test campaign...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    campaign_data = {
        'name': f'Test Campaign {timestamp}',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '50',
        'send_schedule': 'immediate'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/campaigns/create", data=campaign_data)
        if response.status_code in [200, 302]:
            print("✅ Test campaign created successfully")
            return True
        else:
            print(f"❌ Failed to create campaign: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ Error creating campaign: {e}")
        return False

def get_campaigns():
    """Get list of campaigns"""
    try:
        response = requests.get(f"{BASE_URL}/campaigns")
        if response.status_code == 200:
            # Parse HTML to find campaign IDs (simple approach)
            content = response.text
            campaign_ids = []
            
            # Look for campaign links in the HTML
            import re
            pattern = r'/campaigns/(\d+)/'
            matches = re.findall(pattern, content)
            campaign_ids = list(set(matches))  # Remove duplicates
            
            return campaign_ids
        return []
    except:
        return []

def send_campaign(campaign_id):
    """Send a specific campaign"""
    print(f"📤 Sending campaign {campaign_id}...")
    
    try:
        response = requests.post(f"{BASE_URL}/campaigns/{campaign_id}/send")
        if response.status_code in [200, 302]:
            print(f"✅ Campaign {campaign_id} sent successfully")
            return True
        else:
            print(f"❌ Failed to send campaign {campaign_id}: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error sending campaign {campaign_id}: {e}")
        return False

def check_campaign_status(campaign_id):
    """Check the status of a campaign"""
    try:
        response = requests.get(f"{BASE_URL}/campaigns/{campaign_id}")
        if response.status_code == 200:
            content = response.text
            # Simple status checking by looking for keywords in HTML
            if "completed" in content.lower():
                return "completed"
            elif "running" in content.lower():
                return "running"
            elif "draft" in content.lower():
                return "draft"
            else:
                return "unknown"
        return "error"
    except:
        return "error"

def monitor_email_logs():
    """Monitor email logs to see if emails are being sent"""
    print("📊 Checking email logs...")
    
    try:
        response = requests.get(f"{BASE_URL}/analytics/email-logs")
        if response.status_code == 200:
            content = response.text
            # Count email entries in the logs
            email_count = content.count('@')  # Simple way to count emails
            print(f"📧 Found {email_count} email entries in logs")
            return email_count > 0
        return False
    except:
        return False

def main():
    """Main function to create and test campaign"""
    print("🚀 24Seven Assistants - Campaign Creation and Testing")
    print("=" * 60)
    
    # Check if server is running
    if not check_server_status():
        print("❌ Server is not running. Please start the sales system first:")
        print("   python unified_sales_system.py")
        return
    
    print("✅ Server is running")
    
    # Check contact count
    contact_count = get_contact_count()
    print(f"📊 Available contacts: {contact_count}")
    
    if contact_count == 0:
        print("⚠️ No contacts found. Creating test contact...")
        if not create_test_contact():
            print("❌ Failed to create test contact. Exiting.")
            return
        
        # Recheck contact count
        time.sleep(1)
        contact_count = get_contact_count()
        print(f"📊 Updated contact count: {contact_count}")
    
    # Create test campaign
    if not create_test_campaign():
        print("❌ Failed to create campaign. Exiting.")
        return
    
    # Wait a moment for campaign to be created
    time.sleep(2)
    
    # Get campaigns
    campaign_ids = get_campaigns()
    print(f"📋 Found {len(campaign_ids)} campaigns")
    
    if not campaign_ids:
        print("❌ No campaigns found after creation. Check the web interface.")
        return
    
    # Send the most recent campaign (last in list)
    latest_campaign_id = campaign_ids[-1]
    print(f"🎯 Testing latest campaign: {latest_campaign_id}")
    
    # Check initial status
    initial_status = check_campaign_status(latest_campaign_id)
    print(f"📊 Campaign status: {initial_status}")
    
    # Send the campaign
    if send_campaign(latest_campaign_id):
        print("✅ Campaign sending initiated")
        
        # Wait and check status again
        time.sleep(3)
        final_status = check_campaign_status(latest_campaign_id)
        print(f"📊 Campaign status after sending: {final_status}")
        
        # Check email logs
        if monitor_email_logs():
            print("✅ Email logs show activity - emails are being processed")
        else:
            print("⚠️ No email activity detected in logs")
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 CAMPAIGN TEST SUMMARY")
    print("=" * 60)
    print(f"Server Status: ✅ Running")
    print(f"Contacts Available: {contact_count}")
    print(f"Campaign Created: ✅ Yes")
    print(f"Campaign Sent: ✅ Yes")
    print(f"Final Status: {final_status}")
    
    print("\n💡 Next Steps:")
    print("1. Check your email inbox (<EMAIL>) for test messages")
    print("2. Visit the web interface to see campaign details:")
    print(f"   {BASE_URL}/campaigns/{latest_campaign_id}")
    print("3. Check the analytics dashboard:")
    print(f"   {BASE_URL}/analytics")

if __name__ == "__main__":
    main()
