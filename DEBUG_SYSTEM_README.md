# 24Seven Assistants - Enhanced Debug System

## Overview

The enhanced debug system provides comprehensive debugging, monitoring, and diagnostic capabilities for the 24Seven Assistants Sales Department system. It includes real-time logging, performance monitoring, error tracking, and system health diagnostics.

## Features

### 🔧 Debug Dashboard
- **Main Dashboard**: Overview of all debug metrics and system status
- **Real-time Monitoring**: Live updates of system performance and errors
- **Quick Actions**: Clear logs, download logs, refresh stats
- **System Information**: Configuration details and health status

### 📡 Request Monitoring
- **Request Logging**: Track all HTTP requests with timing and status
- **Slow Request Detection**: Automatically flag requests exceeding thresholds
- **Request Analytics**: Success rates, error rates, and performance metrics
- **Detailed Request Info**: Headers, IP addresses, user agents

### 🚨 Error Tracking
- **Exception Handling**: Capture and log all unhandled exceptions
- **Error Details**: Full stack traces, request context, and timestamps
- **Error Classification**: Categorize errors by type and severity
- **Error Analytics**: Track error frequency and patterns

### ⚡ Performance Monitoring
- **Function Timing**: Monitor execution time of critical functions
- **Slow Function Detection**: Identify performance bottlenecks
- **Performance Statistics**: Average, maximum, and total execution times
- **Performance Trends**: Track performance over time

### 🗄️ Database Debugging
- **Database Health**: Connection status and configuration
- **Table Statistics**: Record counts and table information
- **Query Performance**: Monitor slow database queries
- **Database Tools**: SQL query interface for debugging

### 📧 Email Testing
- **SMTP Testing**: Test email server configuration
- **Email Preview**: Preview email content before sending
- **Send Test Emails**: Send test emails to verify delivery
- **Email Statistics**: Track email sending performance

## Installation & Setup

### 1. Install Dependencies

The debug system is already integrated into the main application. No additional dependencies are required.

### 2. Configuration

Configure debug settings using environment variables:

```bash
# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=sales_department.log

# Debug Features
DEBUG_REQUESTS=true
DEBUG_SQL=true
DEBUG_PERFORMANCE=true
DEBUG_ERRORS=true

# Performance Thresholds
SLOW_REQUEST_THRESHOLD=2.0
SLOW_QUERY_THRESHOLD=1.0
```

### 3. Enable Debug System

The debug system is automatically initialized when the application starts. No manual setup required.

## Usage

### Accessing the Debug Dashboard

1. Start the sales department application
2. Navigate to: `http://localhost:5000/debug/`
3. Explore the various debug sections

### Debug Dashboard Sections

#### Main Dashboard (`/debug/`)
- System overview and metrics
- Recent errors and requests
- Quick access to all debug tools

#### Request Logs (`/debug/requests`)
- View all HTTP requests
- Filter by status code, method, or timing
- Pagination for large datasets

#### Error Logs (`/debug/errors`)
- View all application errors
- Full stack traces and context
- Error classification and timing

#### Performance Monitor (`/debug/performance`)
- Function execution statistics
- Slow function detection
- Performance optimization tips

#### Database Debug (`/debug/database`)
- Database health and statistics
- Table information and record counts
- SQL query testing interface

#### Email Testing (`/debug/email-test`)
- Test SMTP configuration
- Send test emails
- Preview email content

### API Endpoints

The debug system provides several API endpoints:

```bash
# Get debug statistics
GET /debug/api/stats

# Clear debug logs
POST /debug/api/clear-logs
Content-Type: application/json
{"type": "all|requests|errors|performance"}

# Test email sending
POST /debug/api/test-email
Content-Type: application/json
{"email": "<EMAIL>"}
```

## Testing the Debug System

Use the provided test script to verify the debug system:

```bash
# Run basic debug system tests
python test_debug_system.py

# Generate test errors for debugging
python test_debug_system.py --errors
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |
| `LOG_FILE` | `sales_department.log` | Log file path |
| `DEBUG_REQUESTS` | `true` | Enable request logging |
| `DEBUG_SQL` | `true` | Enable SQL query logging |
| `DEBUG_PERFORMANCE` | `true` | Enable performance monitoring |
| `DEBUG_ERRORS` | `true` | Enable error tracking |
| `SLOW_REQUEST_THRESHOLD` | `2.0` | Slow request threshold (seconds) |
| `SLOW_QUERY_THRESHOLD` | `1.0` | Slow query threshold (seconds) |

### Debug Configuration Class

```python
class DebugConfig:
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'sales_department.log'
    ENABLE_REQUEST_LOGGING = True
    ENABLE_SQL_LOGGING = True
    ENABLE_PERFORMANCE_MONITORING = True
    ENABLE_ERROR_TRACKING = True
    SLOW_REQUEST_THRESHOLD = 2.0
    SLOW_QUERY_THRESHOLD = 1.0
```

## Performance Monitoring

### Function Monitoring

Add performance monitoring to any function:

```python
from debug_system import performance_monitor

@performance_monitor.monitor_function("my_function")
def my_function():
    # Function code here
    pass
```

### Automatic Monitoring

The following functions are automatically monitored:
- `get_unified_analytics()` - Analytics calculations
- Database queries (when SQL logging is enabled)
- Email sending functions
- Campaign processing functions

## Log Management

### Log Files

- **Main Log**: `sales_department.log`
- **Rotation**: Automatic rotation at 10MB
- **Retention**: 5 backup files
- **Format**: Timestamp, logger name, level, message

### Log Levels

- **DEBUG**: Detailed debugging information
- **INFO**: General information messages
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures

### Clearing Logs

Use the debug dashboard or API to clear logs:

```bash
# Clear all logs
curl -X POST http://localhost:5000/debug/api/clear-logs \
  -H "Content-Type: application/json" \
  -d '{"type": "all"}'

# Clear specific log type
curl -X POST http://localhost:5000/debug/api/clear-logs \
  -H "Content-Type: application/json" \
  -d '{"type": "requests"}'
```

## Troubleshooting

### Common Issues

1. **Debug Dashboard Not Accessible**
   - Check if the application is running
   - Verify the URL: `http://localhost:5000/debug/`
   - Check for any startup errors

2. **No Logs Appearing**
   - Verify debug features are enabled in configuration
   - Check log file permissions
   - Ensure LOG_LEVEL is set appropriately

3. **Performance Monitoring Not Working**
   - Verify `ENABLE_PERFORMANCE_MONITORING=true`
   - Check if functions have monitoring decorators
   - Verify threshold settings

4. **Email Testing Fails**
   - Check SMTP configuration
   - Verify network connectivity
   - Check firewall settings

### Debug Information

To get debug information about the system:

1. Visit `/debug/database` for database health
2. Check `/debug/api/stats` for current statistics
3. Review log files for detailed information
4. Use the email test page to verify SMTP

## Security Considerations

### Production Usage

- **Disable in Production**: The debug system should be disabled in production
- **Access Control**: Implement authentication for debug endpoints
- **Log Sanitization**: Ensure sensitive data is not logged
- **Network Security**: Restrict access to debug endpoints

### Configuration for Production

```python
class ProductionConfig:
    DEBUG = False
    ENABLE_REQUEST_LOGGING = False
    ENABLE_SQL_LOGGING = False
    ENABLE_PERFORMANCE_MONITORING = True  # Keep for monitoring
    ENABLE_ERROR_TRACKING = True  # Keep for error reporting
```

## Integration with Sales System

The debug system is fully integrated with the sales department system:

- **Email Campaigns**: Monitor email sending performance
- **Chatbot Integration**: Track chatbot session performance
- **Analytics**: Monitor analytics calculation performance
- **Database Operations**: Track database query performance
- **User Interactions**: Log all user requests and responses

## Support

For issues or questions about the debug system:

1. Check the debug dashboard for system status
2. Review log files for error details
3. Use the test script to verify functionality
4. Check configuration settings

The debug system provides comprehensive monitoring and debugging capabilities to ensure the 24Seven Assistants Sales Department system runs smoothly and efficiently.
