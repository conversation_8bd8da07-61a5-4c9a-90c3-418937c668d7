#!/usr/bin/env python3
"""
Fix Campaign Recipients
=======================
Check and fix campaign recipient criteria to ensure it only sends to selected groups.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_campaign_recipients():
    """Fix campaign recipient criteria"""
    try:
        from unified_sales_system import app, db, EmailCampaign, ContactGroup, ContactGroupMembership, Contact
        import json
        
        with app.app_context():
            campaigns = EmailCampaign.query.all()
            
            print("🔧 FIXING CAMPAIGN RECIPIENT CRITERIA")
            print("=" * 50)
            
            for campaign in campaigns:
                print(f"\n📋 Campaign: {campaign.name} (ID: {campaign.id})")
                print(f"   Status: {campaign.status}")
                print(f"   Current Recipient Criteria: {campaign.recipient_criteria}")
                
                # Check if recipient criteria is missing or invalid
                if not campaign.recipient_criteria:
                    print("   ⚠️ No recipient criteria found!")
                    
                    # Ask user which group to assign
                    print("\n📊 Available Contact Groups:")
                    groups = ContactGroup.query.filter_by(is_active=True).all()
                    
                    for i, group in enumerate(groups, 1):
                        member_count = ContactGroupMembership.query.filter_by(group_id=group.id).count()
                        print(f"   {i}. {group.name} (ID: {group.id}) - {member_count} members")
                    
                    print(f"   {len(groups) + 1}. All contacts")
                    print(f"   {len(groups) + 2}. Skip this campaign")
                    
                    try:
                        choice = input(f"\nSelect recipient type for '{campaign.name}' (1-{len(groups) + 2}): ").strip()
                        choice_num = int(choice)
                        
                        if choice_num == len(groups) + 2:
                            print("   ⏭️ Skipping this campaign")
                            continue
                        elif choice_num == len(groups) + 1:
                            # Set to all contacts
                            recipient_criteria = {'type': 'all'}
                            print("   ✅ Set to send to ALL contacts")
                        elif 1 <= choice_num <= len(groups):
                            # Set to specific group
                            selected_group = groups[choice_num - 1]
                            recipient_criteria = {
                                'type': 'groups',
                                'group_ids': [selected_group.id]
                            }
                            print(f"   ✅ Set to send to group: {selected_group.name}")
                        else:
                            print("   ❌ Invalid choice, skipping")
                            continue
                        
                        # Update campaign
                        campaign.recipient_criteria = json.dumps(recipient_criteria)
                        db.session.commit()
                        
                        print(f"   💾 Updated recipient criteria: {campaign.recipient_criteria}")
                        
                    except (ValueError, KeyboardInterrupt):
                        print("   ⏭️ Skipping this campaign")
                        continue
                
                else:
                    # Parse existing criteria
                    try:
                        criteria = json.loads(campaign.recipient_criteria)
                        recipient_type = criteria.get('type', 'unknown')
                        
                        print(f"   ✅ Valid recipient criteria found")
                        print(f"   📊 Type: {recipient_type}")
                        
                        if recipient_type == 'groups':
                            group_ids = criteria.get('group_ids', [])
                            if group_ids:
                                groups = ContactGroup.query.filter(ContactGroup.id.in_(group_ids)).all()
                                group_names = [g.name for g in groups]
                                print(f"   👥 Groups: {group_names}")
                                
                                # Count contacts in these groups
                                contact_ids = db.session.query(ContactGroupMembership.contact_id).filter(
                                    ContactGroupMembership.group_id.in_(group_ids)
                                ).distinct().all()
                                contact_count = len(contact_ids)
                                print(f"   📧 Will send to {contact_count} contacts")
                            else:
                                print("   ⚠️ No group IDs specified in criteria!")
                        elif recipient_type == 'all':
                            all_contacts = Contact.query.filter_by(is_active=True, do_not_email=False).all()
                            print(f"   📧 Will send to {len(all_contacts)} contacts (ALL)")
                        elif recipient_type == 'specific':
                            contact_ids = criteria.get('contact_ids', [])
                            print(f"   📧 Will send to {len(contact_ids)} specific contacts")
                        
                    except json.JSONDecodeError:
                        print("   ❌ Invalid JSON in recipient criteria!")
            
            print("\n🎉 Campaign recipient criteria check complete!")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing campaign recipients: {e}")
        return False

def stop_current_campaign():
    """Stop any currently sending campaigns"""
    try:
        from unified_sales_system import app, db, EmailCampaign
        
        with app.app_context():
            sending_campaigns = EmailCampaign.query.filter_by(status='sending').all()
            
            if sending_campaigns:
                print(f"\n⏸️ Found {len(sending_campaigns)} campaigns currently sending")
                
                for campaign in sending_campaigns:
                    print(f"   📋 Stopping: {campaign.name}")
                    campaign.status = 'paused'
                    db.session.commit()
                    print(f"   ✅ Campaign paused")
                
                print("\n✅ All sending campaigns have been paused")
                print("💡 You can now fix the recipient criteria and resume sending")
                return True
            else:
                print("\n✅ No campaigns are currently sending")
                return True
                
    except Exception as e:
        print(f"❌ Error stopping campaigns: {e}")
        return False

def main():
    """Main function"""
    print("🔧 24Seven Assistants - Campaign Recipients Fix Tool")
    print("=" * 60)
    
    print("\n1. Stop any currently sending campaigns")
    print("2. Check and fix recipient criteria")
    print("3. Exit")
    
    try:
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == '1':
            stop_current_campaign()
        elif choice == '2':
            fix_campaign_recipients()
        elif choice == '3':
            print("👋 Goodbye!")
            return
        else:
            print("❌ Invalid choice")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
