{% extends "base.html" %}

{% block title %}Assign Groups to {{ campaign.name }} - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-layer-group"></i> Assign Groups to Campaign</h2>
                    <p class="text-muted">
                        Campaign: <strong>{{ campaign.name }}</strong>
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Campaign
                    </a>
                </div>
            </div>

            {% if available_groups %}
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users"></i> Available Groups ({{ available_groups|length }})</h5>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNone()">
                                <i class="fas fa-square"></i> Select None
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search Filter -->
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="groupSearch" placeholder="Search groups by name or description..." onkeyup="filterGroups()">
                        </div>
                    </div>

                    <form method="POST">
                        <div class="row" id="groupsContainer">
                            {% for group in available_groups %}
                            <div class="col-md-6 col-lg-4 mb-3 group-card">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center" style="background-color: {{ group.color }}; color: white;">
                                        <div class="form-check">
                                            <input class="form-check-input group-checkbox" type="checkbox" name="group_ids" value="{{ group.id }}" id="group_{{ group.id }}" onchange="updateSelectedCount()">
                                            <label class="form-check-label text-white" for="group_{{ group.id }}">
                                                <strong>{{ group.name }}</strong>
                                            </label>
                                        </div>
                                        <span class="badge bg-light text-dark">{{ group.contact_count }}</span>
                                    </div>
                                    <div class="card-body group-info">
                                        {% if group.description %}
                                        <p class="card-text">{{ group.description }}</p>
                                        {% else %}
                                        <p class="card-text text-muted">No description provided.</p>
                                        {% endif %}
                                        
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar"></i> Created: {{ group.created_at.strftime('%Y-%m-%d') }}
                                            {% if group.created_by %}
                                            <br><i class="fas fa-user"></i> By: {{ group.created_by }}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">{{ group.contact_count }} contact(s)</small>
                                            <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <span id="selectedCount">0</span> group(s) selected
                                <span id="totalContacts" class="text-muted ms-2">(0 total contacts)</span>
                            </div>
                            <div>
                                <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary" id="assignGroupsBtn" disabled>
                                    <i class="fas fa-layer-group"></i> Assign Selected Groups
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Available Groups</h4>
                <p class="text-muted">All groups are already assigned to this campaign, or you don't have any groups yet.</p>
                <div>
                    <a href="{{ url_for('create_group') }}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> Create New Group
                    </a>
                    <a href="{{ url_for('groups_list') }}" class="btn btn-success me-2">
                        <i class="fas fa-layer-group"></i> View All Groups
                    </a>
                    <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Campaign
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Campaign Info Card -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="fas fa-info-circle"></i> Campaign Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>Campaign Name:</strong><br>
                <span class="text-muted">{{ campaign.name }}</span>
            </div>
            <div class="col-md-3">
                <strong>Subject Line:</strong><br>
                <span class="text-muted">{{ campaign.subject }}</span>
            </div>
            <div class="col-md-3">
                <strong>Status:</strong><br>
                <span class="badge bg-{{ 'success' if campaign.status == 'completed' else 'primary' if campaign.status == 'running' else 'secondary' }}">
                    {{ campaign.status|title }}
                </span>
            </div>
            <div class="col-md-3">
                <strong>Current Recipients:</strong><br>
                <span class="text-muted">{{ campaign.total_recipients or 0 }}</span>
            </div>
        </div>
    </div>
</div>

<script>
let selectedGroups = new Set();
let groupContactCounts = {};

// Initialize group contact counts
{% for group in available_groups %}
groupContactCounts[{{ group.id }}] = {{ group.contact_count }};
{% endfor %}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.group-checkbox:checked');
    selectedGroups.clear();
    let totalContacts = 0;
    
    checkboxes.forEach(checkbox => {
        const groupId = parseInt(checkbox.value);
        selectedGroups.add(groupId);
        totalContacts += groupContactCounts[groupId] || 0;
    });
    
    const count = selectedGroups.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('totalContacts').textContent = `(${totalContacts} total contacts)`;
    document.getElementById('assignGroupsBtn').disabled = count === 0;
}

function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.group-card:not([style*="display: none"]) .group-checkbox');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.group-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function filterGroups() {
    const searchTerm = document.getElementById('groupSearch').value.toLowerCase();
    const groupCards = document.querySelectorAll('.group-card');
    
    groupCards.forEach(card => {
        const groupInfo = card.querySelector('.group-info').textContent.toLowerCase();
        const groupName = card.querySelector('.form-check-label').textContent.toLowerCase();
        
        if (groupName.includes(searchTerm) || groupInfo.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to group checkboxes
    const checkboxes = document.querySelectorAll('.group-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // Initialize counts
    updateSelectedCount();
    
    // Add debounce to search input
    let searchTimeout;
    document.getElementById('groupSearch').addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(filterGroups, 300);
    });
});
</script>
{% endblock %}
