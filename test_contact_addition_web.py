#!/usr/bin/env python3
"""
Test Contact Addition via Web Interface
=======================================
Test adding a contact through the web form
"""

import requests
import time

def test_contact_addition():
    """Test adding a contact via web form"""
    print("🧪 Testing Contact Addition via Web Interface")
    print("=" * 60)
    
    # Test data for <PERSON> Scof
    contact_data = {
        'first_name': 'Alex',
        'last_name': 'Scof',
        'email': '<EMAIL>',
        'phone': '(*************',
        'company': 'Test Company',
        'job_title': 'CEO',
        'source': 'manual_entry',
        'industry': 'technology',
        'website': 'https://testcompany.com',
        'linkedin_url': 'https://linkedin.com/in/alexscof',
        'notes': 'Test contact for email campaign system',
        'estimated_budget': '10000',
        'company_size': '11-50',
        'decision_maker': 'on',  # Checkbox value
        'do_not_email': ''  # Unchecked checkbox
    }
    
    try:
        print("📝 Submitting contact form...")
        print(f"   Name: {contact_data['first_name']} {contact_data['last_name']}")
        print(f"   Email: {contact_data['email']}")
        print(f"   Company: {contact_data['company']}")
        
        # Submit the form
        response = requests.post(
            'http://localhost:5000/contacts/add',
            data=contact_data,
            timeout=30,
            allow_redirects=False  # Don't follow redirects to see the response
        )
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 302:
            # Redirect usually means success
            redirect_location = response.headers.get('Location', '')
            print(f"✅ Form submitted successfully!")
            print(f"   Redirected to: {redirect_location}")
            
            if 'contacts' in redirect_location:
                print("✅ Redirected to contacts list - contact likely added")
                return True
            else:
                print("⚠️ Unexpected redirect location")
                return False
                
        elif response.status_code == 200:
            # Form returned - check for error messages
            response_text = response.text.lower()
            
            if 'error' in response_text or 'failed' in response_text:
                print("❌ Form returned with errors")
                
                # Try to extract error messages
                if 'already exists' in response_text:
                    print("   Error: Email already exists")
                elif 'required' in response_text:
                    print("   Error: Required field missing")
                else:
                    print("   Error: Unknown validation error")
                
                return False
            else:
                print("⚠️ Form returned but no clear error indication")
                return False
                
        else:
            print(f"❌ Unexpected response status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the application")
        print("   Make sure the application is running on http://localhost:5000")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def check_contact_exists():
    """Check if the contact was actually created"""
    try:
        print("\n🔍 Checking if contact was created...")
        
        # Try to access the contacts list
        response = requests.get('http://localhost:5000/contacts', timeout=10)
        
        if response.status_code == 200:
            response_text = response.text.lower()
            
            if '<EMAIL>' in response_text:
                print("✅ Contact found in contacts list!")
                return True
            else:
                print("❌ Contact not found in contacts list")
                return False
        else:
            print(f"⚠️ Cannot access contacts list (status: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Error checking contact existence: {e}")
        return False

def test_duplicate_contact():
    """Test adding the same contact again to check duplicate handling"""
    print("\n🔄 Testing Duplicate Contact Handling...")
    print("-" * 40)
    
    # Same contact data
    contact_data = {
        'first_name': 'Alex',
        'last_name': 'Scof',
        'email': '<EMAIL>',
        'company': 'Test Company',
        'job_title': 'CEO'
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/contacts/add',
            data=contact_data,
            timeout=30,
            allow_redirects=False
        )
        
        if response.status_code == 200:
            response_text = response.text.lower()
            if 'already exists' in response_text or 'duplicate' in response_text:
                print("✅ Duplicate detection working correctly")
                return True
            else:
                print("⚠️ Duplicate not detected properly")
                return False
        else:
            print(f"⚠️ Unexpected response for duplicate test: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Duplicate test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CONTACT ADDITION WEB TEST")
    print("=" * 60)
    
    # Test 1: Add new contact
    addition_success = test_contact_addition()
    
    # Test 2: Check if contact exists
    if addition_success:
        contact_exists = check_contact_exists()
    else:
        contact_exists = False
    
    # Test 3: Test duplicate handling
    if contact_exists:
        duplicate_handling = test_duplicate_contact()
    else:
        duplicate_handling = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 WEB CONTACT ADDITION TEST SUMMARY")
    print("=" * 60)
    print(f"Contact Addition: {'✅ SUCCESS' if addition_success else '❌ FAILED'}")
    print(f"Contact Verification: {'✅ FOUND' if contact_exists else '❌ NOT FOUND'}")
    print(f"Duplicate Handling: {'✅ WORKING' if duplicate_handling else '❌ ISSUES'}")
    
    if all([addition_success, contact_exists]):
        print("\n🎉 CONTACT ADDITION IS WORKING!")
        print("\n📋 Contact Details:")
        print("   Name: Alex Scof")
        print("   Email: <EMAIL>")
        print("   Company: Test Company")
        print("   Job Title: CEO")
        print("\n✅ You can now proceed to create and send email campaigns!")
        print("\n🔗 Next Steps:")
        print("1. Go to: http://localhost:5000/campaigns")
        print("2. Create a new campaign")
        print("3. Select 'All contacts' as recipients")
        print("4. Send the campaign to test the email system")
    else:
        print("\n❌ CONTACT ADDITION ISSUES DETECTED")
        if not addition_success:
            print("• Form submission failed")
        if not contact_exists:
            print("• Contact was not saved to database")
        
        print("\n🔧 Troubleshooting Steps:")
        print("1. Check application logs for detailed errors")
        print("2. Verify database connection and schema")
        print("3. Check for validation errors in the form")
        print("4. Ensure all required fields are properly handled")
    
    return all([addition_success, contact_exists])

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
