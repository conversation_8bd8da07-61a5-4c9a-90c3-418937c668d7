"""
Simple Enhanced SMTP Service
============================
Simplified SMTP service with IMAP saving that doesn't depend on Flask models.
"""

import smtplib
import imaplib
import ssl
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart

def send_email_with_imap_save(to_email, subject, html_body, text_body=None, from_name="24Seven Assistants Sales Team"):
    """
    Send email via SMTP and save to IMAP sent folder
    
    Returns:
        Tuple of (success: bool, message_id: str, error_message: str)
    """
    
    # Configuration
    config = {
        'smtp_server': 'mail.24seven.site',
        'smtp_port': 465,
        'smtp_use_ssl': True,
        'username': '<EMAIL>',
        'password': 'M@kerere1',
        'imap_server': 'mail.24seven.site',
        'imap_port': 993,
        'imap_use_ssl': True,
        'sent_folder': 'INBOX.Sent'
    }
    
    try:
        # Create email message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = f"{from_name} <{config['username']}>"
        msg['To'] = to_email
        msg['Date'] = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S +0000')
        
        # Add text version if provided
        if text_body:
            text_part = MIMEText(text_body, 'plain', 'utf-8')
            msg.attach(text_part)
        
        # Add HTML version
        html_part = MIMEText(html_body, 'html', 'utf-8')
        msg.attach(html_part)
        
        # Send email via SMTP
        if config['smtp_use_ssl']:
            context = ssl.create_default_context()
            smtp_server = smtplib.SMTP_SSL(config['smtp_server'], config['smtp_port'], context=context)
        else:
            smtp_server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            smtp_server.starttls()
        
        smtp_server.login(config['username'], config['password'])
        smtp_server.send_message(msg)
        message_id = msg['Message-ID']
        smtp_server.quit()
        
        print(f"✅ SMTP: Email sent to {to_email}")
        
        # Save to IMAP sent folder
        try:
            if config['imap_use_ssl']:
                context = ssl.create_default_context()
                imap_server = imaplib.IMAP4_SSL(config['imap_server'], config['imap_port'], ssl_context=context)
            else:
                imap_server = imaplib.IMAP4(config['imap_server'], config['imap_port'])
                imap_server.starttls()
            
            imap_server.login(config['username'], config['password'])
            
            # Select sent folder
            status, response = imap_server.select(config['sent_folder'])
            if status == 'OK':
                # Save message to sent folder
                message_bytes = msg.as_bytes()
                imap_server.append(config['sent_folder'], '\\Seen', None, message_bytes)
                print(f"✅ IMAP: Email saved to {config['sent_folder']}")
            else:
                print(f"⚠️ IMAP: Could not access sent folder {config['sent_folder']}")
            
            imap_server.close()
            imap_server.logout()
            
        except Exception as e:
            print(f"⚠️ IMAP: Failed to save to sent folder: {str(e)}")
            # Don't fail the whole operation if IMAP save fails
        
        return True, message_id, ""
        
    except Exception as e:
        error_msg = f"Failed to send email to {to_email}: {str(e)}"
        print(f"❌ SMTP: {error_msg}")
        return False, "", error_msg

def test_simple_enhanced_smtp():
    """Test the simple enhanced SMTP function"""
    print("🧪 Testing Simple Enhanced SMTP...")
    print("-" * 40)
    
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    subject = f"Simple Enhanced SMTP Test - {timestamp}"
    
    html_body = f"""
    <html>
    <body>
        <h2>Simple Enhanced SMTP Test</h2>
        <p>This email was sent using the simple enhanced SMTP function that:</p>
        <ul>
            <li>✅ Sends emails via SMTP</li>
            <li>✅ Saves emails to IMAP sent folder</li>
            <li>✅ Works without Flask model dependencies</li>
        </ul>
        <p><strong>Test sent at:</strong> {timestamp}</p>
        <p>If you receive this email and it appears in the IMAP sent folder, the integration is working!</p>
    </body>
    </html>
    """
    
    text_body = f"""
Simple Enhanced SMTP Test

This email was sent using the simple enhanced SMTP function that:
✅ Sends emails via SMTP
✅ Saves emails to IMAP sent folder  
✅ Works without Flask model dependencies

Test sent at: {timestamp}

If you receive this email and it appears in the IMAP sent folder, the integration is working!
    """
    
    success, message_id, error_msg = send_email_with_imap_save(
        to_email='<EMAIL>',
        subject=subject,
        html_body=html_body,
        text_body=text_body
    )
    
    if success:
        print(f"✅ Test successful!")
        print(f"   Message ID: {message_id}")
        return True
    else:
        print(f"❌ Test failed: {error_msg}")
        return False

if __name__ == "__main__":
    print("🚀 Simple Enhanced SMTP Test")
    print("=" * 50)
    
    success = test_simple_enhanced_smtp()
    
    if success:
        print("\n🎉 Simple enhanced SMTP is working!")
        print("💡 This function can be used in the campaign system")
        print("📧 Check your email and IMAP sent folder")
    else:
        print("\n❌ Simple enhanced SMTP test failed")
        print("Check the error messages above")
