"""
Email System Configuration
==========================
Configuration management for the email campaign system.
"""

import os
from typing import Dict, Any


def get_email_config() -> Dict[str, Any]:
    """
    Get email system configuration from environment variables or defaults.

    Returns:
        Dictionary with email configuration settings
    """
    # Get configuration from environment variables or use defaults
    config = {
        # SMTP Configuration
        'MAIL_SERVER': os.getenv('MAIL_SERVER', 'smtp.gmail.com'),
        'MAIL_PORT': int(os.getenv('MAIL_PORT', '587')),
        'MAIL_USE_SSL': os.getenv('MAIL_USE_SSL', 'false').lower() == 'true',
        'MAIL_USE_TLS': os.getenv('MAIL_USE_TLS', 'true').lower() == 'true',
        'MAIL_USERNAME': os.getenv('MAIL_USERNAME', '<EMAIL>'),
        'MAIL_PASSWORD': os.getenv('MAIL_PASSWORD', 'wkff biod dzyv obon'),
        'MAIL_DEFAULT_SENDER': os.getenv('MAIL_DEFAULT_SENDER', '<EMAIL>'),

        # IMAP Configuration (for sent folder)
        'IMAP_SERVER': os.getenv('MAIL_IMAP_SERVER', os.getenv('IMAP_SERVER', 'imap.gmail.com')),
        'IMAP_PORT': int(os.getenv('MAIL_IMAP_PORT', os.getenv('IMAP_PORT', '993'))),
        'IMAP_USE_SSL': os.getenv('MAIL_IMAP_USE_SSL', os.getenv('IMAP_USE_SSL', 'true')).lower() == 'true',
        'IMAP_USERNAME': os.getenv('MAIL_IMAP_USERNAME', os.getenv('MAIL_USERNAME', '<EMAIL>')),
        'IMAP_PASSWORD': os.getenv('MAIL_IMAP_PASSWORD', os.getenv('MAIL_PASSWORD', '')),
        'IMAP_SENT_FOLDER': os.getenv('IMAP_SENT_FOLDER', '[Gmail]/Sent Mail'),

        # Campaign Settings
        'SAVE_TO_SENT_FOLDER': os.getenv('SAVE_TO_SENT_FOLDER', 'false').lower() == 'true',
        'EMAIL_BATCH_SIZE': int(os.getenv('EMAIL_BATCH_SIZE', '50')),
        'EMAIL_DELAY_SECONDS': int(os.getenv('EMAIL_DELAY_SECONDS', '2')),
        'MAX_EMAILS_PER_HOUR': int(os.getenv('MAX_EMAILS_PER_HOUR', '100')),
        'DAILY_SEND_LIMIT': int(os.getenv('DAILY_SEND_LIMIT', '500')),

        # Connection Settings
        'SMTP_TIMEOUT': int(os.getenv('SMTP_TIMEOUT', '30')),
        'SMTP_RETRY_COUNT': int(os.getenv('SMTP_RETRY_COUNT', '3')),
        'SMTP_RETRY_DELAY': int(os.getenv('SMTP_RETRY_DELAY', '5')),

        # Fallback Configurations
        'FALLBACK_CONFIGS': [
            {
                'name': 'Gmail STARTTLS (587)',
                'server': 'smtp.gmail.com',
                'port': 587,
                'use_ssl': False,
                'use_tls': True
            },
            {
                'name': 'Gmail SSL (465)',
                'server': 'smtp.gmail.com',
                'port': 465,
                'use_ssl': True,
                'use_tls': False
            },
            {
                'name': '24Seven SSL (465)',
                'server': 'mail.24seven.site',
                'port': 465,
                'use_ssl': True,
                'use_tls': False
            },
            {
                'name': '24Seven STARTTLS (587)',
                'server': 'mail.24seven.site',
                'port': 587,
                'use_ssl': False,
                'use_tls': True
            }
        ]
    }

    return config


def get_flask_email_config() -> Dict[str, Any]:
    """
    Get email configuration in Flask app config format.

    Returns:
        Dictionary with Flask-compatible email configuration
    """
    email_config = get_email_config()

    return {
        'MAIL_SERVER': email_config['MAIL_SERVER'],
        'MAIL_PORT': email_config['MAIL_PORT'],
        'MAIL_USE_SSL': email_config['MAIL_USE_SSL'],
        'MAIL_USE_TLS': email_config['MAIL_USE_TLS'],
        'MAIL_USERNAME': email_config['MAIL_USERNAME'],
        'MAIL_PASSWORD': email_config['MAIL_PASSWORD'],
        'MAIL_DEFAULT_SENDER': email_config['MAIL_DEFAULT_SENDER'],
    }


def validate_email_config(config: Dict[str, Any]) -> tuple[bool, str]:
    """
    Validate email configuration.

    Args:
        config: Email configuration dictionary

    Returns:
        Tuple of (is_valid: bool, error_message: str)
    """
    required_fields = [
        'MAIL_SERVER', 'MAIL_PORT', 'MAIL_USERNAME',
        'MAIL_PASSWORD', 'MAIL_DEFAULT_SENDER'
    ]

    for field in required_fields:
        if not config.get(field):
            return False, f"Missing required configuration: {field}"

    # Validate port numbers
    try:
        mail_port = int(config['MAIL_PORT'])
        if not (1 <= mail_port <= 65535):
            return False, f"Invalid MAIL_PORT: {mail_port}"
    except (ValueError, TypeError):
        return False, f"MAIL_PORT must be a valid integer"

    try:
        imap_port = int(config.get('IMAP_PORT', 993))
        if not (1 <= imap_port <= 65535):
            return False, f"Invalid IMAP_PORT: {imap_port}"
    except (ValueError, TypeError):
        return False, f"IMAP_PORT must be a valid integer"

    # Validate email addresses
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

    username = config.get('MAIL_USERNAME', '')
    if not re.match(email_pattern, username):
        return False, f"Invalid email format for MAIL_USERNAME: {username}"

    sender = config.get('MAIL_DEFAULT_SENDER', '')
    if not re.match(email_pattern, sender):
        return False, f"Invalid email format for MAIL_DEFAULT_SENDER: {sender}"

    return True, "Configuration is valid"


def print_email_config(config: Dict[str, Any] = None):
    """
    Print email configuration for debugging.

    Args:
        config: Optional configuration dictionary. If None, gets current config.
    """
    if config is None:
        config = get_email_config()

    print("📧 Email System Configuration:")
    print("=" * 40)
    print(f"SMTP Server: {config['MAIL_SERVER']}:{config['MAIL_PORT']}")
    print(f"SMTP SSL: {config['MAIL_USE_SSL']}")
    print(f"SMTP TLS: {config['MAIL_USE_TLS']}")
    print(f"Username: {config['MAIL_USERNAME']}")
    print(f"Default Sender: {config['MAIL_DEFAULT_SENDER']}")
    print(f"IMAP Server: {config['IMAP_SERVER']}:{config['IMAP_PORT']}")
    print(f"IMAP SSL: {config['IMAP_USE_SSL']}")
    print(f"Sent Folder: {config['IMAP_SENT_FOLDER']}")
    print(f"Save to Sent: {config['SAVE_TO_SENT_FOLDER']}")
    print(f"Batch Size: {config['EMAIL_BATCH_SIZE']}")
    print(f"Delay (seconds): {config['EMAIL_DELAY_SECONDS']}")
    print("=" * 40)


# Default configuration for 24Seven Assistants
DEFAULT_CONFIG = {
    'MAIL_SERVER': 'smtp.gmail.com',
    'MAIL_PORT': 587,
    'MAIL_USE_SSL': False,
    'MAIL_USE_TLS': True,
    'MAIL_USERNAME': '<EMAIL>',
    'MAIL_PASSWORD': 'wkff biod dzyv obon',  # App password for Gmail
    'MAIL_DEFAULT_SENDER': '<EMAIL>',
    'IMAP_SERVER': 'imap.gmail.com',
    'IMAP_PORT': 993,
    'IMAP_USE_SSL': True,
    'IMAP_SENT_FOLDER': '[Gmail]/Sent Mail',
    'SAVE_TO_SENT_FOLDER': False,  # Disable IMAP saving for now to avoid issues
    'EMAIL_BATCH_SIZE': 50,
    'EMAIL_DELAY_SECONDS': 2,
    'MAX_EMAILS_PER_HOUR': 100,
    'DAILY_SEND_LIMIT': 500,
}
