
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flexible Pricing Cards Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .size-controls {
            margin: 20px 0;
            text-align: center;
        }
        
        .size-controls button {
            margin: 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        .size-controls button:hover {
            background: #0056b3;
        }
        
        .container {
            transition: all 0.3s ease;
            margin: 0 auto;
            border: 2px dashed #ddd;
            padding: 20px;
        }
        
        /* Include the flexible pricing styles */
        .pricing-section {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .pricing-grid {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .pricing-card {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 10px;
            flex: 1;
            min-width: 180px;
            max-width: 220px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .pricing-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255,255,255,0.2);
        }
        
        .pricing-card h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .pricing-amount {
            font-size: 18px;
            font-weight: bold;
            margin: 6px 0;
        }
        
        .pricing-card p {
            margin: 4px 0;
            font-size: 14px;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .pricing-grid {
                flex-direction: column;
                gap: 12px;
                align-items: center;
            }
            
            .pricing-card {
                width: 100%;
                max-width: 100%;
                padding: 12px;
            }
        }
        
        @media (max-width: 480px) {
            .pricing-card {
                padding: 10px;
                border-radius: 8px;
            }
            
            .pricing-card h4 {
                font-size: 14px;
            }
            
            .pricing-amount {
                font-size: 16px;
            }
            
            .pricing-card p {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-section">
        <div class="demo-title">🎯 Flexible Pricing Cards Demo</div>
        <p>The pricing cards are now smaller, more flexible, and responsive. Try different container sizes:</p>
        
        <div class="size-controls">
            <button onclick="setSize('100%')">Full Width</button>
            <button onclick="setSize('800px')">Desktop (800px)</button>
            <button onclick="setSize('600px')">Tablet (600px)</button>
            <button onclick="setSize('400px')">Mobile (400px)</button>
            <button onclick="setSize('300px')">Small (300px)</button>
        </div>
        
        <div class="container" id="container">
            <div class="pricing-section">
                <h3 style="margin-top: 0; text-align: center; font-size: 24px;">💰 Our Pricing Plans</h3>
                
                <div class="pricing-grid">
                    <div class="pricing-card">
                        <h4>Small Business</h4>
                        <p class="pricing-amount">UGX 250K setup</p>
                        <p>UGX 100K/month</p>
                        <p style="opacity: 0.9;">Perfect for startups</p>
                    </div>
                    
                    <div class="pricing-card">
                        <h4>Medium Business</h4>
                        <p class="pricing-amount">UGX 500K setup</p>
                        <p>UGX 250K/month</p>
                        <p style="opacity: 0.9;">Ideal for growth</p>
                    </div>
                    
                    <div class="pricing-card">
                        <h4>Large Enterprise</h4>
                        <p class="pricing-amount">UGX 3M setup</p>
                        <p>UGX 1M/month</p>
                        <p style="opacity: 0.9;">Complete solution</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
            <h4 style="margin-top: 0; color: #2e7d32;">✨ Key Improvements:</h4>
            <ul style="margin-bottom: 0; color: #2e7d32;">
                <li><strong>Smaller Size:</strong> Reduced from 250-300px to 180-220px width</li>
                <li><strong>Compact Padding:</strong> Reduced from 20px to 15px</li>
                <li><strong>Better Typography:</strong> Smaller, more readable font sizes</li>
                <li><strong>Hover Effects:</strong> Smooth animations on hover</li>
                <li><strong>Flexible Layout:</strong> Better wrapping and spacing</li>
                <li><strong>Mobile Optimized:</strong> Responsive design for all screen sizes</li>
            </ul>
        </div>
    </div>
    
    <script>
        function setSize(width) {
            const container = document.getElementById('container');
            container.style.width = width;
            container.style.maxWidth = width;
        }
        
        // Set initial size
        setSize('800px');
    </script>
</body>
</html>
        