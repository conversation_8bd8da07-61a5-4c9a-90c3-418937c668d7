#!/usr/bin/env python3
"""
24Seven Assistants Sales System - Server Launcher
=================================================
Clean server startup with debugging options.
"""

import sys
import os
import argparse

def main():
    parser = argparse.ArgumentParser(description='24Seven Assistants Sales System')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode with verbose logging')
    parser.add_argument('--quiet', action='store_true', help='Minimal output mode')
    parser.add_argument('--port', type=int, default=5000, help='Port to run server on (default: 5000)')
    
    args = parser.parse_args()
    
    if args.quiet:
        print("🚀 Starting server...")
    else:
        print("🚀 24Seven Assistants Sales System")
        print("=" * 50)
    
    # Set environment variables for the main app
    if args.debug:
        os.environ['FLASK_DEBUG'] = '1'
        os.environ['SALES_SYSTEM_VERBOSE'] = '1'
        if not args.quiet:
            print("🔍 Debug mode enabled")
    else:
        os.environ['FLASK_DEBUG'] = '0'
        os.environ['SALES_SYSTEM_VERBOSE'] = '0'
    
    if args.quiet:
        os.environ['SALES_SYSTEM_QUIET'] = '1'
    
    os.environ['SALES_SYSTEM_PORT'] = str(args.port)
    
    if not args.quiet:
        print(f"🌐 Server will run at: http://localhost:{args.port}")
        print("⚡ Press Ctrl+C to stop")
        print("=" * 50)
    
    # Import and run the main application
    try:
        from unified_sales_system import app
        
        # Configure logging based on arguments
        import logging
        if args.debug:
            logging.getLogger('werkzeug').setLevel(logging.INFO)
            logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
            app.run(debug=True, host='0.0.0.0', port=args.port)
        else:
            logging.getLogger('werkzeug').setLevel(logging.ERROR)
            logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
            app.run(debug=False, host='0.0.0.0', port=args.port, use_reloader=False)
            
    except KeyboardInterrupt:
        if not args.quiet:
            print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
