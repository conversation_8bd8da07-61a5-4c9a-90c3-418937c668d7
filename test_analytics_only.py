#!/usr/bin/env python3
"""
Simple test to verify analytics function works correctly
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_analytics():
    """Test analytics function directly"""
    try:
        print("🧪 Testing Analytics Function")
        print("=" * 40)

        # Import the application
        import unified_sales_system
        from unified_sales_system import app, db, get_unified_analytics, get_sales_pipeline_analytics, ChatbotSession

        with app.app_context():
            # Set testing mode to prevent sample data creation in analytics
            app.testing = True

            print("📊 Initializing database...")

            # Initialize database
            db.drop_all()
            db.create_all()
            print("✅ Database tables created")

            # Create sample data
            print("📝 Creating sample data...")
            success = unified_sales_system.create_sample_data()
            if success:
                print("✅ Sample data created successfully")
            else:
                print("❌ Sample data creation failed")
                return False

            # Commit the transaction to ensure data is available
            db.session.commit()
            print("✅ Sample data committed to database")

            # Wait a moment for data to be fully available
            import time
            time.sleep(0.1)

            # Check ChatbotSession data directly
            print("\n🔍 Checking ChatbotSession data...")
            total_sessions = ChatbotSession.query.count()
            converted_sessions = ChatbotSession.query.filter(ChatbotSession.conversion_achieved == True).count()
            print(f"  • Total ChatbotSessions: {total_sessions}")
            print(f"  • Converted ChatbotSessions: {converted_sessions}")

            # Test unified analytics
            print("\n📈 Testing Unified Analytics...")
            print(f"  🔍 get_unified_analytics function: {get_unified_analytics}")
            print(f"  🔍 Function type: {type(get_unified_analytics)}")
            print(f"  🔍 Function callable: {callable(get_unified_analytics)}")
            try:
                print("  🔍 About to call get_unified_analytics()...")
                analytics = get_unified_analytics()
                print("  🔍 get_unified_analytics() returned successfully")
                print(f"  🔍 Analytics type: {type(analytics)}")
                print(f"  🔍 Analytics keys: {analytics.keys() if isinstance(analytics, dict) else 'Not a dict'}")
                print(f"  • Total contacts: {analytics['basic_metrics']['total_contacts']}")
                print(f"  • Total campaigns: {analytics['basic_metrics']['total_campaigns']}")
                print(f"  • Emails sent: {analytics['funnel_metrics']['emails_sent']}")
                print(f"  • Emails opened: {analytics['funnel_metrics']['emails_opened']}")
                print(f"  • Links clicked: {analytics['funnel_metrics']['links_clicked']}")
                print(f"  • Conversations: {analytics['funnel_metrics']['conversations_started']}")
                print(f"  • Conversions: {analytics['funnel_metrics']['conversions']}")
            except Exception as e:
                print(f"  ❌ Error in get_unified_analytics: {str(e)}")
                print(f"  ❌ Error type: {type(e)}")
                import traceback
                print("  ❌ Full traceback:")
                traceback.print_exc()
                analytics = {
                    'basic_metrics': {'total_contacts': 0, 'total_campaigns': 0, 'total_sessions': 0, 'active_sessions': 0},
                    'funnel_metrics': {'emails_sent': 0, 'emails_opened': 0, 'links_clicked': 0, 'conversations_started': 0, 'conversions': 0},
                    'conversion_rates': {},
                    'stage_counts': {},
                    'recent_activities': []
                }

            # Test sales pipeline analytics
            print("\n🔄 Testing Sales Pipeline Analytics...")
            pipeline = get_sales_pipeline_analytics()
            print(f"  • Sessions started: {pipeline['sessions_started']}")
            print(f"  • Opening stage: {pipeline['opening_entered']}")
            print(f"  • Trust stage: {pipeline['trust_entered']}")
            print(f"  • Discovery stage: {pipeline['discovery_entered']}")
            print(f"  • Demo stage: {pipeline['demonstration_entered']}")
            print(f"  • Close stage: {pipeline['close_entered']}")
            print(f"  • Conversions: {pipeline['conversions_completed']}")

            # Check if data is synchronized
            dashboard_conversations = analytics['funnel_metrics']['conversations_started']
            pipeline_conversations = pipeline['sessions_started']

            dashboard_conversions = analytics['funnel_metrics']['conversions']
            pipeline_conversions = pipeline['conversions_completed']

            print("\n🔍 Data Synchronization Check...")
            print(f"  • Dashboard conversations: {dashboard_conversations}")
            print(f"  • Pipeline conversations: {pipeline_conversations}")
            print(f"  • ChatbotSession count: {total_sessions}")
            print(f"  • Conversations match: {'✅' if dashboard_conversations == pipeline_conversations else '❌'}")

            print(f"  • Dashboard conversions: {dashboard_conversions}")
            print(f"  • Pipeline conversions: {pipeline_conversions}")
            print(f"  • ChatbotSession conversions: {converted_sessions}")
            print(f"  • Conversions match: {'✅' if dashboard_conversions == pipeline_conversions else '❌'}")

            # Check if data is synchronized
            if (dashboard_conversations == pipeline_conversations and
                dashboard_conversions == pipeline_conversions and
                dashboard_conversations == total_sessions and
                dashboard_conversions == converted_sessions):
                print("\n🎉 SUCCESS: Dashboard and Sales Pipeline are perfectly synchronized!")
                print("   All systems are using the same data sources.")
                return True
            else:
                print("\n⚠️  WARNING: Data synchronization issues detected!")
                print("   Dashboard and Sales Pipeline may be using different data sources.")
                return False

    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_analytics()
    if success:
        print("\n✅ All tests passed! The analytics are synchronized.")
    else:
        print("\n❌ Tests failed! Please check the error messages above.")

    sys.exit(0 if success else 1)
