#!/usr/bin/env python3
"""
Test Flask Database Connection
"""

import requests
import json

def test_flask_database():
    """Test Flask database through API calls"""
    print("🌐 Testing Flask Database Connection")
    print("=" * 40)
    
    # Test campaigns API
    print("📧 Testing campaigns...")
    try:
        response = requests.get("http://localhost:5000/campaigns")
        if response.status_code == 200:
            # Count campaigns in HTML
            import re
            campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', response.text)
            print(f"   ✅ Found {len(campaign_ids)} campaigns in web interface")
            print(f"   📋 Campaign IDs: {campaign_ids}")
            
            if campaign_ids:
                # Test view campaign with first ID
                first_id = campaign_ids[0]
                print(f"\n👁️ Testing view campaign {first_id}...")
                
                view_response = requests.get(f"http://localhost:5000/campaigns/{first_id}")
                print(f"   Status: {view_response.status_code}")
                
                if view_response.status_code == 200:
                    if "Campaign Details" in view_response.text:
                        print("   ✅ Campaign details page loaded")
                    elif "Error" in view_response.text:
                        print("   ❌ Error page displayed")
                        # Try to extract error message
                        error_match = re.search(r'<p>([^<]+)</p>', view_response.text)
                        if error_match:
                            print(f"   📝 Error: {error_match.group(1)}")
                    else:
                        print("   ⚠️ Unknown page content")
                        # Show first 200 characters
                        content_preview = view_response.text[:200].replace('\n', ' ')
                        print(f"   📄 Preview: {content_preview}...")
                elif view_response.status_code == 302:
                    print("   ❌ Redirected (error occurred)")
                    location = view_response.headers.get('Location', 'Unknown')
                    print(f"   📍 Redirect to: {location}")
                else:
                    print(f"   ❌ Unexpected status: {view_response.status_code}")
            
        else:
            print(f"   ❌ Failed to get campaigns: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test contacts API
    print(f"\n👥 Testing contacts...")
    try:
        response = requests.get("http://localhost:5000/api/contacts/count?active=true")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Found {data.get('count', 0)} active contacts")
        else:
            print(f"   ❌ Failed to get contacts: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test database info API (if exists)
    print(f"\n🗄️ Testing database info...")
    try:
        # Try to get some database info through the debug system
        response = requests.get("http://localhost:5000/debug/")
        if response.status_code == 200:
            print("   ✅ Debug dashboard accessible")
        else:
            print(f"   ❌ Debug dashboard not accessible: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def create_test_campaign():
    """Create a test campaign and immediately test viewing it"""
    print(f"\n🧪 Creating Test Campaign")
    print("=" * 25)
    
    # Create campaign
    campaign_data = {
        'name': 'Database Test Campaign',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate'
    }
    
    print("📝 Creating campaign...")
    try:
        create_response = requests.post(
            'http://localhost:5000/campaigns/create',
            data=campaign_data
        )
        
        if create_response.status_code in [200, 302]:
            print("   ✅ Campaign creation request successful")
            
            # Get updated campaigns list
            print("🔍 Getting updated campaigns list...")
            campaigns_response = requests.get('http://localhost:5000/campaigns')
            
            if campaigns_response.status_code == 200:
                import re
                campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', campaigns_response.text)
                print(f"   📊 Now have {len(campaign_ids)} campaigns")
                
                if campaign_ids:
                    # Test with newest campaign (first in list)
                    newest_id = campaign_ids[0]
                    print(f"🎯 Testing view with newest campaign: {newest_id}")
                    
                    view_response = requests.get(f"http://localhost:5000/campaigns/{newest_id}")
                    print(f"   Status: {view_response.status_code}")
                    
                    if view_response.status_code == 200:
                        if "Database Test Campaign" in view_response.text:
                            print("   ✅ SUCCESS! Campaign view working correctly")
                            return True
                        elif "Campaign Details" in view_response.text:
                            print("   ✅ Campaign details page loaded")
                            return True
                        else:
                            print("   ⚠️ Page loaded but content unclear")
                            return False
                    else:
                        print(f"   ❌ View failed with status: {view_response.status_code}")
                        return False
                else:
                    print("   ❌ No campaigns found after creation")
                    return False
            else:
                print(f"   ❌ Failed to get updated campaigns list: {campaigns_response.status_code}")
                return False
        else:
            print(f"   ❌ Campaign creation failed: {create_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Flask Database Test")
    print("=" * 50)
    
    # Test current state
    test_flask_database()
    
    # Create and test new campaign
    test_result = create_test_campaign()
    
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    if test_result:
        print("🎉 SUCCESS! View campaign functionality is working!")
        print("\n📝 What this means:")
        print("   • Flask app is connected to database")
        print("   • Campaign creation works")
        print("   • View campaign button works")
        print("   • Database queries are successful")
        print("\n💡 The view buttons should now work in the web interface!")
    else:
        print("❌ Issues detected with view campaign functionality")
        print("\n🔧 Troubleshooting steps:")
        print("   • Check Flask application logs")
        print("   • Verify database file location")
        print("   • Restart Flask application")
        print("   • Check for template errors")
