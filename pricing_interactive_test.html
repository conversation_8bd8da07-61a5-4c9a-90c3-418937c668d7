
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Responsive Test</title>
    <style>
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            font-family: Arial, sans-serif;
        }
        
        .test-controls button {
            margin: 3px;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .test-container {
            transition: all 0.3s ease;
            margin: 0 auto;
            border: 2px solid #ddd;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <div style="font-weight: bold; margin-bottom: 10px;">📱 Responsive Test</div>
        <button onclick="setWidth('100%')">Full Width</button><br>
        <button onclick="setWidth('800px')">Desktop (800px)</button><br>
        <button onclick="setWidth('768px')">Tablet (768px)</button><br>
        <button onclick="setWidth('480px')">Mobile (480px)</button><br>
        <button onclick="setWidth('360px')">Small (360px)</button><br>
        <button onclick="setWidth('320px')">Tiny (320px)</button>
    </div>

    <div class="test-container" id="testContainer">
        
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24Seven Assistants - Meet Sarah</title>
    <style>
        /* Desktop-first responsive design */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .email-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header-title {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            margin: 15px 0 0 0;
            font-size: 20px;
            opacity: 0.95;
        }

        .content-section {
            background: #f8f9fa;
            padding: 40px 30px;
        }

        .info-card {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #1976d2;
        }

        .service-item {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #4caf50;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }

        .service-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .features-list {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .pricing-section {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
            overflow: hidden;
        }

        .pricing-grid {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .pricing-card {
            background: rgba(255,255,255,0.15);
            padding: 20px;
            border-radius: 12px;
            flex: 1;
            min-width: 250px;
            max-width: 300px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .pricing-card h4 {
            margin: 0 0 15px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .pricing-amount {
            font-size: 20px;
            font-weight: bold;
            margin: 8px 0;
        }

        .chat-demo-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .chat-interface {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .chat-input-group {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #007bff;
            border-radius: 25px;
            font-size: 16px;
            background: white;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chat-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .chat-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .cta-button {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 18px 35px;
            text-decoration: none;
            border-radius: 30px;
            font-weight: bold;
            display: inline-block;
            box-shadow: 0 6px 20px rgba(76,175,80,0.3);
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76,175,80,0.4);
            color: white;
            text-decoration: none;
        }

        .signature-section {
            border-top: 2px solid #e0e0e0;
            padding-top: 25px;
            margin-top: 35px;
        }

        .footer-section {
            text-align: center;
            margin-top: 25px;
            color: #666;
            font-size: 14px;
            padding: 20px;
        }

        /* Mobile Responsive Design */
        @media only screen and (max-width: 768px) {
            body {
                max-width: 100%;
                padding: 10px;
                font-size: 14px;
            }

            .header-section {
                padding: 25px 20px;
            }

            .header-title {
                font-size: 24px;
            }

            .header-subtitle {
                font-size: 16px;
            }

            .content-section {
                padding: 25px 20px;
            }

            .info-card {
                padding: 20px;
                margin: 20px 0;
            }

            .service-item {
                padding: 15px;
                margin: 12px 0;
            }

            .pricing-grid {
                flex-direction: column;
                gap: 15px;
                justify-content: center;
                align-items: center;
            }

            .pricing-card {
                min-width: auto;
                max-width: 100%;
                width: 100%;
                margin-bottom: 15px;
                flex: none;
            }

            .pricing-section {
                padding: 25px 15px;
                margin: 25px 0;
            }

            .chat-demo-section {
                padding: 25px 20px;
                margin: 25px 0;
            }

            .chat-interface {
                padding: 15px;
                margin: 15px 0;
            }

            .chat-input-group {
                gap: 10px;
            }

            .chat-input {
                padding: 12px 16px;
                font-size: 14px;
            }

            .chat-button {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }

            .cta-button {
                padding: 15px 25px;
                font-size: 14px;
                display: block;
                text-align: center;
                margin: 20px 0;
            }

            .signature-section {
                padding-top: 20px;
                margin-top: 25px;
            }
        }

        @media only screen and (max-width: 480px) {
            body {
                padding: 5px;
            }

            .header-section {
                padding: 20px 10px;
            }

            .header-title {
                font-size: 20px;
            }

            .header-subtitle {
                font-size: 14px;
            }

            .content-section {
                padding: 20px 10px;
            }

            .info-card {
                padding: 15px;
            }

            .service-item {
                padding: 12px;
            }

            .pricing-section {
                padding: 20px 10px;
                margin: 20px 0;
            }

            .pricing-grid {
                gap: 12px;
            }

            .pricing-card {
                padding: 15px;
                min-width: auto;
                max-width: 100%;
                width: 100%;
            }

            .pricing-card h4 {
                font-size: 16px;
                margin-bottom: 10px;
            }

            .pricing-amount {
                font-size: 18px;
            }

            .chat-demo-section {
                padding: 20px 15px;
            }

            .chat-interface {
                padding: 12px;
            }

            .chat-input {
                padding: 10px 14px;
                font-size: 13px;
            }

            .chat-button {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* Extra Small Mobile Devices */
        @media only screen and (max-width: 360px) {
            body {
                padding: 3px;
                font-size: 13px;
            }

            .header-section {
                padding: 15px 8px;
            }

            .header-title {
                font-size: 18px;
            }

            .header-subtitle {
                font-size: 13px;
            }

            .content-section {
                padding: 15px 8px;
            }

            .pricing-section {
                padding: 15px 8px;
                margin: 15px 0;
            }

            .pricing-grid {
                gap: 10px;
            }

            .pricing-card {
                padding: 12px;
                border-radius: 8px;
            }

            .pricing-card h4 {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .pricing-amount {
                font-size: 16px;
            }

            .info-card {
                padding: 12px;
                margin: 15px 0;
            }

            .service-item {
                padding: 10px;
                margin: 10px 0;
            }

            .chat-demo-section {
                padding: 15px 8px;
            }

            .chat-interface {
                padding: 10px;
            }

            .cta-button {
                padding: 12px 20px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header-section">
            <h1 class="header-title">👋 Meet Sarah</h1>
            <p class="header-subtitle">Our Personal AI Sales Assistant</p>
        </div>

        <div class="content-section">
            <p style="margin-top: 0; font-size: 16px;">Hi John Doe,</p>

            <p>I hope this email finds you well. My name is Sarah, and I'm reaching out from <strong>24Seven Assistants</strong> because I believe we can help you streamline your business operations and achieve remarkable growth and efficiency.</p>

            <p>Many businesses struggle with providing round the clock customer service. That's where we come in: we provide dedicated virtual assistants that work 24/7, ensuring your customers always get the help they need.</p>

            <div class="info-card">
                <h3 style="color: #1976d2; margin-top: 0;">🤖 Why 24Seven Assistants?</h3>
                <p style="margin-bottom: 0;">We provide world-class virtual assistant services that work around the clock to support your business operations, allowing you to focus on what matters most - growing your business.</p>
            </div>

            <h3 style="color: #ff9800; margin-bottom: 20px;">📋 Our Core Services:</h3>

            <div class="service-item">
                <strong>📞 Administrative Support</strong><br>
                Email management, scheduling, data entry, and document preparation
            </div>

            <div class="service-item" style="border-left-color: #2196f3;">
                <strong>🎧 Customer Service</strong><br>
                24/7 customer support, live chat, and phone assistance
            </div>

            <div class="service-item" style="border-left-color: #ff9800;">
                <strong>📊 Lead qualification and follow-up</strong><br>
                Appointment scheduling and sales pipeline management
            </div>

            <div class="service-item" style="border-left-color: #9c27b0;">
                <strong>📧 Email and phone support</strong><br>
                Professional communication management
            </div>

            <h3 style="color: #4caf50; margin: 30px 0 20px 0;">💡 What Makes Us Different:</h3>
            <div class="features-list">
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Available 24/7:</strong> We work around the clock for maximum productivity</li>
                    <li><strong>Flexible plans that grow with your business</strong></li>
                    <li><strong>Easy integration with your current systems</strong></li>
                    <li><strong>Competitive pricing in UGX currency</strong></li>
                </ul>
            </div>

            <div class="pricing-section">
                <h3 style="margin-top: 0; text-align: center; font-size: 24px;">💰 Our Pricing Plans</h3>

                <div class="pricing-grid">
                    <div class="pricing-card">
                        <h4>Small Business</h4>
                        <p class="pricing-amount">UGX 250K setup</p>
                        <p style="margin: 5px 0;">UGX 100K/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Perfect for startups</p>
                    </div>

                    <div class="pricing-card">
                        <h4>Medium Business</h4>
                        <p class="pricing-amount">UGX 500K setup</p>
                        <p style="margin: 5px 0;">UGX 250K/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Ideal for growth</p>
                    </div>

                    <div class="pricing-card">
                        <h4>Large Enterprise</h4>
                        <p class="pricing-amount">UGX 3M setup</p>
                        <p style="margin: 5px 0;">UGX 1M/month</p>
                        <p style="font-size: 14px; opacity: 0.9;">Complete solution</p>
                    </div>
                </div>
            </div>

            <div class="chat-demo-section">
                <h3 style="margin-top: 0; text-align: center; font-size: 22px;">💬 Try Our AI Assistant Demo</h3>
                <p style="text-align: center; margin-bottom: 20px;">Experience our AI assistant in action! Click the chat button below to start a conversation:</p>

                <div class="chat-interface">
                    <div class="chat-input-group">
                        <input type="text" class="chat-input" placeholder="Type your message here..." readonly>
                        <a href="http://localhost:5000/chat/test-session-123" class="chat-button" target="_blank">➤</a>
                    </div>
                </div>

                <p style="text-align: center; margin: 15px 0 0 0; font-size: 14px; opacity: 0.9;">
                    <a href="http://localhost:5000/chat/test-session-123" style="color: white; text-decoration: underline;">Click here to start chatting with Sarah →</a>
                </p>
            </div>

            <p>I'd love to schedule a brief call to discuss how 24Seven Assistants can specifically help Test Company Ltd achieve its goals. Are you available for a 15-minute conversation this week?</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="mailto:<EMAIL>" class="cta-button">
                    📞 Schedule a Call
                </a>
            </div>

            <div class="signature-section">
                <p style="margin-bottom: 5px;"><strong>Best regards,</strong></p>
                <p style="margin: 5px 0;"><strong>Sarah</strong></p>
                <p style="margin: 5px 0;">24Seven Assistants</p>
                <p style="margin: 5px 0;">📧 <EMAIL></p>
                <p style="margin: 5px 0;">📞 +256 **********</p>
            </div>
        </div>
    </div>

    <div class="footer-section">
        <p>© 2024 24Seven Assistants. Professional Virtual Assistant Services.</p>
    </div>
</body>
</html>
            
    </div>

    <script>
        function setWidth(width) {
            const container = document.getElementById('testContainer');
            container.style.width = width;
            container.style.maxWidth = width;
            
            if (width !== '100%') {
                container.style.margin = '20px auto';
            }
        }
        
        // Set initial width
        setWidth('800px');
    </script>
</body>
</html>
        