#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix database schema by adding missing columns
"""

import sqlite3
import os

def fix_database():
    """Add missing columns to the contacts table"""
    
    db_path = "unified_sales.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking current contacts table schema...")
        
        # Get current table schema
        cursor.execute("PRAGMA table_info(contacts)")
        columns = cursor.fetchall()
        
        print("Current columns:")
        column_names = []
        for col in columns:
            column_names.append(col[1])
            print(f"  - {col[1]} ({col[2]})")
        
        # Check which columns need to be added
        required_columns = {
            'website': 'VARCHAR(255)',
            'notes': 'TEXT',
            'lead_score': 'FLOAT DEFAULT 0.0'
        }
        
        columns_to_add = []
        for col_name, col_type in required_columns.items():
            if col_name not in column_names:
                columns_to_add.append((col_name, col_type))
        
        if not columns_to_add:
            print("✅ All required columns already exist!")
            return True
        
        print(f"\n🔧 Adding {len(columns_to_add)} missing columns...")
        
        # Add missing columns
        for col_name, col_type in columns_to_add:
            try:
                sql = f"ALTER TABLE contacts ADD COLUMN {col_name} {col_type}"
                print(f"  Executing: {sql}")
                cursor.execute(sql)
                print(f"  ✅ Added column: {col_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"  ⚠️ Column {col_name} already exists")
                else:
                    print(f"  ❌ Error adding column {col_name}: {e}")
                    return False
        
        # Commit changes
        conn.commit()
        
        print("\n🔍 Verifying updated schema...")
        cursor.execute("PRAGMA table_info(contacts)")
        updated_columns = cursor.fetchall()
        
        print("Updated columns:")
        for col in updated_columns:
            print(f"  - {col[1]} ({col[2]})")
        
        # Close connection
        conn.close()
        
        print("\n✅ Database schema updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")
        return False

if __name__ == "__main__":
    print("=== Database Schema Fix ===")
    success = fix_database()
    if success:
        print("\n🎉 Database is ready for CSV/JSON uploads!")
    else:
        print("\n💥 Database fix failed!")
