{% extends "base.html" %}

{% block title %}Edit {{ group.name }} - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-edit"></i> Edit Group: {{ group.name }}</h2>
                <div>
                    <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Group
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-layer-group"></i> Group Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Group Name *</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ group.name }}" required maxlength="255" placeholder="Enter group name">
                            <div class="form-text">Choose a descriptive name for your contact group.</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Optional description of this group">{{ group.description or '' }}</textarea>
                            <div class="form-text">Provide additional details about this group's purpose or criteria.</div>
                        </div>

                        <div class="mb-3">
                            <label for="color" class="form-label">Group Color</label>
                            <div class="d-flex align-items-center">
                                <input type="color" class="form-control form-control-color me-3" id="color" name="color" value="{{ group.color }}" title="Choose group color">
                                <span class="text-muted">Select a color to help identify this group</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Created</label>
                                    <div class="form-control-plaintext">{{ group.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Last Updated</label>
                                    <div class="form-control-plaintext">{{ group.updated_at.strftime('%Y-%m-%d %H:%M') if group.updated_at else 'Never' }}</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('view_group', group_id=group.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Group
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Group Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Group Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-primary">{{ contact_count }}</h3>
                                <p class="mb-0">Total Contacts</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-success">{{ group.created_by or 'System' }}</h3>
                                <p class="mb-0">Created By</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h3 class="text-info">{{ (group.updated_at or group.created_at).strftime('%Y-%m-%d') }}</h3>
                                <p class="mb-0">Last Modified</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Danger Zone</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Delete Group</h6>
                            <p class="text-muted mb-0">Permanently delete this group. This action cannot be undone. Contacts will remain in the system.</p>
                        </div>
                        <button class="btn btn-outline-danger" onclick="confirmDeleteGroup()">
                            <i class="fas fa-trash"></i> Delete Group
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the group "<strong>{{ group.name }}</strong>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone. The group will be permanently deleted, but contacts will remain in the system.
                </div>
                {% if contact_count > 0 %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    This group currently contains <strong>{{ contact_count }}</strong> contact(s). They will be removed from this group but will remain in your contact list.
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteGroupForm" method="POST" action="{{ url_for('delete_group', group_id=group.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Group
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteGroup() {
    const modal = new bootstrap.Modal(document.getElementById('deleteGroupModal'));
    modal.show();
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();

    if (!name) {
        e.preventDefault();
        alert('Group name is required.');
        document.getElementById('name').focus();
        return false;
    }

    if (name.length > 255) {
        e.preventDefault();
        alert('Group name must be 255 characters or less.');
        document.getElementById('name').focus();
        return false;
    }
});

// Real-time character count for name field
document.getElementById('name').addEventListener('input', function() {
    const maxLength = 255;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;

    // Find or create character count display
    let countDisplay = document.getElementById('nameCharCount');
    if (!countDisplay) {
        countDisplay = document.createElement('div');
        countDisplay.id = 'nameCharCount';
        countDisplay.className = 'form-text';
        this.parentNode.appendChild(countDisplay);
    }

    countDisplay.textContent = `${currentLength}/${maxLength} characters`;

    if (remaining < 20) {
        countDisplay.className = 'form-text text-warning';
    } else {
        countDisplay.className = 'form-text text-muted';
    }
});
</script>
{% endblock %}
