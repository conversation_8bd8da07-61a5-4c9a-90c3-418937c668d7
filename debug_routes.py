"""
24Seven Assistants Sales Department - Debug Dashboard Routes
===========================================================
Web interface for debugging, monitoring, and system diagnostics.
"""

import os
import json
import sqlite3
from datetime import datetime, timedelta
from flask import Blueprint, render_template, jsonify, request, flash, redirect, url_for
from debug_system import (
    debug_logger, request_debugger, error_tracker, performance_monitor,
    get_debug_stats, DebugConfig
)

# Create debug blueprint
debug_bp = Blueprint('debug', __name__, url_prefix='/debug')

@debug_bp.route('/')
def debug_dashboard():
    """Main debug dashboard"""
    try:
        stats = get_debug_stats()

        # Get system info
        system_info = {
            'python_version': os.sys.version,
            'debug_mode': DebugConfig.ENABLE_REQUEST_LOGGING,
            'log_level': DebugConfig.LOG_LEVEL,
            'log_file': DebugConfig.LOG_FILE,
            'slow_request_threshold': DebugConfig.SLOW_REQUEST_THRESHOLD,
            'slow_query_threshold': DebugConfig.SLOW_QUERY_THRESHOLD
        }

        return render_template('debug_dashboard.html', stats=stats, system_info=system_info)

    except Exception as e:
        flash(f'Error loading debug dashboard: {str(e)}', 'error')
        return render_template('debug_dashboard.html', stats={}, system_info={})

@debug_bp.route('/requests')
def debug_requests():
    """Request logs page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # Get paginated request logs
        logs = request_debugger.request_logs
        total = len(logs)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_logs = logs[start:end]

        # Calculate pagination info
        has_prev = page > 1
        has_next = end < total
        prev_num = page - 1 if has_prev else None
        next_num = page + 1 if has_next else None

        return render_template('debug_requests.html',
                             logs=paginated_logs,
                             page=page,
                             has_prev=has_prev,
                             has_next=has_next,
                             prev_num=prev_num,
                             next_num=next_num,
                             total=total)

    except Exception as e:
        flash(f'Error loading request logs: {str(e)}', 'error')
        return render_template('debug_requests.html', logs=[], page=1, total=0)

@debug_bp.route('/errors')
def debug_errors():
    """Error logs page"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Get paginated error logs
        logs = error_tracker.error_logs
        total = len(logs)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_logs = logs[start:end]

        # Calculate pagination info
        has_prev = page > 1
        has_next = end < total
        prev_num = page - 1 if has_prev else None
        next_num = page + 1 if has_next else None

        return render_template('debug_errors.html',
                             logs=paginated_logs,
                             page=page,
                             has_prev=has_prev,
                             has_next=has_next,
                             prev_num=prev_num,
                             next_num=next_num,
                             total=total)

    except Exception as e:
        flash(f'Error loading error logs: {str(e)}', 'error')
        return render_template('debug_errors.html', logs=[], page=1, total=0)

@debug_bp.route('/performance')
def debug_performance():
    """Performance monitoring page"""
    try:
        # Get performance statistics
        logs = performance_monitor.performance_logs

        # Calculate statistics
        total_calls = len(logs)
        successful_calls = len([log for log in logs if log['success']])
        failed_calls = total_calls - successful_calls

        # Get slow functions
        slow_functions = [
            log for log in logs
            if log['duration'] > DebugConfig.SLOW_QUERY_THRESHOLD
        ]

        # Get function statistics
        function_stats = {}
        for log in logs:
            func_name = log['function_name']
            if func_name not in function_stats:
                function_stats[func_name] = {
                    'calls': 0,
                    'total_duration': 0,
                    'avg_duration': 0,
                    'max_duration': 0,
                    'failures': 0
                }

            stats = function_stats[func_name]
            stats['calls'] += 1
            stats['total_duration'] += log['duration']
            stats['max_duration'] = max(stats['max_duration'], log['duration'])
            if not log['success']:
                stats['failures'] += 1

        # Calculate averages
        for stats in function_stats.values():
            if stats['calls'] > 0:
                stats['avg_duration'] = stats['total_duration'] / stats['calls']

        # Sort by average duration
        sorted_functions = sorted(
            function_stats.items(),
            key=lambda x: x[1]['avg_duration'],
            reverse=True
        )

        performance_data = {
            'total_calls': total_calls,
            'successful_calls': successful_calls,
            'failed_calls': failed_calls,
            'slow_functions': slow_functions[-20:],  # Last 20 slow functions
            'function_stats': sorted_functions[:20]  # Top 20 slowest functions
        }

        return render_template('debug_performance.html', data=performance_data)

    except Exception as e:
        flash(f'Error loading performance data: {str(e)}', 'error')
        return render_template('debug_performance.html', data={})

@debug_bp.route('/database')
def debug_database():
    """Database debugging page"""
    try:
        from flask import current_app

        # Get database file path
        db_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI', '')
        if db_uri.startswith('sqlite:///'):
            db_path = db_uri.replace('sqlite:///', '')
        else:
            db_path = 'Database not SQLite'

        # Get database statistics
        db_stats = {}
        if db_path != 'Database not SQLite' and os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # Get table information
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()

                table_stats = {}
                for (table_name,) in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                    count = cursor.fetchone()[0]
                    table_stats[table_name] = count

                # Get database size
                db_size = os.path.getsize(db_path)

                db_stats = {
                    'path': db_path,
                    'size_bytes': db_size,
                    'size_mb': round(db_size / (1024 * 1024), 2),
                    'tables': table_stats,
                    'total_tables': len(tables)
                }

                conn.close()

            except Exception as e:
                db_stats = {'error': f'Could not read database: {str(e)}'}
        else:
            db_stats = {'error': 'Database file not found or not SQLite'}

        return render_template('debug_database.html', db_stats=db_stats)

    except Exception as e:
        flash(f'Error loading database info: {str(e)}', 'error')
        return render_template('debug_database.html', db_stats={})

@debug_bp.route('/email-test')
def debug_email_test():
    """Email testing page"""
    return render_template('debug_email_test.html')

@debug_bp.route('/api/test-email', methods=['POST'])
def api_test_email():
    """API endpoint to test email configuration"""
    try:
        from flask import current_app
        from flask_mail import Mail, Message

        # Get test email from form
        test_email = request.json.get('email', '<EMAIL>')

        # Create test message
        mail = Mail(current_app)
        msg = Message(
            subject='24Seven Assistants - Email Test',
            recipients=[test_email],
            body='This is a test email from the 24Seven Assistants Sales Department system.',
            html='<h1>Email Test</h1><p>This is a test email from the 24Seven Assistants Sales Department system.</p>'
        )

        # Try to send
        mail.send(msg)

        return jsonify({
            'success': True,
            'message': f'Test email sent successfully to {test_email}'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Email test failed: {str(e)}'
        }), 500

@debug_bp.route('/api/clear-logs', methods=['POST'])
def api_clear_logs():
    """API endpoint to clear debug logs"""
    try:
        log_type = request.json.get('type', 'all')

        if log_type == 'requests' or log_type == 'all':
            request_debugger.request_logs.clear()

        if log_type == 'errors' or log_type == 'all':
            error_tracker.error_logs.clear()

        if log_type == 'performance' or log_type == 'all':
            performance_monitor.performance_logs.clear()

        return jsonify({
            'success': True,
            'message': f'Cleared {log_type} logs successfully'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to clear logs: {str(e)}'
        }), 500

@debug_bp.route('/api/stats')
def api_debug_stats():
    """API endpoint for debug statistics"""
    try:
        stats = get_debug_stats()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@debug_bp.route('/logs/download')
def download_logs():
    """Download log file"""
    try:
        from flask import send_file

        if os.path.exists(DebugConfig.LOG_FILE):
            return send_file(
                DebugConfig.LOG_FILE,
                as_attachment=True,
                download_name=f'sales_department_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
            )
        else:
            flash('Log file not found', 'error')
            return redirect(url_for('debug.debug_dashboard'))

    except Exception as e:
        flash(f'Error downloading logs: {str(e)}', 'error')
        return redirect(url_for('debug.debug_dashboard'))

def register_debug_routes(app):
    """Register debug routes with the Flask app"""
    app.register_blueprint(debug_bp)
    app.logger.info("DEBUG: Debug routes registered at /debug")
