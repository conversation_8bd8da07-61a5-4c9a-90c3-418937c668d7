#!/usr/bin/env python3
"""
Test Chat Persistence in App Context
====================================
This script tests the chat persistence functionality by simulating
the app.py functions directly.
"""

import sys
import os
import uuid
import requests
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_app_functions():
    """Test the app functions with persistence"""
    print("🧪 Testing App Functions with Chat Persistence")
    print("=" * 60)
    
    # Test session ID
    test_session_id = str(uuid.uuid4())
    print(f"Test Session ID: {test_session_id}")
    
    try:
        # Import app functions
        from app import format_conversation_log_from_history
        print("✅ Successfully imported app functions")
        
        # Test the helper function
        test_history = [
            {"role": "assistant", "content": "Hello! I'm <PERSON>.", "stage": "opening", "task": "greeting"},
            {"role": "user", "content": "Hi <PERSON>, I'm <PERSON><PERSON>", "stage": "opening", "task": "greeting"},
            {"role": "assistant", "content": "Nice to meet you <PERSON>", "stage": "opening", "task": "business_inquiry"}
        ]
        
        formatted_log = format_conversation_log_from_history(test_history)
        print("✅ format_conversation_log_from_history works correctly")
        print("Sample formatted log:")
        print(formatted_log[:100] + "..." if len(formatted_log) > 100 else formatted_log)
        
        # Test API integration
        print("\n🔗 Testing API Integration")
        
        # Save test messages
        for i, msg in enumerate(test_history):
            save_data = {
                'session_id': test_session_id,
                'message_type': msg['role'],
                'content': msg['content'],
                'stage': msg.get('stage'),
                'task': msg.get('task'),
                'message_order': i
            }
            
            response = requests.post('http://localhost:5000/api/save-chat-message', json=save_data)
            if response.status_code == 200:
                print(f"✅ Saved {msg['role']} message {i+1}")
            else:
                print(f"❌ Failed to save {msg['role']} message {i+1}: {response.status_code}")
        
        # Retrieve history
        response = requests.get(f'http://localhost:5000/api/get-chat-history/{test_session_id}')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                retrieved_history = result.get('history', [])
                print(f"✅ Retrieved {len(retrieved_history)} messages from API")
                
                # Test the formatting function with retrieved data
                formatted_retrieved = format_conversation_log_from_history(retrieved_history)
                print("✅ Successfully formatted retrieved history")
                
                # Compare original and retrieved
                if len(test_history) == len(retrieved_history):
                    print("✅ Message count matches")
                else:
                    print(f"❌ Message count mismatch: {len(test_history)} vs {len(retrieved_history)}")
                
            else:
                print(f"❌ API returned error: {result.get('message')}")
        else:
            print(f"❌ Failed to retrieve history: {response.status_code}")
        
        # Test reset functionality
        print("\n🗑️ Testing Reset Functionality")
        reset_data = {'session_id': test_session_id}
        response = requests.post('http://localhost:5000/api/reset-chat-session', json=reset_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Session reset successful")
                
                # Verify history is cleared
                response = requests.get(f'http://localhost:5000/api/get-chat-history/{test_session_id}')
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') and len(result.get('history', [])) == 0:
                        print("✅ History successfully cleared after reset")
                    else:
                        print(f"❌ History not cleared: {len(result.get('history', []))} messages remain")
                else:
                    print(f"❌ Failed to verify reset: {response.status_code}")
            else:
                print(f"❌ Reset failed: {result.get('message')}")
        else:
            print(f"❌ Reset request failed: {response.status_code}")
        
        print("\n🎉 App persistence integration test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def check_server_status():
    """Check if the unified sales system is running"""
    try:
        response = requests.get("http://localhost:5000/")
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("🔍 Checking server status...")
    if not check_server_status():
        print("❌ Unified sales system is not running at http://localhost:5000")
        print("Please start it first: python unified_sales_system.py")
        exit(1)
    
    print("✅ Server is running")
    
    success = test_app_functions()
    exit(0 if success else 1)
