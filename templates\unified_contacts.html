{% extends "base.html" %}

{% block title %}Contacts - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-users text-primary"></i> Contact Management</h1>
    <div>
        <button id="bulkDeleteContactsBtn" class="btn btn-danger me-2" style="display: none;" onclick="bulkDeleteContacts()">
            <i class="fas fa-trash"></i> Delete Selected
        </button>
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContactModal">
            <i class="fas fa-plus"></i> Add Contact
        </a>
    </div>
</div>

<!-- Contact Statistics - Synchronized Analytics -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.basic_metrics.total_contacts if analytics else 0 }}</h4>
                <p class="card-text">Total Contacts</p>
                <i class="fas fa-users fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.emails_sent if analytics else 0 }}</h4>
                <p class="card-text">Emails Sent</p>
                <i class="fas fa-envelope fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.emails_opened if analytics else 0 }}</h4>
                <p class="card-text">Emails Opened</p>
                <small>{{ analytics.conversion_rates.email_open_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-envelope-open fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.links_clicked if analytics else 0 }}</h4>
                <p class="card-text">Links Clicked</p>
                <small>{{ analytics.conversion_rates.click_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-mouse-pointer fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.conversations_started if analytics else 0 }}</h4>
                <p class="card-text">Conversations</p>
                <small>{{ analytics.conversion_rates.conversation_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-robot fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.conversions if analytics else 0 }}</h4>
                <p class="card-text">Conversions</p>
                <small>{{ analytics.conversion_rates.overall_rate if analytics else 0 }}% overall</small>
                <br><i class="fas fa-trophy fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Contacts Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> All Contacts
        </h5>
    </div>
    <div class="card-body">
        {% if contacts and contacts.items %}
        <!-- Search and Filter Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card bg-dark text-light">
                    <div class="card-body">
                        <form method="GET" action="{{ url_for('contacts_list') }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label text-white">Search Contacts</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="Search by name, email, or company..."
                                       value="{{ request.args.get('search', '') }}"
                                       style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                            </div>
                            <div class="col-md-3">
                                <label for="sales_stage" class="form-label text-white">Sales Stage</label>
                                <select class="form-select" id="sales_stage" name="sales_stage"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Stages</option>
                                    <option value="new" {{ 'selected' if request.args.get('sales_stage') == 'new' }} style="background-color: #1a202c; color: white;">New</option>
                                    <option value="email_sent" {{ 'selected' if request.args.get('sales_stage') == 'email_sent' }} style="background-color: #1a202c; color: white;">Email Sent</option>
                                    <option value="email_opened" {{ 'selected' if request.args.get('sales_stage') == 'email_opened' }} style="background-color: #1a202c; color: white;">Email Opened</option>
                                    <option value="link_clicked" {{ 'selected' if request.args.get('sales_stage') == 'link_clicked' }} style="background-color: #1a202c; color: white;">Link Clicked</option>
                                    <option value="opening" {{ 'selected' if request.args.get('sales_stage') == 'opening' }} style="background-color: #1a202c; color: white;">Opening</option>
                                    <option value="trust" {{ 'selected' if request.args.get('sales_stage') == 'trust' }} style="background-color: #1a202c; color: white;">Trust Building</option>
                                    <option value="discovery" {{ 'selected' if request.args.get('sales_stage') == 'discovery' }} style="background-color: #1a202c; color: white;">Discovery</option>
                                    <option value="demonstration" {{ 'selected' if request.args.get('sales_stage') == 'demonstration' }} style="background-color: #1a202c; color: white;">Demonstration</option>
                                    <option value="close" {{ 'selected' if request.args.get('sales_stage') == 'close' }} style="background-color: #1a202c; color: white;">Closing</option>
                                    <option value="converted" {{ 'selected' if request.args.get('sales_stage') == 'converted' }} style="background-color: #1a202c; color: white;">Converted</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="source" class="form-label text-white">Source</label>
                                <select class="form-select" id="source" name="source"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Sources</option>
                                    <option value="manual_entry" {{ 'selected' if request.args.get('source') == 'manual_entry' }} style="background-color: #1a202c; color: white;">Manual Entry</option>
                                    <option value="website" {{ 'selected' if request.args.get('source') == 'website' }} style="background-color: #1a202c; color: white;">Website</option>
                                    <option value="referral" {{ 'selected' if request.args.get('source') == 'referral' }} style="background-color: #1a202c; color: white;">Referral</option>
                                    <option value="social_media" {{ 'selected' if request.args.get('source') == 'social_media' }} style="background-color: #1a202c; color: white;">Social Media</option>
                                    <option value="email_campaign" {{ 'selected' if request.args.get('source') == 'email_campaign' }} style="background-color: #1a202c; color: white;">Email Campaign</option>
                                    <option value="chatbot" {{ 'selected' if request.args.get('source') == 'chatbot' }} style="background-color: #1a202c; color: white;">Chatbot</option>
                                    <option value="other" {{ 'selected' if request.args.get('source') == 'other' }} style="background-color: #1a202c; color: white;">Other</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="per_page" class="form-label text-white">Show</label>
                                <select class="form-select" id="per_page" name="per_page"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="10" {{ 'selected' if current_per_page == 10 }} style="background-color: #1a202c; color: white;">10 per page</option>
                                    <option value="20" {{ 'selected' if current_per_page == 20 }} style="background-color: #1a202c; color: white;">20 per page</option>
                                    <option value="50" {{ 'selected' if current_per_page == 50 }} style="background-color: #1a202c; color: white;">50 per page</option>
                                    <option value="100" {{ 'selected' if current_per_page == 100 }} style="background-color: #1a202c; color: white;">100 per page</option>
                                    <option value="200" {{ 'selected' if current_per_page == 200 }} style="background-color: #1a202c; color: white;">200 per page</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label text-light">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <a href="{{ url_for('contacts_list') }}" class="btn btn-outline-light">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Info and Bulk Actions Bar -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <small class="text-muted">
                    Showing {{ contacts.items|length }} of {{ contacts.total }} contacts
                    {% if contacts.pages > 1 %}
                    (Page {{ contacts.page }} of {{ contacts.pages }})
                    {% endif %}
                </small>
            </div>
            <div>
                <button id="bulkDeleteContactsBtn" class="btn btn-danger" onclick="bulkDeleteContacts()" style="display: none;">
                    <i class="fas fa-trash"></i> Delete Selected Contacts
                </button>
                <small class="text-muted ms-3">Select contacts using checkboxes to enable bulk actions</small>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAllContacts" onchange="toggleSelectAllContacts()" class="form-check-input">
<!-- csrf token handled inside forms -->
                        </th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Company</th>
                        <th>Sales Stage</th>
                        <th>Source</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for contact in contacts.items %}
                    <tr>
                        <td>
                            <input type="checkbox" name="contact_ids" value="{{ contact.id }}" class="form-check-input contact-checkbox" onchange="updateBulkDeleteContactsButtons()">
                        </td>
                        <td>
                            <strong>{{ contact.first_name }} {{ contact.last_name }}</strong>
                            {% if contact.job_title %}
                            <br><small class="text-muted">{{ contact.job_title }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <a href="mailto:{{ contact.email }}">{{ contact.email }}</a>
                            {% if contact.phone %}
                            <br><small class="text-muted">{{ contact.phone }}</small>
                            {% endif %}
                        </td>
                        <td>{{ contact.company or '-' }}</td>
                        <td>
                            {% if contact.current_sales_stage %}
                                {% if contact.current_sales_stage == 'email_sent' %}
                                    <span class="badge bg-primary">Email Sent</span>
                                {% elif contact.current_sales_stage == 'email_opened' %}
                                    <span class="badge bg-info">Email Opened</span>
                                {% elif contact.current_sales_stage == 'link_clicked' %}
                                    <span class="badge bg-warning">Link Clicked</span>
                                {% elif contact.current_sales_stage == 'opening' %}
                                    <span class="badge bg-secondary">Opening</span>
                                {% elif contact.current_sales_stage == 'trust' %}
                                    <span class="badge bg-info">Trust Building</span>
                                {% elif contact.current_sales_stage == 'discovery' %}
                                    <span class="badge bg-primary">Discovery</span>
                                {% elif contact.current_sales_stage == 'demonstration' %}
                                    <span class="badge bg-warning">Demonstration</span>
                                {% elif contact.current_sales_stage == 'close' %}
                                    <span class="badge bg-danger">Closing</span>
                                {% elif contact.current_sales_stage == 'converted' %}
                                    <span class="badge bg-success">Converted</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">{{ contact.current_sales_stage|title }}</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-light text-dark">New</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ contact.source|title }}</span>
                        </td>
                        <td>
                            {% if contact.is_customer %}
                                <span class="badge bg-success">Customer</span>
                            {% elif contact.do_not_email %}
                                <span class="badge bg-danger">Do Not Email</span>
                            {% elif contact.is_active %}
                                <span class="badge bg-primary">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ contact.created_at.strftime('%Y-%m-%d %H:%M') if contact.created_at else '-' }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('view_contact', contact_id=contact.id) }}"
                                   class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_contact', contact_id=contact.id) }}"
                                   class="btn btn-outline-secondary btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if contact.chatbot_session_id %}
                                <a href="http://localhost:7861?session={{ contact.chatbot_session_id }}"
                                   target="_blank" class="btn btn-outline-success btn-sm" title="View Chatbot Session">
                                    <i class="fas fa-robot"></i>
                                </a>
                                {% endif %}
                                <button class="btn btn-outline-danger btn-sm"
                                        onclick="deleteContact({{ contact.id }}, '{{ contact.full_name }}')"
                                        title="Delete Contact and All Data">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Hidden form for bulk delete operations -->
        <form id="bulkDeleteContactsForm" action="{{ url_for('bulk_delete_contacts') }}" method="POST" style="display: none;">
            <!-- Contact IDs will be added dynamically by JavaScript -->
        </form>

        <!-- Pagination -->
        {% if contacts.pages > 1 %}
        <nav aria-label="Contacts pagination">
            <ul class="pagination justify-content-center">
                {% if contacts.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('contacts_list', page=contacts.prev_num, per_page=current_per_page, search=request.args.get('search', ''), sales_stage=request.args.get('sales_stage', ''), source=request.args.get('source', '')) }}">Previous</a>
                </li>
                {% endif %}

                {% for page_num in contacts.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != contacts.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('contacts_list', page=page_num, per_page=current_per_page, search=request.args.get('search', ''), sales_stage=request.args.get('sales_stage', ''), source=request.args.get('source', '')) }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if contacts.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('contacts_list', page=contacts.next_num, per_page=current_per_page, search=request.args.get('search', ''), sales_stage=request.args.get('sales_stage', ''), source=request.args.get('source', '')) }}">Next</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Contacts Found</h4>
            <p class="text-muted">Start by adding your first contact or importing from a campaign.</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addContactModal">
                <i class="fas fa-plus"></i> Add First Contact
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Contact Modal -->
<div class="modal fade" id="addContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Contact</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_contact') }}" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="job_title" class="form-label">Job Title</label>
                                <input type="text" class="form-control" id="job_title" name="job_title">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="source" class="form-label">Source</label>
                        <select class="form-select" id="source" name="source">
                            <option value="manual_entry">Manual Entry</option>
                            <option value="website">Website</option>
                            <option value="referral">Referral</option>
                            <option value="social_media">Social Media</option>
                            <option value="email_campaign">Email Campaign</option>
                            <option value="chatbot">Chatbot</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Contact</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">⚠️ Confirm Complete Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteContactName"></strong>?</p>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> This will permanently delete:</h6>
                    <ul class="mb-0">
                        <li>Contact information and profile</li>
                        <li>All chatbot conversations and sessions</li>
                        <li>All activity history and tracking data</li>
                        <li>Email campaign statistics (will be updated)</li>
                        <li>Sales stage progression history</li>
                    </ul>
                </div>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteContactForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete All Data
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteContact(contactId, contactName) {
    document.getElementById('deleteContactName').textContent = contactName;
    document.getElementById('deleteContactForm').action = '/contacts/' + contactId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteContactModal')).show();
}

// Bulk delete functionality for contacts
function toggleSelectAllContacts() {
    const selectAll = document.getElementById('selectAllContacts');
    const checkboxes = document.querySelectorAll('.contact-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkDeleteContactsButtons();
}

function updateBulkDeleteContactsButtons() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
    const bulkDeleteBtn = document.getElementById('bulkDeleteContactsBtn');

    if (checkedBoxes.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
    } else {
        bulkDeleteBtn.style.display = 'none';
    }

    // Update select all checkbox state
    const selectAll = document.getElementById('selectAllContacts');
    const allCheckboxes = document.querySelectorAll('.contact-checkbox');
    if (allCheckboxes.length > 0) {
        selectAll.checked = checkedBoxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;
    }
}

function bulkDeleteContacts() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Please select contacts to delete using the checkboxes.');
        return false;
    }

    const confirmMessage = `⚠️ BULK DELETE: This will permanently delete ${checkedBoxes.length} contact(s) and ALL related data including:

• Contact information and profiles
• All chatbot conversations and sessions
• All activity history and tracking data
• Email campaign statistics (will be updated)
• Sales stage progression history

This action cannot be undone. Are you absolutely sure?`;

    if (confirm(confirmMessage)) {
        // Clear any existing contact_ids inputs
        const existingInputs = document.querySelectorAll('#bulkDeleteContactsForm input[name="contact_ids"]');
        existingInputs.forEach(input => input.remove());

        // Add selected contact IDs to the form
        const form = document.getElementById('bulkDeleteContactsForm');
        checkedBoxes.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'contact_ids';
            hiddenInput.value = checkbox.value;
            form.appendChild(hiddenInput);
        });

        form.submit();
        return true;
    }
    return false;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkDeleteContactsButtons();

    // Auto-submit form when page size changes
    const perPageSelect = document.getElementById('per_page');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            // Reset to page 1 when changing page size
            const form = this.closest('form');
            const pageInput = form.querySelector('input[name="page"]');
            if (pageInput) {
                pageInput.value = '1';
            } else {
                // Create hidden page input if it doesn't exist
                const hiddenPageInput = document.createElement('input');
                hiddenPageInput.type = 'hidden';
                hiddenPageInput.name = 'page';
                hiddenPageInput.value = '1';
                form.appendChild(hiddenPageInput);
            }
            form.submit();
        });
    }
});
</script>
{% endblock %}
