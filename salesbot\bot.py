"""salesbot.bot
Main SalesBot class with stage-aware prompt construction that follows JSON rules.
"""
from __future__ import annotations

import json
from typing import List, Dict, Any

from .api import chat_completion

__all__ = ["SalesBot"]


class SalesBot:
    """Stage-aware sales assistant that follows JSON configuration rules.

    Parameters
    ----------
    config : dict
        A configuration dictionary with the following top-level keys:
        - company_config
        - sales_stages
        - objection_handling (optional)
        - referral_handling (optional)
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.chat_history: List[Dict[str, str]] = []
        self.current_stage_index: int = 0
        self.current_task_index: int = 0
        self.completed_stages: List[str] = []
        self.completed_tasks: List[str] = []  # Track individual completed tasks
        self.prospect_name: str = ""
        self.waiting_for_user_response: bool = False  # Track if we're waiting for user to respond

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    def _build_system_prompt(self) -> str:
        """Compose the system prompt following the JSON rules exactly."""
        company_cfg = self.config.get("company_config", {})
        base_prompt = company_cfg.get("system_prompt", "You are a helpful sales assistant.")

        # Add company context
        context_prompt = "\n\n--- COMPANY AND PRODUCT CONTEXT ---\n" + json.dumps(
            company_cfg.get("context", {}), indent=2
        )

        # Get current stage and task
        current_stage = self.config["sales_stages"][self.current_stage_index]
        current_task = current_stage["tasks"][self.current_task_index]

        # Build stage-specific prompt with current task focus
        stage_prompt = (
            f"\n\n--- CURRENT SALES STAGE: {current_stage['id'].upper()} ---\n"
            f"Stage Objective: {current_stage['objective']}\n\n"
            f"CURRENT TASK ({self.current_task_index + 1}/{len(current_stage['tasks'])}): {current_task['task']}\n"
            f"Task Examples:\n"
        )

        for example in current_task['examples']:
            stage_prompt += f"- {example}\n"

        # Get the real task ID from config
        task_id = current_task.get('id', f"{current_stage['id']}_task_{self.current_task_index}")

        # Add special instructions for pricing task
        pricing_instructions = ""
        if task_id == "demonstration_pricing_interest":
            pricing_data = self.config["company_config"]["context"]["pricing"]
            pricing_instructions = (
                f"\nSPECIAL PRICING TASK INSTRUCTIONS:\n"
                f"If the user says YES to seeing prices, provide ALL pricing options in your response:\n\n"
                f"Small Business Package:\n"
                f"- Setup: {pricing_data['small_business']['setup']}\n"
                f"- Monthly: {pricing_data['small_business']['monthly']}\n"
                f"- Perfect for restaurants, cafes, small retail stores\n\n"
                f"Medium Business Package:\n"
                f"- Setup: {pricing_data['medium_business']['setup']}\n"
                f"- Monthly: {pricing_data['medium_business']['monthly']}\n"
                f"- Ideal for multi-location businesses, service companies\n\n"
                f"Enterprise Package:\n"
                f"- Setup: {pricing_data['enterprise']['setup']}\n"
                f"- Monthly: {pricing_data['enterprise']['monthly']}\n"
                f"- Designed for large corporations, extensive customization\n\n"
                f"Then ask: 'Which package suits your business best?'\n"
            )

        stage_prompt += (
            f"\nIMPORTANT RULES:\n"
            f"1. Use easy to understand english and Ask exactly ONE question per message\n"
            f"2. Focus ONLY on the current task: {current_task['task']}\n"
            f"3. Use the examples above as guidance for your response style\n"
            f"4. Do NOT combine tasks and follow the sequence numbering of tasks\n"
            f"5. Wait for the user to respond before moving to the next task\n"
            f"6. Current task ID: {task_id}\n"
            f"7. This is task {self.current_task_index + 1} of {len(current_stage['tasks'])} in the {current_stage['id']} stage\n"
            f"{pricing_instructions}"
        )

        # Add objection handling if available
        obj_prompt = ""
        if self.config.get("objection_handling"):
            obj_prompt = (
                "\n\n--- OBJECTION HANDLING FRAMEWORK ---\n"
                "If the prospect raises an objection, use Steve Schiffman's 4-step framework:\n"
                "1. Acknowledge and Cushion\n2. Question to Clarify\n3. Answer the Real Objection\n4. Confirm and Close\n"
                + json.dumps(self.config["objection_handling"]["common_objections"], indent=2)
            )

        # Add referral handling if available
        ref_prompt = ""
        if self.config.get("referral_handling"):
            ref_prompt = (
                "\n\n--- REFERRAL HANDLING ---\n"
                + json.dumps(self.config["referral_handling"], indent=2)
            )

        return f"{base_prompt}{context_prompt}{stage_prompt}{obj_prompt}{ref_prompt}"

    def _format_message(self, template: str) -> str:
        """Format message template with prospect information."""
        company = self.config["company_config"]["company"]
        agent = self.config["company_config"]["agent_name"]

        formatted = (
            template.replace("{{name}}", self.prospect_name)
            .replace("{{agent_name}}", agent)
            .replace("{{company}}", company)
        )
        return formatted

    # ------------------------------------------------------------------
    # Public interface
    # ------------------------------------------------------------------

    def start_conversation(self, prospect_name: str = "there") -> str:
        """Return the first assistant message based on the opening stage."""
        self.prospect_name = prospect_name

        # Get the first task of the first stage
        stage0 = self.config["sales_stages"][0]
        first_task = stage0["tasks"][0]
        first_example = first_task["examples"][0]

        formatted = self._format_message(first_example)
        self.chat_history.append({"role": "assistant", "content": formatted})

        # We're now waiting for user response to the opening question
        self.waiting_for_user_response = True

        return formatted

    def chat(self, user_message: str) -> str:
        """Process a user message and return the assistant reply."""
        self.chat_history.append({"role": "user", "content": user_message})

        # Check if we need to advance to next task based on user response
        current_stage = self.config["sales_stages"][self.current_stage_index]
        current_task = current_stage["tasks"][self.current_task_index]
        task_id = current_task.get('id', f"{current_stage['id']}_task_{self.current_task_index}")

        # If we were waiting for user response and got one, advance the task
        if self.waiting_for_user_response and task_id not in self.completed_tasks:
            # Mark current task as complete
            self.completed_tasks.append(task_id)
            self.waiting_for_user_response = False

            # Check if we should advance to next task or next stage
            if self.current_task_index < len(current_stage["tasks"]) - 1:
                # Move to next task in current stage
                self.current_task_index += 1
            else:
                # All tasks in current stage are complete, move to next stage
                stage_id = current_stage["id"]
                if stage_id not in self.completed_stages:
                    self.completed_stages.append(stage_id)

                if self.current_stage_index < len(self.config["sales_stages"]) - 1:
                    self.current_stage_index += 1
                    self.current_task_index = 0

        # Build system prompt for current task
        system_prompt = self._build_system_prompt()
        messages = [{"role": "system", "content": system_prompt}] + self.chat_history

        ai_response = chat_completion(messages)

        # After bot responds with a question, we're waiting for user response
        if "?" in ai_response:
            self.waiting_for_user_response = True

        self.chat_history.append({"role": "assistant", "content": ai_response})
        return ai_response

    # Convenience getters ---------------------------------------------------

    @property
    def current_stage(self) -> str:
        return self.config["sales_stages"][self.current_stage_index]["id"]

    @property
    def current_task(self) -> str:
        current_stage = self.config["sales_stages"][self.current_stage_index]
        return current_stage["tasks"][self.current_task_index]["task"]

    @property
    def stage_progress(self) -> str:
        current_stage = self.config["sales_stages"][self.current_stage_index]
        return f"{self.current_task_index + 1}/{len(current_stage['tasks'])}"

    @property
    def completed_tasks_info(self) -> str:
        return f"Completed tasks: {len(self.completed_tasks)} | Tasks: {self.completed_tasks}"

    @property
    def is_finished(self) -> bool:
        return self.current_stage_index >= len(self.config["sales_stages"]) - 1 and (
            self.config["sales_stages"][-1]["id"] in self.completed_stages
        )
