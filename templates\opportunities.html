{% extends "base.html" %}

{% block title %}Opportunities - 24Seven Assistants{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-bullseye text-primary"></i> Sales Opportunities</h1>
    <div>
        <button class="btn btn-success" onclick="createOpportunity()">
            <i class="fas fa-plus"></i> Create Opportunity
        </button>
        <button class="btn btn-outline-secondary" onclick="exportOpportunities()">
            <i class="fas fa-download"></i> Export
        </button>
    </div>
</div>

<!-- Opportunity Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="metric-value">{{ opportunities.total if opportunities else 0 }}</div>
                <div class="metric-label">Total Opportunities</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="metric-value">
                    {% if opportunities %}
                        {{ opportunities.items|selectattr('status', 'equalto', 'open')|list|length }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="metric-label">Open</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="metric-value">
                    {% if opportunities %}
                        {{ opportunities.items|selectattr('status', 'equalto', 'won')|list|length }}
                    {% else %}
                        0
                    {% endif %}
                </div>
                <div class="metric-label">Won</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="metric-value">
                    {% if opportunities %}
                        ${{ "%.0f"|format(opportunities.items|selectattr('status', 'equalto', 'open')|map(attribute='estimated_value')|sum) }}
                    {% else %}
                        $0
                    {% endif %}
                </div>
                <div class="metric-label">Pipeline Value</div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Stage Pipeline -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-funnel-dollar"></i> Sales Pipeline by Stage</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-door-open fa-2x text-primary mb-2"></i>
                            <h6>Opening</h6>
                            <span class="badge bg-primary">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('current_stage.name', 'equalto', 'Opening')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-handshake fa-2x text-success mb-2"></i>
                            <h6>Trust</h6>
                            <span class="badge bg-success">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('current_stage.name', 'equalto', 'Trust')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-search fa-2x text-info mb-2"></i>
                            <h6>Discovery</h6>
                            <span class="badge bg-info">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('current_stage.name', 'equalto', 'Discovery')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-presentation fa-2x text-warning mb-2"></i>
                            <h6>Demonstration</h6>
                            <span class="badge bg-warning">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('current_stage.name', 'equalto', 'Demonstration')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-light rounded">
                            <i class="fas fa-trophy fa-2x text-danger mb-2"></i>
                            <h6>Close</h6>
                            <span class="badge bg-danger">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('current_stage.name', 'equalto', 'Close')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="p-3 bg-success text-white rounded">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h6>Won</h6>
                            <span class="badge bg-light text-dark">
                                {% if opportunities %}
                                    {{ opportunities.items|selectattr('status', 'equalto', 'won')|list|length }}
                                {% else %}
                                    0
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Opportunities Table -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Opportunities List
                    {% if opportunities and opportunities.total %}
                        <span class="badge bg-primary ms-2">{{ opportunities.total }} total</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if opportunities and opportunities.items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Opportunity</th>
                                    <th>Contact</th>
                                    <th>Stage</th>
                                    <th>Value</th>
                                    <th>Probability</th>
                                    <th>Expected Close</th>
                                    <th>Status</th>
                                    <th>Days in Stage</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for opportunity in opportunities.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ opportunity.name }}</strong>
                                            {% if opportunity.description %}
                                                <small class="text-muted d-block">{{ opportunity.description[:50] }}{% if opportunity.description|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                            {% if opportunity.source %}
                                                <span class="badge bg-light text-dark">{{ opportunity.source }}</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if opportunity.contact %}
                                            <div>
                                                <strong>{{ opportunity.contact.full_name }}</strong>
                                                <small class="text-muted d-block">{{ opportunity.contact.email }}</small>
                                                {% if opportunity.contact.company %}
                                                    <small class="text-muted d-block">{{ opportunity.contact.company }}</small>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <span class="text-muted">No contact</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if opportunity.current_stage %}
                                            <span class="badge bg-primary">{{ opportunity.current_stage.name }}</span>
                                            <small class="text-muted d-block">Order: {{ opportunity.current_stage.order }}</small>
                                        {% else %}
                                            <span class="badge bg-secondary">No stage</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>${{ "%.0f"|format(opportunity.estimated_value) }}</strong>
                                        {% if opportunity.actual_value %}
                                            <small class="text-success d-block">Actual: ${{ "%.0f"|format(opportunity.actual_value) }}</small>
                                        {% endif %}
                                        <small class="text-muted d-block">{{ opportunity.currency }}</small>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar
                                                {% if opportunity.probability_percent >= 75 %}bg-success
                                                {% elif opportunity.probability_percent >= 50 %}bg-info
                                                {% elif opportunity.probability_percent >= 25 %}bg-warning
                                                {% else %}bg-secondary{% endif %}"
                                                role="progressbar"
                                                style="width: {{ opportunity.probability_percent }}%"
                                                title="{{ opportunity.probability_percent }}%">
                                                {{ opportunity.probability_percent }}%
                                            </div>
                                        </div>
                                        <small class="text-muted">Weighted: ${{ "%.0f"|format(opportunity.estimated_value * opportunity.probability_percent / 100) }}</small>
                                    </td>
                                    <td>
                                        {% if opportunity.expected_close_date %}
                                            {{ opportunity.expected_close_date.strftime('%m/%d/%Y') }}
                                            <small class="text-muted d-block">Expected close date</small>
                                        {% else %}
                                            <span class="text-muted">Not set</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ opportunity.status }}">
                                            {{ opportunity.status.title() }}
                                        </span>
                                        {% if opportunity.lost_reason and opportunity.status == 'lost' %}
                                            <small class="text-muted d-block">{{ opportunity.lost_reason }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if opportunity.days_in_current_stage > 14 %}bg-danger
                                            {% elif opportunity.days_in_current_stage > 7 %}bg-warning
                                            {% else %}bg-info{% endif %}">
                                            {{ opportunity.days_in_current_stage }} days
                                        </span>
                                        {% if opportunity.last_activity_at %}
                                            <small class="text-muted d-block">
                                                Last activity: {{ opportunity.last_activity_at.strftime('%m/%d') }}
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="viewOpportunity({{ opportunity.id }})" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="moveStage({{ opportunity.id }})" title="Move Stage">
                                                <i class="fas fa-arrow-right"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="addActivity({{ opportunity.id }})" title="Add Activity">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if opportunities.pages > 1 %}
                    <nav aria-label="Opportunities pagination">
                        <ul class="pagination justify-content-center">
                            {% if opportunities.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('opportunities_list', page=opportunities.prev_num) }}">Previous</a>
                                </li>
                            {% endif %}

                            {% for page_num in opportunities.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != opportunities.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('opportunities_list', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if opportunities.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('opportunities_list', page=opportunities.next_num) }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-bullseye fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Opportunities Found</h4>
                        <p class="text-muted">Start tracking your sales opportunities to manage your pipeline effectively.</p>
                        <button class="btn btn-primary" onclick="createOpportunity()">
                            <i class="fas fa-plus"></i> Create Your First Opportunity
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
</div>

<!-- Create Opportunity Modal -->
<div class="modal fade" id="opportunityModal" tabindex="-1" aria-labelledby="opportunityModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="opportunityModalLabel"><i class="fas fa-plus"></i> Create Opportunity</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="opportunityForm">
          <div class="mb-3">
            <label class="form-label">Opportunity Name<span class="text-danger">*</span></label>
            <input type="text" class="form-control" name="name" required>
          </div>
          <div class="mb-3">
            <label class="form-label">Description</label>
            <textarea class="form-control" name="description" rows="3"></textarea>
          </div>
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Contact Email</label>
              <input type="email" class="form-control" name="contact_email">
            </div>
            <div class="col-md-6">
              <label class="form-label">Estimated Value ($)</label>
              <input type="number" min="0" class="form-control" name="estimated_value" required>
            </div>
            <div class="col-md-6">
              <label class="form-label">Expected Close Date</label>
              <input type="date" class="form-control" name="expected_close_date">
            </div>
            <div class="col-md-6">
              <label class="form-label">Stage</label>
              <select class="form-select" name="stage">
                <option value="Opening">Opening</option>
                <option value="Trust">Trust</option>
                <option value="Discovery">Discovery</option>
                <option value="Demonstration">Demonstration</option>
                <option value="Close">Close</option>
                <option value="Won">Won</option>
              </select>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveOpportunityBtn"><i class="fas fa-save"></i> Save</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function createOpportunity() {
        const modal = new bootstrap.Modal(document.getElementById('opportunityModal'));
        document.getElementById('opportunityForm').reset();
        if (typeof $ !== 'undefined' && $.fn.selectpicker) {
            $('#opportunityModal').find('select').selectpicker('refresh');
        }
        modal.show();
    }

    function viewOpportunity(opportunityId) {
        // In a real implementation, this would show opportunity details
        alert('Opportunity details for ID: ' + opportunityId + '\n\nThis would show:\n- Full opportunity information\n- Stage history\n- Activities timeline\n- Contact details\n- Notes and attachments');
    }

    function moveStage(opportunityId) {
        // In a real implementation, this would show stage selection modal
        const stages = ['Opening', 'Trust', 'Discovery', 'Demonstration', 'Close'];
        const newStage = prompt('Move to stage:\n' + stages.map((s, i) => (i+1) + '. ' + s).join('\n') + '\n\nEnter stage number (1-5):');

        if (newStage && newStage >= 1 && newStage <= 5) {
            alert('Opportunity ' + opportunityId + ' moved to ' + stages[newStage-1] + ' stage');
            location.reload();
        }
    }

    function addActivity(opportunityId) {
        // In a real implementation, this would open activity creation form
        const activityTypes = ['Email', 'Call', 'Meeting', 'Note'];
        const type = prompt('Add activity:\n' + activityTypes.map((t, i) => (i+1) + '. ' + t).join('\n') + '\n\nEnter activity type number (1-4):');

        if (type && type >= 1 && type <= 4) {
            const subject = prompt('Activity subject:');
            if (subject) {
                alert('Activity "' + subject + '" added to opportunity ' + opportunityId);
                location.reload();
            }
        }
    }

    function exportOpportunities() {
        // In a real implementation, this would export opportunities to CSV/Excel
        alert('Export functionality would be implemented here.\n\nThis would generate a CSV/Excel file with:\n- All opportunity data\n- Contact information\n- Stage history\n- Performance metrics');
    }

    // Add moment.js functionality for date calculations
    function moment() {
        return {
            date: function() {
                return new Date();
            }
        };
    }

    // Save button handler
    document.getElementById('saveOpportunityBtn').addEventListener('click', function () {
        const form = document.getElementById('opportunityForm');
        if (!form.reportValidity()) return;
        const data = Object.fromEntries(new FormData(form).entries());
        fetch('/api/opportunities', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        }).then(r => r.json()).then(resp => {
            if (resp.success) {
                window.location.reload();
            } else {
                alert(resp.message || 'Failed to create opportunity');
            }
        }).catch(err => { console.error(err); alert('Error creating opportunity'); });
    });
</script>
{% endblock %}
