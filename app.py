"""
24Seven Assistants Sales Bot Application
========================================
This is the main application file that creates a Gradio UI for the sales bot.
The bot follows the JSON configuration rules from original.md and properly tracks
stages and tasks throughout the sales conversation.

Features:
- Stage-by-stage progression following JSON rules
- Task tracking within each stage
- Objection handling using <PERSON>'s framework
- Referral handling for internal and external referrals
- Real-time stage and task progress display
"""

import os
import json
import requests  # Added for chat event tracking
from typing import List, Tuple, Optional

import gradio as gr
from salesbot.bot import SalesBot
from salesbot.config_data import CONFIG_DATA

# ---------------------------------------------------------------------------
# 1. Configuration & Credentials
# ---------------------------------------------------------------------------

# NOTE: In production you should **not** hard-code secrets.
# The application now supports Google Gemini as well as SambaNova.
# Priority: GEMINI_* env vars > SAMBA_* env vars > demo defaults.

_GEMINI_KEY = os.getenv("GEMINI_API_KEY")
_GEMINI_BASE = os.getenv("GEMINI_BASE_URL", "https://generativelanguage.googleapis.com/v1beta/openai")
_GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.5-flash-lite-preview-06-17")

if _GEMINI_KEY:
    API_KEY: str = _GEMINI_KEY
    BASE_URL: str = _GEMINI_BASE
    MODEL: str = _GEMINI_MODEL
else:
    API_KEY: str = os.getenv("SAMBA_API_KEY", "76273dc7-7c75-417f-8cfe-ef88ad56db78")
    BASE_URL: str = os.getenv("SAMBA_BASE_URL", "https://api.sambanova.ai/v1")
    MODEL: str = os.getenv("SAMBA_MODEL", "Meta-Llama-3.3-70B-Instruct")

if not API_KEY:
    raise RuntimeError(
        "API key missing. Set GEMINI_API_KEY or SAMBA_API_KEY environment variable."
    )

# ---------------------------------------------------------------------------
# 2. Helper Functions
# ---------------------------------------------------------------------------

def format_stage_info(bot: SalesBot) -> str:
    """Format current stage and task information for display."""
    if bot is None:
        return "**Status:** Not started"

    stage_name = bot.current_stage.title()
    task_name = bot.current_task
    progress = bot.stage_progress
    completed_info = bot.completed_tasks_info
    waiting_status = "Waiting for response" if bot.waiting_for_user_response else "Ready to respond"

    return (
        f"**Current Stage:** {stage_name} ({bot.current_stage_index + 1}/5)\n"
        f"**Current Task:** {task_name}\n"
        f"**Task Progress:** {progress}\n"
        f"**Status:** {waiting_status}\n"
        f"**Debug:** {completed_info}"
    )

def format_conversation_log(history: List[List[str]]) -> str:
    """Format conversation history for the raw log display."""
    if not history:
        return ""

    log_entries = []
    for h in history:
        if h[0] is not None:  # User message exists
            log_entries.append(f"USER: {h[0]}")
        log_entries.append(f"SARAH: {h[1]}")

    return "\n\n".join(log_entries)

def format_conversation_log_from_history(history: List[dict]) -> str:
    """Format conversation history from API response for the raw log display."""
    if not history:
        return ""

    log_entries = []
    for msg in history:
        role = "USER" if msg["role"] == "user" else "SARAH"
        log_entries.append(f"{role}: {msg['content']}")

    return "\n\n".join(log_entries)

# ---------------------------------------------------------------------------
# 3. Gradio UI
# ---------------------------------------------------------------------------

def build_interface() -> gr.Blocks:
    """Build the main Gradio interface."""

    with gr.Blocks(
        theme=gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="purple",
            neutral_hue="slate",
            font=gr.themes.GoogleFont("Inter")
        ),
        title="24Seven Assistants Sales Bot - Chat with Sarah",
        css="""
        /* Enhanced Modern Design Variables */
        :root {
            --chat-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --message-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-soft: 0 10px 25px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 20px 40px rgba(0, 0, 0, 0.15);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Main container enhancements */
        .gradio-container {
            background: var(--chat-gradient) !important;
            min-height: 100vh;
        }

        /* Enhanced header styling */
        .prose h1 {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            color: white !important;
            padding: 20px 30px !important;
            border-radius: var(--border-radius) !important;
            border: 1px solid var(--glass-border) !important;
            box-shadow: var(--shadow-soft) !important;
            text-align: center !important;
            margin-bottom: 20px !important;
            font-weight: 600 !important;
        }

        /* Enhanced stage info panel */
        .prose.stage-info {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            color: white !important;
            padding: 20px !important;
            border-radius: var(--border-radius) !important;
            margin: 15px 0 !important;
            border: 1px solid var(--glass-border) !important;
            box-shadow: var(--shadow-soft) !important;
            transition: var(--transition) !important;
        }

        .prose.stage-info:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-medium) !important;
        }

        /* Enhanced chat container */
        .chatbot {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius) !important;
            box-shadow: var(--shadow-soft) !important;
            max-height: 500px !important;
            overflow-y: auto !important;
        }

        /* Enhanced message styling */
        .message {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 18px !important;
            padding: 12px 16px !important;
            margin: 8px !important;
            box-shadow: var(--shadow-soft) !important;
            transition: var(--transition) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .message:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-medium) !important;
        }

        .message.user {
            background: var(--message-gradient) !important;
            color: white !important;
            margin-left: auto !important;
            max-width: 75% !important;
        }

        .message.bot {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #2d3748 !important;
            margin-right: auto !important;
            max-width: 75% !important;
        }

        /* Enhanced input styling */
        .textbox input, .textbox textarea {
            background: rgba(255, 255, 255, 0.9) !important;
            border: 2px solid transparent !important;
            border-radius: 25px !important;
            padding: 12px 20px !important;
            transition: var(--transition) !important;
            font-size: 0.95rem !important;
        }

        .textbox input:focus, .textbox textarea:focus {
            border-color: rgba(255, 255, 255, 0.5) !important;
            background: white !important;
            box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1) !important;
            outline: none !important;
        }

        /* Enhanced button styling */
        .btn {
            border-radius: 25px !important;
            padding: 12px 24px !important;
            font-weight: 500 !important;
            transition: var(--transition) !important;
            border: none !important;
            box-shadow: var(--shadow-soft) !important;
        }

        .btn-primary {
            background: var(--success-gradient) !important;
            color: white !important;
        }

        .btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-medium) !important;
        }

        .btn-secondary {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            color: white !important;
            border: 1px solid var(--glass-border) !important;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            transform: translateY(-2px) !important;
        }

        /* Enhanced panels */
        .panel {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius) !important;
            box-shadow: var(--shadow-soft) !important;
            padding: 20px !important;
            margin: 15px 0 !important;
        }

        /* Enhanced accordion */
        .accordion {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius) !important;
            box-shadow: var(--shadow-soft) !important;
        }

        /* Enhanced markdown content */
        .prose {
            background-color: transparent !important;
            color: white !important;
        }

        .prose p, .prose h2, .prose h3, .prose strong, .prose li {
            color: white !important;
        }

        .prose h2 {
            color: white !important;
            font-weight: 600 !important;
            margin-bottom: 15px !important;
        }

        /* Enhanced scrollbars */
        .chatbot::-webkit-scrollbar {
            width: 6px;
        }

        .chatbot::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .chatbot::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .chatbot::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Enhanced form styling */
        .form {
            background: var(--glass-bg) !important;
            backdrop-filter: blur(20px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius) !important;
            padding: 20px !important;
            box-shadow: var(--shadow-soft) !important;
        }

        /* Enhanced labels */
        label {
            color: white !important;
            font-weight: 500 !important;
            margin-bottom: 8px !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .prose h1 {
                padding: 15px 20px !important;
                font-size: 1.5rem !important;
            }

            .panel {
                padding: 15px !important;
                margin: 10px 0 !important;
            }

            .btn {
                padding: 10px 20px !important;
            }
        }

        /* Animation enhancements */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message {
            animation: slideIn 0.3s ease-out !important;
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus styles for accessibility */
        .btn:focus-visible,
        .textbox input:focus-visible,
        .textbox textarea:focus-visible {
            outline: 2px solid rgba(255, 255, 255, 0.8) !important;
            outline-offset: 2px !important;
        }
        """
    ) as demo:

        # Header
        gr.Markdown("# 🤖 24Seven Assistants – Sales Bot")
        gr.Markdown(
            "**Meet Sarah, your AI sales agent!** She'll guide you through our 5-stage sales process: "
            "Opening → Trust → Discovery → Demonstration → Closing. "
            "Each stage follows specific tasks and objectives from our proven sales methodology."
        )

        # State variables
        bot_state = gr.State(None)  # Will hold the SalesBot instance

        # Status display
        with gr.Row():
            with gr.Column(scale=2):
                stage_info = gr.Markdown("**Status:** Ready to start", elem_classes=["stage-info"])
            with gr.Column(scale=1):
                reset_btn = gr.Button("🔄 Reset Chat", variant="secondary", size="sm")

        # Hidden session ID field for email campaign tracking
        session_id_input = gr.Textbox(visible=False, value="")

        # Startup UI – ask prospect name
        with gr.Row(variant="panel") as startup_row:
            with gr.Column(scale=3):
                name_box = gr.Textbox(
                    label="What is your name?",
                    placeholder="e.g. Alex Johnson",
                    info="Sarah will use your name to personalize the conversation"
                )
            with gr.Column(scale=1):
                start_btn = gr.Button("▶️ Start Chat", variant="primary", size="lg")

        # Chat UI (initially hidden)
        with gr.Column(visible=False) as chat_column:
            chatbot = gr.Chatbot(
                label="Conversation with Sarah",
                height=400,
                type="messages",
                elem_classes=["chat-container"]
            )

            with gr.Row():
                msg_input = gr.Textbox(
                    label="Your Message",
                    placeholder="Type your message here and press Enter...",
                    scale=4
                )
                send_btn = gr.Button("Send", variant="primary", scale=1)

        # Debug/Admin panel (collapsible)
        with gr.Accordion("📊 Sales Process Details", open=False):
            with gr.Row():
                with gr.Column():
                    gr.Markdown("### Sales Stages Overview")
                    stages_info = gr.Markdown("""
                    **1. Opening:** Introduction and permission request
                    **2. Trust:** Build rapport with success stories
                    **3. Discovery:** Understand challenges and needs (3 tasks)
                    **4. Demonstration:** Connect product to pain points (3 tasks)
                    **5. Closing:** Finalize sale and collect contact details
                    """)

                with gr.Column():
                    gr.Markdown("### Conversation Log")
                    history_box = gr.Textbox(
                        label="Raw Message Log",
                        value="",
                        interactive=False,
                        lines=8,
                        max_lines=15
                    )

            with gr.Column():
                gr.Markdown("### Conversation Log")
                history_box = gr.Textbox(
                    label="Raw Message Log",
                    value="",
                    interactive=False,
                    lines=8,
                    max_lines=15
                )

    # ------------------------------------------------------------------
    # Event callbacks
    # ------------------------------------------------------------------

    import requests

    def start_chat(user_name: str, session_id_from_url: str = "") -> Tuple:
        """Initialize a new chat session with unified tracking, persistence, and event tracking."""
        if not user_name.strip():
            gr.Warning("Please enter your name to start the chat.")
            return (
                None,
                [],
                gr.update(visible=True),
                gr.update(visible=False),
                "**Status:** Please enter your name",
                ""
            )

        # Use session ID from URL if available, otherwise generate new one
        if session_id_from_url and session_id_from_url.strip():
            session_id = session_id_from_url.strip()
            print(f"🔗 Using session ID from email campaign: {session_id}")
        else:
            import uuid
            session_id = str(uuid.uuid4())
            print(f"🆕 Generated new session ID: {session_id}")

        # Check if there's existing chat history for this session
        existing_history = []
        try:
            response = requests.get(f'http://localhost:5000/api/get-chat-history/{session_id}')
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('history'):
                    existing_history = result['history']
                    print(f"📚 Found {len(existing_history)} existing messages for session {session_id}")
        except Exception as e:
            print(f"⚠️ Could not load chat history: {e}")

        # Create new bot instance
        bot = SalesBot(CONFIG_DATA, user_name=user_name, session_id=session_id)

        # Track chat_open event
        try:
            requests.post('http://localhost:5000/track/chat_event', json={
                'session_id': session_id,
                'contact_id': '',  # Fill if you have contact mapping client-side
                'event_type': 'chat_open'
            })
        except Exception as e:
            print(f"⚠️ Could not send chat_open event: {e}")

        # Store session ID in bot for tracking
        bot.session_id = session_id

        # If we have existing history, restore the bot state
        if existing_history:
            # Restore chat history to bot
            bot.chat_history = []
            for msg in existing_history:
                bot.chat_history.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

            # Restore bot state from last message
            if existing_history:
                last_msg = existing_history[-1]
                if last_msg.get('stage'):
                    # Find stage index
                    for i, stage in enumerate(bot.config["sales_stages"]):
                        if stage["id"] == last_msg['stage']:
                            bot.current_stage_index = i
                            break
                bot.chat_history = []
                for msg in existing_history:
                    bot.chat_history.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

                # Restore bot state from last message
                if existing_history:
                    last_msg = existing_history[-1]
                    if last_msg.get('stage'):
                        # Find stage index
                        for i, stage in enumerate(bot.config["sales_stages"]):
                            if stage["id"] == last_msg['stage']:
                                bot.current_stage_index = i
                                break

                # Return existing conversation
                return (
                    bot,
                    existing_history,
                    gr.update(visible=False),  # Hide startup row
                    gr.update(visible=True),   # Show chat column
                    format_stage_info(bot) + f"\n**Session ID:** {session_id[:8]}... (Restored)",
                    format_conversation_log_from_history(existing_history)
                )

            # Start new conversation
            first_msg = bot.start_conversation(user_name.strip())

            # Save the first message to database
            try:
                import requests
                save_data = {
                    'session_id': session_id,
                    'message_type': 'assistant',
                    'content': first_msg,
                    'stage': bot.current_stage,
                    'task': bot.current_task,
                    'message_order': 0
                }
                requests.post('http://localhost:5000/api/save-chat-message', json=save_data)
                print(f"💾 Saved first message to database for session {session_id}")
            except Exception as e:
                print(f"⚠️ Could not save first message: {e}")

            # Track session start in unified system with enhanced data
            try:
                import requests

                # If this is from an email campaign, the contact already exists
                # Just update the session to show conversation started
                tracking_data = {
                    'session_id': session_id,
                    'stage': 'opening',
                    'task': 'greeting',
                    'contact_name': user_name.strip(),
                    'contact_email': None,  # Will be collected later
                    'action': 'conversation_started',
                    'message_count': 1,
                    'bot_response': first_msg
                }

                # If session ID came from URL, this is from email campaign
                if session_id_from_url and session_id_from_url.strip():
                    tracking_data['from_email_campaign'] = True
                    print(f"🔗 Tracking email campaign session: {session_id}")

                response = requests.post('http://localhost:5000/api/track-session', json=tracking_data)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Conversation started tracking successful for session {session_id}")
                    print(f"   Contact ID: {result.get('contact_id', 'Unknown')}")
                    print(f"   Stage: {result.get('stage', 'Unknown')}")
                    if session_id_from_url:
                        print(f"   📧 Email Campaign Session Linked!")
                else:
                    print(f"⚠️ Conversation tracking failed: {response.status_code}")
            except Exception as e:
                print(f"⚠️ Conversation tracking error: {e}")

            # Store session info for later tracking
            bot.session_tracking = {
                'session_id': session_id,
                'conversation_started': True,
                'stages_completed': ['opening']
            }

            # Format for new message format
            history = [{"role": "assistant", "content": first_msg}]

            return (
                bot,
                history,
                gr.update(visible=False),  # Hide startup row
                gr.update(visible=True),   # Show chat column
                format_stage_info(bot) + f"\n**Session ID:** {session_id[:8]}...",
                format_conversation_log([[None, first_msg]])
            )

        def chat_with_bot(user_msg: str, history: List[dict], bot: SalesBot) -> Tuple:
            """Process user message and get bot response with enhanced unified tracking and persistence."""
            if bot is None:
                gr.Warning("Please start a new chat session.")
                return "", history, bot, format_stage_info(None), ""

            if not user_msg.strip():
                gr.Warning("Please enter a message.")
                return "", history, bot, format_stage_info(bot), ""

            # Store previous stage for comparison
            previous_stage = bot.current_stage

            # Get bot response
            ai_msg = bot.chat(user_msg.strip())

            # Save both messages to database
            try:
                import requests
                session_id = getattr(bot, 'session_id', 'unknown')

                # Calculate message order
                user_message_order = len(history) + 1
                bot_message_order = len(history) + 2

                # Save user message
                user_save_data = {
                    'session_id': session_id,
                    'message_type': 'user',
                    'content': user_msg.strip(),
                    'stage': bot.current_stage,
                    'task': bot.current_task,
                    'message_order': user_message_order
                }
                requests.post('http://localhost:5000/api/save-chat-message', json=user_save_data)

                # Save bot response
                bot_save_data = {
                    'session_id': session_id,
                    'message_type': 'assistant',
                    'content': ai_msg,
                    'stage': bot.current_stage,
                    'task': bot.current_task,
                    'message_order': bot_message_order
                }
                requests.post('http://localhost:5000/api/save-chat-message', json=bot_save_data)

                print(f"💾 Saved messages to database for session {session_id}")
            except Exception as e:
                print(f"⚠️ Could not save messages: {e}")

            # Enhanced tracking with detailed information
            try:
                import requests

                # Extract email if in close stage
                contact_email = None
                if bot.current_stage == 'close' and '@' in user_msg:
                    import re
                    email_match = re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', user_msg)
                    if email_match:
                        contact_email = email_match.group()

                # Determine action type
                action = 'message_exchange'
                if bot.current_stage != previous_stage:
                    action = 'stage_progression'
                elif 'not interested' in user_msg.lower() or 'too expensive' in user_msg.lower() or 'no thanks' in user_msg.lower():
                    action = 'objection_handled'

                # Calculate message count
                message_count = len(history) + 2  # +2 for current exchange

                tracking_data = {
                    'session_id': getattr(bot, 'session_id', 'unknown'),
                    'stage': bot.current_stage,
                    'task': bot.current_task,
                    'contact_email': contact_email,
                    'action': action,
                    'message_count': message_count,
                    'user_message': user_msg,
                    'bot_response': ai_msg,
                    'conversion_completed': bot.current_stage == 'close' and contact_email is not None
                }

                response = requests.post('http://localhost:5000/api/track-session', json=tracking_data)
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Enhanced tracking successful: {action} - {bot.current_stage}")
                    print(f"   Engagement: {result.get('engagement_level', 'unknown')}, Messages: {result.get('total_messages', 0)}")
                else:
                    print(f"⚠️ Enhanced tracking failed: {response.status_code}")
            except Exception as e:
                print(f"⚠️ Enhanced tracking error: {e}")
                pass  # Continue even if tracking fails

            # Update history with new message format
            history.append({"role": "user", "content": user_msg})
            history.append({"role": "assistant", "content": ai_msg})

            # Format for raw log (convert back to old format for logging)
            log_history = []
            for i in range(0, len(history), 2):
                if i + 1 < len(history):
                    user_content = history[i]["content"] if history[i]["role"] == "user" else None
                    assistant_content = history[i + 1]["content"] if history[i + 1]["role"] == "assistant" else history[i]["content"]
                    log_history.append([user_content, assistant_content])
                else:
                    # Handle case where we have an odd number of messages
                    log_history.append([None, history[i]["content"]])

            # Enhanced status info
            status_info = format_stage_info(bot)
            if bot.current_stage != previous_stage:
                status_info += f"\n🎯 **Stage Changed:** {previous_stage} → {bot.current_stage}"

            return (
                "",  # Clear input
                history,
                bot,
                status_info,
                format_conversation_log(log_history)
            )

        def reset_chat(bot_state) -> Tuple:
            """Reset the chat to initial state and clear database."""
            # Clear database if we have a session ID
            if bot_state and hasattr(bot_state, 'session_id'):
                try:
                    import requests
                    reset_data = {'session_id': bot_state.session_id}
                    response = requests.post('http://localhost:5000/api/reset-chat-session', json=reset_data)
                    if response.status_code == 200:
                        print(f"🗑️ Chat session {bot_state.session_id} reset in database")
                    else:
                        print(f"⚠️ Failed to reset session in database: {response.status_code}")
                except Exception as e:
                    print(f"⚠️ Could not reset session in database: {e}")

            return (
                None,  # Clear bot state
                [],    # Clear history
                gr.update(visible=True),   # Show startup row
                gr.update(visible=False),  # Hide chat column
                "**Status:** Ready to start",
                ""     # Clear log
            )

        # JavaScript to extract session_id from URL and populate hidden field
        demo.load(
            fn=None,
            js="""
            function() {
                const urlParams = new URLSearchParams(window.location.search);
                const sessionId = urlParams.get('session_id');
                if (sessionId) {
                    console.log('Found session_id in URL:', sessionId);
                    return sessionId;
                }
                return '';
            }
            """,
            outputs=[session_id_input]
        )

        # Wire up the events
        start_btn.click(
            start_chat,
            inputs=[name_box, session_id_input],
            outputs=[bot_state, chatbot, startup_row, chat_column, stage_info, history_box],
        )

        msg_input.submit(
            chat_with_bot,
            inputs=[msg_input, chatbot, bot_state],
            outputs=[msg_input, chatbot, bot_state, stage_info, history_box],
        )

        send_btn.click(
            chat_with_bot,
            inputs=[msg_input, chatbot, bot_state],
            outputs=[msg_input, chatbot, bot_state, stage_info, history_box],
        )

        reset_btn.click(
            reset_chat,
            inputs=[bot_state],
            outputs=[bot_state, chatbot, startup_row, chat_column, stage_info, history_box]
        )

        # Add some example interactions
        gr.Markdown("""
        ### 💡 Tips for Testing:
        - Try different responses to see how Sarah handles objections
        - Notice how she moves through each stage systematically
        - Test objections like "It's too expensive" or "I'm not interested"
        - See how she builds trust with the LocalCafe success story
        - Watch the stage progression in the status panel above
        """)

    return demo

# ---------------------------------------------------------------------------
# 4. Entrypoint
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    print("🚀 Starting 24Seven Assistants Sales Bot...")
    print(f"📡 Using SambaNova API: {BASE_URL}")
    print(f"🤖 Model: {MODEL}")
    print("=" * 50)

    ui = build_interface()

    # Launch with better configuration
    ui.launch(
        server_name="127.0.0.1",
        server_port=7862,  # Use different port to avoid conflicts
        show_error=True,
        quiet=False,
        share=True  # Create public link
    )
