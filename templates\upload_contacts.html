{% extends "base.html" %}

{% block title %}Upload Contacts - 24Seven Assistants{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-upload"></i> Upload Contacts</h2>
                <div>
                    <a href="{{ url_for('contacts_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Contacts
                    </a>
                </div>
            </div>

            <!-- Upload Form -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-upload"></i> Contact File Upload</h5>
                            <div class="btn-group btn-group-sm" role="group" aria-label="File format selection">
                                <input type="radio" class="btn-check" name="file_format" id="format_csv" value="csv" checked onchange="toggleFileFormat()">
                                <label class="btn btn-outline-primary" for="format_csv">
                                    <i class="fas fa-file-csv"></i> CSV
                                </label>

                                <input type="radio" class="btn-check" name="file_format" id="format_json" value="json" onchange="toggleFileFormat()">
                                <label class="btn btn-outline-primary" for="format_json">
                                    <i class="fas fa-file-code"></i> JSON
                                </label>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <!-- File Format Selection -->
                                <input type="hidden" name="file_format" id="selected_format" value="csv">

                                <!-- File Upload -->
                                <div class="mb-3">
                                    <label for="contact_file" class="form-label">
                                        <span id="file_label">CSV File</span> *
                                    </label>
                                    <input type="file" class="form-control" id="contact_file" name="contact_file" accept=".csv" required>
                                    <div class="form-text" id="file_help">
                                        Select a CSV file containing contact information. Maximum file size: 10MB.
                                    </div>
                                </div>

                                <!-- Group Options -->
                                <div class="mb-3">
                                    <label class="form-label">Group Assignment (Optional)</label>

                                    <!-- Create New Group -->
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="create_new_group" name="create_new_group" onchange="toggleGroupOptions()">
                                        <label class="form-check-label" for="create_new_group">
                                            Create new group for uploaded contacts
                                        </label>
                                    </div>

                                    <div id="new_group_section" style="display: none;">
                                        <div class="mb-2">
                                            <input type="text" class="form-control" id="group_name" name="group_name" placeholder="Enter group name">
                                        </div>
                                    </div>

                                    <!-- Existing Group -->
                                    {% if groups %}
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="group_option" id="existing_group_option" value="existing" onchange="toggleGroupOptions()">
                                        <label class="form-check-label" for="existing_group_option">
                                            Add to existing group
                                        </label>
                                    </div>

                                    <div id="existing_group_section" style="display: none;">
                                        <select class="form-select" id="existing_group_id" name="existing_group_id">
                                            <option value="">Select a group...</option>
                                            {% for group in groups %}
                                            <option value="{{ group.id }}">{{ group.name }} ({{ group.contact_count }} contacts)</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload"></i> Upload Contacts
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="col-md-4">
                    <!-- CSV Instructions -->
                    <div class="card" id="csv_instructions">
                        <div class="card-header">
                            <h5><i class="fas fa-file-csv"></i> CSV Format Instructions</h5>
                        </div>
                        <div class="card-body">
                            <h6>Required Columns:</h6>
                            <ul class="list-unstyled">
                                <li><strong>email</strong> - Contact email address</li>
                                <li><strong>first_name</strong> - Contact first name</li>
                            </ul>

                            <h6>Optional Columns:</h6>
                            <ul class="list-unstyled">
                                <li><strong>last_name</strong> - Contact last name</li>
                                <li><strong>phone</strong> - Phone number</li>
                                <li><strong>company</strong> - Company name</li>
                                <li><strong>job_title</strong> - Job title</li>
                                <li><strong>website</strong> - Website URL</li>
                                <li><strong>notes</strong> - Additional notes</li>
                            </ul>

                            <div class="alert alert-info">
                                <strong>Example CSV:</strong><br>
                                <code>
                                email,first_name,last_name,company<br>
                                <EMAIL>,John,Smith,Tech Corp<br>
                                <EMAIL>,Jane,Doe,Sales Inc
                                </code>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Instructions -->
                    <div class="card" id="json_instructions" style="display: none;">
                        <div class="card-header">
                            <h5><i class="fas fa-file-code"></i> JSON Format Instructions</h5>
                        </div>
                        <div class="card-body">
                            <h6>Required Fields:</h6>
                            <ul class="list-unstyled">
                                <li><strong>email</strong> - Contact email address</li>
                                <li><strong>first_name</strong> - Contact first name</li>
                            </ul>

                            <h6>Optional Fields:</h6>
                            <ul class="list-unstyled">
                                <li><strong>last_name</strong> - Contact last name</li>
                                <li><strong>phone</strong> - Phone number</li>
                                <li><strong>company</strong> - Company name</li>
                                <li><strong>job_title</strong> - Job title</li>
                                <li><strong>website</strong> - Website URL</li>
                                <li><strong>notes</strong> - Additional notes</li>
                            </ul>

                            <div class="alert alert-info">
                                <strong>Example JSON:</strong><br>
                                <code style="white-space: pre-wrap;">[
  {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Smith",
    "company": "Tech Corp",
    "phone": "******-0101"
  },
  {
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Doe",
    "company": "Sales Inc"
  }
]</code>
                            </div>
                        </div>
                    </div>

                    <!-- Common Note -->
                    <div class="alert alert-warning mt-3">
                        <strong>Note:</strong> Existing contacts with the same email will be updated with new information.
                    </div>

                    <!-- Sample Downloads -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-download"></i> Sample Files</h5>
                        </div>
                        <div class="card-body">
                            <p>Download sample files to see the correct format:</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="downloadSampleCSV()" id="download_csv_btn">
                                    <i class="fas fa-download"></i> Download Sample CSV
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="downloadSampleJSON()" id="download_json_btn" style="display: none;">
                                    <i class="fas fa-download"></i> Download Sample JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFileFormat() {
    const csvSelected = document.getElementById('format_csv').checked;
    const jsonSelected = document.getElementById('format_json').checked;

    // Update hidden field
    document.getElementById('selected_format').value = csvSelected ? 'csv' : 'json';

    // Update file input
    const fileInput = document.getElementById('contact_file');
    const fileLabel = document.getElementById('file_label');
    const fileHelp = document.getElementById('file_help');

    // Update instructions visibility
    const csvInstructions = document.getElementById('csv_instructions');
    const jsonInstructions = document.getElementById('json_instructions');
    const csvDownloadBtn = document.getElementById('download_csv_btn');
    const jsonDownloadBtn = document.getElementById('download_json_btn');

    if (csvSelected) {
        fileInput.accept = '.csv';
        fileLabel.textContent = 'CSV File';
        fileHelp.textContent = 'Select a CSV file containing contact information. Maximum file size: 10MB.';
        csvInstructions.style.display = 'block';
        jsonInstructions.style.display = 'none';
        csvDownloadBtn.style.display = 'block';
        jsonDownloadBtn.style.display = 'none';
    } else {
        fileInput.accept = '.json';
        fileLabel.textContent = 'JSON File';
        fileHelp.textContent = 'Select a JSON file containing contact information. Maximum file size: 10MB.';
        csvInstructions.style.display = 'none';
        jsonInstructions.style.display = 'block';
        csvDownloadBtn.style.display = 'none';
        jsonDownloadBtn.style.display = 'block';
    }

    // Clear file input when switching formats
    fileInput.value = '';
}

function toggleGroupOptions() {
    const createNewGroup = document.getElementById('create_new_group').checked;
    const existingGroupOption = document.getElementById('existing_group_option');
    const existingGroupChecked = existingGroupOption ? existingGroupOption.checked : false;

    const newGroupSection = document.getElementById('new_group_section');
    const existingGroupSection = document.getElementById('existing_group_section');

    if (createNewGroup) {
        newGroupSection.style.display = 'block';
        if (existingGroupOption) existingGroupOption.checked = false;
        if (existingGroupSection) existingGroupSection.style.display = 'none';
    } else {
        newGroupSection.style.display = 'none';
    }

    if (existingGroupChecked) {
        if (existingGroupSection) existingGroupSection.style.display = 'block';
        document.getElementById('create_new_group').checked = false;
        newGroupSection.style.display = 'none';
    } else if (!createNewGroup && existingGroupSection) {
        existingGroupSection.style.display = 'none';
    }
}

function downloadSampleCSV() {
    const csvContent = `email,first_name,last_name,phone,company,job_title,website,notes
<EMAIL>,John,Smith,+256-**********,Tech Solutions Inc,CEO,https://techsolutions.com,Interested in AI solutions
<EMAIL>,Sarah,Johnson,+256-**********,Marketing Pro,Marketing Director,https://marketingpro.com,Looking for automation tools
<EMAIL>,Mike,Davis,+256-**********,Sales Corp,Sales Manager,https://salescorp.com,Needs CRM integration`;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'sample_contacts.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function downloadSampleJSON() {
    const jsonContent = [
        {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Smith",
            "phone": "+256-**********",
            "company": "Tech Solutions Inc",
            "job_title": "CEO",
            "website": "https://techsolutions.com",
            "notes": "Interested in AI solutions"
        },
        {
            "email": "<EMAIL>",
            "first_name": "Sarah",
            "last_name": "Johnson",
            "phone": "+256-**********",
            "company": "Marketing Pro",
            "job_title": "Marketing Director",
            "website": "https://marketingpro.com",
            "notes": "Looking for automation tools"
        },
        {
            "email": "<EMAIL>",
            "first_name": "Mike",
            "last_name": "Davis",
            "phone": "+256-**********",
            "company": "Sales Corp",
            "job_title": "Sales Manager",
            "website": "https://salescorp.com",
            "notes": "Needs CRM integration"
        }
    ];

    const blob = new Blob([JSON.stringify(jsonContent, null, 2)], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'sample_contacts.json');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// File validation
document.getElementById('contact_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const selectedFormat = document.getElementById('selected_format').value;

    if (file) {
        if (file.size > 10 * 1024 * 1024) { // 10MB
            alert('File size must be less than 10MB');
            e.target.value = '';
            return;
        }

        const fileName = file.name.toLowerCase();
        if (selectedFormat === 'csv' && !fileName.endsWith('.csv')) {
            alert('Please select a CSV file');
            e.target.value = '';
            return;
        }

        if (selectedFormat === 'json' && !fileName.endsWith('.json')) {
            alert('Please select a JSON file');
            e.target.value = '';
            return;
        }
    }
});
</script>
{% endblock %}
