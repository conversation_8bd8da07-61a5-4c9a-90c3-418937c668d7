#!/usr/bin/env python3
"""
Test Analytics <NAME_EMAIL>
=======================================
This script tests the analytics data to show exactly what's being tracked
for <NAME_EMAIL> and their session progression.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_contact_progression():
    """Test the contact progression through stages"""
    print("🔍 TESTING CONTACT PROGRESSION")
    print("=" * 50)
    
    # Test multiple stage progressions
    session_id = "4f3b1f41-38bc-4fef-bf04-9b802c77af6"
    
    stages_to_test = [
        {
            "stage": "opening",
            "task": "greeting",
            "message_count": 3,
            "user_message": "Hello",
            "bot_response": "Hi! I'm <PERSON>, your AI sales assistant..."
        },
        {
            "stage": "trust",
            "task": "building_rapport",
            "message_count": 8,
            "user_message": "tell me more",
            "bot_response": "Let me share our LocalCafe success story..."
        },
        {
            "stage": "discovery",
            "task": "understanding_needs",
            "message_count": 15,
            "user_message": "I need automation help",
            "bot_response": "Great! Let me understand your specific needs..."
        },
        {
            "stage": "demonstration",
            "task": "showing_pricing",
            "message_count": 20,
            "user_message": "show me pricing",
            "bot_response": "Here are our pricing packages..."
        }
    ]
    
    for i, stage_data in enumerate(stages_to_test):
        print(f"\n📊 Testing Stage {i+1}: {stage_data['stage'].upper()}")
        print("-" * 30)
        
        # Track the stage
        tracking_data = {
            "session_id": session_id,
            "stage": stage_data["stage"],
            "task": stage_data["task"],
            "contact_name": "allan scof",
            "contact_email": "<EMAIL>",
            "action": "stage_progression",
            "message_count": stage_data["message_count"],
            "user_message": stage_data["user_message"],
            "bot_response": stage_data["bot_response"]
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/track-session", json=tracking_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Stage tracked successfully")
                print(f"   📝 Contact ID: {result.get('contact_id')}")
                print(f"   🎯 Current Stage: {result.get('stage')}")
                print(f"   📈 Previous Stage: {result.get('previous_stage')}")
                print(f"   💬 Total Messages: {result.get('total_messages')}")
                print(f"   📊 Engagement: {result.get('engagement_level')}")
            else:
                print(f"❌ Stage tracking failed: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"❌ Error tracking stage: {e}")

def check_analytics_visibility():
    """Check if the data appears in analytics"""
    print("\n🔍 CHECKING ANALYTICS VISIBILITY")
    print("=" * 50)
    
    # Check session analytics
    try:
        response = requests.get(f"{BASE_URL}/analytics/sessions")
        if response.status_code == 200:
            print("✅ Session analytics accessible")
            content = response.text.lower()
            
            # Check for specific indicators
            indicators = [
                ("alscof", "Contact email"),
                ("allan", "Contact name"),
                ("trust", "Trust stage"),
                ("discovery", "Discovery stage"),
                ("demonstration", "Demo stage"),
                ("medium", "Engagement level"),
                ("session", "Session data")
            ]
            
            for indicator, description in indicators:
                if indicator in content:
                    print(f"   ✅ {description} found in analytics")
                else:
                    print(f"   ⚠️ {description} not visible in analytics")
        else:
            print(f"❌ Session analytics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking session analytics: {e}")

def check_comprehensive_analytics():
    """Check comprehensive analytics"""
    print("\n🔍 CHECKING COMPREHENSIVE ANALYTICS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/analytics/comprehensive")
        if response.status_code == 200:
            print("✅ Comprehensive analytics accessible")
            content = response.text.lower()
            
            # Check for stage progression indicators
            stage_indicators = [
                ("opening", "Opening stage"),
                ("trust", "Trust stage"),
                ("discovery", "Discovery stage"),
                ("demonstration", "Demo stage"),
                ("funnel", "Sales funnel"),
                ("session", "Session tracking")
            ]
            
            for indicator, description in stage_indicators:
                if indicator in content:
                    print(f"   ✅ {description} found in comprehensive analytics")
                else:
                    print(f"   ⚠️ {description} not visible in comprehensive analytics")
        else:
            print(f"❌ Comprehensive analytics failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking comprehensive analytics: {e}")

def check_contact_details():
    """Check contact details page"""
    print("\n🔍 CHECKING CONTACT DETAILS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/contacts/1")
        if response.status_code == 200:
            print("✅ Contact details accessible")
            content = response.text.lower()
            
            # Check for progression indicators
            progression_indicators = [
                ("<EMAIL>", "Contact email"),
                ("allan", "Contact name"),
                ("trust", "Trust stage"),
                ("discovery", "Discovery stage"),
                ("demonstration", "Demo stage"),
                ("session", "Session data"),
                ("activity", "Activity tracking")
            ]
            
            for indicator, description in progression_indicators:
                if indicator in content:
                    print(f"   ✅ {description} found in contact details")
                else:
                    print(f"   ⚠️ {description} not visible in contact details")
        else:
            print(f"❌ Contact details failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking contact details: {e}")

def main():
    """Main test function"""
    print("🧪 ANALYTICS DATA TEST")
    print("=" * 60)
    print("Testing analytics visibility for contact: <EMAIL>")
    print("Session ID: 4f3b1f41-38bc-4fef-bf04-9b802c77af6")
    print("=" * 60)
    
    # Run all tests
    test_contact_progression()
    check_analytics_visibility()
    check_comprehensive_analytics()
    check_contact_details()
    
    print("\n🎯 SUMMARY")
    print("=" * 50)
    print("1. The contact should now be in DEMONSTRATION stage")
    print("2. Check these URLs to see the data:")
    print(f"   📊 Comprehensive Analytics: {BASE_URL}/analytics/comprehensive")
    print(f"   💬 Session Analytics: {BASE_URL}/analytics/sessions")
    print(f"   👤 Contact Details: {BASE_URL}/contacts/1")
    print("\n3. Look for:")
    print("   - Contact name: allan scof")
    print("   - Email: <EMAIL>")
    print("   - Current stage: demonstration")
    print("   - Message count: 20")
    print("   - Engagement: medium")

if __name__ == "__main__":
    main()
