#!/usr/bin/env python3
"""
Comprehensive test of all track_session endpoint functionality
"""

import requests
import json
import time
import uuid

def test_endpoint(action, stage=None, **kwargs):
    """Test a specific action on the track_session endpoint"""
    session_id = str(uuid.uuid4())[:8]
    
    test_data = {
        "session_id": session_id,
        "action": action,
        "contact_name": "Test User",
        "contact_email": "<EMAIL>"
    }
    
    if stage:
        test_data["stage"] = stage
    
    # Add any additional kwargs
    test_data.update(kwargs)
    
    print(f"\n🧪 Testing: {action}")
    print(f"📤 Payload: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(
            'http://localhost:5000/api/track-session',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📥 Status: {response.status_code}")
        
        if response.status_code == 200:
            response_json = response.json()
            print(f"✅ Success: {response_json}")
            return True
        else:
            print(f"❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 Comprehensive Track Session Test")
    print("=" * 50)
    
    # Wait for server
    print("\n🔍 Waiting for server...")
    for i in range(5):
        try:
            response = requests.get('http://localhost:5000/', timeout=2)
            if response.status_code == 200:
                print("✅ Server ready!")
                break
        except:
            time.sleep(1)
    
    tests = [
        # Basic conversation flow
        ("conversation_started", "opening"),
        ("stage_progression", "qualification"),
        ("stage_progression", "presentation"),
        ("stage_progression", "closing"),
        
        # Message tracking
        ("message_sent", "qualification", {
            "user_message": "I'm interested in your product",
            "bot_response": "Great! Let me tell you more about it."
        }),
        
        # Task completion
        ("task_completed", "qualification", {
            "task": "lead_qualification"
        }),
        
        # Conversion
        ("conversion", "closing", {
            "task": "purchase_completed"
        }),
        
        # Email interactions
        ("email_opened", None),
        ("email_link_clicked", None),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_args in tests:
        if len(test_args) == 2:
            action, stage = test_args
            kwargs = {}
        else:
            action, stage, kwargs = test_args
            
        if test_endpoint(action, stage, **kwargs):
            passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {total - passed} tests failed")

if __name__ == "__main__":
    main()
