#!/usr/bin/env python3
"""
Test Contact Action Buttons Fix
==============================
Test that contact view and edit buttons are now working after fixing the full_name property.
"""

import requests

def test_contact_buttons_fix():
    """Test that contact action buttons are now working"""
    print("🧪 Testing Contact Action Buttons Fix")
    print("=" * 45)
    
    base_url = 'http://localhost:5000'
    
    try:
        # Test 1: Check contacts page
        print("1. Testing contacts page...")
        response = requests.get(f'{base_url}/contacts', timeout=5)
        if response.status_code == 200:
            print("✅ Contacts page loads successfully")
        else:
            print(f"❌ Contacts page failed: {response.status_code}")
            return False
        
        # Test 2: Look for contact IDs in the page
        print("2. Looking for contact IDs...")
        import re
        
        # Look for contact IDs in view links
        view_links = re.findall(r'/contacts/(\d+)', response.text)
        edit_links = re.findall(r'/contacts/(\d+)/edit', response.text)
        
        if view_links:
            contact_id = view_links[0]
            print(f"✅ Found contact ID {contact_id} for testing")
            
            # Test 3: Try to access view contact page
            print("3. Testing view contact route...")
            view_response = requests.get(f'{base_url}/contacts/{contact_id}', timeout=10)
            
            if view_response.status_code == 200:
                print("✅ View contact page works!")
                view_success = True
            else:
                print(f"❌ View contact failed: {view_response.status_code}")
                view_success = False
            
            # Test 4: Try to access edit contact page
            print("4. Testing edit contact route...")
            edit_response = requests.get(f'{base_url}/contacts/{contact_id}/edit', timeout=10)
            
            if edit_response.status_code == 200:
                print("✅ Edit contact page works!")
                edit_success = True
            else:
                print(f"❌ Edit contact failed: {edit_response.status_code}")
                edit_success = False
            
            return view_success and edit_success
        else:
            print("ℹ️ No contacts found to test")
            print("   Add some contacts first, then test the action buttons")
            return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 CONTACT ACTION BUTTONS FIX TEST")
    print("=" * 55)
    print("This script tests that contact view and edit buttons work after fixing the full_name property.")
    print("=" * 55)
    
    # Test the fix
    success = test_contact_buttons_fix()
    
    # Summary
    print("\n" + "=" * 55)
    print("📊 TEST SUMMARY")
    print("=" * 55)
    
    if success:
        print("✅ CONTACT ACTION BUTTONS ARE NOW WORKING!")
        print("\nThe fix was successful:")
        print("• View contact button works")
        print("• Edit contact button works")
        print("• No more 'None has no attribute replace' errors")
        print("\n🎉 You can now click the action buttons in the contacts table!")
    else:
        print("❌ CONTACT ACTION BUTTONS STILL HAVE ISSUES!")
        print("\nPossible remaining issues:")
        print("• Check server logs for other errors")
        print("• Restart the Flask application")
        print("• Clear browser cache")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
