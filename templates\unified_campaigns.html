{% extends "base.html" %}

{% block title %}Email Campaigns - 24Seven Assistants{% endblock %}

{% block extra_css %}
<style>
/* Fix dropdown z-index issues */
.btn-group .dropdown-menu {
    z-index: 1050 !important;
    position: absolute !important;
}

.table-responsive {
    overflow: visible !important;
}

/* Ensure dropdowns appear above other elements */
.dropdown-menu {
    z-index: 1050 !important;
    position: absolute !important;
}

/* Fix for Bootstrap dropdown positioning */
.btn-group {
    position: relative;
}

.dropdown-menu.show {
    z-index: 1050 !important;
    position: absolute !important;
}

/* Ensure table cells don't clip dropdowns */
.table td {
    overflow: visible !important;
}

/* Fix for action column */
.table td:last-child {
    position: relative;
    overflow: visible !important;
}

/* Ensure dropdown menus are positioned correctly */
.dropdown-menu {
    transform: none !important;
    will-change: auto !important;
}

/* Fix for Bootstrap 5 dropdown positioning */
.dropdown-toggle::after {
    vertical-align: 0.125em;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-envelope-open-text text-primary"></i> Email Campaigns</h1>
    <div>
        <button id="bulkDeleteBtn" class="btn btn-danger me-2" style="display: none;" onclick="bulkDeleteCampaigns()">
            <i class="fas fa-trash"></i> Delete Selected
        </button>
        <button id="forceDeleteBtn" class="btn btn-outline-danger me-2" style="display: none;" onclick="bulkDeleteCampaigns(true)">
            <i class="fas fa-exclamation-triangle"></i> Force Delete
        </button>
        <button id="debugTestBtn" class="btn btn-warning me-2" onclick="testDebugFunction()">
            <i class="fas fa-bug"></i> Test Debug
        </button>
        <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Campaign
        </a>
    </div>
</div>

<!-- Campaign Statistics - Synchronized Analytics -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.basic_metrics.total_campaigns if analytics else 0 }}</h4>
                <p class="card-text">Total Campaigns</p>
                <i class="fas fa-envelope fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.emails_sent if analytics else 0 }}</h4>
                <p class="card-text">Emails Sent</p>
                <i class="fas fa-paper-plane fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.emails_opened if analytics else 0 }}</h4>
                <p class="card-text">Emails Opened</p>
                <small>{{ analytics.conversion_rates.email_open_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-envelope-open fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.links_clicked if analytics else 0 }}</h4>
                <p class="card-text">Links Clicked</p>
                <small>{{ analytics.conversion_rates.click_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-mouse-pointer fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.conversations_started if analytics else 0 }}</h4>
                <p class="card-text">Conversations</p>
                <small>{{ analytics.conversion_rates.conversation_rate if analytics else 0 }}% rate</small>
                <br><i class="fas fa-robot fa-2x"></i>
            </div>
        </div>
    </div>
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body text-center">
                <h4 class="card-title">{{ analytics.funnel_metrics.conversions if analytics else 0 }}</h4>
                <p class="card-text">Conversions</p>
                <small>{{ analytics.conversion_rates.overall_rate if analytics else 0 }}% overall</small>
                <br><i class="fas fa-trophy fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Campaigns Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i> All Campaigns
        </h5>
    </div>
    <div class="card-body">
        <!-- DEBUG: Always show filters for testing -->
        <!-- Search and Filter Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card bg-dark text-light">
                    <div class="card-body">
                        <form method="GET" action="{{ url_for('campaigns_list') }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label text-white">Search Campaigns</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="Search by name, subject, or template..."
                                       value="{{ request.args.get('search', '') }}"
                                       style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label text-white">Status</label>
                                <select class="form-select" id="status" name="status"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Statuses</option>
                                    <option value="draft" {{ 'selected' if request.args.get('status') == 'draft' }} style="background-color: #1a202c; color: white;">Draft</option>
                                    <option value="sending" {{ 'selected' if request.args.get('status') == 'sending' }} style="background-color: #1a202c; color: white;">Sending</option>
                                    <option value="completed" {{ 'selected' if request.args.get('status') == 'completed' }} style="background-color: #1a202c; color: white;">Completed</option>
                                    <option value="failed" {{ 'selected' if request.args.get('status') == 'failed' }} style="background-color: #1a202c; color: white;">Failed</option>
                                    <option value="partial_failure" {{ 'selected' if request.args.get('status') == 'partial_failure' }} style="background-color: #1a202c; color: white;">Partial Failure</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="template" class="form-label text-white">Template</label>
                                <select class="form-select" id="template" name="template"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Templates</option>
                                    <option value="introduction" {{ 'selected' if request.args.get('template') == 'introduction' }} style="background-color: #1a202c; color: white;">Introduction</option>
                                    <option value="follow_up" {{ 'selected' if request.args.get('template') == 'follow_up' }} style="background-color: #1a202c; color: white;">Follow Up</option>
                                    <option value="promotion" {{ 'selected' if request.args.get('template') == 'promotion' }} style="background-color: #1a202c; color: white;">Promotion</option>
                                    <option value="newsletter" {{ 'selected' if request.args.get('template') == 'newsletter' }} style="background-color: #1a202c; color: white;">Newsletter</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="performance" class="form-label text-white">Performance</label>
                                <select class="form-select" id="performance" name="performance"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Performance</option>
                                    <option value="high_open" {{ 'selected' if request.args.get('performance') == 'high_open' }} style="background-color: #1a202c; color: white;">High Open Rate (>30%)</option>
                                    <option value="high_click" {{ 'selected' if request.args.get('performance') == 'high_click' }} style="background-color: #1a202c; color: white;">High Click Rate (>10%)</option>
                                    <option value="high_conversion" {{ 'selected' if request.args.get('performance') == 'high_conversion' }} style="background-color: #1a202c; color: white;">High Conversion (>5%)</option>
                                    <option value="low_performance" {{ 'selected' if request.args.get('performance') == 'low_performance' }} style="background-color: #1a202c; color: white;">Low Performance (<10%)</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="dateRange" class="form-label text-white">Date Range</label>
                                <select class="form-select" id="dateRange" name="date_range"
                                        style="background-color: #1a202c !important; border: 1px solid #4a5568 !important; color: white !important;">
                                    <option value="" style="background-color: #1a202c; color: white;">All Dates</option>
                                    <option value="today" {{ 'selected' if request.args.get('date_range') == 'today' }} style="background-color: #1a202c; color: white;">Today</option>
                                    <option value="yesterday" {{ 'selected' if request.args.get('date_range') == 'yesterday' }} style="background-color: #1a202c; color: white;">Yesterday</option>
                                    <option value="last-7-days" {{ 'selected' if request.args.get('date_range') == 'last-7-days' }} style="background-color: #1a202c; color: white;">Last 7 Days</option>
                                    <option value="last-30-days" {{ 'selected' if request.args.get('date_range') == 'last-30-days' }} style="background-color: #1a202c; color: white;">Last 30 Days</option>
                                    <option value="this-month" {{ 'selected' if request.args.get('date_range') == 'this-month' }} style="background-color: #1a202c; color: white;">This Month</option>
                                    <option value="last-month" {{ 'selected' if request.args.get('date_range') == 'last-month' }} style="background-color: #1a202c; color: white;">Last Month</option>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row for Actions -->
                        <div class="row g-3 mt-2">
                            <div class="col-md-12">
                                <div class="d-flex gap-2 justify-content-center">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Apply Filters
                                    </button>
                                    <a href="{{ url_for('campaigns_list') }}" class="btn btn-outline-light">
                                        <i class="fas fa-times"></i> Clear All
                                    </a>
                                    <button type="button" class="btn btn-outline-info" onclick="exportCampaigns()">
                                        <i class="fas fa-download"></i> Export Results
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {% if campaigns %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="form-check-input">
                        </th>
                        <th>Campaign Name</th>
                        <th>Template</th>
                        <th>Status</th>
                        <th>Recipients</th>
                        <th>Email Performance</th>
                        <th>Chatbot Performance</th>
                        <th>Conversions</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for campaign in campaigns %}
                    <tr>
                        <td>
                            <input type="checkbox" name="campaign_ids" value="{{ campaign.id }}" class="form-check-input campaign-checkbox" onchange="updateBulkDeleteButtons()">
                        </td>
                        <td>
                            <strong>{{ campaign.name }}</strong>
                            <br><small class="text-muted">{{ campaign.subject }}</small>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ campaign.template_name|title }}</span>
                        </td>
                        <td>
                            {% if campaign.status == 'draft' %}
                                <span class="badge bg-secondary">Draft</span>
                            {% elif campaign.status == 'sending' %}
                                <span class="badge bg-warning">Sending</span>
                            {% elif campaign.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                            {% elif campaign.status == 'failed' %}
                                <span class="badge bg-danger">Failed</span>
                            {% elif campaign.status == 'partial_failure' %}
                                <span class="badge bg-warning">Partial Failure</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ campaign.status|title }}</span>
                            {% endif %}

                            {% if campaign.retry_count and campaign.retry_count > 0 %}
                            <br><small class="text-muted">Retry {{ campaign.retry_count }}/{{ campaign.max_retries or 3 }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="text-center">
                                <strong>{{ campaign.total_recipients or 0 }}</strong>
                                <br><small class="text-muted">Recipients</small>
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <div class="d-flex justify-content-between">
                                    <span>Sent:</span>
                                    <strong class="text-success">{{ campaign.emails_sent or 0 }}</strong>
                                </div>
                                {% if campaign.emails_failed and campaign.emails_failed > 0 %}
                                <div class="d-flex justify-content-between">
                                    <span>Failed:</span>
                                    <strong class="text-danger">{{ campaign.emails_failed }}</strong>
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between">
                                    <span>Opened:</span>
                                    <strong class="text-info">{{ campaign.emails_opened or 0 }}</strong>
                                </div>
                                {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-info" style="width: {{ ((campaign.emails_opened or 0) / campaign.emails_sent * 100)|round(1) }}%"></div>
                                </div>
                                <small class="text-muted">{{ ((campaign.emails_opened or 0) / campaign.emails_sent * 100)|round(1) }}% open rate</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                <div class="d-flex justify-content-between">
                                    <span>Clicked:</span>
                                    <strong>{{ campaign.chatbot_links_clicked or 0 }}</strong>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Started:</span>
                                    <strong>{{ campaign.chatbot_conversations_started or 0 }}</strong>
                                </div>
                                {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-warning" style="width: {{ ((campaign.chatbot_links_clicked or 0) / campaign.emails_sent * 100)|round(1) }}%"></div>
                                </div>
                                <small class="text-muted">{{ ((campaign.chatbot_links_clicked or 0) / campaign.emails_sent * 100)|round(1) }}% click rate</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                <strong class="text-success">{{ campaign.conversions_achieved or 0 }}</strong>
                                <br>
                                {% if campaign.emails_sent and campaign.emails_sent > 0 %}
                                <small class="text-muted">{{ ((campaign.conversions_achieved or 0) / campaign.emails_sent * 100)|round(1) }}%</small>
                                {% else %}
                                <small class="text-muted">0%</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <small>{{ campaign.created_at.strftime('%Y-%m-%d %H:%M') if campaign.created_at else '-' }}</small>
                            {% if campaign.started_at %}
                            <br><small class="text-muted">Started: {{ campaign.started_at.strftime('%H:%M') }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                {% if campaign.status == 'draft' %}
                                <form action="{{ url_for('send_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                    <button type="submit" class="btn btn-success btn-sm"
                                            onclick="return confirm('Send this campaign to all active contacts?')"
                                            title="Send Campaign">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </form>
                                {% elif campaign.status in ['failed', 'partial_failure'] %}
                                <div class="btn-group">
                                    <button type="button" class="btn btn-warning btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="Retry Options">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <form action="{{ url_for('retry_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                                <input type="hidden" name="retry_type" value="failed_only">
                                                <button type="submit" class="dropdown-item" onclick="return confirm('Retry only failed emails from this campaign?')">
                                                    <i class="fas fa-exclamation-triangle text-warning"></i> Retry Failed Only
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form action="{{ url_for('retry_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                                <input type="hidden" name="retry_type" value="all">
                                                <button type="submit" class="dropdown-item" onclick="return confirm('Retry sending to ALL contacts in this campaign?')">
                                                    <i class="fas fa-redo text-info"></i> Retry All Emails
                                                </button>
                                            </form>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ url_for('skip_failed_emails', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                                <button type="submit" class="dropdown-item text-muted" onclick="return confirm('Skip all failed emails and mark campaign as completed?')">
                                                    <i class="fas fa-forward"></i> Skip Failed & Complete
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}
                                {% if campaign.status in ['failed', 'partial_failure'] and campaign.emails_failed and campaign.emails_failed > 0 %}
                                <a href="{{ url_for('view_campaign_failures', campaign_id=campaign.id) }}" class="btn btn-outline-danger btn-sm" title="View Failure Details">
                                    <i class="fas fa-exclamation-circle"></i> {{ campaign.emails_failed }}
                                </a>
                                {% endif %}
                                <a href="{{ url_for('view_campaign', campaign_id=campaign.id) }}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_campaign', campaign_id=campaign.id) }}" class="btn btn-outline-secondary btn-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('duplicate_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-info btn-sm"
                                            onclick="return confirm('Create a copy of this campaign?')"
                                            title="Duplicate Campaign">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </form>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-danger btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="Delete Options">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% if campaign.status in ['draft', 'failed'] %}
                                        <li>
                                            <form action="{{ url_for('delete_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                                <button type="submit" class="dropdown-item text-warning"
                                                        onclick="return confirm('Delete this campaign? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i> Delete Campaign
                                                </button>
                                            </form>
                                        </li>
                                        {% else %}
                                        <li>
                                            <form action="{{ url_for('delete_campaign', campaign_id=campaign.id) }}" method="POST" style="display: inline;">
                                                <input type="hidden" name="force_delete" value="true">
                                                <button type="submit" class="dropdown-item text-danger"
                                                        onclick="return confirm('⚠️ FORCE DELETE: This will permanently delete the campaign and ALL related data including tracking, activities, and contact links. This action cannot be undone. Are you absolutely sure?')">
                                                    <i class="fas fa-exclamation-triangle"></i> Force Delete All Data
                                                </button>
                                            </form>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                                {% if campaign.status == 'completed' %}
                                <a href="{{ url_for('analytics_dashboard') }}" class="btn btn-outline-info btn-sm" title="View Analytics">
                                    <i class="fas fa-chart-line"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Hidden form for bulk delete operations -->
        <form id="bulkDeleteForm" action="{{ url_for('bulk_delete_campaigns') }}" method="POST" style="display: none;">
            <input type="hidden" name="force_delete" id="forceDeleteInput" value="false">
            <!-- Campaign IDs will be added dynamically by JavaScript -->
        </form>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-envelope-open-text fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Campaigns Found</h4>
            <p class="text-muted">Create your first email campaign to start engaging with prospects through AI-powered conversations.</p>
            <a href="{{ url_for('create_campaign') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create First Campaign
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Campaign Performance Summary - Synchronized Analytics -->
{% if analytics %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> Email Performance Overview
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-primary">{{ analytics.funnel_metrics.emails_sent }}</h5>
                        <small class="text-muted">Emails Sent</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info">{{ analytics.funnel_metrics.emails_opened }}</h5>
                        <small class="text-muted">Opened</small>
                        <br><small class="text-success">{{ analytics.conversion_rates.email_open_rate }}%</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-warning">{{ analytics.funnel_metrics.links_clicked }}</h5>
                        <small class="text-muted">Clicked</small>
                        <br><small class="text-success">{{ analytics.conversion_rates.click_rate }}%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-robot"></i> Chatbot Conversion Overview
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-warning">{{ analytics.funnel_metrics.conversations_started }}</h5>
                        <small class="text-muted">Conversations</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success">{{ analytics.funnel_metrics.conversions }}</h5>
                        <small class="text-muted">Conversions</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-primary">{{ analytics.conversion_rates.conversion_rate }}%</h5>
                        <small class="text-muted">Conversion Rate</small>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="http://localhost:7861" target="_blank" class="btn btn-success btn-sm w-100">
                        <i class="fas fa-robot"></i> Open Sales Chatbot
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
// Auto-refresh campaign status every 30 seconds for active campaigns
{% if campaigns|selectattr('status', 'equalto', 'sending')|list|length > 0 %}
setTimeout(function() {
    location.reload();
}, 30000);
{% endif %}

// Bulk delete functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.campaign-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkDeleteButtons();
}

function updateBulkDeleteButtons() {
    const checkedBoxes = document.querySelectorAll('.campaign-checkbox:checked');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const forceDeleteBtn = document.getElementById('forceDeleteBtn');

    if (checkedBoxes.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
        forceDeleteBtn.style.display = 'inline-block';
    } else {
        bulkDeleteBtn.style.display = 'none';
        forceDeleteBtn.style.display = 'none';
    }

    // Update select all checkbox state
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.campaign-checkbox');
    selectAll.checked = checkedBoxes.length === allCheckboxes.length;
    selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;
}

function bulkDeleteCampaigns(forceDelete = false) {
    const checkedBoxes = document.querySelectorAll('.campaign-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Please select campaigns to delete using the checkboxes.');
        return false;
    }

    let confirmMessage;
    if (forceDelete) {
        confirmMessage = `⚠️ FORCE DELETE: This will permanently delete ${checkedBoxes.length} campaign(s) and ALL related data including tracking, activities, and contact links. This action cannot be undone. Are you absolutely sure?`;
    } else {
        confirmMessage = `Delete ${checkedBoxes.length} selected campaign(s)? This action cannot be undone.`;
    }

    if (confirm(confirmMessage)) {
        // Clear any existing campaign_ids inputs
        const existingInputs = document.querySelectorAll('#bulkDeleteForm input[name="campaign_ids"]');
        existingInputs.forEach(input => input.remove());

        // Add selected campaign IDs to the form
        const form = document.getElementById('bulkDeleteForm');
        checkedBoxes.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'campaign_ids';
            hiddenInput.value = checkbox.value;
            form.appendChild(hiddenInput);
        });

        document.getElementById('forceDeleteInput').value = forceDelete ? 'true' : 'false';
        form.submit();
        return true;
    }
    return false;
}

// Export campaigns to CSV
function exportCampaigns() {
    const table = document.querySelector('.table');
    const rows = table.querySelectorAll('tr');
    let csvContent = '';

    // Add header row
    const headers = ['Campaign Name', 'Subject', 'Template', 'Status', 'Recipients', 'Emails Sent', 'Emails Opened', 'Links Clicked', 'Conversions', 'Created Date'];
    csvContent += headers.join(',') + '\n';

    // Add data rows (skip header row)
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');
        const rowData = [];

        // Skip checkbox column
        for (let j = 1; j < cells.length - 1; j++) { // Skip checkbox and actions columns
            let cellText = cells[j].textContent.trim().replace(/\n/g, ' ').replace(/"/g, '""');
            // Clean up extra whitespace
            cellText = cellText.replace(/\s+/g, ' ');
            rowData.push('"' + cellText + '"');
        }

        csvContent += rowData.join(',') + '\n';
    }

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'email_campaigns_export.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkDeleteButtons();

    // Fix dropdown positioning issues
    const dropdowns = document.querySelectorAll('.dropdown-toggle');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            // Ensure the dropdown menu appears above other elements
            const menu = this.nextElementSibling;
            if (menu && menu.classList.contains('dropdown-menu')) {
                setTimeout(() => {
                    menu.style.zIndex = '1050';
                    menu.style.position = 'absolute';
                }, 10);
            }
        });
    });

    // Handle dropdown show events
    document.addEventListener('shown.bs.dropdown', function(e) {
        const menu = e.target.querySelector('.dropdown-menu');
        if (menu) {
            menu.style.zIndex = '1050';
            menu.style.position = 'absolute';
        }
    });

    // Debug: Log all view campaign buttons
    const viewButtons = document.querySelectorAll('a[title="View Details"]');
    console.log('=== VIEW CAMPAIGN BUTTONS DEBUG ===');
    console.log('Found', viewButtons.length, 'view buttons');
    viewButtons.forEach((button, index) => {
        console.log(`Button ${index + 1}:`, {
            href: button.href,
            onclick: button.onclick,
            title: button.title,
            classes: button.className
        });
    });
});
</script>
{% endblock %}
