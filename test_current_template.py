#!/usr/bin/env python3
"""
Test Current Template
====================
Test what the current email template actually contains to verify our changes are there.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_current_template():
    """Test the current email template to see if our changes are there"""
    print("🔍 Testing Current Email Template")
    print("=" * 50)
    
    try:
        # Import the template manager
        from email_system.email_templates import EmailTemplateManager
        
        print("✅ EmailTemplateManager imported successfully")
        
        # Create template manager
        template_manager = EmailTemplateManager()
        print("✅ Template manager created")
        
        # Test context
        test_context = {
            'contact_name': 'Test User',
            'company_name': 'Test Company Ltd',
            'agent_name': 'Sarah',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-current',
            'session_id': 'test-current'
        }
        
        # Render the introduction template
        print("\n📧 Rendering introduction template...")
        rendered = template_manager.render_template('introduction', test_context)
        
        html_content = rendered['html_body']
        
        # Check for our specific changes
        checks = [
            ('Table layout present', '<table cellpadding="0" cellspacing="0"' in html_content),
            ('Emoji button text', '💬 Click here to start typing your message...' in html_content),
            ('Chat URL variable', '{{ chat_url }}' in html_content or 'http://localhost:5000/chat/test-current' in html_content),
            ('Block display styling', 'display: block' in html_content),
            ('Fixed width styling', 'width: 300px' in html_content),
            ('Target blank', 'target="_blank"' in html_content),
            ('No old input element', '<input type="text"' not in html_content),
            ('Table cell structure', '<td>' in html_content),
            ('Link href present', 'href=' in html_content)
        ]
        
        print("\n🔍 Checking for our changes:")
        all_passed = True
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        # Show a snippet of the relevant part
        print("\n📄 Template snippet around input area:")
        lines = html_content.split('\n')
        for i, line in enumerate(lines):
            if 'chat-input-group' in line:
                start = max(0, i-2)
                end = min(len(lines), i+10)
                for j in range(start, end):
                    marker = ">>> " if j == i else "    "
                    print(f"{marker}{lines[j].strip()}")
                break
        
        # Save the full rendered template for inspection
        with open('current_template_output.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"\n📁 Full template saved to: current_template_output.html")
        
        if all_passed:
            print("\n🎉 All our changes are present in the template!")
            print("The email template has been successfully updated.")
            return True
        else:
            print("\n❌ Some changes are missing from the template.")
            print("The template may not have been updated correctly.")
            return False
        
    except Exception as e:
        print(f"❌ Error testing template: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_current_template()
    sys.exit(0 if success else 1)
