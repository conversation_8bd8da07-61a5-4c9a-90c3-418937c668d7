"""
Opportunity Model
================
Tracks sales opportunities through the sales process.
"""

from datetime import datetime
from unified_sales_system import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey
from sqlalchemy.orm import relationship



class Opportunity(db.Model):
    """Sales opportunity tracking model"""
    
    __tablename__ = 'opportunities'
    
    id = Column(Integer, primary_key=True)
    
    # Basic Information
    name = Column(String(255), nullable=False)  # Deal name
    description = Column(Text, nullable=True)
    
    # Relationships
    contact_id = Column(Integer, ForeignKey('contacts.id'), nullable=False, index=True)
    current_stage_id = Column(Integer, ForeignKey('sales_stages.id'), nullable=False, index=True)
    
    # Financial Information
    estimated_value = Column(Float, default=0.0)
    actual_value = Column(Float, nullable=True)
    currency = Column(String(3), default='USD')
    probability_percent = Column(Float, default=0.0)  # Win probability
    
    # Timeline
    expected_close_date = Column(DateTime, nullable=True, index=True)
    actual_close_date = Column(DateTime, nullable=True)
    
    # Status
    status = Column(String(50), default='open', index=True)  # open, won, lost, abandoned
    lost_reason = Column(String(255), nullable=True)
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity_at = Column(DateTime, nullable=True, index=True)
    
    # Additional Information
    source = Column(String(100), nullable=True)  # Lead source
    notes = Column(Text, nullable=True)
    tags = Column(String(500), nullable=True)  # Comma-separated tags
    
    # AI/Bot Information
    bot_session_id = Column(String(100), nullable=True)  # Link to chatbot session
    ai_insights = Column(Text, nullable=True)  # AI-generated insights
    
    # Relationships
    contact = relationship("Contact", backref="opportunities")
    current_stage = relationship("SalesStage")
    stage_history = relationship("SalesStageHistory", back_populates="opportunity", cascade="all, delete-orphan")
    activities = relationship("Activity", back_populates="opportunity", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f'<Opportunity {self.name} - {self.status}>'
    
    @property
    def weighted_value(self):
        """Calculate weighted value based on probability"""
        return (self.estimated_value * self.probability_percent) / 100
    
    @property
    def days_in_current_stage(self):
        """Calculate days in current stage"""
        if self.stage_history:
            current_stage_entry = None
            for history in self.stage_history:
                if history.stage_id == self.current_stage_id and history.exited_at is None:
                    current_stage_entry = history
                    break
            
            if current_stage_entry:
                delta = datetime.utcnow() - current_stage_entry.entered_at
                return delta.days
        return 0
    
    @property
    def total_sales_cycle_days(self):
        """Calculate total days in sales cycle"""
        if self.created_at:
            end_date = self.actual_close_date or datetime.utcnow()
            delta = end_date - self.created_at
            return delta.days
        return 0
    
    def move_to_stage(self, new_stage_id, notes=None, changed_by=None, reason=None):
        """Move opportunity to a new stage"""
        from .sales_stage import SalesStageHistory
        
        # Close current stage
        current_history = None
        for history in self.stage_history:
            if history.stage_id == self.current_stage_id and history.exited_at is None:
                current_history = history
                break
        
        if current_history:
            current_history.exited_at = datetime.utcnow()
            current_history.calculate_duration()
        
        # Create new stage entry
        new_history = SalesStageHistory(
            opportunity_id=self.id,
            stage_id=new_stage_id,
            entered_at=datetime.utcnow(),
            notes=notes,
            changed_by=changed_by,
            reason=reason
        )
        
        self.stage_history.append(new_history)
        self.current_stage_id = new_stage_id
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Update probability based on new stage
        from .sales_stage import SalesStage
        new_stage = SalesStage.query.get(new_stage_id)
        if new_stage:
            self.probability_percent = new_stage.probability_percent
    
    def close_as_won(self, actual_value=None, notes=None):
        """Close opportunity as won"""
        self.status = 'won'
        self.actual_close_date = datetime.utcnow()
        if actual_value is not None:
            self.actual_value = actual_value
        self.probability_percent = 100.0
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if notes:
            self.notes = (self.notes or '') + f"\n[WON] {notes}"
    
    def close_as_lost(self, reason=None, notes=None):
        """Close opportunity as lost"""
        self.status = 'lost'
        self.actual_close_date = datetime.utcnow()
        self.lost_reason = reason
        self.probability_percent = 0.0
        self.last_activity_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        if notes:
            self.notes = (self.notes or '') + f"\n[LOST] {notes}"
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'contact_id': self.contact_id,
            'contact_name': self.contact.full_name if self.contact else None,
            'contact_email': self.contact.email if self.contact else None,
            'contact_company': self.contact.company if self.contact else None,
            'current_stage_id': self.current_stage_id,
            'current_stage_name': self.current_stage.name if self.current_stage else None,
            'estimated_value': self.estimated_value,
            'actual_value': self.actual_value,
            'currency': self.currency,
            'probability_percent': self.probability_percent,
            'weighted_value': self.weighted_value,
            'expected_close_date': self.expected_close_date.isoformat() if self.expected_close_date else None,
            'actual_close_date': self.actual_close_date.isoformat() if self.actual_close_date else None,
            'status': self.status,
            'lost_reason': self.lost_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_activity_at': self.last_activity_at.isoformat() if self.last_activity_at else None,
            'source': self.source,
            'notes': self.notes,
            'tags': self.tags,
            'bot_session_id': self.bot_session_id,
            'ai_insights': self.ai_insights,
            'days_in_current_stage': self.days_in_current_stage,
            'total_sales_cycle_days': self.total_sales_cycle_days
        }
