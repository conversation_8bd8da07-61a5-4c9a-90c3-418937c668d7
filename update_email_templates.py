#!/usr/bin/env python3
"""
Update Email Templates Script
============================
Script to update the existing email system to use the enhanced responsive templates.
"""

import os
import shutil
from datetime import datetime

def backup_original_templates():
    """Create a backup of the original email templates"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"email_templates_backup_{timestamp}.py"

    if os.path.exists("email_system/email_templates.py"):
        shutil.copy2("email_system/email_templates.py", f"email_system/{backup_filename}")
        print(f"✅ Backup created: email_system/{backup_filename}")
        return True
    else:
        print("❌ Original email_templates.py not found")
        return False

def update_templates():
    """Update the email templates to use enhanced responsive design"""

    # Create backup first
    if not backup_original_templates():
        print("❌ Failed to create backup. Aborting update.")
        return False

    try:
        # Replace the original with enhanced version
        if os.path.exists("email_system/enhanced_email_templates.py"):
            shutil.copy2("email_system/enhanced_email_templates.py", "email_system/email_templates.py")

            # Update the class name in the copied file
            with open("email_system/email_templates.py", "r", encoding='utf-8') as f:
                content = f.read()

            # Replace class name to maintain compatibility
            content = content.replace("class EnhancedEmailTemplateManager:", "class EmailTemplateManager:")
            content = content.replace("Enhanced email template manager", "Email template manager")
            content = content.replace("Enhanced responsive email templates", "Responsive email templates")

            with open("email_system/email_templates.py", "w", encoding='utf-8') as f:
                f.write(content)

            print("✅ Email templates updated successfully!")
            print("📧 Enhanced responsive design features:")
            print("   • Desktop mode: 800px max-width for better screen utilization")
            print("   • Mobile responsive: Optimized for tablets (768px) and phones (480px)")
            print("   • Enhanced typography: Better fonts and text hierarchy")
            print("   • Improved pricing cards: Better visual layout and mobile stacking")
            print("   • Enhanced chat interface: More interactive and touch-friendly")
            print("   • Better shadows and gradients: Modern visual design")
            print("   • Touch-friendly buttons: Larger tap targets for mobile")

            return True
        else:
            print("❌ Enhanced email templates file not found")
            return False

    except Exception as e:
        print(f"❌ Error updating templates: {e}")
        return False

def create_demo_page():
    """Create a demo page to showcase the responsive design"""
    try:
        if os.path.exists("enhanced_email_demo.html"):
            print("✅ Demo page created: enhanced_email_demo.html")
            print("🌐 Open this file in your browser to see the responsive design in action")
            print("📱 Use the demo controls to test different screen sizes")
            return True
        else:
            print("❌ Demo page file not found")
            return False
    except Exception as e:
        print(f"❌ Error with demo page: {e}")
        return False

def main():
    """Main function to run the update process"""
    print("🚀 Starting Email Template Enhancement Process...")
    print("=" * 60)

    # Check if we're in the right directory
    if not os.path.exists("email_system"):
        print("❌ email_system directory not found. Please run this script from the project root.")
        return

    # Update templates
    if update_templates():
        print("\n" + "=" * 60)
        print("✅ Email templates have been successfully enhanced!")

        # Create demo page
        create_demo_page()

        print("\n📋 Next Steps:")
        print("1. Test the enhanced templates by sending test emails")
        print("2. Open enhanced_email_demo.html in your browser to see the responsive design")
        print("3. Use the demo controls to test different screen sizes")
        print("4. The original templates have been backed up for safety")

        print("\n🎨 Key Improvements:")
        print("• Desktop: Wider 800px layout for better screen utilization")
        print("• Mobile: Responsive design that adapts to all screen sizes")
        print("• Typography: Enhanced fonts and better text hierarchy")
        print("• Interactive: Improved chat interface and touch-friendly buttons")
        print("• Visual: Modern shadows, gradients, and card designs")
        print("• Performance: Optimized CSS for faster loading")

    else:
        print("\n❌ Failed to update email templates")

if __name__ == "__main__":
    main()
