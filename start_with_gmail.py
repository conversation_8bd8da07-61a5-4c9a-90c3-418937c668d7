#!/usr/bin/env python3
"""
Start 24Seven Assistants Sales System with Gmail Configuration
==============================================================
Startup script that loads environment variables from .env file
"""

import os

def load_env_variables():
    """Load environment variables from .env file"""
    try:
        env_vars = {}
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        
        print(f"✅ Loaded {len(env_vars)} environment variables from .env")
        
        # Show email configuration
        email_vars = {k: v for k, v in env_vars.items() if 'MAIL' in k}
        if email_vars:
            print("📧 Email configuration loaded:")
            for key, value in email_vars.items():
                if 'PASSWORD' in key:
                    print(f"   {key}: {'*' * len(value)}")
                else:
                    print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Could not load .env file: {e}")
        print("   Using default configuration")
        return False

if __name__ == '__main__':
    print("🚀 24Seven Assistants Sales System")
    print("=" * 50)
    
    # Load environment variables first
    env_loaded = load_env_variables()
    
    if env_loaded:
        print("📧 Email server: Gmail (smtp.gmail.com)")
    else:
        print("📧 Email server: Default configuration")
    
    print("🌐 Server: http://localhost:5000")
    print("⚡ Press Ctrl+C to stop")
    print("=" * 50)
    
    # Import and start the application
    try:
        from unified_sales_system import app
        
        # Set logging level to reduce verbosity
        import logging
        logging.getLogger('werkzeug').setLevel(logging.ERROR)
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
