#!/usr/bin/env python3
"""
Direct test of track_session endpoint to isolate the issue
"""

import requests
import json
import time
import uuid

def test_track_session():
    """Test the track_session endpoint directly"""
    
    # Wait for server to be ready
    print("🔍 Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get('http://localhost:5000/', timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            print(f"⏳ Waiting... ({i+1}/10)")
            time.sleep(2)
    else:
        print("❌ Server not ready after 20 seconds")
        return False
    
    # Test data
    session_id = str(uuid.uuid4())[:8]
    test_data = {
        "session_id": session_id,
        "action": "conversation_started",
        "stage": "opening",
        "contact_name": "Test User",
        "contact_email": "<EMAIL>"
    }
    
    print(f"\n🧪 Testing track_session endpoint")
    print(f"📝 Session ID: {session_id}")
    print(f"📤 Sending payload: {json.dumps(test_data, indent=2)}")
    
    try:
        # Make the request
        response = requests.post(
            'http://localhost:5000/api/track-session',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"📥 Response Body: {json.dumps(response_json, indent=2)}")
        except:
            print(f"📥 Response Body (raw): {response.text}")
        
        if response.status_code == 200:
            print("✅ Success!")
            return True
        else:
            print(f"❌ Failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Direct Track Session Test")
    print("=" * 40)
    
    success = test_track_session()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n💥 Test failed!")
