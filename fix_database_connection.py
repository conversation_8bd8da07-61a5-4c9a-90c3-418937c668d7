#!/usr/bin/env python3
"""
Fix Database Connection Issue
"""

import os
import sqlite3
import requests
import shutil
from datetime import datetime

def find_database_files():
    """Find all database files in the current directory and subdirectories"""
    print("🔍 Searching for Database Files")
    print("=" * 35)

    db_files = []

    # Search in current directory
    for file in os.listdir('.'):
        if file.endswith('.db'):
            size = os.path.getsize(file)
            print(f"   📁 Found: {file} ({size} bytes)")
            db_files.append((file, size))

    # Check if any have data
    for db_file, size in db_files:
        if size > 0:
            print(f"\n🔍 Checking {db_file}...")
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # Check for campaigns
                cursor.execute("SELECT COUNT(*) FROM email_campaigns")
                campaigns_count = cursor.fetchone()[0]
                print(f"   📧 Campaigns: {campaigns_count}")

                # Check for contacts
                cursor.execute("SELECT COUNT(*) FROM contacts")
                contacts_count = cursor.fetchone()[0]
                print(f"   👥 Contacts: {contacts_count}")

                conn.close()

                if campaigns_count > 0:
                    print(f"   ✅ {db_file} contains data!")
                    return db_file

            except Exception as e:
                print(f"   ❌ Error reading {db_file}: {str(e)}")

    return None

def create_working_database():
    """Create a working database with proper schema and test data"""
    print("\n🔧 Creating Working Database")
    print("=" * 30)

    db_file = 'unified_sales.db'

    # Remove existing file if it exists
    if os.path.exists(db_file):
        print(f"📁 Removing existing {db_file}")
        os.remove(db_file)

    # Create new database
    print(f"🆕 Creating new {db_file}")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()

    # Create tables
    print("📋 Creating tables...")

    # Contacts table
    cursor.execute('''
        CREATE TABLE contacts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(200),
            job_title VARCHAR(150),
            source VARCHAR(100),
            status VARCHAR(50) DEFAULT 'new',
            lead_score FLOAT DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            do_not_email BOOLEAN DEFAULT 0,
            is_customer BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            email_campaign_id INTEGER,
            chatbot_session_id VARCHAR(100),
            first_email_sent DATETIME,
            email_opened DATETIME,
            chatbot_link_clicked DATETIME,
            chatbot_conversation_started DATETIME,
            current_sales_stage VARCHAR(50) DEFAULT 'email_sent',
            sales_stage_progression TEXT,
            conversion_completed DATETIME,
            total_interaction_time INTEGER DEFAULT 0
        )
    ''')

    # Email campaigns table
    cursor.execute('''
        CREATE TABLE email_campaigns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            template_name VARCHAR(100) NOT NULL,
            status VARCHAR(50) DEFAULT 'draft',
            total_recipients INTEGER DEFAULT 0,
            emails_sent INTEGER DEFAULT 0,
            emails_delivered INTEGER DEFAULT 0,
            emails_failed INTEGER DEFAULT 0,
            emails_opened INTEGER DEFAULT 0,
            chatbot_links_clicked INTEGER DEFAULT 0,
            chatbot_conversations_started INTEGER DEFAULT 0,
            sales_conversations_completed INTEGER DEFAULT 0,
            conversions_achieved INTEGER DEFAULT 0,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            retry_failed_only BOOLEAN DEFAULT 1,
            last_retry_at DATETIME,
            daily_send_limit INTEGER DEFAULT 100,
            emails_sent_today INTEGER DEFAULT 0,
            last_send_date DATE,
            send_schedule VARCHAR(50) DEFAULT 'immediate',
            scheduled_start_date DATE,
            scheduled_start_time TIME,
            send_days_of_week VARCHAR(20) DEFAULT '1,2,3,4,5',
            is_recurring BOOLEAN DEFAULT 0,
            batch_status VARCHAR(50) DEFAULT 'not_started',
            next_batch_date DATETIME,
            total_batches_planned INTEGER DEFAULT 1,
            batches_completed INTEGER DEFAULT 0,
            recipient_criteria TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            started_at DATETIME,
            completed_at DATETIME
        )
    ''')

    # Email logs table
    cursor.execute('''
        CREATE TABLE email_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            contact_id INTEGER NOT NULL,
            recipient_email VARCHAR(255) NOT NULL,
            recipient_name VARCHAR(255),
            subject VARCHAR(255) NOT NULL,
            email_body_html TEXT,
            email_body_text TEXT,
            sent_at DATETIME,
            delivered_at DATETIME,
            opened_at DATETIME,
            first_clicked_at DATETIME,
            replied_at DATETIME,
            bounced_at DATETIME,
            unsubscribed_at DATETIME,
            status VARCHAR(50) DEFAULT 'pending',
            error_message TEXT,
            open_count INTEGER DEFAULT 0,
            click_count INTEGER DEFAULT 0,
            user_agent VARCHAR(500),
            ip_address VARCHAR(45),
            message_id VARCHAR(255),
            thread_id VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Activities table
    cursor.execute('''
        CREATE TABLE activities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contact_id INTEGER,
            session_id VARCHAR(100),
            activity_type VARCHAR(50) NOT NULL,
            subject VARCHAR(255),
            description TEXT,
            extra_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Sales stages table
    cursor.execute('''
        CREATE TABLE sales_stages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            "order" INTEGER NOT NULL,
            probability_percent FLOAT DEFAULT 0.0,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Other required tables
    cursor.execute('''
        CREATE TABLE chatbot_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) UNIQUE NOT NULL,
            contact_id INTEGER,
            email_campaign_id INTEGER,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            ended_at DATETIME,
            current_stage VARCHAR(50) DEFAULT 'opening',
            current_task VARCHAR(200),
            stage_progression TEXT,
            total_messages INTEGER DEFAULT 0,
            user_messages INTEGER DEFAULT 0,
            bot_messages INTEGER DEFAULT 0,
            objections_handled INTEGER DEFAULT 0,
            completed_successfully BOOLEAN DEFAULT 0,
            conversion_achieved BOOLEAN DEFAULT 0,
            final_stage_reached VARCHAR(50),
            FOREIGN KEY (contact_id) REFERENCES contacts (id),
            FOREIGN KEY (email_campaign_id) REFERENCES email_campaigns (id)
        )
    ''')

    # Chat messages table for persisting individual messages
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id VARCHAR(100) NOT NULL,
            message_type VARCHAR(20) NOT NULL,
            content TEXT NOT NULL,
            stage VARCHAR(50),
            task VARCHAR(200),
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            message_order INTEGER NOT NULL,
            FOREIGN KEY (session_id) REFERENCES chatbot_sessions (session_id)
        )
    ''')

    cursor.execute('''
        CREATE TABLE contact_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    cursor.execute('''
        CREATE TABLE contact_group_memberships (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contact_id INTEGER NOT NULL,
            group_id INTEGER NOT NULL,
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contact_id) REFERENCES contacts (id),
            FOREIGN KEY (group_id) REFERENCES contact_groups (id)
        )
    ''')

    cursor.execute('''
        CREATE TABLE campaign_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            group_id INTEGER NOT NULL,
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (group_id) REFERENCES contact_groups (id)
        )
    ''')

    cursor.execute('''
        CREATE TABLE email_failures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            campaign_id INTEGER NOT NULL,
            contact_id INTEGER NOT NULL,
            recipient_email VARCHAR(255) NOT NULL,
            error_message TEXT,
            error_type VARCHAR(100),
            retry_count INTEGER DEFAULT 0,
            last_retry_at DATETIME,
            resolved BOOLEAN DEFAULT 0,
            resolved_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (campaign_id) REFERENCES email_campaigns (id),
            FOREIGN KEY (contact_id) REFERENCES contacts (id)
        )
    ''')

    # Create indexes
    print("🔍 Creating indexes...")
    cursor.execute('CREATE INDEX idx_contacts_email ON contacts(email)')
    cursor.execute('CREATE INDEX idx_contacts_is_active ON contacts(is_active)')
    cursor.execute('CREATE INDEX idx_email_logs_campaign_id ON email_logs(campaign_id)')
    cursor.execute('CREATE INDEX idx_email_logs_contact_id ON email_logs(contact_id)')
    cursor.execute('CREATE INDEX idx_activities_contact_id ON activities(contact_id)')
    cursor.execute('CREATE INDEX idx_activities_session_id ON activities(session_id)')

    # Insert default data
    print("📝 Inserting default data...")

    # Sales stages
    stages = [
        ('Email Sent', 1, 5.0),
        ('Email Opened', 2, 10.0),
        ('Link Clicked', 3, 15.0),
        ('Opening', 4, 20.0),
        ('Trust', 5, 35.0),
        ('Discovery', 6, 55.0),
        ('Demonstration', 7, 75.0),
        ('Close', 8, 90.0),
        ('Converted', 9, 100.0)
    ]

    for name, order, probability in stages:
        cursor.execute('''
            INSERT INTO sales_stages (name, "order", probability_percent)
            VALUES (?, ?, ?)
        ''', (name, order, probability))

    # Sample contacts
    contacts = [
        ('John', 'Doe', '<EMAIL>', 'Example Corp', 'CEO'),
        ('Jane', 'Smith', '<EMAIL>', 'Test Inc', 'CTO')
    ]

    for first_name, last_name, email, company, job_title in contacts:
        cursor.execute('''
            INSERT INTO contacts (first_name, last_name, email, company, job_title, source, status)
            VALUES (?, ?, ?, ?, ?, 'demo_data', 'new')
        ''', (first_name, last_name, email, company, job_title))

    # Commit and close
    conn.commit()
    conn.close()

    print(f"✅ Database {db_file} created successfully!")
    return db_file

def test_database_with_flask():
    """Test the database connection with Flask"""
    print(f"\n🧪 Testing Database with Flask")
    print("=" * 30)

    # Wait a moment for Flask to restart
    import time
    time.sleep(2)

    # Test Flask connection
    try:
        response = requests.get("http://localhost:5000/", timeout=10)
        if response.status_code == 200:
            print("✅ Flask app is responding")
        else:
            print(f"❌ Flask app returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Flask app not responding: {str(e)}")
        return False

    # Create a test campaign
    print("📝 Creating test campaign...")
    campaign_data = {
        'name': 'Database Fix Test Campaign',
        'template': 'introduction',
        'recipient_type': 'all',
        'daily_send_limit': '100',
        'send_schedule': 'immediate'
    }

    try:
        create_response = requests.post(
            'http://localhost:5000/campaigns/create',
            data=campaign_data,
            timeout=10
        )

        if create_response.status_code in [200, 302]:
            print("✅ Test campaign created")

            # Get campaigns and test view
            campaigns_response = requests.get('http://localhost:5000/campaigns')
            if campaigns_response.status_code == 200:
                import re
                campaign_ids = re.findall(r'value="(\d+)".*campaign-checkbox', campaigns_response.text)

                if campaign_ids:
                    test_id = campaign_ids[0]
                    print(f"🎯 Testing view campaign {test_id}...")

                    view_response = requests.get(f'http://localhost:5000/campaigns/{test_id}')

                    if view_response.status_code == 200:
                        if "Database Fix Test Campaign" in view_response.text or "Campaign Details" in view_response.text:
                            print("✅ SUCCESS! View campaign is working!")
                            return True
                        else:
                            print("❌ View campaign page loaded but content is wrong")
                            return False
                    else:
                        print(f"❌ View campaign failed: {view_response.status_code}")
                        return False
                else:
                    print("❌ No campaigns found after creation")
                    return False
            else:
                print("❌ Failed to get campaigns list")
                return False
        else:
            print(f"❌ Failed to create campaign: {create_response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error testing with Flask: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Database Connection Fix Tool")
    print("=" * 50)

    # Step 1: Find existing database files
    existing_db = find_database_files()

    # Step 2: Create a working database
    working_db = create_working_database()

    # Step 3: Test with Flask
    if working_db:
        print(f"\n🔄 Please restart the Flask application now!")
        print("=" * 45)
        print("1. Go to the terminal running Flask")
        print("2. Press Ctrl+C to stop it")
        print("3. Run: python unified_sales_system.py")
        print("4. Wait for it to start")
        print("5. Then run this test again to verify")

        input("\nPress Enter after restarting Flask to test...")

        success = test_database_with_flask()

        print("\n" + "=" * 50)
        print("📋 FINAL RESULT")
        print("=" * 50)

        if success:
            print("🎉 SUCCESS! Database connection fixed!")
            print("💡 View campaign buttons should now work perfectly!")
            print(f"📁 Database file: {working_db}")
        else:
            print("❌ Issues remain. Please check:")
            print("   • Flask application is running")
            print("   • Database file permissions")
            print("   • Flask application logs")
    else:
        print("❌ Failed to create working database")
