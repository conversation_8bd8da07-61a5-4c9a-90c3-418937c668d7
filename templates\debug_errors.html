{% extends "base.html" %}

{% block title %}Error Logs - Debug Dashboard{% endblock %}

{% block extra_css %}
<style>
    .error-card {
        border-left: 4px solid #dc3545;
        margin-bottom: 15px;
    }
    .error-details {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 10px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        max-height: 200px;
        overflow-y: auto;
    }
    .traceback {
        white-space: pre-wrap;
        word-break: break-all;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-exclamation-triangle text-danger"></i> Error Logs</h1>
    <div>
        <a href="{{ url_for('debug.debug_dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Debug Dashboard
        </a>
        <button class="btn btn-outline-danger btn-sm" onclick="clearLogs('errors')">
            <i class="fas fa-trash"></i> Clear Error Logs
        </button>
    </div>
</div>

<!-- Summary Stats -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h4 class="text-danger">{{ total }}</h4>
                        <small class="text-muted">Total Errors</small>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-warning">{{ logs|groupby('type')|list|length }}</h4>
                        <small class="text-muted">Error Types</small>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-info">{{ logs|selectattr('timestamp', 'gt', (moment().subtract(1, 'hour').format() if moment else ''))|list|length }}</h4>
                        <small class="text-muted">Last Hour</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Logs -->
<div class="row">
    <div class="col-md-12">
        {% if logs %}
            {% for error in logs %}
            <div class="card error-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">
                            <span class="badge bg-danger me-2">{{ error.type }}</span>
                            {{ error.message[:100] }}{% if error.message|length > 100 %}...{% endif %}
                        </h6>
                    </div>
                    <small class="text-muted">{{ error.timestamp }}</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Error ID:</strong> {{ error.id }}<br>
                            <strong>Request URL:</strong> {{ error.request_url or 'N/A' }}<br>
                            <strong>Request Method:</strong> {{ error.request_method or 'N/A' }}<br>
                            <strong>IP Address:</strong> {{ error.ip_address or 'N/A' }}
                        </div>
                        <div class="col-md-6">
                            <strong>User Agent:</strong><br>
                            <small>{{ error.user_agent[:100] }}{% if error.user_agent|length > 100 %}...{% endif %}</small>
                        </div>
                    </div>
                    
                    {% if error.traceback %}
                    <div class="mt-3">
                        <strong>Traceback:</strong>
                        <div class="error-details">
                            <div class="traceback">{{ error.traceback }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}

            <!-- Pagination -->
            {% if has_prev or has_next %}
            <nav aria-label="Error logs pagination">
                <ul class="pagination justify-content-center">
                    {% if has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('debug.debug_errors', page=prev_num) }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">Page {{ page }}</span>
                    </li>
                    
                    {% if has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('debug.debug_errors', page=next_num) }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

        {% else %}
        <div class="card">
            <div class="card-body text-center text-muted py-5">
                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                <h5>No Errors Found</h5>
                <p>Great! No errors have been logged recently.</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearLogs(type) {
    if (confirm('Are you sure you want to clear ' + type + ' logs?')) {
        fetch('/debug/api/clear-logs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({type: type})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error clearing logs: ' + error);
        });
    }
}
</script>
{% endblock %}
