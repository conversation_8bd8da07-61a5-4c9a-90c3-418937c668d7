#!/usr/bin/env python3
"""
Test Campaign Action Buttons Functionality
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_campaign_actions():
    """Test all campaign action buttons"""
    print("🚀 Testing Campaign Action Buttons")
    print("=" * 50)
    
    # First, get the list of campaigns
    print("📋 Getting campaigns list...")
    response = requests.get(f"{BASE_URL}/campaigns")
    if response.status_code != 200:
        print(f"❌ Failed to get campaigns list: {response.status_code}")
        return False
    
    print("✅ Campaigns page loaded successfully")
    
    # Check if campaigns page contains action buttons
    content = response.text
    action_buttons = [
        'btn-outline-primary',  # View button
        'btn-outline-secondary',  # Edit button
        'btn-outline-info',  # Duplicate button
        'btn-outline-danger',  # Delete button
    ]
    
    buttons_found = 0
    for button_class in action_buttons:
        if button_class in content:
            buttons_found += 1
            print(f"✅ Found {button_class} button")
        else:
            print(f"❌ Missing {button_class} button")
    
    print(f"\n📊 Action Buttons Summary: {buttons_found}/{len(action_buttons)} buttons found")
    
    # Test if forms are properly structured
    form_actions = [
        'duplicate_campaign',
        'delete_campaign',
        'send_campaign',
    ]
    
    forms_found = 0
    for action in form_actions:
        if action in content:
            forms_found += 1
            print(f"✅ Found {action} form")
        else:
            print(f"❌ Missing {action} form")
    
    print(f"\n📊 Forms Summary: {forms_found}/{len(form_actions)} forms found")
    
    # Check for JavaScript functionality
    js_functions = [
        'bulkDeleteCampaigns',
        'toggleSelectAll',
        'updateBulkDeleteButtons',
    ]
    
    js_found = 0
    for func in js_functions:
        if func in content:
            js_found += 1
            print(f"✅ Found {func} JavaScript function")
        else:
            print(f"❌ Missing {func} JavaScript function")
    
    print(f"\n📊 JavaScript Summary: {js_found}/{len(js_functions)} functions found")
    
    return buttons_found == len(action_buttons) and forms_found == len(form_actions)

def test_campaign_routes():
    """Test if campaign routes are accessible"""
    print("\n🔗 Testing Campaign Routes")
    print("=" * 30)
    
    # Test routes that should be accessible
    routes_to_test = [
        ('/campaigns', 'GET'),
        ('/campaigns/create', 'GET'),
    ]
    
    accessible_routes = 0
    for route, method in routes_to_test:
        try:
            if method == 'GET':
                response = requests.get(f"{BASE_URL}{route}")
            else:
                response = requests.post(f"{BASE_URL}{route}")
            
            if response.status_code in [200, 302]:  # 302 for redirects
                print(f"✅ {method} {route} - Status: {response.status_code}")
                accessible_routes += 1
            else:
                print(f"❌ {method} {route} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {method} {route} - Error: {str(e)}")
    
    print(f"\n📊 Routes Summary: {accessible_routes}/{len(routes_to_test)} routes accessible")
    return accessible_routes == len(routes_to_test)

def test_specific_campaign_actions():
    """Test specific campaign actions if campaigns exist"""
    print("\n🎯 Testing Specific Campaign Actions")
    print("=" * 35)
    
    # Get campaigns to test with
    try:
        response = requests.get(f"{BASE_URL}/campaigns")
        if 'No Campaigns Found' in response.text or 'No campaigns' in response.text:
            print("ℹ️ No campaigns found to test actions with")
            return True
        
        # Look for campaign IDs in the response
        import re
        campaign_ids = re.findall(r'/campaigns/(\d+)', response.text)
        
        if not campaign_ids:
            print("ℹ️ No campaign IDs found in campaigns page")
            return True
        
        campaign_id = campaign_ids[0]
        print(f"🎯 Testing with campaign ID: {campaign_id}")
        
        # Test view campaign
        view_response = requests.get(f"{BASE_URL}/campaigns/{campaign_id}")
        if view_response.status_code == 200:
            print("✅ View campaign works")
        else:
            print(f"❌ View campaign failed: {view_response.status_code}")
        
        # Test edit campaign
        edit_response = requests.get(f"{BASE_URL}/campaigns/{campaign_id}/edit")
        if edit_response.status_code == 200:
            print("✅ Edit campaign page works")
        else:
            print(f"❌ Edit campaign page failed: {edit_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing specific actions: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Campaign Action Buttons Test Suite")
    print("=" * 60)
    
    # Test 1: Check if action buttons are present
    buttons_test = test_campaign_actions()
    
    # Test 2: Check if routes are accessible
    routes_test = test_campaign_routes()
    
    # Test 3: Test specific campaign actions
    actions_test = test_specific_campaign_actions()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Action Buttons Present:     {'✅ PASSED' if buttons_test else '❌ FAILED'}")
    print(f"Routes Accessible:          {'✅ PASSED' if routes_test else '❌ FAILED'}")
    print(f"Specific Actions:           {'✅ PASSED' if actions_test else '❌ FAILED'}")
    
    overall_result = buttons_test and routes_test and actions_test
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_result else '❌ SOME TESTS FAILED'}")
    
    if overall_result:
        print("\n🎉 Campaign action buttons should be working correctly!")
        print("📝 Available actions:")
        print("   • View Campaign Details")
        print("   • Edit Campaign")
        print("   • Send Campaign (for draft campaigns)")
        print("   • Duplicate Campaign")
        print("   • Delete Campaign")
        print("   • Retry Failed Emails")
        print("   • Bulk Delete Operations")
    else:
        print("\n⚠️ Some action buttons may not be working correctly.")
        print("Please check the Flask application logs for errors.")
