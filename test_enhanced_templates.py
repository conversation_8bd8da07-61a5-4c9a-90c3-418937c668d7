#!/usr/bin/env python3
"""
Test Enhanced Email Templates
============================
Test script to verify the enhanced responsive email templates are working correctly.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_system.email_templates import EmailTemplateManager

def test_enhanced_templates():
    """Test the enhanced email templates"""
    print("🧪 Testing Enhanced Email Templates")
    print("=" * 60)
    
    try:
        # Initialize template manager
        template_manager = EmailTemplateManager()
        
        # Test context
        test_context = {
            'contact_name': '<PERSON>',
            'company_name': 'Test Company Ltd',
            'agent_name': '<PERSON>',
            'reply_email': '<EMAIL>',
            'phone_number': '+256 **********',
            'chat_url': 'http://localhost:5000/chat/test-session-123',
            'session_id': 'test-session-123'
        }
        
        # Test all available templates
        templates = template_manager.get_template_list()
        print(f"📧 Found {len(templates)} templates:")
        
        for template in templates:
            print(f"   • {template['display_name']}")
        
        print("\n🔍 Testing Template Rendering:")
        
        # Test introduction template
        print("\n1. Testing Introduction Template...")
        intro_result = template_manager.render_template('introduction', test_context)
        
        # Check for responsive design features
        html_content = intro_result['html_body']
        
        checks = [
            ('Desktop max-width: 800px', 'max-width: 800px' in html_content),
            ('Mobile media queries', '@media only screen and (max-width: 768px)' in html_content),
            ('Small mobile queries', '@media only screen and (max-width: 480px)' in html_content),
            ('Enhanced typography', 'Segoe UI' in html_content),
            ('Pricing grid layout', 'pricing-grid' in html_content),
            ('Chat interface', 'chat-interface' in html_content),
            ('Responsive cards', 'email-container' in html_content),
            ('Modern shadows', 'box-shadow:' in html_content),
            ('Gradient backgrounds', 'linear-gradient' in html_content),
            ('Touch-friendly buttons', 'cta-button' in html_content)
        ]
        
        print("   Responsive Design Features:")
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
        
        # Test customer support template
        print("\n2. Testing Customer Support Template...")
        support_result = template_manager.render_template('customer_support', test_context)
        
        # Check for enhanced chat demo
        support_html = support_result['html_body']
        chat_checks = [
            ('Enhanced chat demo', 'chat-demo-enhanced' in support_html),
            ('Chat preview', 'chat-preview' in support_html),
            ('Interactive send button', 'chat-send-btn' in support_html),
            ('Info tips', 'info-tip' in support_html)
        ]
        
        print("   Enhanced Chat Features:")
        for check_name, passed in chat_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
        
        # Test follow-up template
        print("\n3. Testing Follow-up Template...")
        followup_result = template_manager.render_template('followup', test_context)
        
        # Check template content
        followup_html = followup_result['html_body']
        followup_checks = [
            ('Responsive container', 'email-container' in followup_html),
            ('Highlight cards', 'highlight-card' in followup_html),
            ('CTA buttons', 'cta-button' in followup_html),
            ('Mobile responsive', '@media only screen' in followup_html)
        ]
        
        print("   Follow-up Features:")
        for check_name, passed in followup_checks:
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
        
        # Save sample rendered templates for inspection
        print("\n📁 Saving Sample Templates:")
        
        # Save introduction template
        with open('enhanced_intro_sample.html', 'w', encoding='utf-8') as f:
            f.write(intro_result['html_body'])
        print("   ✅ enhanced_intro_sample.html")
        
        # Save customer support template
        with open('enhanced_support_sample.html', 'w', encoding='utf-8') as f:
            f.write(support_result['html_body'])
        print("   ✅ enhanced_support_sample.html")
        
        # Save follow-up template
        with open('enhanced_followup_sample.html', 'w', encoding='utf-8') as f:
            f.write(followup_result['html_body'])
        print("   ✅ enhanced_followup_sample.html")
        
        print("\n🎉 All Enhanced Templates Tested Successfully!")
        print("\n📱 Key Enhancements Verified:")
        print("   • Desktop: 800px max-width for better screen utilization")
        print("   • Mobile: Responsive breakpoints at 768px and 480px")
        print("   • Typography: Enhanced Segoe UI font family")
        print("   • Layout: Modern card-based design with shadows")
        print("   • Interactive: Enhanced chat interface with better UX")
        print("   • Visual: Gradient backgrounds and modern styling")
        print("   • Touch: Larger buttons and touch-friendly elements")
        
        print("\n🌐 Next Steps:")
        print("   1. Open enhanced_email_demo.html to see responsive design")
        print("   2. Test the sample files in different screen sizes")
        print("   3. Send test emails to verify email client compatibility")
        print("   4. Use browser dev tools to test mobile responsiveness")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing templates: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_templates()
    sys.exit(0 if success else 1)
