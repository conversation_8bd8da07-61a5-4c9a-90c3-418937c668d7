# Contacts and Campaigns System Documentation

## Overview
This document provides comprehensive documentation for the creation and deletion of contacts and campaigns in the 24Seven Assistants Sales Department system. The system is built using Flask with SQLAlchemy ORM and includes advanced features like chatbot integration, group management, and comprehensive tracking.

## Table of Contents
1. [Contact Management](#contact-management)
2. [Campaign Management](#campaign-management)
3. [Database Models](#database-models)
4. [API Endpoints](#api-endpoints)
5. [Deletion Operations](#deletion-operations)
6. [Group Management](#group-management)
7. [Integration Features](#integration-features)

## Contact Management

### Contact Model Structure
The Contact model (`models/contact.py`) contains comprehensive contact information:

**Core Fields:**
- `id`: Primary key (Integer)
- `first_name`, `last_name`: Required contact names (String)
- `email`: Unique email address (String, indexed)
- `phone`: Optional phone number (String)
- `company`: Company name (String)
- `job_title`: Job position (String)

**Extended Information:**
- `website`, `linkedin_url`: Contact URLs
- `industry`, `company_size`: Business details
- `source`: How contact was acquired
- `status`: Contact stage (new, contacted, qualified, customer, lost)
- `lead_score`: Scoring from 0-100 (Float)

**Communication Preferences:**
- `preferred_contact_method`: email, phone, linkedin
- `timezone`, `best_contact_time`: Scheduling preferences
- `do_not_email`, `do_not_call`: Communication restrictions

**Tracking Fields:**
- `created_at`, `updated_at`: Timestamps
- `last_contacted`, `last_activity`: Activity tracking
- `is_active`, `is_customer`: Status flags

### Contact Creation

#### Web Interface Route
```python
@app.route('/contacts/add', methods=['GET', 'POST'])
def add_contact():
    if request.method == 'POST':
        contact = Contact(
            first_name=request.form.get('first_name'),
            last_name=request.form.get('last_name'),
            email=request.form.get('email'),
            phone=request.form.get('phone'),
            company=request.form.get('company'),
            job_title=request.form.get('job_title'),
            source=request.form.get('source', 'manual_entry'),
            status='new'
        )
        db.session.add(contact)
        db.session.commit()
        flash('Contact added successfully!', 'success')
        return redirect(url_for('contacts_list'))
    return render_template('add_contact.html')
```

#### Required Fields
- `first_name`: Contact's first name
- `last_name`: Contact's last name
- `email`: Unique email address (validated for uniqueness)

#### Optional Fields
- `phone`: Phone number
- `company`: Company name
- `job_title`: Job position
- `source`: Lead source (defaults to 'manual_entry')

#### Validation Rules
- Email must be unique across all contacts
- Email field is required and indexed for performance
- Status defaults to 'new' for new contacts
- All contacts are active by default (`is_active=True`)

### Contact Viewing and Editing

#### View Contact Details
```python
@app.route('/contacts/<int:contact_id>')
def view_contact(contact_id):
    contact = Contact.query.get_or_404(contact_id)
    activities = Activity.query.filter_by(contact_id=contact_id).order_by(Activity.created_at.desc()).limit(20).all()
    chatbot_sessions = ChatbotSession.query.filter_by(contact_id=contact_id).order_by(ChatbotSession.started_at.desc()).all()
    return render_template('view_contact.html', contact=contact, activities=activities, chatbot_sessions=chatbot_sessions)
```

#### Edit Contact
```python
@app.route('/contacts/<int:contact_id>/edit', methods=['GET', 'POST'])
def edit_contact(contact_id):
    contact = Contact.query.get_or_404(contact_id)
    if request.method == 'POST':
        contact.first_name = request.form.get('first_name')
        contact.last_name = request.form.get('last_name')
        contact.email = request.form.get('email')
        # ... update other fields
        contact.do_not_email = 'do_not_email' in request.form
        contact.is_active = 'is_active' in request.form
        db.session.commit()
        flash('Contact updated successfully!', 'success')
        return redirect(url_for('view_contact', contact_id=contact.id))
    return render_template('edit_contact.html', contact=contact)
```

## Campaign Management

### Campaign Model Structure
The EmailCampaign model (`models/email_campaign.py`) manages email campaigns:

**Core Fields:**
- `id`: Primary key (Integer)
- `name`: Campaign name (String, required)
- `subject`: Email subject line (String, required)
- `template_name`: Template identifier (String, required)

**Content Fields:**
- `email_body_html`: HTML email content (Text)
- `email_body_text`: Plain text email content (Text)
- `sender_name`: Sender display name
- `sender_email`: From email address
- `reply_to_email`: Reply-to address

**Targeting:**
- `target_audience`: Description of target (String)
- `contact_filters`: JSON filters for contact selection
- `recipient_criteria`: JSON criteria for recipient selection

**Scheduling:**
- `scheduled_at`: When to send (DateTime)
- `started_at`: When sending began (DateTime)
- `completed_at`: When sending finished (DateTime)
- `daily_send_limit`: Max emails per day (Integer)
- `send_schedule`: immediate, scheduled, etc.

**Status and Statistics:**
- `status`: draft, scheduled, running, completed, paused, cancelled
- `total_recipients`: Number of target contacts
- `emails_sent`, `emails_delivered`, `emails_opened`: Tracking counters
- `emails_clicked`, `emails_replied`, `emails_bounced`: Engagement metrics

### Campaign Creation

#### Web Interface Route
```python
@app.route('/campaigns/create', methods=['GET', 'POST'])
def create_campaign():
    if request.method == 'POST':
        campaign_name = request.form.get('name')
        template_name = request.form.get('template', 'introduction')
        recipient_type = request.form.get('recipient_type', 'all')
        daily_send_limit = int(request.form.get('daily_send_limit', 100))
        send_schedule = request.form.get('send_schedule', 'immediate')

        campaign = EmailCampaign(
            name=campaign_name,
            template_name=template_name,
            subject=f"24Seven Assistants - Meet Sarah, Your AI Sales Assistant",
            status='draft',
            daily_send_limit=daily_send_limit,
            send_schedule=send_schedule
        )

        # Store recipient selection criteria
        recipient_criteria = {'type': recipient_type}
        if recipient_type == 'specific':
            selected_contacts = request.form.getlist('selected_contacts')
            recipient_criteria['contact_ids'] = selected_contacts
        elif recipient_type == 'groups':
            selected_groups = request.form.getlist('selected_groups')
            recipient_criteria['group_ids'] = selected_groups

        campaign.recipient_criteria = json.dumps(recipient_criteria)

        db.session.add(campaign)
        db.session.commit()

        flash('Campaign created successfully!', 'success')
        return redirect(url_for('campaigns_list'))
```

#### Campaign Creation Parameters

**Required Fields:**
- `name`: Campaign name
- `template`: Email template to use ('introduction', 'followup')

**Recipient Selection:**
- `recipient_type`: 'all', 'specific', 'groups'
- `selected_contacts`: List of contact IDs (for 'specific' type)
- `selected_groups`: List of group IDs (for 'groups' type)

**Scheduling Options:**
- `daily_send_limit`: Maximum emails per day (default: 100)
- `send_schedule`: 'immediate' or 'scheduled'
- `scheduled_start_date`: Date to start sending (for scheduled)
- `scheduled_start_time`: Time to start sending (for scheduled)

**Advanced Options:**
- `batch_size`: Number of emails per batch
- `batch_delay_minutes`: Delay between batches

#### Available Templates
1. **Introduction Template**: "24Seven Assistants + AI Sales Assistant"
2. **Follow-up Template**: "Follow-up with AI Assistant"

### Campaign Recipient Selection

#### Get Campaign Contacts Function
```python
def get_campaign_contacts(campaign):
    """Get contacts for a campaign based on recipient criteria"""
    if not campaign.recipient_criteria:
        return Contact.query.filter_by(is_active=True, do_not_email=False).all()

    criteria = json.loads(campaign.recipient_criteria)
    recipient_type = criteria.get('type', 'all')

    if recipient_type == 'all':
        return Contact.query.filter_by(is_active=True, do_not_email=False).all()
    elif recipient_type == 'specific':
        contact_ids = criteria.get('contact_ids', [])
        return Contact.query.filter(
            Contact.id.in_(contact_ids),
            Contact.is_active == True,
            Contact.do_not_email == False
        ).all()
    elif recipient_type == 'groups':
        group_ids = criteria.get('group_ids', [])
        contact_ids_query = db.session.query(ContactGroupMembership.contact_id).filter(
            ContactGroupMembership.group_id.in_(group_ids)
        )
        return Contact.query.filter(
            Contact.id.in_(contact_ids_query),
            Contact.is_active == True,
            Contact.do_not_email == False
        ).all()
```

## Database Models

### Core Models

#### Contact Model
```python
class Contact(db.Model):
    __tablename__ = 'contacts'
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=False, index=True)
    # ... additional fields

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()
```

#### EmailCampaign Model
```python
class EmailCampaign(db.Model):
    __tablename__ = 'email_campaigns'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    subject = db.Column(db.String(255), nullable=False)
    template_name = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(50), default='draft', index=True)
    # ... additional fields
```

#### Activity Model
```python
class Activity(db.Model):
    __tablename__ = 'activities'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=True, index=True)
    activity_type = db.Column(db.String(50), nullable=False, index=True)
    subject = db.Column(db.String(255), nullable=True)
    description = db.Column(db.Text, nullable=True)
    # ... additional fields
```

### Relationship Models

#### ContactGroup Model
```python
class ContactGroup(db.Model):
    __tablename__ = 'contact_groups'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    color = db.Column(db.String(7), default='#007bff')
```

#### ContactGroupMembership Model
```python
class ContactGroupMembership(db.Model):
    __tablename__ = 'contact_group_memberships'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('contact_groups.id'), nullable=False)
    __table_args__ = (db.UniqueConstraint('contact_id', 'group_id', name='unique_contact_group'),)
```

#### CampaignGroup Model
```python
class CampaignGroup(db.Model):
    __tablename__ = 'campaign_groups'
    id = db.Column(db.Integer, primary_key=True)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'), nullable=False)
    group_id = db.Column(db.Integer, db.ForeignKey('contact_groups.id'), nullable=False)
    __table_args__ = (db.UniqueConstraint('campaign_id', 'group_id', name='unique_campaign_group'),)
```

#### EmailLog Model
```python
class EmailLog(db.Model):
    __tablename__ = 'email_logs'
    id = db.Column(db.Integer, primary_key=True)
    campaign_id = db.Column(db.Integer, db.ForeignKey('email_campaigns.id'), nullable=False)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=False)
    status = db.Column(db.String(50), default='pending', index=True)
    sent_at = db.Column(db.DateTime, nullable=True, index=True)
    delivered_at = db.Column(db.DateTime, nullable=True)
    opened_at = db.Column(db.DateTime, nullable=True)
    # ... tracking fields
```

## API Endpoints

### Contact API Endpoints

#### Contact Count API
```python
@app.route('/api/contacts/count', methods=['GET', 'POST'])
def api_contacts_count():
    """Count contacts based on criteria"""
    if request.method == 'GET':
        active = request.args.get('active', 'true').lower() == 'true'
        do_not_email = request.args.get('do_not_email', 'false').lower() == 'true'

        query = Contact.query
        if active:
            query = query.filter_by(is_active=True)
        if not do_not_email:
            query = query.filter_by(do_not_email=False)

        count = query.count()
        return jsonify({'count': count})
    else:
        # POST with filter criteria
        filters = request.get_json() or {}
        query = Contact.query.filter_by(is_active=True, do_not_email=False)
        # Apply additional filters...
        count = query.count()
        return jsonify({'count': count})
```

#### Chatbot Session Tracking API
```python
@app.route('/api/chatbot/session', methods=['POST'])
def track_chatbot_session():
    """Track chatbot session progression"""
    data = request.json
    session_id = data.get('session_id')
    stage = data.get('stage')
    contact_email = data.get('contact_email')

    # Find or create contact
    contact = Contact.query.filter_by(chatbot_session_id=session_id).first()
    if not contact and contact_email:
        contact = Contact.query.filter_by(email=contact_email).first()
        if contact:
            contact.chatbot_session_id = session_id

    # Create activity record
    activity = Activity(
        contact_id=contact.id if contact else None,
        activity_type='chatbot_interaction',
        subject=f'Chatbot Session - {stage}',
        description=f'Stage: {stage}, Task: {data.get("task")}'
    )
    db.session.add(activity)
    db.session.commit()

    return jsonify({'success': True})
```

### Campaign API Endpoints

#### Statistics Synchronization API
```python
@app.route('/api/sync-statistics', methods=['POST'])
def sync_statistics():
    """Manually sync all campaign statistics"""
    success = sync_campaign_statistics()
    if success:
        return jsonify({'success': True, 'message': 'Statistics synchronized successfully'})
    else:
        return jsonify({'success': False, 'message': 'Failed to synchronize statistics'})
```

## Deletion Operations

### Contact Deletion

#### Comprehensive Contact Deletion Function
```python
def delete_contact_and_content(contact_id):
    """Comprehensive contact deletion with all related content"""
    contact = Contact.query.get(contact_id)
    if not contact:
        return "Contact not found"

    deleted_items = []

    # 1. Delete all activities for this contact
    activities_deleted = db.session.execute(
        db.text("DELETE FROM activities WHERE contact_id = :contact_id"),
        {"contact_id": contact_id}
    ).rowcount
    if activities_deleted > 0:
        deleted_items.append(f"{activities_deleted} activities")

    # 2. Delete all chatbot sessions for this contact
    sessions_deleted = db.session.execute(
        db.text("DELETE FROM chatbot_sessions WHERE contact_id = :contact_id"),
        {"contact_id": contact_id}
    ).rowcount
    if sessions_deleted > 0:
        deleted_items.append(f"{sessions_deleted} chatbot sessions")

    # 3. Delete all email logs for this contact
    email_logs_deleted = db.session.execute(
        db.text("DELETE FROM email_logs WHERE contact_id = :contact_id"),
        {"contact_id": contact_id}
    ).rowcount
    if email_logs_deleted > 0:
        deleted_items.append(f"{email_logs_deleted} email logs")

    # 4. Delete all group memberships for this contact
    memberships_deleted = db.session.execute(
        db.text("DELETE FROM contact_group_memberships WHERE contact_id = :contact_id"),
        {"contact_id": contact_id}
    ).rowcount
    if memberships_deleted > 0:
        deleted_items.append(f"{memberships_deleted} group memberships")

    # 5. Delete all opportunities for this contact
    opportunities_deleted = db.session.execute(
        db.text("DELETE FROM opportunities WHERE contact_id = :contact_id"),
        {"contact_id": contact_id}
    ).rowcount
    if opportunities_deleted > 0:
        deleted_items.append(f"{opportunities_deleted} opportunities")

    # 6. Update campaign statistics
    try:
        campaigns = EmailCampaign.query.all()
        for campaign in campaigns:
            if hasattr(contact, 'chatbot_session_started') and contact.chatbot_session_started:
                campaign.chatbot_conversations_started = max(0, campaign.chatbot_conversations_started - 1)
            if hasattr(contact, 'conversion_completed') and contact.conversion_completed:
                campaign.conversions_achieved = max(0, campaign.conversions_achieved - 1)
        deleted_items.append("campaign statistics updated")
    except Exception as e:
        app.logger.warning(f"Error updating campaign statistics: {str(e)}")

    # 7. Delete the contact itself
    db.session.delete(contact)
    deleted_items.append("1 contact")

    # 8. Commit all changes
    db.session.commit()

    return ", ".join(deleted_items)
```

#### Contact Deletion Route
```python
@app.route('/contacts/<int:contact_id>/delete', methods=['POST'])
def delete_contact(contact_id):
    """Delete contact and all related content"""
    contact = Contact.query.get_or_404(contact_id)
    deleted_items = delete_contact_and_content(contact_id)
    flash(f'Contact "{contact.full_name}" and all related content deleted successfully! Removed: {deleted_items}', 'success')
    return redirect(url_for('contacts_list'))
```

#### Bulk Contact Deletion
```python
@app.route('/contacts/bulk-delete', methods=['POST'])
def bulk_delete_contacts():
    """Bulk delete multiple contacts and all their content"""
    contact_ids = request.form.getlist('contact_ids')
    contact_ids = [int(id) for id in contact_ids if id.isdigit()]

    total_deleted_items = []
    contacts_deleted = 0

    for contact_id in contact_ids:
        try:
            deleted_items = delete_contact_and_content(contact_id)
            total_deleted_items.append(deleted_items)
            contacts_deleted += 1
        except Exception as e:
            app.logger.error(f"Error deleting contact {contact_id}: {str(e)}")
            continue

    if contacts_deleted > 0:
        flash(f'Successfully deleted {contacts_deleted} contacts and all related content!', 'success')
    else:
        flash('No contacts were deleted due to errors.', 'error')

    return redirect(url_for('contacts_list'))
```

### Campaign Deletion

#### Comprehensive Campaign Deletion Function
```python
def delete_campaign_and_content(campaign_id):
    """Comprehensive campaign deletion with all related content"""
    campaign = EmailCampaign.query.get(campaign_id)
    if not campaign:
        return "Campaign not found"

    deleted_items = []

    # 1. Delete all activities related to this campaign
    activities_deleted = Activity.query.filter_by(session_id=str(campaign_id)).delete()
    if activities_deleted > 0:
        deleted_items.append(f"{activities_deleted} activities")

    # 2. Delete all email logs for this campaign
    email_logs_deleted = EmailLog.query.filter_by(campaign_id=campaign_id).delete()
    if email_logs_deleted > 0:
        deleted_items.append(f"{email_logs_deleted} email logs")

    # 3. Delete all campaign group associations
    campaign_groups_deleted = CampaignGroup.query.filter_by(campaign_id=campaign_id).delete()
    if campaign_groups_deleted > 0:
        deleted_items.append(f"{campaign_groups_deleted} group associations")

    # 4. Delete the campaign itself
    db.session.delete(campaign)
    deleted_items.append("1 campaign")

    # 5. Commit all changes
    db.session.commit()

    return ", ".join(deleted_items)
```

#### Campaign Deletion Route
```python
@app.route('/campaigns/<int:campaign_id>/delete', methods=['POST'])
def delete_campaign(campaign_id):
    """Delete campaign and all related content"""
    campaign = EmailCampaign.query.get_or_404(campaign_id)
    force_delete = request.form.get('force_delete') == 'true'

    if campaign.status not in ['draft', 'failed'] and not force_delete:
        flash('This campaign has been sent and contains tracking data. Use "Force Delete" to permanently remove all data.', 'warning')
        return redirect(url_for('view_campaign', campaign_id=campaign_id))

    deleted_items = delete_campaign_and_content(campaign_id)
    flash(f'Campaign "{campaign.name}" and all related content deleted successfully! Removed: {deleted_items}', 'success')
    return redirect(url_for('campaigns_list'))
```

#### Bulk Campaign Deletion
```python
@app.route('/campaigns/bulk-delete', methods=['POST'])
def bulk_delete_campaigns():
    """Bulk delete multiple campaigns and all their content"""
    campaign_ids = request.form.getlist('campaign_ids')
    force_delete = request.form.get('force_delete') == 'true'
    campaign_ids = [int(id) for id in campaign_ids if id.isdigit()]

    total_deleted_items = []
    campaigns_deleted = 0

    for campaign_id in campaign_ids:
        try:
            deleted_items = delete_campaign_and_content(campaign_id)
            total_deleted_items.append(deleted_items)
            campaigns_deleted += 1
        except Exception as e:
            app.logger.error(f"Error deleting campaign {campaign_id}: {str(e)}")
            continue

    if campaigns_deleted > 0:
        flash(f'Successfully deleted {campaigns_deleted} campaigns and all related content!', 'success')
    else:
        flash('No campaigns were deleted due to errors.', 'error')

    return redirect(url_for('campaigns_list'))
```

## Group Management

### Contact Group Creation
```python
@app.route('/groups/create', methods=['GET', 'POST'])
def create_group():
    """Create new contact group"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        color = request.form.get('color', '#007bff')

        if not name:
            flash('Group name is required.', 'error')
            return redirect(request.url)

        # Check if group already exists
        existing_group = ContactGroup.query.filter_by(name=name).first()
        if existing_group:
            flash('A group with this name already exists.', 'error')
            return redirect(request.url)

        group = ContactGroup(
            name=name,
            description=description,
            color=color,
            created_by='manual'
        )

        db.session.add(group)
        db.session.commit()

        flash(f'Group "{name}" created successfully!', 'success')
        return redirect(url_for('groups_list'))

    return render_template('create_group.html')
```

### Adding Contacts to Groups
```python
@app.route('/groups/<int:group_id>/add-contacts', methods=['GET', 'POST'])
def add_contacts_to_group(group_id):
    """Add contacts to a group"""
    group = ContactGroup.query.get_or_404(group_id)

    if request.method == 'POST':
        selected_contact_ids = request.form.getlist('contact_ids')
        contacts_added = 0
        contacts_skipped = 0

        for contact_id in selected_contact_ids:
            # Check if membership already exists
            existing_membership = ContactGroupMembership.query.filter_by(
                contact_id=contact_id,
                group_id=group_id
            ).first()

            if not existing_membership:
                membership = ContactGroupMembership(
                    contact_id=contact_id,
                    group_id=group_id,
                    added_by='manual'
                )
                db.session.add(membership)
                contacts_added += 1
            else:
                contacts_skipped += 1

        db.session.commit()

        message_parts = []
        if contacts_added > 0:
            message_parts.append(f'{contacts_added} contacts added')
        if contacts_skipped > 0:
            message_parts.append(f'{contacts_skipped} already in group')

        flash(f'Group updated! {", ".join(message_parts)}.', 'success')
        return redirect(url_for('view_group', group_id=group_id))

    # Get contacts not in this group
    existing_contact_ids = db.session.query(ContactGroupMembership.contact_id).filter_by(group_id=group_id)
    available_contacts = Contact.query.filter(
        ~Contact.id.in_(existing_contact_ids),
        Contact.is_active == True
    ).order_by(Contact.first_name, Contact.last_name).all()

    return render_template('add_contacts_to_group.html', group=group, available_contacts=available_contacts)
```

### Group Deletion (Soft Delete)
```python
@app.route('/groups/<int:group_id>/delete', methods=['POST'])
def delete_group(group_id):
    """Delete group (soft delete)"""
    group = ContactGroup.query.get_or_404(group_id)

    # Soft delete - just mark as inactive
    group.is_active = False
    group.updated_at = datetime.utcnow()

    db.session.commit()

    flash(f'Group "{group.name}" deleted successfully!', 'success')
    return redirect(url_for('groups_list'))
```

## Integration Features

### Chatbot Integration
The system includes comprehensive chatbot integration for tracking customer interactions:

#### Chatbot Session Model
```python
class ChatbotSession(db.Model):
    __tablename__ = 'chatbot_sessions'
    id = db.Column(db.Integer, primary_key=True)
    contact_id = db.Column(db.Integer, db.ForeignKey('contacts.id'), nullable=True)
    session_id = db.Column(db.String(255), unique=True, nullable=False)
    started_at = db.Column(db.DateTime, default=datetime.utcnow)
    ended_at = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(50), default='active')
    conversation_data = db.Column(db.JSON, nullable=True)
```

#### Email-to-Chatbot Tracking
```python
@app.route('/api/session-from-email/<session_id>')
def track_email_to_session(session_id):
    """Track when user clicks from email to chatbot"""
    contact = Contact.query.filter_by(chatbot_session_id=session_id).first()
    if contact:
        # Update contact's chatbot engagement
        contact.chatbot_session_started = True
        contact.last_activity = datetime.utcnow()

        # Create activity record
        activity = Activity(
            contact_id=contact.id,
            activity_type='email_to_chatbot',
            subject='Email to Chatbot Transition',
            description=f'Contact clicked from email campaign to start chatbot session'
        )
        db.session.add(activity)
        db.session.commit()

    return redirect(url_for('chatbot_interface', session_id=session_id))
```

### Analytics and Reporting
The system provides comprehensive analytics for both contacts and campaigns:

#### Unified Analytics Function
```python
def get_unified_analytics():
    """Get synchronized analytics across all systems"""
    try:
        total_contacts = Contact.query.filter_by(is_active=True).count()
        total_campaigns = EmailCampaign.query.count()
        active_campaigns = EmailCampaign.query.filter_by(status='running').count()

        # Email statistics
        total_emails_sent = db.session.query(db.func.sum(EmailCampaign.emails_sent)).scalar() or 0
        total_emails_opened = db.session.query(db.func.sum(EmailCampaign.emails_opened)).scalar() or 0

        # Chatbot statistics
        total_chatbot_sessions = ChatbotSession.query.count()
        active_chatbot_sessions = ChatbotSession.query.filter_by(status='active').count()

        return {
            'total_contacts': total_contacts,
            'total_campaigns': total_campaigns,
            'active_campaigns': active_campaigns,
            'total_emails_sent': total_emails_sent,
            'total_emails_opened': total_emails_opened,
            'email_open_rate': (total_emails_opened / total_emails_sent * 100) if total_emails_sent > 0 else 0,
            'total_chatbot_sessions': total_chatbot_sessions,
            'active_chatbot_sessions': active_chatbot_sessions
        }
    except Exception as e:
        app.logger.error(f"Analytics error: {str(e)}")
        return {}
```

## Error Handling and Validation

### Contact Validation
- Email uniqueness is enforced at the database level
- Required fields are validated before database insertion
- Error messages are displayed to users via Flask flash messages
- Database rollback occurs on errors to maintain data integrity

### Campaign Validation
- Campaign names must be provided
- Template names must match available templates
- Recipient criteria is validated before campaign creation
- Daily send limits are enforced with integer validation

### Deletion Safety
- Confirmation dialogs are required for all deletion operations
- Force delete option is available for campaigns with tracking data
- Comprehensive logging tracks all deletion operations
- Database transactions ensure atomic deletion of related records

## Best Practices for Implementation

1. **Always use database transactions** for multi-table operations
2. **Implement proper error handling** with try-catch blocks and rollbacks
3. **Use foreign key constraints** to maintain referential integrity
4. **Index frequently queried fields** like email, status, and timestamps
5. **Implement soft deletes** where historical data is important
6. **Use JSON fields** for flexible criteria storage
7. **Maintain audit trails** through activity logging
8. **Validate user input** before database operations
9. **Use pagination** for large data sets
10. **Implement proper logging** for debugging and monitoring

This documentation provides a complete reference for implementing the contacts and campaigns system with all its features, relationships, and safety mechanisms.
