#!/usr/bin/env python3
"""
Create test data directly in the database for analytics demonstration
"""

import sqlite3
import json
from datetime import datetime, timedelta
import random

def create_test_data():
    """Create comprehensive test data directly in database"""
    
    print('🚀 Creating test data directly in database...')
    print('=' * 50)
    
    # Connect to database
    conn = sqlite3.connect('instance/unified_sales.db')
    cursor = conn.cursor()
    
    # Test sessions with different progression patterns
    test_sessions = [
        {'session_id': 'analytics_test_001', 'name': '<PERSON>', 'email': '<EMAIL>', 'stages': 5, 'convert': True},
        {'session_id': 'analytics_test_002', 'name': '<PERSON>', 'email': '<EMAIL>', 'stages': 3, 'convert': False},
        {'session_id': 'analytics_test_003', 'name': '<PERSON>', 'email': '<EMAIL>', 'stages': 4, 'convert': False},
        {'session_id': 'analytics_test_004', 'name': '<PERSON>', 'email': '<EMAIL>', 'stages': 5, 'convert': True},
        {'session_id': 'analytics_test_005', 'name': '<PERSON> <PERSON>', 'email': '<EMAIL>', 'stages': 2, 'convert': False},
        {'session_id': 'analytics_test_006', 'name': 'Frank Miller', 'email': '<EMAIL>', 'stages': 5, 'convert': True},
        {'session_id': 'analytics_test_007', 'name': 'Grace Lee', 'email': '<EMAIL>', 'stages': 1, 'convert': False},
        {'session_id': 'analytics_test_008', 'name': 'Henry Taylor', 'email': '<EMAIL>', 'stages': 4, 'convert': False},
        {'session_id': 'analytics_test_009', 'name': 'Ivy Chen', 'email': '<EMAIL>', 'stages': 5, 'convert': True},
        {'session_id': 'analytics_test_010', 'name': 'Jack Wilson', 'email': '<EMAIL>', 'stages': 3, 'convert': False},
    ]
    
    base_time = datetime.now() - timedelta(hours=3)
    stages = ['opening', 'trust', 'discovery', 'demonstration', 'close']
    
    for i, session in enumerate(test_sessions):
        print(f'📊 Creating session {i+1}: {session["name"]} (stages: {session["stages"]}, convert: {session["convert"]})')
        
        # Create contact
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO contacts 
                (first_name, last_name, email, chatbot_session_id, created_at, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                session['name'].split()[0],
                session['name'].split()[1],
                session['email'],
                session['session_id'],
                base_time + timedelta(minutes=i*15),
                1
            ))
            
            contact_id = cursor.lastrowid
            print(f'  ✅ Contact created: ID {contact_id}')
            
        except Exception as e:
            print(f'  ❌ Contact creation failed: {e}')
            continue
        
        # Calculate session timing
        session_start = base_time + timedelta(minutes=i*15)
        session_end = session_start + timedelta(minutes=random.randint(20, 50))
        
        # Create stage timing based on progression
        stage_times = {}
        current_time = session_start
        
        for stage_idx in range(session['stages']):
            stage = stages[stage_idx]
            stage_duration = random.randint(3, 10)  # 3-10 minutes per stage
            
            stage_times[f'{stage}_started_at'] = current_time.isoformat()
            if stage_idx < session['stages'] - 1 or session['convert']:
                stage_times[f'{stage}_completed_at'] = (current_time + timedelta(minutes=stage_duration)).isoformat()
            current_time += timedelta(minutes=stage_duration + 2)
        
        # Create chatbot session
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO chatbot_sessions 
                (session_id, contact_id, started_at, ended_at, current_stage, 
                 total_messages, user_messages, bot_messages, 
                 opening_started_at, opening_completed_at,
                 trust_started_at, trust_completed_at,
                 discovery_started_at, discovery_completed_at,
                 demonstration_started_at, demonstration_completed_at,
                 close_started_at, close_completed_at,
                 completed_successfully, conversion_achieved, engagement_level,
                 conversion_value, final_stage_reached)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                session['session_id'],
                contact_id,
                session_start.isoformat(),
                session_end.isoformat() if session['convert'] else None,
                stages[session['stages']-1],
                random.randint(15, 35),
                random.randint(7, 18),
                random.randint(8, 17),
                stage_times.get('opening_started_at'),
                stage_times.get('opening_completed_at'),
                stage_times.get('trust_started_at'),
                stage_times.get('trust_completed_at'),
                stage_times.get('discovery_started_at'),
                stage_times.get('discovery_completed_at'),
                stage_times.get('demonstration_started_at'),
                stage_times.get('demonstration_completed_at'),
                stage_times.get('close_started_at'),
                stage_times.get('close_completed_at'),
                1 if session['convert'] else 0,
                1 if session['convert'] else 0,
                'high' if session['convert'] else random.choice(['low', 'medium', 'high']),
                round(random.uniform(1500, 4500), 2) if session['convert'] else None,
                stages[session['stages']-1]
            ))
            
            print(f'  ✅ Session created: {session["session_id"]}')
            
        except Exception as e:
            print(f'  ❌ Session creation failed: {e}')
            continue
    
    # Commit changes
    conn.commit()
    
    # Verify data
    cursor.execute('SELECT COUNT(*) FROM chatbot_sessions WHERE session_id LIKE "analytics_test_%"')
    session_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM chatbot_sessions WHERE session_id LIKE "analytics_test_%" AND conversion_achieved = 1')
    conversion_count = cursor.fetchone()[0]
    
    conn.close()
    
    print('\n' + '=' * 50)
    print('🎉 Test data creation completed!')
    print(f'📊 Created {session_count} test sessions')
    print(f'🎯 {conversion_count} conversions achieved')
    print('\n📈 Analytics dashboards now show:')
    print('   • Complete sales pipeline progression')
    print('   • Stage-to-stage conversion rates')
    print('   • Sales cycle timing analysis')
    print('   • Drop-off patterns and optimization opportunities')
    print('\n🔗 View analytics at:')
    print('   • Sales Pipeline: http://localhost:5000/analytics/sales-pipeline')
    print('   • Sales Cycle: http://localhost:5000/analytics/sales-cycle')
    print('   • Session Analytics: http://localhost:5000/analytics/sessions')
    print('   • Main Dashboard: http://localhost:5000/analytics')

if __name__ == '__main__':
    create_test_data()
